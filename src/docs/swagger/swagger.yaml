swagger: "2.0"
info:
  description: "This will define all the configuration API's exposed to create the configurations in AppsOne."
  version: "1.0"
  title: "AppsOne Configuration APIs"
  contact:
    name: "Appnomic Support"
    email: "<EMAIL>"
host: "keycloak.appnomic"
basePath: "/heal-controlcenter/v2.0/api/"
schemes:
  - "https"
securityDefinitions:
  authKey:
    type: apiKey
    in: header
    name: Authorization
tags:
  - name: application
    description: Application resource
  - name: service
    description: Service resource
  - name: agent
    description: Agent resource
  - name: user
    description: User resource
  - name: standard
    description: Standard resource
  - name: batch
    description: Batch resource
paths:
  /timezones:
    get:
      tags:
        - "standard"
      summary: "Get all the timezones."
      operationId: "GetTimezones"
      consumes:
        - application/json
      produces:
        - application/json
      security:
        - authKey: []
      responses:
        200:
          description: "Timezone list is fetched successfully."
          schema:
            $ref: "#/definitions/TimezoneResponse"
        500:
          description: "Server related issues."
          schema:
            $ref: "#/definitions/TimezoneResponse"
  /installation-attributes:
    get:
      tags:
        - "standard"
      summary: "Get all installation attributes."
      operationId: "GetInstallationAttributes"
      consumes:
        - application/json
      produces:
        - application/json
      security:
        - authKey: []
      responses:
        200:
          description: "Installation attributes fetched successfully."
          schema:
            $ref: "#/definitions/InstallationResponse"
  /accounts/{accountIdentifier}/connections:
    post:
      tags:
        - service
      summary: "Add new service connections to the specified account."
      operationId: "AddConnections"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier where connections will be added"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "List of connections to be added to the account."
          required: true
          schema:
            $ref: "#/definitions/ConnectionDetailsList"
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/ResponseSuccess"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /accounts/{accountIdentifier}/connections/{sourceIdentifier}/{destinationIdentifier}:
    delete:
      tags:
        - service
      summary: "Delete connection for the specified account"
      operationId: "DeleteConnection"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier of the connection to be deleted."
          required: true
          type: "string"
        - name: "sourceIdentifier"
          in: path
          description: "Service identifier of the source."
          required: true
          type: "string"
        - name: "destinationIdentifier"
          in: path
          description: "Service identifier of the destination."
          required: true
          type: "string"
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/Success"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /{accountIdentifier}/service:
    post:
      tags:
        - application
      summary: "Add new service to the specified account"
      operationId: "Add"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier where service will be added"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "An array of service objects that needs to be added to the account."
          required: true
          schema:
            $ref: '#/definitions/Service'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/SuccessResponse"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /accounts/{accountIdentifier}/categories:
    post:
      tags:
        - application
      summary: "Add a new category to the specified account."
      operationId: "AddCategory"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier where application will be added"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "Category object that needs to be added to the account."
          required: true
          schema:
            $ref: '#/definitions/AddCategory'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/ResponseSuccess"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Server related issues."
          schema:
            $ref: "#/definitions/ServerErrorResponse"
    get:
      tags:
        - application
      summary: "KPI categories for the specified account."
      operationId: "GetCategories"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier"
          required: true
          type: "string"
      security:
        - authKey: [ ]
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/CategoriesFetchSuccessResponse"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/RequestFailureResponse"
        500:
          description: "Server related issues."
          schema:
            $ref: "#/definitions/ServerErrorResponse"
  /accounts/{accountIdentifier}/component-details:
    get:
      tags:
        - application
      summary: "Component details for the specified account."
      operationId: "GetComponentDetails"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier"
          required: true
          type: "string"
      security:
        - authKey: [ ]
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/ComponentDetailsFetchSuccessResponse"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/RequestFailureResponse"
        500:
          description: "Server related issues."
          schema:
            $ref: "#/definitions/ServerErrorResponse"
  /{accountIdentifier}/addForensics:
    post:
      tags:
        - application
      summary: "Add a new forensic to the specified account and map it to existing categories."
      operationId: "AddForensic"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier where application will be added"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "Application object that needs to be added to the account."
          required: true
          schema:
            $ref: '#/definitions/AddForensic'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/ResponseSuccess"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /{accountIdentifier}/addProducer:
    post:
      tags:
        - application
      summary: "Add a new producer to the specified account."
      operationId: "AddProducer"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier where application will be added"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "Application object that needs to be added to the account."
          required: true
          schema:
            $ref: '#/definitions/AddProducer'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/Success"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /{accountIdentifier}/applications-old:
    post:
      tags:
        - application
      summary: "Add new application to the specified account"
      operationId: "AddApplicationToAccount"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier where application will be added"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "Application object that needs to be added to the account."
          required: true
          schema:
            $ref: '#/definitions/Application'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/SuccessResponse"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /accounts/{accountIdentifier}/applications:
    get:
      tags:
        - application
      summary: "Get the list of Applications for a specified account"
      operationId: "GetApplications"
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier"
          required: true
          type: "string"
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/GetApplicationsResponse"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Internal server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
    post:
      tags:
        - application
      summary: "Add a new application to the specified account."
      operationId: "AddApplication"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier where application will be added"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "Application object that needs to be added to the account."
          required: true
          schema:
            $ref: '#/definitions/AddApplication'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/SuccessResponse"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /accounts/{accountIdentifier}/applications/{applicationIdentifier}:
    put:
      tags:
        - application
      summary: "Edit Application."
      operationId: "EditApplication"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier for the application to be edited"
          required: true
          type: "string"
        - name: "applicationIdentifier"
          in: path
          description: "Identifier of the application to be edited"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "Edit application details"
          required: true
          schema:
            $ref: '#/definitions/EditApplication'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/ResponseSuccess"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /{accountIdentifier}/addKPIGroup:
    post:
      tags:
        - application
      summary: "Add new kpi group to the specified account."
      operationId: "AddKPIGroup"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier where application will be added"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "Application object that needs to be added to the account."
          required: true
          schema:
            $ref: '#/definitions/AddKPIGroup'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/ResponseSuccess"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /{accountIdentifier}/addKPI:
    post:
      tags:
        - application
      summary: "Add new kpi to the specified account."
      operationId: "AddKPI"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier where application will be added"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "Application object that needs to be added to the account."
          required: true
          schema:
            $ref: '#/definitions/AddKPI'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/ResponseSuccess"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /{accountIdentifier}/createComponent:
    post:
      tags:
        - application
      summary: "Add new component to the specified account."
      operationId: "AddComponent"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier where application will be added"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "Application object that needs to be added to the account."
          required: true
          schema:
            $ref: '#/definitions/AddComponent'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/SuccessResponseAddComponent"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /{accountIdentifier}/addHost:
    post:
      tags:
        - application
      summary: "Add new host to the specified account."
      operationId: "AddHost"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier where application will be added"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "Application object that needs to be added to the account."
          required: true
          schema:
            $ref: '#/definitions/AddHost'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/Success"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /{accountIdentifier}/addCluster:
    post:
      tags:
        - application
      summary: "Add new cluster to the specified account."
      operationId: "AddCluster"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier where application will be added"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "Application object that needs to be added to the account."
          required: true
          schema:
            $ref: '#/definitions/AddCluster'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/Success"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /{accountIdentifier}/addTransaction:
    post:
      tags:
        - application
      summary: "Add new transaction to the specified account."
      operationId: "AddTransaction"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier where application will be added"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "Application object that needs to be added to the account."
          required: true
          schema:
            $ref: '#/definitions/AddTransaction'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/Success"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /addAgent:
    post:
      tags:
        - agent
      summary: "Add new agent."
      operationId: "AddAgent"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "body"
          in: body
          description: "Agent(s) that needs to be added to the system."
          required: true
          schema:
            $ref: '#/definitions/AddAgent'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/Success"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /accounts/{identifier}/services/{serviceId}/threshold-types/{thresholdType}/thresholds:
    get:
      tags:
        - "service"
      summary: "Get service threshold list to the specified service."
      operationId: "GetServiceThresholdList"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "identifier"
          in: path
          description: "Account identifier is a unique value which represent a distinct account."
          required: true
          type: "string"
        - name: "serviceId"
          in: path
          description: "A uniquely identifier of the service."
          required: true
          type: "string"
        - name: "thresholdType"
          in: path
          description: "Threshold Type is SOR or NOR"
          required: true
          type: "string"
        - name: "kpiType"
          in: query
          description: "It is type of KPI."
          required: true
          type: "string"
          enum:
            - "Core"
            - "Availability"
      security:
        - authKey: []
      responses:
        200:
          description: "KPI list is successfully extracted"
          schema:
            $ref: "#/definitions/ConfigSuccessResponse"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
    post:
      tags:
        - "service"
      summary: "Update service threshold list to the specified service."
      operationId: "UpdateServiceThreshold"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "identifier"
          in: path
          description: "Account identifier is a unique value which represent a distinct account."
          required: true
          type: "string"
        - name: "serviceId"
          in: path
          description: "A uniquely identifier of the service."
          required: true
          type: "string"
        - name: "thresholdType"
          in: path
          description: "Threshold Type is SOR or NOR"
          required: true
          type: "string"
        - name: "kpiType"
          in: query
          description: "It is type of KPI.Ex: Core,Availability"
          required: true
          type: "string"
          enum: [Core,Availability]
        - name: "body"
          in: body
          description: "Service Threshold Objects that needs to be added to the service."
          required: true
          schema:
            $ref: '#/definitions/ListOfKpiConfigDetails'
        - name: "isSystem"
          in: query
          description: "It is flag only for System thresholds."
          type: boolean
      security:
        - authKey: []
      responses:
        200:
          description: "Thresholds are updated."
          schema:
            $ref: "#/definitions/KpiConfigSuccessResponse"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /accounts/{accountIdentifier}/sms-configurations:
    post:
      tags:
        - application
      summary: "Add new SMS settings to the specified account."
      operationId: "AddSmsSettings"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier where SMS will be added"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "SMS object that needs to be added to the account."
          required: true
          schema:
            $ref: '#/definitions/SmsDetails'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: '#/definitions/Success'
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
    put:
      tags:
        - application
      summary: "Update SMS settings of the specified account."
      operationId: "UpdateSmsSettings"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier where SMS will be updated"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "SMS object that needs to be updated to the account."
          required: true
          schema:
            $ref: '#/definitions/SmsDetails'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: '#/definitions/Success'
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
    get:
      tags:
        - application
      summary: "Get SMS settings of the specified account."
      operationId: "GetSmsSettings"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier of the account."
          required: true
          type: "string"
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/SuccessSMSResponse"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /accounts/{accountIdentifier}/email-Configurations:
    post:
      tags:
        - application
      summary: "Add new SMTP settings to the specified account."
      operationId: "AddEmailSettings"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier where SMS will be added"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "Email object that needs to be added to the account."
          required: true
          schema:
            $ref: '#/definitions/EmailDetails'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/Success"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
    put:
      tags:
        - application
      summary: "Update SMTP settings of the specified account."
      operationId: "UpdateEmailSettings"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier where SMS will be added"
          required: true
          type: "string"
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/Success"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
    get:
      tags:
        - application
      summary: "Get SMTP settings of the specified account."
      operationId: "GetEmailSettings"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier of the account."
          required: true
          type: "string"
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: '#/definitions/SuccessSMTPResponse'
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /users:
    get:
      tags:
        - user
      summary: "Get the list of Users"
      operationId: "GetUsers"
      produces:
        - application/json
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/GetUsersResponse"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Internal server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
    post:
      tags:
        - user
      summary: "Add a new user."
      operationId: "AddUser"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "body"
          in: body
          description: "User object that needs to be added."
          required: true
          schema:
            $ref: '#/definitions/UserInfo'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/SuccessResponse"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /users/{userIdentifier}:
    delete:
      tags:
        - user
      summary: "Delete user from the system."
      operationId: "DeleteUser"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "userIdentifier"
          in: path
          description: "User identifier which will be deleted."
          required: true
          type: "string"
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/Success"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
    get:
      tags:
        - user
      summary: "Get user details."
      operationId: "GetUserDetails"
      produces:
        - application/json
      parameters:
        - name: "userIdentifier"
          in: path
          description: "User identifier of the user"
          required: true
          type: "string"
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/UserInfo"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Internal server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
    put:
      tags:
        - user
      summary: "Update user details"
      operationId: "EditUser"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "userIdentifier"
          in: path
          description: "User identifier of the user to be edited"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "User object that needs to be updated."
          required: true
          schema:
            $ref: '#/definitions/UserInfo'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/SuccessResponse"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /users/username-validation:
    get:
      tags:
        - user
      summary: "To check for validity of a username"
      operationId: "ValidateUserName"
      parameters:
        - name: "name"
          in: query
          description: "username "
          required: true
          type: "string"
      produces:
        - application/json
      security:
        - authKey: []
      responses:
        200:
          description: "Username Valid."
          schema:
            $ref: "#/definitions/ResponseSuccess"
        400:
          description: "Username Invalid"
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Internal server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /page-details:
    get:
      tags:
        - standard
      summary: "get page details from the system."
      operationId: "GetPageDetails"
      consumes:
        - application/json
      produces:
        - application/json
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/GetPageDetailsResponse"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /accounts/{accountIdentifier}/notification-settings:
    get:
      tags:
        - application
      summary: "List of notification settings to the specified account."
      operationId: "GetNotificationSettings"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier"
          required: true
          type: "string"
      security:
        - authKey: [ ]
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/NotificationSuccess"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
    put:
      tags:
        - application
      summary: "Add new notification settings to the specified account."
      operationId: "AddNotificationSettings"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier where notification settings will be added"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "Notification settings object that needs to be added to the account."
          required: true
          schema:
            $ref: '#/definitions/NotificationPreferenceList'
      security:
        - authKey: []
      responses:
        200:
          description: "Successful operation"
          schema:
            $ref: "#/definitions/Success"
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /accounts/{accountIdentifier}/transactions:
    put:
      tags:
        - service
      summary: "Updated the list of Transctions to the specified account."
      operationId: "UpdateTransactions"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "Transaction that needs to be updated to the service."
          required: true
          schema:
            $ref: '#/definitions/TransactionsList'
      security:
        - authKey: [ ]
      responses:
        200:
          description: "Records updated successfully"
          schema:
            $ref: '#/definitions/Success'
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /accounts/{accountIdentifier}/rules:
    put:
      tags:
        - service
      summary: "Updated the list of Rule to the specified account."
      operationId: "UpdateRules"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier"
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "Rule that needs to be updated to the service."
          required: true
          schema:
            $ref: '#/definitions/RulesList'
      security:
        - authKey: [ ]
      responses:
        200:
          description: "Records updated successfully"
          schema:
            $ref: '#/definitions/Success'
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /accounts/{identifier}/batch-details/{batchJobName}/process-details:
    post:
      tags:
        - batch
      summary: "Add process to the specified batch."
      operationId: "PostBatchProcess"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "identifier"
          in: path
          description: "Account identifier"
          required: true
          type: "string"
        - name: "batchJobName"
          in: path
          description: "Unique identifier of the batch."
          required: true
          type: "string"
        - name: "body"
          in: body
          description: "Process that needs to be added to the batch."
          required: true
          schema:
            $ref: '#/definitions/BatchProcessDetails'
      security:
        - authKey: [ ]
      responses:
        200:
          description: "Process added successfully"
          schema:
            $ref: '#/definitions/SuccessResponse'
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/RequestFailureResponse"
        500:
          description: "Server related issues."
          schema:
            $ref: "#/definitions/ServerErrorResponse"
    get:
      tags:
        - batch
      summary: "Fetch all processes for the specified batch."
      operationId: "GetBatchProcesses"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "identifier"
          in: path
          description: "Account identifier"
          required: true
          type: "string"
        - name: "batchJobName"
          in: path
          description: "Unique identifier of the batch."
          required: true
          type: "string"
      security:
        - authKey: [ ]
      responses:
        200:
          description: "Batch processes fetched successfully."
          schema:
            $ref: '#/definitions/GetBatchProcessesResponse'
        400:
          description: "Validation failures."
          schema:
            $ref: "#/definitions/RequestFailureResponse"
        500:
          description: "Server related issues."
          schema:
            $ref: "#/definitions/ServerErrorResponse"
  /accounts/{identifier}/batch-details/{batchJobName}/process-details/{processDetailsId}:
    put:
      tags:
        - batch
      summary: "Edit process for the specified batch."
      operationId: "PutBatchProcess"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "identifier"
          in: path
          description: "Account identifier"
          required: true
          type: "string"
        - name: "batchJobName"
          in: path
          description: "Unique identifier of the batch."
          required: true
          type: "string"
        - name: "processDetailsId"
          in: path
          description: "Id of the process that needs to be updated for the batch."
          required: true
          type: "number"
        - name: "body"
          in: body
          description: "Process details to be updated to the batch."
          required: true
          schema:
            $ref: '#/definitions/BatchProcessDetails'
      security:
        - authKey: [ ]
      responses:
        200:
          description: "Process updated successfully"
          schema:
            $ref: '#/definitions/SuccessResponse'
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/RequestFailureResponse"
        500:
          description: "Server related issues."
          schema:
            $ref: "#/definitions/ServerErrorResponse"
    delete:
      tags:
        - batch
      summary: "Delete process for the specified batch."
      operationId: "DeleteBatchProcess"
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: "identifier"
          in: path
          description: "Account identifier"
          required: true
          type: "string"
        - name: "batchJobName"
          in: path
          description: "Unique identifier of the batch."
          required: true
          type: "string"
        - name: "processDetailsId"
          in: path
          description: "Id of the process that needs to be deleted for the batch."
          required: true
          type: "number"
      security:
        - authKey: [ ]
      responses:
        200:
          description: "Process deleted successfully"
          schema:
            $ref: '#/definitions/ResponseSuccess'
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/RequestFailureResponse"
        500:
          description: "Server related issues."
          schema:
            $ref: "#/definitions/ServerErrorResponse"
  /accounts/{accountIdentifier}/batch-job/thresholds:
    get:
      tags:
        - standard
      summary: "Get batch job threshold list of the specified account."
      operationId: "fetchBatchJobThreshold"
      consumes:
        - application/json
      produces:
        - application/json
      security:
        - authKey: [ ]
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier is a unique value which represent a distinct account."
          required: true
          type: "string"
        - name: "kpiIdentifier"
          in: query
          description: "Kpi Identifier for the batch job"
          required: true
          type: "string"
      responses:
        200:
          description: "Thresholds List fetched Successfully"
          schema:
            $ref: '#/definitions/BatchJobThresholdResponse'
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
  /accounts/{accountIdentifier}/batch-job/{batch-job-id}/thresholds:
    post:
      tags:
        - standard
      summary: "Process batch-job-data and update in batch-job-threshold table and fetch the updated records."
      operationId: "updateBatchJobThreshold"
      consumes:
        - application/json
      produces:
        - application/json
      security:
        - authKey: [ ]
      parameters:
        - name: "accountIdentifier"
          in: path
          description: "Account identifier is a unique value which represent a distinct account."
          required: true
          type: "string"
        - name: "batch-job-id"
          in: path
          description: "Batch-Job-Id"
          required: true
          type: "string"
        - name: "kpiIdentifier"
          in: query
          description: "Kpi Identifier for the batch job"
          required: true
          type: "string"
        - name: "from"
          in: query
          description: "Time in format YYYY-MM-DD HH:MM:SS to specify from where batch-job-data will be filtered example: 2020-07-03 07:22:54"
          required: true
          type: "string"
        - name: "to"
          in: query
          description: "Time in format YYYY-MM-DD HH:MM:SS to specify till where batch-job-data will be filtered example: 2020-07-13 07:22:54"
          required: true
          type: "string"
      responses:
        200:
          description: "Thresholds List fetched Successfully"
          schema:
            $ref: '#/definitions/BatchJobThresholdResponse'
        400:
          description: "Parameter validation failures."
          schema:
            $ref: "#/definitions/FailureResponse"
        500:
          description: "Invalid Json or any other server related issues."
          schema:
            $ref: "#/definitions/FailureResponse"
definitions:
  TimezoneResponse:
    required:
      - "responseMessage"
      - "responseStatus"
      - "data"
    properties:
      responseMessage:
        type: "string"
        example: "SUCCESS"
      responseStatus:
        type: "string"
        example: "SUCCESS"
      data:
        type: "array"
        items:
          $ref: "#/definitions/TimezoneDetail"
  TimezoneDetail:
    required:
      - "id"
      - "timeZoneId"
      - "offset"
      - "userDetailsId"
      - "accountId"
      - "status"
    properties:
      id:
        type: "number"
        format: "[0-9]"
        example: 34
      timeZoneId:
        type: "string"
        format: "[A-Za-z0-9_-]"
        example: "(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi"
      offset:
        type: "number"
        format: "[0-9]"
        example: ********
      userDetailsId:
        type: "string"
        format: "[A-Za-z0-9_-]"
        example: "abcd-57fg-tc76-8vgh"
      accountId:
        type: "number"
        format: "[0-9]"
        example: 1
      status:
        type: "number"
        format: "[0-9]"
        example: 1
  InstallationResponse:
    required:
      - "responseMessage"
      - "responseStatus"
      - "result"
    properties:
      message:
        type: "string"
        example: "SUCCESS"
      responseStatus:
        type: "string"
        example: "SUCCESS"
      result:
        type: "array"
        items:
          $ref: "#/definitions/InstallationAttribute"
  InstallationAttribute:
    required:
      - "name"
      - "value"
    properties:
      name:
        type: "string"
        format: "[A-Za-z0-9_-]"
        example: "SetupType"
      value:
        type: "string"
        format: "[A-Za-z0-9_-]"
        example: "Keycloak"
  ResponseSuccess:
    required:
      - "message"
      - "responseStatus"
      - "data"
    properties:
      message:
        type: "string"
        example: "Operation successful"
      responseStatus:
        type: "string"
        example: "SUCCESS"
      data:
        type: "number"
        example: 12
  ConnectionDetailsList:
    description: "List of connection details."
    type: "object"
    required:
      - "ConnectionDetailsList"
    properties:
      ConnectionDetailsList:
        type: "array"
        items:
          $ref: '#/definitions/ConnectionDetail'
  ConnectionDetail:
    required:
      - "sourceServiceIdentifier"
      - "destinationServiceIdentifier"
      - "isDiscovery"
    properties:
      sourceName:
        type: "string"
        format: "[A-Za-z0-9_-]"
        example: "NB-User"
      sourceServiceIdentifier:
        type: "string"
        format: "[A-Za-z0-9_-]"
        example: "abcd-57fg-tc76-8vgh"
      destinationName:
        type: "string"
        format: "[A-Za-z0-9_-]"
        example: "NB-User"
      destinationServiceIdentifier:
        type: "string"
        format: "[A-Za-z0-9_-]"
        example: "NB-DB-Service"
      isDiscovery:
        type: "number"
        format: "[0-9]"
        example: 1
  GetApplicationsResponse:
    type: "object"
    required:
      - "message"
      - "responseStatus"
      - "data"
    properties:
      message:
        type: "string"
        example: "Account add successful."
      responseStatus:
        type: "string"
        description: "Success or failure status of the request."
        enum:
          - "SUCCESS"
          - "FAILURE"
        example: "SUCCESS"
      data:
        type: "array"
        items:
          $ref: "#/definitions/GetApplication"
  GetApplication:
    type: "object"
    required:
      - "id"
      - "identifier"
      - "name"
      - "lastModifiedOn"
      - "lastModifiedBy"
      - "services"
    properties:
      id:
        type: "number"
        format: "[0-9]"
        example: 34
      identifier:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 4
        maxLength: 128
        example: "abcd-efgh-jklm-nopq"
      name:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 4
        maxLength: 128
        example: "application1"
      lastModifiedOn:
        type: "number"
        format: "[0-9]"
        example: 34
      lastModifiedBy:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 4
        maxLength: 64
        example: "abcd-efgh-jklm-nopq"
      services:
        type: "array"
        items:
          $ref: "#/definitions/Service"
  Service:
    type: "object"
    required:
      - "id"
      - "name"
    properties:
      id:
        type: "number"
        format: "[0-9]"
        example: 2
      name:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 4
        maxLength: 64
        example: "service-web"
  Application:
    type: "object"
    required:
      - "identifier"
      - "name"
      - "tags"
    properties:
      identifier:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 4
        maxLength: 64
        example: "abcd-efgh-jklm-nopq"
      name:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 4
        maxLength: 64
        example: "HDFC"
      tags:
        type: "array"
        items:
          $ref: "#/definitions/Tag"
  Tag:
    type: "object"
    required:
      - "identifier"
      - "subTypeName"
      - "name"
      - "layer"
    properties:
      identifier:
        type: "string"
        description: "This value will be validate (based on Tag name) and tagged to the parent object."
        example: "abcd-efgh-ijkl-mnop"
      name:
        type: "string"
        description: "Will allow pre-defined tags"
        enum:
          - "Controller"
          - "Timezone"
          - "Account"
          - "LayerName"
          - "Category"
          - "Agent"
        example: "Controller"
      subTypeName:
        type: "string"
        description: "Will tell identifier belongs to either Services or Application in case of `Controller` tag."
        enum:
          - "Services"
          - "Application"
        example: "Application"
      layer:
        type: "string"
        description: "This will be used to tag the layer name in case of `Controller` tag. Values can be one of `user`, `app_server`, `web_server`, `db_server`"
        example: "user"
  FailureResponse:
    type: "object"
    required:
      - "message"
      - "responseStatus"
    properties:
      message:
        type: "string"
        example: "Failure reason why the account is not added."
      responseStatus:
        type: "string"
        description: "Success or failure status of the request."
        enum:
          - "FAILURE"
          - "SUCCESS"
        example: "FAILURE"
  SuccessResponse:
    type: "object"
    required:
      - "message"
      - "responseStatus"
      - "data"
    properties:
      message:
        type: "string"
        example: "Operation successful."
      responseStatus:
        type: "string"
        description: "Success or failure status of the request."
        enum:
          - "SUCCESS"
          - "FAILURE"
        example: "SUCCESS"
      data:
        required:
          - "id"
          - "name"
          - "identifier"
        properties:
          id:
            type: "number"
            example: 2
          name:
            type: "string"
            example: "name-1"
          identifier:
            type: "string"
            example: "abcd-efgh-ijkl-mnop"
        type: "object"
  SuccessSMSResponse:
    required:
      - "message"
      - "responseStatus"
      - "data"
    properties:
      message:
        type: "string"
        example: "SMS configuration fetched successfully."
      responseStatus:
        type: "string"
        example: "SUCCESS"
      data:
        $ref: '#/definitions/SmsDetails'
  SuccessSMTPResponse:
    required:
      - "message"
      - "responseStatus"
      - "data"
    properties:
      message:
        type: "string"
        example: "SMTP configuration fetched successfully."
      responseStatus:
        type: "string"
        example: "SUCCESS"
      data:
        $ref: '#/definitions/EmailDetails'
  AddApplication:
    type: "object"
    required:
      - "name"
      - "services"
    properties:
      name:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 4
        maxLength: 128
        example: "HDFC"
        description: "Name of the application to be created"
      identifier:
        type: "string"
        description: "Application identifier"
        format: "[A-Za-z0-9_-]"
        minLength: 4
        maxLength: 128
        example: "HDFC_identifier"
      services:
        type: "array"
        description: "Identifier of the services to be mapped to the application"
        items:
          type: "string"
      timezone:
        type: "string"
        description: "Timezone to be mapped to the application"
        format: "[A-Za-z0-9_-]"
        example: "(GMT+08:00) Perth"
  AddCategory:
    required:
      - "name"
    properties:
      name:
        type: "string"
        description: "Name of the category"
        format: "[A-Za-z0-9_-]"
        minLength: 4
        maxLength: 128
        example: "test_category"
      identifier:
        type: "string"
        description: "Identifier of the category"
        format: "[A-Za-z0-9_-]"
        minLength: 4
        maxLength: 128
        example:
  CategoriesFetchSuccessResponse:
    type: "object"
    required:
      - "message"
      - "responseStatus"
      - "data"
    properties:
      message:
        type: "string"
        example: "KPI categories fetched successfully"
      responseStatus:
        type: "string"
        description: "Success or failure status of the request."
        enum:
          - "SUCCESS"
          - "FAILURE"
        example: "SUCCESS"
      data:
        type: "array"
        items:
          $ref: "#/definitions/GetCategory"
  GetCategory:
    required:
      - "id"
      - "name"
      - "isWorkLoad"
    properties:
      id:
        type: "number"
        description: "Object Id"
        example: 2
      name:
        type: "string"
        description: "Name of the object"
        format: "[A-Za-z0-9_-]"
        minLength: 4
        maxLength: 128
        example: "category1_name"
      isWorkLoad:
        type: "number"
        description: "Specifies if the category is workLoad."
        example: 0
  Id:
    required:
      - "id"
      - "name"
    properties:
      id:
        type: "number"
        description: "Object Id"
        example: 2
      name:
        type: "string"
        description: "Name of the object"
        format: "[A-Za-z0-9_-]"
        minLength: 4
        maxLength: 128
        example: "test_name"
  ComponentDetailsFetchSuccessResponse:
    type: "object"
    required:
      - "message"
      - "responseStatus"
      - "data"
    properties:
      message:
        type: "string"
        example: "Component details fetched successfully"
      responseStatus:
        type: "string"
        description: "Success or failure status of the request."
        enum:
          - "SUCCESS"
          - "FAILURE"
        example: "SUCCESS"
      data:
        type: "array"
        items:
          $ref: "#/definitions/ComponentDetails"
  ComponentDetails:
    type: "object"
    required:
      - "id"
      - "name"
      - "componentTypes"
      - "commonVersions"
    properties:
      id:
        type: "number"
        description: "Component Id"
        example: 2
      name:
        type: "string"
        description: "Name of the component"
        format: "[A-Za-z0-9_-]"
        example: "component-1"
      componentTypes:
        type: "array"
        items:
          $ref: "#/definitions/Id"
      commonVersions:
        type: "array"
        items:
          $ref: "#/definitions/Version"
  Version:
    required:
      - "id"
      - "version"
    properties:
      id:
        type: "number"
        description: "Version Id"
        example: 2
      version:
        type: "string"
        description: "Version name"
        format: "[A-Za-z0-9_-]"
        example: "test_version_7.x"
  RequestFailureResponse:
    type: "object"
    required:
      - "message"
      - "responseStatus"
    properties:
      message:
        type: "string"
        example: "Invalid account identifier provided."
      responseStatus:
        type: "string"
        description: "Success or failure status of the request."
        enum:
          - "FAILURE"
          - "SUCCESS"
        example: "FAILURE"
  ServerErrorResponse:
    type: "object"
    required:
      - "message"
      - "responseStatus"
    properties:
      message:
        type: "string"
        example: "Internal server error, Kindly contact the Administrator."
      responseStatus:
        type: "string"
        description: "Success or failure status of the request."
        enum:
          - "FAILURE"
          - "SUCCESS"
        example: "FAILURE"
  AddForensic:
    required:
      - "name"
      - "categories"
      - "commandDetails"
    properties:
      name:
        type: "string"
        description: "Name of the forensic"
        example: "test12"
      categories:
        type: "array"
        description: "List of categories to be mapped to forensic"
        items:
          type: "string"
        example:
          - "Others"
          - "Category"
      commandDetails:
        required:
          - "name"
          - "commandName"
        properties:
          name:
            type: "string"
            description: "name to be added"
            example: "cname"
          commandName:
            type: "string"
            description: "Alias name of the command to be added"
            example: "cn7"
          commandArguments:
            type: "array"
            description: "List of argument to be added to a command"
            items:
              type: "object"
              properties:
                argumentKey:
                  type: "string"
                  description: "Argument key to identify the argument for a command"
                  example: "test_key"
                argumentValue:
                  type: "string"
                  description: "Value for the argument."
                  example: "test_value"
                defaultValue:
                  type: "string"
                  description: "Default value for the argument."
                  example: "test_default"
                argumentType:
                  type: "string"
                  description: "Type of the argument"
                  example: "standardInput"
                argumentValueType:
                  description: "Type of the value of the argument"
                  type: "string"
                  example: "password"
  AddProducer:
    required:
      - "name"
      - "description"
      - "kpiType"
      - "isGroupKpi"
      - "producerType"
      - "producerAttributes"
      - "kpiMapping"
      - "parameters"
    properties:
      name:
        type: "string"
        example: "TestProducer"
      description:
        type: "string"
        example: "This is a producer"
      kpiType:
        type: "string"
        example: "Core"
      isGroupKpi:
        type: "number"
        example: 0
      producerType:
        type: "string"
        example: "SCRIPT"
      producerAttributes:
        required:
          - "script_name"
          - "signature"
        properties:
          script_name:
            type: "string"
            example: "test.sh"
          signature:
            type: "string"
            example: "testSignature"
        type: "object"
      kpiMapping:
        type: "array"
        items:
          type: "object"
          properties:
            kpiIdentifier:
              type: "string"
              example: "CPU_UTIL"
            componentVersionId:
              type: "string"
              example: "6.1"
            componentName:
              type: "string"
              example: "AIX"
            componentTypeName:
              type: "string"
              example: "Host"
      parameters:
        type: "array"
        items:
          type: "object"
          properties:
            parameterType:
              type: "string"
              example: "COMMANDLINE"
            parameterName:
              type: "string"
              example: "host"
            parameterValue:
              type: "string"
              example: "localhost"
  Success:
    required:
      - "message"
      - "responseStatus"
      - "data"
    properties:
      message:
        type: "string"
        example: "SUCCESS"
      responseStatus:
        type: "string"
        example: "SUCCESS"
      data:
        type: object
  NotificationSuccess:
    required:
      - "message"
      - "responseStatus"
      - "data"
    properties:
      message:
        type: "string"
        example: "SUCCESS"
      responseStatus:
        type: "string"
        example: "SUCCESS"
      data:
        description: "List of required config data."
        type: "array"
        items:
          $ref: '#/definitions/NotificationPreferenceList'
  AddKPIGroup:
    required:
      - "groupKpiName"
      - "groupKpiIdentifier"
      - "description"
      - "kpiType"
      - "discovery"
    properties:
      groupKpiName:
        type: "string"
        example: "GROUP_KPI_NAME"
      groupKpiIdentifier:
        type: "string"
        example: "GROUP_KPI_UNIQUE_IDENTIFIER"
      description:
        type: "string"
        example: "Description of the KPI Group"
      kpiType:
        type: "string"
        example: "CORE"
      discovery:
        type: "number"
        example: 0
  AddKPI:
    required:
      - "kpiName"
      - "description"
      - "groupKpiIdentifier"
      - "dataType"
      - "kpiType"
      - "clusterOperation"
      - "rollupOperation"
      - "instanceAggregation"
      - "clusterAggregation"
      - "collectionIntervalSeconds"
      - "measureUnits"
      - "componentName"
      - "componentType"
      - "componentVersion"
      - "categoryName"
      - "enableAnalytics"
    properties:
      kpiName:
        type: "string"
        example: "KPI_NAME"
      description:
        type: "string"
        example: "Description of the KPI"
      groupKpiIdentifier:
        type: "string"
        example: "GROUP_KPI_UNIQUE_IDENTIFIER"
      dataType:
        type: "string"
        example: "Integer"
      kpiType:
        type: "string"
        example: "Core"
      clusterOperation:
        type: "string"
        example: "Sum"
      rollupOperation:
        type: "string"
        example: "Sum"
      instanceAggregation:
        type: "string"
        example: "MultiValue"
      clusterAggregation:
        type: "string"
        example: "MultiValue"
      collectionIntervalSeconds:
        type: "number"
        example: 60
      measureUnits:
        type: "string"
        example: "percentile"
      componentName:
        type: "string"
        example: "CentOS"
      componentType:
        type: "string"
        example: "Host"
      componentVersion:
        type: "string"
        example: "6.x"
      categoryName:
        type: "string"
        example: "Disk Space"
      enableAnalytics:
        type: "number"
        example: 1
  AddComponent:
    required:
      - "componentName"
      - "type"
      - "commonVersion"
      - "version"
      - "description"
      - "attributes"
    properties:
      componentName:
        type: "string"
        example: "test component"
      type:
        type: "string"
        example: "HOST"
      commonVersion:
        type: "string"
        example: "5.x"
      version:
        type: "string"
        example: "5.1"
      description:
        type: "string"
        example: "test component addition"
      attributes:
        type: "array"
        items:
          type: "object"
          properties:
            name:
              type: "string"
              example: "HostAddress"
            type:
              type: "string"
              example: "TextBox"
  SuccessResponseAddComponent:
    type: "object"
    required:
      - "message"
      - "responseStatus"
      - "data"
    properties:
      message:
        type: "string"
        example: "Account add successful."
      responseStatus:
        type: "string"
        description: "Success or failure status of the request."
        enum:
          - "SUCCESS"
          - "FAILURE"
        example: "SUCCESS"
      data:
        required:
          - "id"
          - "name"
        properties:
          id:
            type: "number"
            example: 2
          name:
            type: "string"
            example: "HDFC"
        type: "object"
  AddHost:
    type: "array"
    items:
      $ref: "#/definitions/Host"
  Host:
    required:
      - "name"
      - "identifier"
      - "agentIdentifier"
      - "clusterName"
      - "mstComponentVersion"
      - "mstComponentName"
      - "mstComponentType"
      - "tags"
      - "groupKpi"
      - "discovery"
      - "attributes"
    properties:
      name:
        type: "string"
        example: "compInstance-1"
      identifier:
        type: "string"
        example: "identifier"
      agentIdentifier:
        type: "string"
        example: "7fb4c12c-6d84-475f-9aee-593496d00ca8"
      clusterName:
        type: "string"
        example: "clusterName"
      mstComponentVersion:
        type: "string"
        example: "6.1"
      mstComponentName:
        type: "string"
        example: "AIX"
      mstComponentType:
        type: "string"
        example: "Host"
      tags:
        type: "array"
        items:
          type: "object"
          properties:
            name:
              type: "string"
              example: "Controller"
            value:
              type: "string"
              example: "CBC Service"
      groupKpi:
        type: "array"
        items:
          type: "object"
          properties:
            name:
              type: "string"
              example: "LogForwarder"
            attributes:
              type: "array"
              items:
                type: "string"
              example:
                - "nollPointer"
                - "streamClosed"
      discovery:
        type: "number"
        example: 1
      attributes:
        type: "array"
        items:
          type: "object"
          properties:
            name:
              type: "string"
              example: "HostAddress"
            value:
              type: "string"
              example: "********"
    type: "object"
  AddCluster:
    type: "array"
    items:
      $ref: "#/definitions/Cluster"
  Cluster:
    required:
      - "name"
      - "identifier"
      - "agentIdentifier"
      - "mstComponentVersion"
      - "mstComponentName"
      - "mstComponentType"
      - "discovery"
      - "kpi"
      - "tags"
    properties:
      name:
        type: "string"
        example: "compInstance-1"
      identifier:
        type: "string"
        example: "identifier"
      agentIdentifier:
        type: "string"
        example: "7fb4c12c-6d84-475f-9aee-593496d00ca8"
      mstComponentVersion:
        type: "string"
        example: "6.1"
      mstComponentName:
        type: "string"
        example: "AIX"
      mstComponentType:
        type: "string"
        example: "Host"
      discovery:
        type: "number"
        example: 1
      kpi:
        type: "array"
        items:
          type: "object"
          properties:
            name:
              type: "string"
              example: "CpuUtil"
            id:
              type: "number"
              example: 1
            collectionInterval:
              type: "number"
              example: 60
      tags:
        type: "array"
        items:
          type: "object"
          properties:
            name:
              type: "string"
              example: "Controller"
            value:
              type: "string"
              example: "CBC Service"
    type: "object"
  AddTransaction:
    type: array
    items:
      $ref: "#/definitions/Transaction"
  Transaction:
    required:
      - "txnGrp"
      - "txnType"
      - "tags"
      - "txnName"
      - "txnIdentifier"
      - "description"
      - "isAuditEnabled"
      - "isAutoConfigured"
      - "isRawEnabled"
      - "txnResponseThresholds"
      - "subTransactions"
      - "isBusinessTxn"
      - "bizConfiguration"
      - "transactionAttributes"
    properties:
      txnGrp:
        type: "string"
        example: "XpT"
      txnType:
        type: "string"
        example: "HTTP"
      tags:
        type: "array"
        items:
          type: "object"
          properties:
            name:
              type: "string"
              example: "Controller"
            value:
              type: "string"
              example: "UPI Switch"
            identifier:
              type: "string"
              example: "UPI Switch"
            subTypeName:
              type: "string"
              example: "Services"
      txnName:
        type: "string"
        example: "upi-npci"
      txnIdentifier:
        type: "string"
        example: "upi-npci"
      description:
        type: "string"
        example: "upi-npci"
      isAuditEnabled:
        type: "boolean"
        example: false
      isAutoConfigured:
        type: "boolean"
        example: false
      isRawEnabled:
        type: "boolean"
        example: false
      txnThresholds:
        type: "array"
        items:
          type: "object"
        example: []
      txnResponseThresholds:
        type: "array"
        items:
          type: "object"
          properties:
            profileName:
              type: "string"
              example: "24x7"
            responseTimeType:
              type: "string"
              example: "DC"
            slowThresholdValue:
              type: "number"
              example: 5000
      subTransactions:
        type: "array"
        items:
          type: "object"
          properties:
            httpTxnConfig:
              required:
                - "type"
                - "urlPattern"
                - "queryParam"
                - "headerPattern"
                - "bodyPattern"
              properties:
                type:
                  type: "string"
                  example: "GET"
                urlPattern:
                  type: "string"
                  example: "upi-npci"
              type: "object"
      isBusinessTxn:
        type: "number"
        example: 1
      bizValueExtractorList:
        type: "array"
        items:
          type: "object"
        example: []
      txnAuditDetails:
        type: "array"
        items:
          type: "object"
        example: []
      bizConfiguration:
        required:
          - "req"
          - "res"
        properties:
          req:
            required:
              - "name"
              - "identifier"
              - "stitchField"
            properties:
              name:
                type: "string"
                example: "upi-npci-req"
              identifier:
                type: "string"
                example: "upi-npci-req"
              stitchField:
                type: "string"
                example: "xpt_seq_no"
            type: "object"
          res:
            required:
              - "name"
              - "identifier"
              - "stitchField"
            properties:
              name:
                type: "string"
                example: "upi-npci-res"
              identifier:
                type: "string"
                example: "upi-npci-res"
              stitchField:
                type: "string"
                example: "xpt_Ack_reqMsgId"
            type: "object"
        type: "object"
      transactionAttributes:
        type: "array"
        items:
          type: "object"
          properties:
            name:
              type: "string"
              example: "xpt_seq_no"
            value:
              type: "string"
              example: "xpt_seq_no"
            segmentFlag:
              type: "number"
              example: 0
            businessStatusFlag:
              type: "number"
              example: 0
            techStatusFlag:
              type: "number"
              example: 0
            businessValueFlag:
              type: "number"
              example: 0
            searchFlag:
              type: "number"
              example: 1
            descriptor:
              type: "string"
              example: "GOOD"
  AddAgent:
    type: array
    items:
      $ref: "#/definitions/Agent"
  Agent:
    required:
      - "mode"
      - "accountMappings"
      - "uniqueToken"
      - "physicalAgentIdentifier"
      - "name"
      - "description"
      - "subType"
      - "hostAddress"
      - "agentMappingDetails"
      - "compInstIdentifiers"
      - "status"
    properties:
      mode:
        type: "string"
        example: "MONITOR"
      accountMappings:
        type: "array"
        items:
          type: "object"
          properties:
            accountIdentifier:
              type: "string"
              example: "d681ef13-d690-4917-jkhg-6c79b-3"
            tags:
              type: "array"
              items:
                type: "object"
                properties:
                  identifier:
                    type: "string"
                    example: "ecorebanking_6"
                  subTypeName:
                    type: "string"
                    example: "Application"
                  name:
                    type: "string"
                    example: "Controller"
      uniqueToken:
        type: "string"
        example: "e570de02-c585-4917-bbb7-5c97b35e-13"
      physicalAgentIdentifier:
        type: "string"
        example: "e570de02-c585-4917-bbb7-5c97b35e-13-sp"
        description: "Physical agent identifier will be used to map multiple instances of virtual Log-Forwarder agents on a particular host."
      name:
        type: "string"
        example: "ComponentAgent_13"
      description:
        type: "string"
        example: ""
      subType:
        type: "string"
        example: "ComponentAgent"
      hostAddress:
        type: "string"
        example: "**************"
      agentMappingDetails:
        required:
          - "dataCommunication"
          - "timeoutMultiplier"
          - "configOperationMode"
          - "dataOperationMode"
          - "status"
        properties:
          dataCommunication:
            required:
              - "protocol"
              - "port"
              - "name"
              - "host"
              - "description"
              - "type"
              - "status"
            properties:
              protocol:
                type: "string"
                example: "GRPC"
              port:
                type: "number"
                example: 11000
              name:
                type: "string"
                example: "GRPC"
              host:
                type: "string"
                example: "haproxy.appnomic"
              description:
                type: "string"
                example: ""
              type:
                type: "string"
                example: "Communication_Endpoint"
              status:
                type: "number"
                example: 1
            type: "object"
          timeoutMultiplier:
            type: "number"
            example: 2
          configOperationMode:
            type: "string"
            example: "Local"
          dataOperationMode:
            type: "string"
            example: "Remote"
          status:
            type: "number"
            example: 1
        type: "object"
      compInstIdentifiers:
        type: "array"
        items:
          type: "object"
          properties:
            accountId:
              type: "string"
              example: "d681ef13-d690-4917-jkhg-6c79b-3"
            compInstIds:
              type: "array"
              items:
                type: "string"
              example:
                - "HPUX_ECORE_Host_150_Inst_1"
                - "IHS_ECORE_Web_150_Inst_1"
                - "WAS_ECORE_App_150_Inst_1"
                - "FINACLE_ECORE_Finlistval_150_Inst_1"
                - "ORACLE_ECORE_DB_150_Inst_1"
            tags:
              type: "array"
              items:
                type: "object"
                properties:
                  identifier:
                    type: "string"
                    example: "(GMT-09:00) Alaska"
                  subTypeName:
                    type: "string"
                    example: "Account"
                  name:
                    type: "string"
                    example: "Timezone"
      status:
        type: "number"
        example: 1
    type: "object"
  EditApplication:
    type: "object"
    properties:
      name:
        type: "string"
        description: "Name of the controller"
        format: "[A-Za-z0-9_-]"
        minLength: 4
        maxLength: 128
        example: "app_name"
      addServices:
        type: "array"
        description: "List of service identifiers to be mapped to application"
        items:
          type: "string"
      deleteServices:
        type: "array"
        description: "List of service identifiers to be deleted from the application"
        items:
          type: "string"
  ConfigSuccessResponse:
    type: "object"
    required:
      - "message"
      - "responseStatus"
      - "data"
    properties:
      message:
        type: "string"
        example: "Fetch Successfull."
      responseStatus:
        type: "string"
        description: "Success or failure status of the request."
        enum:
          - "SUCCESS"
          - "FAILURE"
        example: "SUCCESS"
      data:
        description: "List of required config data."
        type: "array"
        items:
          $ref: '#/definitions/KpiConfigDetail'
  KpiConfigDetail:
    type: "object"
    required:
      - "kpiId"
      - "kpiName"
      - "kpiUnit"
      - "kpiAttribute"
      - "dataId"
      - "kpidataType"
      - "categoryId"
      - "categoryName"
      - "kpiLevel"
      - "generateAnomaly"
      - "userDefinedSOR"
      - "systemOperationType"
      - "userDefinedOperationType"
    properties:
      kpiId:
        description: "It is an id associated with the KPI and it can't be 0"
        type: "number"
        example: "1"
      kpiName:
        description: "Name of the KPI."
        type: "string"
        example: "CPU Util"
      kpiUnit:
        description: "Unit of measurement for the KPI."
        type: "string"
        example: "%,KB"
      kpiAttribute:
        description: "Value is ALL irrespecitive of group or non-group KPI."
        type: "string"
        example: "ALL"
      dataId:
        type: "number"
        description: "The row id of the KPI."
        example: 5
      kpidataType:
        type: "string"
        enum:
          - "Integer"
          - "Double"
        example: "Integer"
      categoryId:
        type: "number"
        example: 1
      categoryName:
        type: "string"
        description: "Category to which the KPI belongs to."
        example: "CPU"
      kpiLevel:
        description: "It is either of two levels Cluster or Any instances."
        type: "string"
        example: "cluster"
        enum:
          - "Cluster"
          - "Any Instances"
      generateAnomaly:
        type: "boolean"
        example: "true"
      userDefinedSOR:
        example: "false"
        type: "boolean"
      systemOperationType:
        example: "greater than"
        type: "string"
        description: "System configured Operation "
        enum:
          - "greater than"
          - "lesser than"
          - "not between"
      userDefinedOperationType:
        example: "greater than"
        type: "string"
        description: "User opted Operation "
        enum:
          - "greater than"
          - "lesser than"
          - "not between"
      systemThresholds:
        $ref: '#/definitions/Threshold'
      userThresholds:
        $ref: '#/definitions/Threshold'
  Threshold:
    description: "It is a map of Max and Min threshold value of particular kpi. "
    type: object
    required:
      - "MAX"
      - "MIN"
    properties:
      MAX:
        type: "number"
        description: "Max threshold is used only in case of not between operation type."
        example: 80
      MIN:
        type: "number"
        description: "Min threshold will be used for greater than, lesser than and not between"
        example: 20
  ListOfKpiConfigDetails:
    description: "It is a list of kpi config details."
    type: "object"
    required:
      - "ListOfKpiConfigDetail"
    properties:
      ListOfKpiConfigDetail:
        type: "array"
        items:
          $ref: '#/definitions/KpiConfigDetail'
  KpiConfigSuccessResponse:
    type: "object"
    required:
      - "message"
      - "responseStatus"
      - "data"
    properties:
      message:
        type: "string"
        example: "update Successful."
      responseStatus:
        type: "string"
        description: "Success or failure status of the request."
        enum:
          - "SUCCESS"
          - "FAILURE"
        example: "SUCCESS"
      data:
        description: "List is partially updated,below invalid jsons are listed."
        type: "array"
        items:
          $ref: '#/definitions/ListOfKpiConfigDetails'
  SmsParametrs:
    type: object
    required:
      - "parameterName"
      - "parameterValue"
      - "parameterType"
      - "isPlaceholder"
      - "action"
    properties:
      parameterName:
        type: "string"
        example: "mobileNumber"
        description: "Parameter key for request parameter or query parameter."
      parameterValue:
        type: "string"
        example: "{MobileNumber}"
        description: "Parameter value for request parameter or query parameter."
      parameterType:
        type: string
        example: "QueryParameter"
        description: "Used to distinguish between Query or Tcp parameter."
      isPlaceholder:
        type: boolean
        example: true
        description: "Flag which indicate parameterName is static configured or not. Example: If it is a value from which this user can be defined further like its account details is come under static configurations so in this case it is not a placeholder.But if it is shows as any non related name like mobile number or message content, it will come under non static one so it is a placeholder only."
      action:
        type: string
        example: "add"
        description: "Action defines what is action type request for sms parameter."
  SmsDetails:
    type: "object"
    required:
      - "address"
      - "port"
      - "countryCode"
      - "protocolName"
      - "parameters"
    properties:
      address:
        type: "string"
        example: "www.appnomic.com"
        description: "web address"
      port:
        type: integer
        example: "9090"
        description: "Port of the web address"
      countryCode:
        type: string
        example: "IND"
        description: "Code of the country"
      httpMethod:
        type: string
        example: "GET"
        description: "Request-Response protocol between a client and server."
      httpRelativeUrl:
        type: string
        example: "/home/<USER>"
        description: "Url where message has to send"
      protocolName:
        type: string
        example: "HTTP"
        description: "Name of the protocol used."
      postData:
        type: string
        description: "Data inside the post body. This is only valid when httpMethod is POST. It is not mandatory parameter."
      parameters:
        type: array
        description: "list of sms parameters"
        items:
          $ref: '#/definitions/SmsParametrs'
  EmailDetails:
    type: object
    required:
      - "address"
      - "port"
      - "security"
      - "fromRecipient"
    properties:
      address:
        type: "string"
        example: "www.appnomic.com"
        description: "web address"
      port:
        type: integer
        example: "9090"
        description: "Port of the web address"
      userName:
        type: string
        example: "<EMAIL>"
        description: "user name used for authentication"
      password:
        type: string
        example: "appnomic_1"
        description: "Password used for authentication and it is saved in encrypted form."
      security:
        type: string
        example: "NONE"
        description: "Security protocol used to send email."
      fromRecipient:
        type: string
        example: "<EMAIL>"
        description: "email sent by."
  GetPageDetailsResponse:
    type: "object"
    required:
      - "message"
      - "responseStatus"
      - "data"
    properties:
      message:
        type: "string"
        example: "Page details fetch successfully."
      responseStatus:
        type: "string"
        description: "Success or failure status of the request."
        enum:
          - "SUCCESS"
          - "FAILURE"
        example: "SUCCESS"
      data:
        type: "array"
        items:
          $ref: "#/definitions/PageDetails"
  PageDetails:
    type: "object"
    required:
      - "id"
      - "refreshInSecs"
      - "pageName"
      - "refreshEnabled"
    properties:
      id:
        type: "number"
        format: "[0-9]"
        example: 3
      refreshInSecs:
        type: "number"
        format: "[0-9]"
        example: 300
      pageName:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 4
        maxLength: 128
        example: "ProblemPage"
      refreshEnabled:
        type: "number"
        format: "[0-9]"
        example: 1
  GetUsersResponse:
    type: "object"
    required:
      - "message"
      - "responseStatus"
      - "data"
    properties:
      message:
        type: "string"
        example: "User List fetched Successfully"
      responseStatus:
        type: "string"
        description: "Success or failure status of the request."
        enum:
          - "SUCCESS"
          - "FAILURE"
        example: "SUCCESS"
      data:
        type: "array"
        items:
          $ref: "#/definitions/UserDetails"
  UserDetails:
    type: "object"
    required:
      - "userId"
      - "userName"
      - "lastModifiedOn"
      - "lastModifiedBy"
    properties:
      userId:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 16
        maxLength: 128
        example: "abcd-e54h-jklm-n645"
      userName:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 1
        maxLength: 128
        example: "admin_user"
      role:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 1
        maxLength: 32
        example: "admin"
      userProfile:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 1
        maxLength: 64
        example: "heal admin"
      status:
        type: "number"
        format: "[0-9]"
        example: 0
      createdOn:
        type: "number"
        format: "[0-9]"
        example: 346545645481
      createdBy:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 1
        maxLength: 256
        example: "appsoneadmin"
  UserInfo:
    type: "object"
    required:
      - "userName"
      - "emailId"
      - "roleId"
      - "profileId"
      - "status"
    properties:
      id:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 16
        maxLength: 128
        example: "as54-efgh-58s5-nopq"
      userName:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 4
        maxLength: 256
        example: "user1"
      firstName:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 0
        maxLength: 128
        example: "user"
      lastName:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 0
        maxLength: 128
        example: "test"
      emailId:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 1
        maxLength: 256
        example: "<EMAIL>"
      contactNumber:
        type: "number"
        format: "[0-9]"
        minLength: 10
        maxLength: 16
        example: 9876543210
      roleId:
        type: "number"
        format: "[0-9]"
        example: 4
      profileId:
        type: "number"
        format: "[0-9]"
        example: 5
      status:
        type: "number"
        format: "[0-9]"
        example: 0
      profileChange:
        type: "number"
        format: "[0-9]"
        example: 0
      accessDetails:
        type: "array"
        items:
          $ref: "#/definitions/AccessDetails"
  AccessDetails:
    type: "object"
    required:
      - "action"
      - "accountId"
    properties:
      action:
        type: "string"
        enum:
          - "add"
          - "edit"
          - "delete"
        example: "add"
      accountId:
        type: "object"
        format: "[A-Za-z0-9_-]"
        example: "2"
      applications:
        type: "array"
        items:
          $ref: "#/definitions/UserAppAccess"
  UserAppAccess:
    type: "object"
    required:
      - "action"
      - "ids"
    properties:
      action:
        type: "string"
        enum:
          - "add"
          - "delete"
        example: "add"
      ids:
        type: "array"
        items:
          type: "object"
          format: "[A-Za-z0-9_-]"
          example: "*"
  NotificationPreferenceList:
    type: array
    description: "List of Notification preference"
    items:
      $ref: '#/definitions/NotificationPreference'
  NotificationPreference:
    type: "object"
    required:
      - "applicationId"
      - "signalType"
      - "signalTypeId"
      - "severityTypeId"
      - "severityType"
      - "notificationTypeId"
    properties:
      applicationId:
        type: "string"
        example: "1"
        description: "Unique application id."
      applicationName:
        type: "string"
        example: "Web-App"
        description: "Application name in which account is added."
      signalTypeId:
        type: integer
        example: "298"
        description: "Unique id which can denote signal type."
      signalType:
        type: string
        example: "Early Warning"
        description: "This will denote what type of notification should be sent to the user."
      severityTypeId:
        type: integer
        example: 295
        description: "Unique id which can denote signal severity."
      severityType:
        type: string
        example: "severe"
        description: "This will denote what type of severity should be sent to the user."
      notificationTypeId:
        type: integer
        example: 291
        description: "This id will signify when a signal should be sent to the user."
  RulesList:
    type: array
    description: "List of Rule"
    items:
      $ref: '#/definitions/Rules'
  Rules:
    type: "object"
    required:
      - "id"
    properties:
      id:
        type: "string"
        example: "1"
        description: "Unique Rule id."
      name:
        type: "string"
        example: "FEBA_new"
        description: "Rule name which will be updated."
      status:
        type: boolean
        example: "true"
        description: "It means that it is active or inactive"
      addDiscoveryTags:
        type: array
        items:
          type: string
        description: "List of new tags which will be added."
        example: ["Demo","Ftags"]
      removeDiscoveryTags:
        type: array
        items:
          type: string
        description: "List of old tags which will be removed."
        example: ["transcationDone","Rtags"]
  TransactionsList:
    type: array
    description: "List of Transaction"
    items:
      $ref: '#/definitions/Transactions'
  Transactions:
    type: "object"
    required:
      - "id"
    properties:
      id:
        type: "string"
        example: "1"
        description: "Unique transaction id."
      name:
        type: "string"
        example: "FEBA_new"
        description: "Transaction name which will be updated."
      monitorEnabled:
        type: boolean
        example: "true"
        description: "This will denote transaction is being monitor by application or not."
  GetBatchProcessesResponse:
    type: "object"
    required:
      - "message"
      - "responseStatus"
      - "data"
    properties:
      message:
        type: "string"
        example: "Batch processes fetched successfully."
      responseStatus:
        type: "string"
        description: "Success or failure status of the request."
        enum:
          - "SUCCESS"
          - "FAILURE"
        example: "SUCCESS"
      data:
        type: "array"
        items:
          $ref: "#/definitions/BatchProcessDetails"
  BatchProcessDetails:
    type: "object"
    required:
      - "processName"
      - "processIdentifier"
      - "hostDetails"
    properties:
      id:
        type: "number"
        example: "7"
      processName:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 4
        maxLength: 256
        example: "process-name-1"
      processIdentifier:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 1
        maxLength: 128
        example: "process-identifier-1"
      hostDetails:
        type: "array"
        items:
          $ref: "#/definitions/HostDetails"
  HostDetails:
    type: "object"
    required:
      - "hostAddress"
      - "directoryPath"
    properties:
      id:
        type: "number"
        example: "7"
      hostAddress:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 1
        maxLength: 256
        example: "*************"
      directoryPath:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 1
        maxLength: 128
        example: "/root"
      arguments:
        type: "array"
        items:
          $ref: "#/definitions/ProcessArgument"
  ProcessArgument:
    type: "object"
    required:
      - "name"
      - "value"
      - "defaultValue"
      - "order"
      - "placeHolder"
    properties:
      id:
        type: "number"
        example: "7"
      name:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 1
        maxLength: 256
        example: "arg-name"
      value:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 1
        maxLength: 128
        example: "arg-value"
      defaultValue:
        type: "string"
        format: "[A-Za-z0-9_-]"
        minLength: 1
        maxLength: 128
        example: "default-arg-value"
      order:
        type: "number"
        format: "[0-9]"
        example: 1
      placeholder:
        type: "number"
        format: "[0-9]"
        example: 0
  BatchJobThresholdResponse:
    type: "object"
    description: "List of Batch Job Data at different minute of time"
    required:
      - "message"
      - "responseStatus"
      - "data"
    properties:
      message:
        type: "string"
        example: "Thresholds List fetched Successfully"
      responseStatus:
        type: "string"
        description: "Success or failure status of the request."
        enum:
          - "SUCCESS"
          - "FAILURE"
        example: "SUCCESS"
      data:
        type: "array"
        description: "List of Batch Job Data"
        items:
          $ref: '#/definitions/BatchJobData'
  BatchJobData:
    description: "Batch Job Data"
    required:
      - "batchJobId"
      - "kpiId"
      - "minute"
      - "operationType"
      - "thresholdTime"
      - "thresholdType"
      - "thresholdsMap"
    properties:
      batchJobId:
        type: "string"
        description: "batch JobId"
        example: "101"
      kpiId:
        type: "string"
        description: "KpiId"
        example: "325"
      minute:
        type: "integer"
        description: "Threshold at this minute of time."
        example: "1"
      operationType:
        type: "string"
        description: "Operation type : how we are comparing the data"
        example: "lesser than"
      thresholdTime:
        type: number
        description: "Time at which the aggregation is done by the api"
      thresholdType:
        type: "string"
        description: "How the operation is performed"
        example: "static"
      thresholdsMap:
        type: "object"
        description: "Threshold key value pair"
        example: { "min": 123.45 }