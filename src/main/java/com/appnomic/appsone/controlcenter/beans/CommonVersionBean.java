package com.appnomic.appsone.controlcenter.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonVersionBean {

    private int id;
    private String name;
    private int componentId;
    private int isCustom;
    private int status;
    private Timestamp createdAt;
    private Timestamp updatedAt;
    private String userId;
    private int accountId;
}
