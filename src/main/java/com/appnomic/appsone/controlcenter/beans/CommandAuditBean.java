package com.appnomic.appsone.controlcenter.beans;


import com.appnomic.appsone.controlcenter.common.CommandOutputType;
import com.appnomic.appsone.controlcenter.pojo.CommandAuditDetails;
import com.appnomic.appsone.controlcenter.pojo.CommandMetadataAuditDetails;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class CommandAuditBean {

    private String commandJobID;
    private String supervisorIdentifier;
    private int supervisorCtrlTimeout;
    private int supervisorCtrlTTL;
    private int retryNumber;
    private String agentIdentifier;
    private String exitCode;
    private String triggerSource;
    private int commandTimeout;
    private long triggerTime;
    private long commandCompleteTime;
    private String commandExecType;
    private String commandType;
    private CommandOutputType commandOutputType;
    private String userDetailsId;
    private long violationTime;
    private Map<String, String> metaData;


    @JsonIgnore
    public CommandAuditDetails getCommandAuditDetails() {
        CommandAuditDetails details = new CommandAuditDetails();
        details.setCommandJobID(this.commandJobID);
        details.setSupervisorIdentifier(this.supervisorIdentifier);
        details.setAgentIdentifier(this.agentIdentifier);
        details.setCommandTimeout(this.commandTimeout);
        details.setRetryNumber(this.retryNumber);
        details.setTriggerTime(DateTimeUtil.getTimeInGMT(this.triggerTime));
        details.setCommandCompleteTime(DateTimeUtil.getTimeInGMT(this.commandCompleteTime));
        details.setExitCode(this.exitCode);
        details.setTriggerSource(this.triggerSource);
        details.setUserDetailId(this.userDetailsId);
        details.setSupervisorCtrlTTL(this.supervisorCtrlTTL);
        details.setViolationTime(DateTimeUtil.getTimeInGMT(this.violationTime));
        details.setCreatedTime(DateTimeUtil.getTimeInGMT(new Date().getTime()));
        return details;
    }

    public List<CommandMetadataAuditDetails> getCommandMetadataAuditDetails(int commandAuditId){
        List<CommandMetadataAuditDetails> list = new ArrayList<>();

        if(this.metaData == null){
            return list;
        }

        for(Map.Entry<String, String> entry : this.metaData.entrySet()){
            String key = entry.getKey();
            String value = entry.getValue();
            if(key != null && value != null){
                CommandMetadataAuditDetails detail = new CommandMetadataAuditDetails();
                detail.setCommandAuditId(commandAuditId);
                detail.setKey(key);
                detail.setValue(value);
                detail.setCreatedTime(new Timestamp(new Date().getTime()));
                list.add(detail);
            }
        }
        return list;
    }

}
