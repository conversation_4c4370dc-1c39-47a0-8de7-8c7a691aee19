package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.Operation;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ApplicationPercentilesDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.KPIDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ApplicationPercentilesBean;
import com.appnomic.appsone.controlcenter.dao.redis.ApplicationRepo;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.Percentile;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.Application;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

public class UpdateAppPercentilesBL implements BusinessLogic<List<Percentile>, List<ApplicationPercentilesBean>, String> {

    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateAppPercentilesBL.class);
    ApplicationPercentilesDataService applicationPercentilesDataService = new ApplicationPercentilesDataService();
    String accountIdentifier;
    String applicationIdentifier;

    @Override
    public UtilityBean<List<Percentile>> clientValidation(RequestObject request) throws ClientException {

        if (request == null) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (authToken == null || authToken.trim().isEmpty()) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty.");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            LOGGER.error(UIMessages.ACCOUNT_NULL_OR_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_NULL_OR_EMPTY);
        }

        String appIdString = request.getParams().get(Constants.APPLICATION_ID);
        if (appIdString == null || appIdString.trim().isEmpty()) {
            String error = "Invalid path parameter 'applicationId'. Reason: It is either is NULL or empty.";
            LOGGER.error(error);
            throw new ClientException(error);
        }

        String error = "'applicationId' is not a positive integer.";
        try {
            if (Integer.parseInt(appIdString) < 1) {
                LOGGER.error(error);
                throw new ClientException(error);
            }
        } catch (NumberFormatException e) {
            LOGGER.error(error);
            throw new ClientException(error);
        }

        String requestBody = request.getBody();
        List<Percentile> appPercentiles;
        try {
            appPercentiles = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestBody,
                    new TypeReference<>() {
                    });
        } catch (IOException e) {
            LOGGER.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }
        for (Percentile p : appPercentiles) {
            if (!p.validate()) {
                throw new ClientException(UIMessages.INVALID_REQUEST);
            }
        }

        return UtilityBean.<List<Percentile>>builder()
                .authToken(authToken)
                .accountIdentifier(identifier)
                .applicationId(appIdString)
                .pojoObject(appPercentiles)
                .build();
    }

    @Override
    public List<ApplicationPercentilesBean> serverValidation(UtilityBean<List<Percentile>> utilityBean) throws ServerException {

        UserAccountBean userAccountBean;
        try {
            userAccountBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            throw new ServerException(e.getSimpleMessage());
        }
        accountIdentifier = utilityBean.getAccountIdentifier();

        String appIdString = utilityBean.getApplicationId();
        int accountId = userAccountBean.getAccount().getId();
        String userId = userAccountBean.getUserId();
        int appId = Integer.parseInt(appIdString);

        int typeId = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT, Constants.APPLICATION_CONTROLLER_TYPE).getSubTypeId();
        ControllerBean application = new ControllerDataService().getControllerByIdAndTypeId(appId, accountId, typeId, null);

        if (application == null) {
            LOGGER.error("Application with ID [{}] is unavailable for account [{}]", appId, accountId);
            throw new ServerException("Application('applicationId') is not present for the specified account.");
        }
        applicationIdentifier = application.getIdentifier();
        List<Integer> existingAppPercentileIds;
        try {
            existingAppPercentileIds = applicationPercentilesDataService.getApplicationPercentiles(accountId, appId)
                    .parallelStream().map(ApplicationPercentilesBean::getId).collect(Collectors.toList());
        } catch (ControlCenterException e) {
            throw new ServerException(e.getSimpleMessage());
        }

        List<Percentile> percentiles = utilityBean.getPojoObject();
        List<ApplicationPercentilesBean> applicationPercentilesBeanList = new ArrayList<>();
        List<Integer> invalidAppPercentileIds = new ArrayList<>();
        int count = existingAppPercentileIds.size();

        for (Percentile percentile : percentiles) {
            if (percentile.getOperation().equals(Operation.ADD)) {
                applicationPercentilesBeanList.add(createApplicationPercentilesBean(accountId, appId, 0, percentile.getName(), userId));
                count++;
            } else {
                if (!existingAppPercentileIds.contains(percentile.getId())) {
                    invalidAppPercentileIds.add(percentile.getId());
                }
                if (percentile.getOperation().equals(Operation.DELETE)) {
                    applicationPercentilesBeanList.add(createApplicationPercentilesBean(accountId, appId, percentile.getId(),
                            null, userId));
                    count--;
                } else if (percentile.getOperation().equals(Operation.UPDATE)) {
                    applicationPercentilesBeanList.add(createApplicationPercentilesBean(accountId, appId, percentile.getId(),
                            percentile.getName(), userId));
                }
            }
        }

        if (!invalidAppPercentileIds.isEmpty()) {
            String invalidIds = Arrays.toString(invalidAppPercentileIds.toArray());
            LOGGER.error("Percentile ids [{}] not available for the specified application.", invalidIds);
            throw new ServerException("Percentile ids - " + invalidIds + " : not available for the specified application.");
        }

        if (count < 1 || count > 5) {
            throw new ServerException("An application should have at least 1 percentile and not more than 5 percentiles.");
        }

        return applicationPercentilesBeanList;
    }

    private ApplicationPercentilesBean createApplicationPercentilesBean(int accountId, int applicationId, int id,
                                                                        String kpiIdentifier, String userId) throws ServerException {

        int kpiId = new KPIDataService().checkForKpiIdUsingIdentifier(kpiIdentifier, null);
        if (kpiId > 0) {
            return ApplicationPercentilesBean.builder()
                    .id(id)
                    .accountId(accountId)
                    .applicationId(applicationId)
                    .kpiId(kpiId)
                    .kpiIdentifier(kpiIdentifier)
                    .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .userDetailsId(userId)
                    .build();
        } else {
            throw new ServerException("No kpi details found for kpi identifier " + kpiIdentifier + " in percona.");
        }
    }

    @Override
    public String process(List<ApplicationPercentilesBean> percentiles) throws DataProcessingException {
        try {
            String successMessage = MySQLConnectionManager.getInstance().getHandle()
                    .inTransaction((conn, status) -> updateAppPercentiles(percentiles, conn));
            updateAppPercentilesInRedis(percentiles);
            return successMessage;
        } catch (Exception e) {
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }

    private String updateAppPercentiles(List<ApplicationPercentilesBean> percentiles, Handle conn) throws ControlCenterException {

        List<ApplicationPercentilesBean> addPercentiles = new ArrayList<>();
        List<ApplicationPercentilesBean> updatePercentiles = new ArrayList<>();
        List<Integer> deletePercentiles = new ArrayList<>();

        percentiles.forEach(percentile -> {
            if (percentile.getKpiIdentifier() == null) {
                deletePercentiles.add(percentile.getId());
            } else if (percentile.getId() == 0) {
                addPercentiles.add(percentile);
            } else {
                updatePercentiles.add(percentile);
            }
        });

        if (!addPercentiles.isEmpty()) {
            applicationPercentilesDataService.addApplicationPercentiles(addPercentiles, conn);
        }

        if (!updatePercentiles.isEmpty()) {
            applicationPercentilesDataService.updateApplicationPercentiles(updatePercentiles, conn);
        }

        if (!deletePercentiles.isEmpty()) {
            new BindInDataService().deleteApplicationPercentiles(deletePercentiles, conn);
        }

        return StatusResponse.SUCCESS.toString();
    }

    private void updateAppPercentilesInRedis(List<ApplicationPercentilesBean> percentiles) {
        ApplicationRepo applicationRepo = new ApplicationRepo();
        Application application = applicationRepo.getApplicationForIdentifier(accountIdentifier, applicationIdentifier);
        if (application == null) {
            LOGGER.error("Application details not found for the given accountIdentifier: [{}] and applicationIdentifier: [{}]", accountIdentifier, applicationIdentifier);
            return;
        }

        String percentileKpisSuffix = ConfProperties.getString(Constants.PERCENTILE_KPIS_IDENTIFIER_SUFFIX, Constants.PERCENTILE_KPIS_IDENTIFIER_SUFFIX_DEFAULT);

        Map<String, Set<Double>> percentilesMap = new HashMap<>();
        for (ApplicationPercentilesBean applicationPercentile : percentiles) {
            String[] kpiIdentifierArray = applicationPercentile.getKpiIdentifier().split("_");
            if (kpiIdentifierArray[kpiIdentifierArray.length - 1].equals(percentileKpisSuffix)) {
                Double percentile = Double.valueOf(kpiIdentifierArray[kpiIdentifierArray.length - 2]);
                String percentileKpiIdentifier = Arrays.stream(kpiIdentifierArray)
                        .limit(kpiIdentifierArray.length - 2)
                        .collect(Collectors.joining("_"));

                percentilesMap.compute(percentileKpiIdentifier, (o, n) -> {
                    if (n == null) {
                        n = new HashSet<>();
                    }

                    n.add(percentile);
                    return n;
                });
            }
        }
        application.setPercentiles(percentilesMap);

        applicationRepo.updateApplication(accountIdentifier, application);
    }
}