package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.BatchProcessMappingBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.BatchProcessDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProcessArgumentBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProcessDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProcessHostDetailsBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.BatchProcessDetails;
import com.appnomic.appsone.controlcenter.pojo.HostDetails;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class UpdateBatchProcessDetailsBL implements BusinessLogic<BatchProcessDetails, ProcessDetailsBean, String> {

    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateBatchProcessDetailsBL.class);
    BatchProcessDataService batchProcessDataService = new BatchProcessDataService();
    BindInDataService bindInDataService = new BindInDataService();

    @Override
    public UtilityBean<BatchProcessDetails> clientValidation(RequestObject request) throws ClientException {

        if (request == null) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(request.getBody())) {
            LOGGER.error(UIMessages.REQUEST_BODY_NULL_EMPTY);
            throw new ClientException(UIMessages.REQUEST_BODY_NULL_EMPTY);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (authToken == null || authToken.trim().isEmpty()) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            LOGGER.error(UIMessages.ACCOUNT_NULL_OR_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_NULL_OR_EMPTY);
        }

        String batchProcessIdString = request.getParams().get(Constants.PROCESS_DETAILS_ID);
        if (batchProcessIdString == null || batchProcessIdString.trim().isEmpty()) {
            LOGGER.error(UIMessages.PROCESS_DETAILS_ID_NULL_OR_EMPTY);
            throw new ClientException(UIMessages.PROCESS_DETAILS_ID_NULL_OR_EMPTY);
        }
        int batchProcessId;
        try {
            batchProcessId = Integer.parseInt(batchProcessIdString);
        } catch (NumberFormatException e) {
            LOGGER.error("Batch process id is not a positive integer.");
            throw new ClientException("Batch process id is not a positive integer.");
        }

        String requestBody = request.getBody();
        BatchProcessDetails batchProcessDetails;
        try {
            batchProcessDetails = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestBody,
                    new TypeReference<BatchProcessDetails>() {
                    });
        } catch (IOException e) {
            LOGGER.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        if(!batchProcessDetails.validateForUpdate()) {
            LOGGER.error("Validation failure of batch process details provided");
            throw new ClientException(UIMessages.INVALID_REQUEST);
        }

        batchProcessDetails.setProcessId(batchProcessId);

        return UtilityBean.<com.appnomic.appsone.controlcenter.pojo.BatchProcessDetails>builder()
                .authToken(authToken)
                .accountIdentifier(identifier)
                .pojoObject(batchProcessDetails)
                .build();
    }

    @Override
    public ProcessDetailsBean serverValidation(UtilityBean<BatchProcessDetails> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            LOGGER.error("Error while extracting userIdentifier from authorization token. Reason: Invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            LOGGER.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }
        int accountId = account.getId();

        int batchProcessId = utilityBean.getPojoObject().getProcessId();

        BatchProcessDetails batchProcessDetails = utilityBean.getPojoObject();

        try {
            ProcessDetailsBean processDetailsBean = batchProcessDataService.getProcessDetailsWithId(batchProcessId);

            if(processDetailsBean == null) {
                LOGGER.error("Process with id [{}] unavailable", batchProcessId);
                throw new ServerException(String.format("Process with id [%d] unavailable", batchProcessId));
            }
        } catch (ControlCenterException e) {
            LOGGER.error("Exception occurred. Details: ", e);
        }

        if (batchProcessDetails.getProcessIdentifier() != null) {
            try {
                ProcessDetailsBean processDetailsBean = batchProcessDataService.getBatchProcessDetailsByIdentifier(batchProcessDetails.getProcessIdentifier());
                if (processDetailsBean != null) {
                    LOGGER.error("Process with identifier [{}] already exists", batchProcessDetails.getProcessIdentifier());
                    throw new ServerException(String.format("Process with identifier [%s] already exists", batchProcessDetails.getProcessIdentifier()));
                }
            } catch (ControlCenterException e) {
                LOGGER.error("Error while fetching process details using identifier [{}]", batchProcessDetails.getProcessIdentifier());
                throw new ServerException(String.format("Error while fetching process details using identifier [%s]", batchProcessDetails.getProcessIdentifier()));
            }
        }

        if(batchProcessDetails.getProcessName() != null) {
            try {
                ProcessDetailsBean processDetailsBean = batchProcessDataService.getBatchProcessDetailsByNameAndAccount(accountId, batchProcessDetails.getProcessName());
                if (processDetailsBean != null) {
                    LOGGER.error("Process with name [{}] already exists for accountId [{}]", batchProcessDetails.getProcessIdentifier(), accountId);
                    throw new ServerException(String.format("Process with name [%s] already exists for accountId [%d]", batchProcessDetails.getProcessIdentifier(), accountId));
                }
            } catch (ControlCenterException e) {
                LOGGER.error("Error while fetching process details using name [{}] for accountId [{}]", batchProcessDetails.getProcessIdentifier(), accountId);
                throw new ServerException(String.format("Error while fetching process details using name [%s] for accountId [%s]", batchProcessDetails.getProcessIdentifier(), accountId));
            }
        }

        if(batchProcessDetails.getBatchJobsToBeAdded() != null && !batchProcessDetails.getBatchJobsToBeAdded().isEmpty()) {
            try {
                List<Integer> mappingIds = bindInDataService.fetchBatchJobsMappedToProcessId(batchProcessId, batchProcessDetails.getBatchJobsToBeAdded(), null);
                if(mappingIds.size() > 0) {
                    LOGGER.error("Some or all of the entries of batchJobsToBeAdded already exist");
                    throw new ServerException("Some or all of the entries of batchJobsToBeAdded already exist");
                }
            } catch (ControlCenterException e) {
                LOGGER.error("Error while fetching process to batch mapping details for process Id [{}]", batchProcessId);
                throw new ServerException(String.format("Error while fetching process to batch mapping details for process Id [%d]", batchProcessId));
            }
        }

        if(batchProcessDetails.getBatchJobsToBeDeleted() != null && !batchProcessDetails.getBatchJobsToBeDeleted().isEmpty()) {
            try {
                List<Integer> mappingIds = bindInDataService.fetchBatchJobsMappedToProcessId(batchProcessId, batchProcessDetails.getBatchJobsToBeDeleted(), null);
                if(mappingIds.size() != batchProcessDetails.getBatchJobsToBeDeleted().size()) {
                    LOGGER.error("Some or all of the entries of batchJobsToBeDeleted are unavailable");
                    throw new ServerException("Some or all of the entries of batchJobsToBeDeleted are unavailable");
                }
            } catch (ControlCenterException e) {
                LOGGER.error("Error while fetching process to batch mapping details for process Id [{}]", batchProcessId);
                throw new ServerException(String.format("Error while fetching process to batch mapping details for process Id [%d]", batchProcessId));
            }
        }

        return ProcessDetailsBean.builder()
                .id(batchProcessId)
                .name(batchProcessDetails.getProcessName())
                .identifier(batchProcessDetails.getProcessIdentifier())
                .hostDetails(batchProcessDetails.getHostDetails() == null ? new ArrayList<>() : getHostDetails(batchProcessDetails.getHostDetails()))
                .batchJobsToBeAdded(batchProcessDetails.getBatchJobsToBeAdded() == null ? new ArrayList<>() : batchProcessDetails.getBatchJobsToBeAdded())
                .batchJobsToBeDeleted(batchProcessDetails.getBatchJobsToBeDeleted() == null ? new ArrayList<>() : batchProcessDetails.getBatchJobsToBeDeleted())
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .userDetailsId(userId)
                .build();
    }

    private List<ProcessHostDetailsBean> getHostDetails(List<HostDetails> hosts) {
        return hosts.parallelStream().map(host -> {
            if ((host.getHostAddress() != null || host.getDirectoryPath() != null)) {
                LOGGER.error("'hostAddress' or 'directoryPath' cannot be modified.");
                return null;
            }

            List<ProcessArgumentBean> argumentBeans = host.getArguments().parallelStream().map(arg -> ProcessArgumentBean.builder()
                    .id(arg.getId())
                    .name(arg.getName())
                    .value(arg.getValue())
                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .build()).collect(Collectors.toList());

            return ProcessHostDetailsBean.builder()
                    .id(host.getId())
                    .arguments(argumentBeans)
                    .build();
        }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public String process(ProcessDetailsBean processDetailsBean) throws DataProcessingException {
        try {
            return MySQLConnectionManager.getInstance().getHandle().inTransaction((conn, status) -> {
                batchProcessDataService.updateBatchProcessDetails(processDetailsBean, conn);

                List<ProcessArgumentBean> args = processDetailsBean.getHostDetails().parallelStream().map(ProcessHostDetailsBean::getArguments)
                        .flatMap(Collection::stream).collect(Collectors.toList());

                if(!args.isEmpty()) {
                    batchProcessDataService.updateBatchProcessHostArguments(args, conn);
                }

                List<BatchProcessMappingBean> processMappingBeans = new ArrayList<>();
                if(!processDetailsBean.getBatchJobsToBeAdded().isEmpty()) {
                    for (String batchJob : processDetailsBean.getBatchJobsToBeAdded()) {
                        processMappingBeans.add(BatchProcessMappingBean.builder()
                                .processId(processDetailsBean.getId())
                                .batchJobName(batchJob)
                                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                                .userDetailsId(processDetailsBean.getUserDetailsId())
                                .build());
                    }

                    int[] mappingIds = batchProcessDataService.addProcessBatchMapping(processMappingBeans, conn);

                    if(mappingIds.length != processDetailsBean.getBatchJobsToBeAdded().size()) {
                        LOGGER.error("Batch process mappings insertion failedReason: Row count mismatch; affected row count [{}] " +
                                "and batch jobs to be deleted [{}]", mappingIds, processDetailsBean.getBatchJobsToBeAdded());
                        throw new DataProcessingException("Batch process mappings insertion failed");
                    }
                }

                if(!processDetailsBean.getBatchJobsToBeDeleted().isEmpty()) {
                    int mappingIds = new BindInDataService().deleteBatchProcessMapping(processDetailsBean.getId(), processDetailsBean.getBatchJobsToBeDeleted(), conn);

                    if(mappingIds != processDetailsBean.getBatchJobsToBeDeleted().size()) {
                        LOGGER.error("Batch process mappings deletion failed. Reason: Row count mismatch; affected row count [{}] " +
                                "and batch jobs to be deleted [{}]", mappingIds, processDetailsBean.getBatchJobsToBeDeleted());
                        throw new DataProcessingException("Batch process mappings deletion failed");
                    }
                }

                return Constants.SUCCESS;
            });
        } catch (Exception e) {
            LOGGER.error("Error while updating process details. Details: ", e);
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }
}
