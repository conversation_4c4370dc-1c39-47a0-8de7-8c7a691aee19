package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.UserAccessDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.opensearch.AgentHealthStatusRepo;
import com.appnomic.appsone.controlcenter.dao.redis.AgentRepo;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.dao.redis.MasterDataRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.AgentHeartBeatPojo;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.UserValidationUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.entities.BasicBean;
import com.heal.configuration.pojos.BasicAgentEntity;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.Supervisor;
import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Slf4j
public class GetAgentHeartBeatDetailsBL implements BusinessLogic<String, AccountBean, List<AgentHeartBeatPojo>> {

    private String accountIdentifier;
    private String userId;
    private int accountId;
    @Override
    public UtilityBean<String> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Account identifier is invalid");
            throw new ClientException("Account identifier is invalid");
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);

        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        return UtilityBean.<String>builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .build();
    }

    @Override
    public AccountBean serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);
        if (account == null) {
            throw new ServerException("Account identifier is invalid");
        }
        accountId = account.getId();

        return account;
    }

    @Override
    public List<AgentHeartBeatPojo> process(AccountBean account) throws DataProcessingException {
        long startTime = System.currentTimeMillis();
        AgentRepo agentRepo = new AgentRepo();
        InstanceRepo instanceRepo = new InstanceRepo();
        MasterDataRepo masterDataRepo = new MasterDataRepo();
        List<String> userAccessibleApplications = new ArrayList<>();
        List<BasicAgentEntity> agents = agentRepo.getAgents(account.getIdentifier());
        log.debug("total time to get agent account mapping {}", System.currentTimeMillis() - startTime);

        UserAccessDetails userAccessDetails = UserValidationUtil.getUserAccessDetails(userId, accountIdentifier);
        if (userAccessDetails == null) {
            log.error("User access details is invalid for user [{}] and account [{}]", userId, accountIdentifier);
        } else {
            if (userAccessDetails.getApplicationIdentifiers() == null || userAccessDetails.getApplicationIdentifiers().isEmpty()) {
                log.error("Applications unavailable for user [{}] and account [{}]", userId, accountIdentifier);
                throw new DataProcessingException("applications not found");
            }
            userAccessibleApplications = userAccessDetails.getApplicationIdentifiers();
            if (userAccessDetails.getAgents() == null || userAccessDetails.getAgents().isEmpty()) {
                log.warn("Agents unavailable for user [{}] and account [{}]", userId, accountIdentifier);
            } else {
                List<Integer> agentBeans = userAccessDetails.getAgents().parallelStream().map(AgentBean::getId).collect(toList());

                agents = agents.parallelStream().filter(a -> agentBeans.contains(a.getId())).collect(toList());
            }
        }

        startTime = System.currentTimeMillis();
        List<Supervisor> supervisors = agentRepo.getSupervisors(account.getIdentifier());
        log.debug("total time to get supervisor bean {}", System.currentTimeMillis() - startTime);

        if(agents.isEmpty() && supervisors.isEmpty()) {
            log.warn("Agents unavailable for account [{}]", accountIdentifier);
            return Collections.emptyList();
        }

        Map<String, String> usersMap = UserAccessDataService.getActiveUsers().parallelStream().collect(Collectors.toMap(IdPojo::getIdentifier, IdPojo::getName));

        Map<Integer, String> statusMap = masterDataRepo.getTypes()
                .parallelStream()
                .collect(Collectors.toMap(com.heal.configuration.pojos.ViewTypes::getSubTypeId, com.heal.configuration.pojos.ViewTypes::getSubTypeName));
        startTime = System.currentTimeMillis();
        Map<String, List<HostDetailsBean>> hostAddressVsDetails = instanceRepo.getInstances(account.getIdentifier())
                .parallelStream()
                .filter(c -> c.getComponentTypeId() == 1)
                .filter(c -> !c.isCluster())
                .filter(c -> c.getStatus() == 1)
                .map(c -> HostDetailsBean.builder()
                        .hostId(c.getHostId())
                        .hostAddress(c.getHostAddress())
                        .hostInstanceName(c.getName())
                        .componentTypeName(c.getComponentTypeName())
                        .build())
                .collect(Collectors.groupingBy(HostDetailsBean::getHostAddress));
        log.debug("total time to get host details bean {}", System.currentTimeMillis() - startTime);

        Map<Integer, String> agentStatus = agents.parallelStream()
                .collect(Collectors.toMap(BasicAgentEntity::getPhysicalAgentId, s -> statusMap.getOrDefault(s.getLastStatusId(), "Unavailable"), (x1,x2) -> x1));

        startTime = System.currentTimeMillis();
        Map<String, Long> agentHeartBeatTimeMap = new AgentHealthStatusRepo().getAgentHeartBeatTime(accountIdentifier);
        log.debug("total time to get all agent health {}", System.currentTimeMillis() - startTime);

        startTime = System.currentTimeMillis();
        List<BasicBean> applications = AgentDataService.getAllApplicationMappedToServices(accountId, null);
       log.debug("total time taken by getting the applications from DB {}ms",System.currentTimeMillis()-startTime);

//Mapping applications based on service Identifier
        List<String> finalUserAccessibleApplications = userAccessibleApplications;
        Map<String, List<BasicBean>> servicesApplicationsMapping = applications.parallelStream()
                .filter(basicBean -> finalUserAccessibleApplications.contains(basicBean.getIdentifier()))
                .collect(Collectors.groupingBy(BasicBean::getConcernedConfigIdentifier));

        startTime = System.currentTimeMillis();
        List<AgentHeartBeatPojo> agentHeartBeatPojoList = agents.parallelStream()
                .map(a -> {
                    try {
                        return mapToAgentHeartbeatPojo(a, null, agentStatus, hostAddressVsDetails, agentHeartBeatTimeMap, usersMap, servicesApplicationsMapping);
                    } catch (Exception e) {
                        CCCache.INSTANCE.updateCCErrors(1);
                        log.error("Error occurred while mapping agent heartbeat pojo Agent {}, Status {}, AgentHeartBeatTime {}", a, agentStatus, agentHeartBeatTimeMap, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        log.debug("Total time taken by getting services and applications from agent {}ms", System.currentTimeMillis()-startTime);
        startTime = System.currentTimeMillis();
        agentHeartBeatPojoList.addAll(supervisors.parallelStream()
                .map(a -> {
                    try {
                        return mapToAgentHeartbeatPojo(null, a, agentStatus, hostAddressVsDetails, agentHeartBeatTimeMap, usersMap, servicesApplicationsMapping);
                    } catch (Exception e) {
                        CCCache.INSTANCE.updateCCErrors(1);
                        log.error("Error occurred while mapping supervisor heartbeat pojo Agent {}, Status {}, AgentHeartBeatTime {}", a, agentStatus, agentHeartBeatTimeMap, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList()));
        log.debug("Total time taken by getting services and applications from supervisor {}ms", System.currentTimeMillis()-startTime);

        return agentHeartBeatPojoList.parallelStream().sorted(Comparator.comparing(AgentHeartBeatPojo::getCommunicationStatus)
                .thenComparing(AgentHeartBeatPojo::getActiveOn)
                .thenComparing(AgentHeartBeatPojo::getName)
                .thenComparing(AgentHeartBeatPojo::getType)).collect(Collectors.toList());
    }

    private List<AgentHeartBeatPojo> mapToAgentHeartbeatPojo(BasicAgentEntity agent,
                                                             Supervisor supervisor,
                                                             Map<Integer, String> physicalAgentStatus,
                                                             Map<String, List<HostDetailsBean>> hostAddressVsDetails,
                                                             Map<String, Long> agentHeartBeatTimeMap,
                                                             Map<String, String> usersMap,
                                                             Map<String, List<BasicBean>> servicesApplicationsMapping) throws Exception {
        int agentTypeId;
        int id;
        int physicalAgentId;
        int communicationInterval;
        int agentStatus;
        String name;
        String physicalAgentIdentifier;
        String version;
        String hostAddress;
        String agentType = "Supervisor";
        String actionStatus = "Unavailable";
        String userDetailsId;
        Timestamp updatedTime;

        Set<String> services = new HashSet<>();
        Set<String> applications = new HashSet<>();

        if(agent != null) {
            id = agent.getId();
            physicalAgentId = agent.getPhysicalAgentId();
            agentTypeId = agent.getTypeId();
            communicationInterval = agent.getCommunicationInterval();
            agentStatus = agent.getStatus();
            name = agent.getName();
            physicalAgentIdentifier = agent.getPhysicalAgentIdentifier();
            version = agent.getVersion();
            hostAddress = agent.getHostAddress();
            userDetailsId = agent.getLastModifiedBy();
            updatedTime = DateTimeUtil.getTimestampInGMT(agent.getUpdatedTime());
            actionStatus = "Unavailable";
            if(physicalAgentStatus.containsKey(physicalAgentId)) {
                actionStatus = physicalAgentStatus.get(physicalAgentId);
            }

            ViewTypes agentTypeView = MasterCache.getMstSubTypeForSubTypeId(agentTypeId);
            if (agentTypeView == null) {
                log.error("Agent type unavailable for typeId [{}]", agentTypeId);
                throw new DataProcessingException("Agent type unavailable");
            }
            agentType = agentTypeView.getSubTypeName();

            List<BasicEntity> svcs = agent.getServices();
            services = svcs.stream().map(BasicEntity::getName).collect(Collectors.toSet());
            log.debug("total services based on physical agentIdentifier {}", services);

            applications = svcs.parallelStream()
                    .map(BasicEntity::getIdentifier)
                    .filter(Objects::nonNull)
                    .map(x ->servicesApplicationsMapping.getOrDefault(x, new ArrayList<>()) )
                    .flatMap(Collection::stream)
                    .map(BasicEntity::getName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

        } else {
            id = supervisor.getId();
            physicalAgentId = supervisor.getId();
            communicationInterval = supervisor.getCommunicationInterval();
            agentStatus = supervisor.getStatus();
            name = supervisor.getName();
            physicalAgentIdentifier = supervisor.getIdentifier();
            version = supervisor.getVersion();
            hostAddress = supervisor.getHostAddress();
            userDetailsId = supervisor.getLastModifiedBy();
            updatedTime = DateTimeUtil.getTimestampInGMT(supervisor.getUpdatedTime());
        }

        List<AgentHeartBeatPojo> agentHeartBeatPojoList = new ArrayList<>();

        String hostType = "NA";
        String hostInstanceName = "NA";

        List<HostDetailsBean> hostDetailsList = hostAddressVsDetails.get(hostAddress);
        if (hostDetailsList == null) {
            log.warn("Host details unavailable for host address [{}]", hostAddress);
        } else {
            hostType = String.join(", ", hostDetailsList.parallelStream().map(HostDetailsBean::getComponentTypeName).collect(Collectors.toSet()));
            hostInstanceName = String.join(", ", hostDetailsList.parallelStream().map(HostDetailsBean::getHostInstanceName).collect(Collectors.toSet()));
        }

        long lastActiveTime = agentHeartBeatTimeMap.getOrDefault(physicalAgentIdentifier, 0L);
        int communicationStatusMillis = communicationInterval * 60000;
        int commStatus = (lastActiveTime + communicationStatusMillis > System.currentTimeMillis()) ? 1 : 0;

        AgentHeartBeatPojo agentHeartBeatPojo = AgentHeartBeatPojo.builder()
                .id(id)
                .physicalAgentId(physicalAgentId)
                .name(name)
                .physicalAgentIdentifier(physicalAgentIdentifier)
                .type(agentType)
                .version(version)
                .hostAddress(hostAddress)
                .hostType(hostType)
                .hostInstanceName(hostInstanceName)
                .lastModifiedOn(updatedTime.getTime())
                .lastModifiedBy(usersMap.getOrDefault(userDetailsId, null))
                .communicationStatus(commStatus)
                .activeOn(lastActiveTime)
                .services(new ArrayList<>(services))
                .applications(new ArrayList<>(applications))
                .status(agentStatus)
                .actionStatus(actionStatus)
                .build();

        agentHeartBeatPojoList.add(agentHeartBeatPojo);

        return agentHeartBeatPojoList;
    }
}
