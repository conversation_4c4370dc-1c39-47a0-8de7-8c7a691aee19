package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.SMTPDetailsBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.SmtpDetails;
import com.appnomic.appsone.controlcenter.dao.mysql.NotificationDataService;
import com.appnomic.appsone.controlcenter.util.AECSBouncyCastleUtil;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GetEmailBL implements BusinessLogic<Integer, Integer, SmtpDetails> {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetEmailBL.class);

    @Override
    public UtilityBean<Integer> clientValidation(RequestObject requestObject) throws ClientException {
        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authKey)) {
            authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION.toLowerCase());
            if (StringUtils.isEmpty(authKey)) {
                LOGGER.error(UIMessages.AUTH_KEY_EMPTY);
                throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
            }
        }
        return UtilityBean.<Integer>builder().accountIdentifier(identifier).authToken(authKey).build();
    }

    @Override
    public Integer serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            LOGGER.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }
        return account.getId();
    }

    private String decryptBCECAndEncryptAECS(String input) throws DataProcessingException {
        String plainTxt = "";
        try {
            plainTxt = CommonUtils.decryptInBCEC(input);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while decrypting the password. Details: {}", e.getMessage(), e);
            throw new DataProcessingException("Error while decrypting the password from the database.");
        }
        try {
            return new AECSBouncyCastleUtil().encrypt(plainTxt);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while encrypting the password. Details: {}", e.getMessage(), e);
            throw new DataProcessingException("Error while encrypting the password from the database.");
        }

    }
    @Override
    public SmtpDetails process(Integer accountId) throws DataProcessingException {
        SMTPDetailsBean smtpDetailsBean;
        try {
            smtpDetailsBean = new NotificationDataService().getSMTPDetails(accountId, null);
        }catch (ServerException e){
            throw new DataProcessingException(e.getSimpleMessage());
        }
        if (smtpDetailsBean != null) {
            String encryptedString = smtpDetailsBean.getPassword();
            smtpDetailsBean.setPassword(this.decryptBCECAndEncryptAECS(encryptedString));
            return CommonUtils.getSMTPDetails(smtpDetailsBean);
        }
        return null;
    }
}
