package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.LicenseDetailService;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.LicenseInfo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR> - 07-12-2023
 */
@Slf4j
public class GetLicenseDetailsBL implements BusinessLogic<List<String>, Object, List<LicenseInfo>> {
    @Override
    public UtilityBean<List<String>> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }
        String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            log.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }

        return UtilityBean.<List<String>>builder()
                .authToken(authToken)
                .build();
    }

    @Override
    public Object serverValidation(UtilityBean<List<String>> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }
        return null;
    }

    @Override
    public List<LicenseInfo> process(Object bean) throws DataProcessingException {
        try {
            List<LicenseInfo> licenseInfoDetails = new LicenseDetailService().getLicenseInfoDetails(null);
            if(licenseInfoDetails != null) {
                log.debug("license info details with size {}", licenseInfoDetails.size());
                return licenseInfoDetails;
            } else {
                throw new DataProcessingException("Failed to get license details");
            }
        } catch (Exception e) {
            log.error("error while getting license details from db, er - {}", e.getMessage());
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }
}
