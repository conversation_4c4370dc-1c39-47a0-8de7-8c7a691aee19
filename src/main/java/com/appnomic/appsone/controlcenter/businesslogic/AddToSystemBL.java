package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.enums.DiscoveryStatus;
import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.AutoDiscoveredConfigurationEntities;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.AutoDiscoveryDiscoveredConnections;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.AutoDiscoveryServiceMapping;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.Host;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Actions;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.SupervisorBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.autodiscovery.DiscoveredAttributes;
import com.appnomic.appsone.controlcenter.dao.redis.ComponentRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.ComponentKpiEntity;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
public class AddToSystemBL implements BusinessLogic<List<String>, UtilityBean<List<String>>, String> {

    private static final Logger log = LoggerFactory.getLogger(AddToSystemBL.class);
    private static final ObjectMapper OBJECT_MAPPER = CommonUtils.getObjectMapperWithHtmlEncoder();
    AutoDiscoveryDataService autoDiscoveryDataService = new AutoDiscoveryDataService();
    AddConnectionBL addConnectionBL = new AddConnectionBL();
    AddAgentConfigBL addAgentConfigBL = new AddAgentConfigBL();
    private static final int controllerId = Objects.requireNonNull(MasterCache.getTagDetails(Constants.CONTROLLER_TAG)).getId();

    private static int syncToSystem = ConfProperties.getInt(Constants.SYNC_TO_SYSTEM, Constants.SYNC_TO_SYSTEM_DEFAULT);

    @Override
    public UtilityBean<List<String>> clientValidation(RequestObject requestObject) throws ClientException {
        if (null == requestObject) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }
        try {
            String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);
            String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

            if (requestObject.getQueryParams() != null && !requestObject.getQueryParams().isEmpty() && requestObject.getQueryParams().containsKey("syncToSystem")) {
                syncToSystem = Integer.parseInt(requestObject.getQueryParams().get("syncToSystem")[0]);
            }

            String requestBody = requestObject.getBody();
            List<String> addToSystemRequest;

            if (authToken == null || authToken.trim().isEmpty()) {
                log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
                throw new ClientException(UIMessages.AUTH_KEY_INVALID);
            }

            if (identifier == null || identifier.trim().isEmpty()) {
                log.error(UIMessages.ACCOUNT_NULL_OR_EMPTY);
                throw new ClientException(UIMessages.ACCOUNT_NULL_OR_EMPTY);
            }

            addToSystemRequest = OBJECT_MAPPER.readValue(requestBody, new TypeReference<List<String>>() {
            });

            if (addToSystemRequest.isEmpty()) {
                log.error("Validation failure for add to system request.");
                throw new ClientException("Validation failure for add to system request.");
            }
            log.info("Completed the client validation in addToSystemBL. syncToSystem:{}", syncToSystem);
            return UtilityBean.<List<String>>builder()
                    .authToken(authToken)
                    .accountIdentifier(identifier)
                    .pojoObject(addToSystemRequest)
                    .build();
        } catch (IOException e) {
            log.error(Constants.JSON_PARSE_ERROR, e);
            throw new ClientException(Constants.JSON_PARSE_ERROR+e.getMessage());
        } catch (Exception e) {
            log.error("Error occurred while doing client validation.", e);
            throw new ClientException("Error occurred while doing client validation." + e.getMessage());
        }
    }

    @Override
    public UtilityBean<List<String>> serverValidation(UtilityBean<List<String>> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }
        utilityBean.setAccount(account);
        utilityBean.setUserId(userId);
        return utilityBean;
    }

    @Override
    public String process(UtilityBean<List<String>> bean) throws DataProcessingException {
        /*
         * Get all host data with host identifier from staging
         * Check if it is already added to system
         * If not add to system else check discoveryRunTime is after lastUpdatedTime then do incremental updates to existing hosts.
         */
        try {
            List<com.appnomic.appsone.controlcenter.beans.autodiscovery.Host> hostList = autoDiscoveryDataService.getHostListByIdentifier(bean.getPojoObject(), null);
            for (com.appnomic.appsone.controlcenter.beans.autodiscovery.Host h : hostList) {
                h.setEndpoints(autoDiscoveryDataService.getHostEndpointByIdentifier(h.getHostIdentifier(), null));
                h.setConnections(autoDiscoveryDataService.getHostNetworkConnectionsByIdentifier(h.getHostIdentifier(), null));
                h.setRunningProcesses(autoDiscoveryDataService.getHostProcessByIdentifier(h.getHostIdentifier(), null));
                h.setNetworkInterfaces(autoDiscoveryDataService.getHostNetworkInterfacesByIdentifier(h.getHostIdentifier(), null));
                h.setMountPoints(autoDiscoveryDataService.getHostMountPointByIdentifier(h.getHostIdentifier(), null));
            }
            log.info("Number of hosts:{}", hostList.size());
            return checkAndUpdateHost(hostList, bean);
        } catch (ControlCenterException e) {
            log.error("Error occurred while processing data for host {}", bean.getPojoObject().toString());
            throw new DataProcessingException(e.getMessage());
        }
    }

    private String checkAndUpdateHost(List<com.appnomic.appsone.controlcenter.beans.autodiscovery.Host> hosts, UtilityBean<List<String>> bean) throws DataProcessingException {
        List<Integer> list = new ArrayList<>();
        EnvironmentHelper envHelper = new EnvironmentHelper();
        try {
            MasterComponentBean componentBean;
            List<AutoDiscoveryServiceMapping> serviceMappingList;
            List<DiscoveredAttributes> discoveredAttributesList;
            List<AgentBean>  discoveredAgentList = new ArrayList<>();
            SupervisorBean supervisorBean = null;
            List<ComponentInstancePojo> requestObj = new ArrayList<>();

            List<ConnectionDetailsPojo> connectionRequestObj = new ArrayList<>();

            List<ComponentInstanceBean> componentsList = new MasterDataService().getCompInstForAccount(bean.getAccount().getId(), null);
            Map<String, ComponentInstanceBean> componentInstanceBeanMap = componentsList.parallelStream()
                    .collect(Collectors.toMap(ComponentInstanceBean::getIdentifier, Function.identity()));


            for (com.appnomic.appsone.controlcenter.beans.autodiscovery.Host h : hosts) {

                componentBean = MasterCache.getMasterComponentUsingNameAndVersion(h.getOperatingSystem(),
                        h.getOperatingSystemVersion(), String.valueOf(Constants.DEFAULT_ACCOUNT_ID));

                if (componentBean == null) {
                    log.warn("Component not found for OS:{}, OSVersion:{}, Hostname:{}, HostIdentifier:{}", h.getOperatingSystem(), h.getOperatingSystemVersion(), h.getHostname(), h.getHostIdentifier());
                    continue;
                }

                log.info("Component found for OS:{}, OSVersion:{}", h.getOperatingSystem(), h.getOperatingSystemVersion());
                List<AgentDetails> agentDetails = autoDiscoveryDataService.getDiscoveryAgentForHostIdentifier(h.getHostIdentifier(), null);
                Map<String, String> instanceToJIMAgentMap = agentDetails
                        .parallelStream()
                        .filter(a -> a.getInstanceIdentifier() != null && a.getAgentTypeName().equalsIgnoreCase("JIMAgent"))
                        .collect(Collectors.toMap(AgentDetails::getInstanceIdentifier, AgentDetails::getAgentIdentifier));
                List<String> agentIdentifiers = agentDetails
                        .parallelStream()
                        .filter(f -> !f.getAgentTypeName().equals("Supervisor") && !f.getAgentTypeName().equalsIgnoreCase("JIMAgent"))
                        .map(AgentDetails::getAgentIdentifier)
                        .distinct()
                        .collect(Collectors.toList());

                serviceMappingList = autoDiscoveryDataService.getEntityServiceMapping(h.getHostIdentifier(), null);
                if (serviceMappingList.isEmpty()) {
                    log.error("At least one service should me mapped to component. Host: {}", h.getHostIdentifier());
                    throw new DataProcessingException(MessageFormat.format("At least one service should me mapped to host. {0}", h.getHostIdentifier()));
                }

                discoveredAttributesList = autoDiscoveryDataService.getDiscoveredAttributes(h.getHostIdentifier(), null);
                DiscoveredAttributes hostAddressAttribute = discoveredAttributesList.parallelStream().filter(f -> f.getAttributeName().equals("HostAddress")).findAny().orElse(null);
                if(hostAddressAttribute == null) {
                    log.error("Host address should be mandatory for discovery. Host identifier: {}, name:{}", h.getHostIdentifier(), h.getHostname());
                    throw new DataProcessingException(MessageFormat.format("Host address should be mandatory for discovery. Host identifier: {0}, name:{1}", h.getHostIdentifier(), h.getHostname()));
                }

                ComponentInstancePojo hostBean = new ComponentInstancePojo();
                hostBean.setName(h.getHostname());
                hostBean.setIdentifier(h.getHostIdentifier());
                hostBean.setComponentName(componentBean.getName());
                hostBean.setComponentVersion(componentBean.getComponentVersionName());
                hostBean.setCommonVersion(componentBean.getCommonVersionName());
                hostBean.setId(componentBean.getId());
                hostBean.setServiceIdentifiers(serviceMappingList.stream()
                        .map(AutoDiscoveryServiceMapping::getServiceIdentifier).distinct()
                        .collect(Collectors.toList()));
                hostBean.setDiscovery(1);
                hostBean.setEnvironment(envHelper.getOrDefaultEnvironmentName(h.getEnvironment()));
                if(!agentIdentifiers.isEmpty()) {
                    hostBean.setAgentIdentifiers(agentIdentifiers);
                }

                List<Attributes> hostAttributes = new ArrayList<>();
                for (DiscoveredAttributes a : discoveredAttributesList) {
                    Attributes hostAtt = new Attributes();
                    hostAtt.setName(a.getAttributeName());
                    hostAtt.setValue(a.getAttributeValue());
                    hostAttributes.add(hostAtt);
                }
                hostBean.setAttributes(hostAttributes);
                if (!componentInstanceBeanMap.containsKey(h.getHostIdentifier())) {
                    requestObj.add(hostBean);
                } else {
                    log.info("Host instance already present in the system. Host identifier:{}, hostname:{}", h.getHostIdentifier(), h.getHostname());
                }

                for (Process p : h.getRunningProcesses()) {
                    if(p.getComponentId() <= 0) {
                        log.warn("Component not found for process:{}, Hostname:{}, HostIdentifier:{}", p.getComponentId(), h.getHostname(), h.getHostIdentifier());
                        continue;
                    }
                    ComponentInstancePojo instanceBean = new ComponentInstancePojo();

                    componentBean = MasterCache.getMasterComponentUsingId(p.getComponentId(), p.getComponentVersion() ,String.valueOf(bean.getAccount().getId()));
                    if(componentBean == null) {
                        log.warn("Component not found for process componentId:{}, component version:{}, accountId:{}, Hostname:{}, HostIdentifier:{}", p.getComponentId(),p.getComponentVersion(), bean.getAccount().getId(), h.getHostname(), h.getHostIdentifier());
                        continue;
                    }
                    serviceMappingList = autoDiscoveryDataService.getEntityServiceMapping(p.getProcessIdentifier(), null);
                    if (serviceMappingList.isEmpty()) {
                        log.error("At least one service should me mapped to component. Process: {}, Host: {}", p.getProcessIdentifier(), h.getHostIdentifier());
                        throw new DataProcessingException(MessageFormat.format("At least one service should me mapped to component. {0}", p.getProcessIdentifier()));
                    }
                    discoveredAttributesList = autoDiscoveryDataService.getDiscoveredAttributes(p.getProcessIdentifier(), null);

                    instanceBean.setName(p.getProcessName());
                    instanceBean.setIdentifier(p.getProcessIdentifier());
                    instanceBean.setComponentName(componentBean.getName());
                    instanceBean.setComponentVersion(componentBean.getComponentVersionName());
                    instanceBean.setId(componentBean.getId());
                    instanceBean.setServiceIdentifiers(serviceMappingList.stream()
                            .map(AutoDiscoveryServiceMapping::getServiceIdentifier)
                            .distinct()
                            .collect(Collectors.toList()));
                    instanceBean.setDiscovery(1);
                    instanceBean.setEnvironment(envHelper.getOrDefaultEnvironmentName(h.getEnvironment()));
                    String jimAgentIdentifier = instanceToJIMAgentMap.getOrDefault(p.getProcessIdentifier(), null);
                    if (jimAgentIdentifier != null) {
                        if (!agentIdentifiers.isEmpty()) {
                            agentIdentifiers.add(jimAgentIdentifier);
                        } else {
                            agentIdentifiers = Collections.singletonList(jimAgentIdentifier);
                        }
                    }
                    instanceBean.setAgentIdentifiers(agentIdentifiers);
                    List<Attributes> instanceAttributes = new ArrayList<>();
                    for (DiscoveredAttributes a : discoveredAttributesList) {
                        Attributes instanceAtt = new Attributes();
                        instanceAtt.setName(a.getAttributeName());
                        instanceAtt.setValue(a.getAttributeValue());
                        instanceAttributes.add(instanceAtt);
                    }
                    instanceBean.setAttributes(instanceAttributes);
                    if (!componentInstanceBeanMap.containsKey(p.getProcessIdentifier())) {
                        requestObj.add(instanceBean);
                    } else {
                        log.info("Component instance already present in the system. Component instance identifier:{}, hostname:{}", p.getProcessIdentifier(), h.getHostname());
                    }
                }

                Timestamp timestamp = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
                for(AgentDetails agentDetail : agentDetails) {
                    if(agentDetail.getAgentTypeName().equalsIgnoreCase("ComponentAgent")) {
                        final String ip = ConfProperties.getString(Constants.HAPROXY_IP, Constants.HAPROXY_IP_DEFAULT);
                        final String port = ConfProperties.getString(Constants.HAPROXY_PORT, Constants.HAPROXY_PORT_DEFAULT);

                        DataCommunicationDetailsBean newCommunicationDetails = new DataCommunicationDetailsBean();

                        DataCommunicationDetailsBean dataCommunicationDetailsBean =  DataCommunicationDataService.getDataCommunicationDetailsByHostNPort(ip, Integer.parseInt(port));
                        if(dataCommunicationDetailsBean == null){
                            newCommunicationDetails.setHost(ip);
                            newCommunicationDetails.setName(agentDetail.getAgentName());
                            newCommunicationDetails.setProtocolId(20);
                            newCommunicationDetails.setPort(Integer.parseInt(port));
                            newCommunicationDetails.setUserDetailsId(bean.getAccount().getUserIdDetails());
                            newCommunicationDetails.setDescription(" ");
                            newCommunicationDetails.setUpdatedTime(timestamp);
                            newCommunicationDetails.setCreatedTime(timestamp);
                            DataCommunicationDataService.addDataCommunicationDetails(newCommunicationDetails, null);
                        }

                        Network networkDetails = Network.builder()
                                .host(ip)
                                .port(Integer.parseInt(port))
                                .build();
                        ComponentAgent componentAgent = new ComponentAgent();
                        componentAgent.setDataCommunication(networkDetails);
                        componentAgent.setDataOperationMode("REMOTE");
                        componentAgent.setConfigOperationMode("REMOTE");
                        componentAgent.setTimeoutMultiplier(2);
                        ViewTypes agentTypeId = MasterCache.getMstSubTypeForSubTypeName(agentDetail.getAgentTypeName());
                        AgentBean agent = AgentBean.builder()
                                .agentTypeId(agentTypeId.getTypeId())
                                .name(agentDetail.getAgentName())
                                .uniqueToken(agentDetail.getAgentIdentifier())
                                .hostAddress(hostAddressAttribute.getAttributeValue())
                                .accountId(bean.getAccount().getId())
                                .accountIdentifier(bean.getAccountIdentifier())
                                .userDetailsId(bean.getUserId())
                                .mode("MONITOR")
                                .subType(agentDetail.getAgentTypeName())
                                .createdTime(timestamp)
                                .description(" ")
                                .status(1)
                                .build();
                        agent.setAgentMappingDetails(componentAgent);
                        discoveredAgentList.add(agent);
                    }else if(agentDetail.getAgentTypeName().equals("Supervisor"))
                    {
                        ViewTypes agentTypeId = MasterCache.getMstSubTypeForSubTypeName(agentDetail.getAgentTypeName());
                        supervisorBean = new SupervisorBean();
                        supervisorBean.setSupervisorType(agentTypeId.getSubTypeId());
                        supervisorBean.setHostAddress(hostAddressAttribute.getAttributeValue());
                        supervisorBean.setAccountId(bean.getAccount().getId());
                        supervisorBean.setIdentifier(agentDetail.getAgentIdentifier());
                        supervisorBean.setName(agentDetail.getAgentName());
                        supervisorBean.setHostBoxName(h.getHostname());
                        supervisorBean.setMode("LOCAL");
                        supervisorBean.setUserId(bean.getUserId());
                        supervisorBean.setCreatedTime(new Timestamp(new Date().getTime()));
                        supervisorBean.setUpdatedTime(new Timestamp(new Date().getTime()));
                        supervisorBean.setStatus(true);
                        supervisorBean.setVersion(" ");
                    }
                    else{
                        ViewTypes agentTypeId = MasterCache.getMstSubTypeForSubTypeName(agentDetail.getAgentTypeName());
                        AgentBean agent = AgentBean.builder()
                                .agentTypeId(agentTypeId.getTypeId())
                                .name(agentDetail.getAgentName())
                                .uniqueToken(agentDetail.getAgentIdentifier())
                                .hostAddress(hostAddressAttribute.getAttributeValue())
                                .accountId(bean.getAccount().getId())
                                .accountIdentifier(bean.getAccountIdentifier())
                                .userDetailsId(bean.getUserId())
                                .mode("MONITOR")
                                .createdTime(timestamp)
                                .subType(agentDetail.getAgentTypeName())
                                .description(" ")
                                .status(1)
                                .build();
                        discoveredAgentList.add(agent);
                    }
                }

                List<AutoDiscoveryDiscoveredConnections> discConn = new AutoDiscoveryDataService().getDiscoveredConnectionsListByHost(h.getHostIdentifier(), null);
                for (AutoDiscoveryDiscoveredConnections c : discConn) {
                    ConnectionDetailsPojo conn = new ConnectionDetailsPojo();
                    conn.setIsDiscovery(1);
                    conn.setDestinationServiceIdentifier(c.getDestinationIdentifier());
                    conn.setSourceServiceIdentifier(c.getSourceIdentifier());
                    connectionRequestObj.add(conn);
                }

                log.debug("RequestObj: {}", requestObj);
                list.addAll(addToSystem(requestObj, h, connectionRequestObj, bean,  discoveredAgentList, supervisorBean));
            }
        } catch (Exception e) {
            throw new DataProcessingException(e, e.getMessage());
        }
        return list.toString();
    }

    private List<Integer> addToSystem(List<ComponentInstancePojo> requestObj, Host h, List<ConnectionDetailsPojo> connectionRequestObj, UtilityBean<List<String>> bean, List<AgentBean> agentDetailsList, SupervisorBean supervisor) throws ControlCenterException {

        List<Integer> newlyAddedInstancesAndConnections = new ArrayList<>();
        Map<String, Integer> agentSubTypeNameAndIdMap = new HashMap<>();
        Map<Integer, String> instanceIdAndComponentTypeMap = new HashMap<>();

        try {
            List<ComponentInstanceBean> componentInstanceBeanList = new ArrayList<>();
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();

            dbi.inTransaction((conn, status) -> {
                try {
                    if (supervisor != null && supervisor.getName() != null) {
                        SupervisorDataService supervisorDataService = new SupervisorDataService();
                        List<SupervisorBean> supervisorBeans = supervisorDataService.getAccountWiseSupervisorDetailsWithGlobalAccount(bean.getAccount().getId(), null);
                        boolean supNameExists = supervisorBeans.parallelStream()
                                .anyMatch(m -> supervisor.getName().equalsIgnoreCase(m.getName())
                                        || supervisor.getIdentifier().equals(m.getIdentifier())
                                        || supervisor.getHostAddress().equals(m.getHostAddress()));
                        if (supNameExists) {
                            log.debug("Supervisor with name:{} or identifier:{} or host address:{} already exists", supervisor.getName(), supervisor.getIdentifier(), supervisor.getHostAddress());
                        } else {
                            int result = supervisorDataService.addSupervisor(supervisor, conn);
                            log.debug("Supervisor add to db result:{}, name:{}, host address:{}", result, supervisor.getName(), supervisor.getHostAddress());
                        }
                    }
                    List<Integer> agentIdsList = new ArrayList<>();

                    for (AgentBean agentDetails : agentDetailsList) {
                        //TODO: Add a check with identifier as well
                        AgentBean agentBean = AgentDataService.getAgentBeanDataForName(agentDetails.getName());
                        if (agentBean != null) {
                            agentIdsList.add(agentBean.getId());
                            agentSubTypeNameAndIdMap.put(agentBean.getSubType(), agentBean.getId());
                            log.debug("Agent name: {} already exists", agentDetails.getName());
                        } else {
                            IdPojo idPojo = addAgentConfigBL.addAgentConfig(agentDetails, conn);
                            agentIdsList.add(idPojo.getId());
                            agentSubTypeNameAndIdMap.put(agentDetails.getSubType(), idPojo.getId());
                        }
                    }

                    String userId = bean.getUserId();
                    int accId = bean.getAccount().getId();

                    List<Controller> serviceList = CommonUtils.getControllersByTypeBypassCache(Constants.SERVICES_CONTROLLER_TYPE, accId);

                    for (ComponentInstancePojo componentInstancePojo : requestObj) {
                        ComponentInstanceBean componentInstanceBean = ComponentInstanceUtil.validateAndGetComponentInstance(componentInstancePojo, userId, accId, serviceList, agentIdsList, conn);

                        if (syncToSystem == 1) {
                            int mstComponentId = MasterCache.getMasterComponentTypeUsingName(Constants.COMPONENT_TYPE_HOST, String.valueOf(bean.getAccount().getId())).getId();
                            int existingHostCluster;
                            for (int i = 0; i < componentInstanceBean.getServiceIdentifiers().size(); i++) {
                                existingHostCluster = TagsDataService.getExistingHostClusterForService(controllerId, componentInstanceBean.getServiceIdentifiers().get(i), componentInstanceBean.getServiceIds()[i],
                                        Constants.COMP_INSTANCE_TABLE, mstComponentId, conn);

                                if (existingHostCluster == 0) {
                                    int clusterId = TagsDataService.getClusterIdForHost(componentInstanceBean.getHostAddress(), bean.getAccount().getId(), mstComponentId, conn);
                                    TagMappingBL.addTagMapping(controllerId, clusterId, Constants.COMP_INSTANCE_TABLE,
                                            String.valueOf(componentInstanceBean.getServiceIds()[0]), componentInstanceBean.getServiceIdentifiers().get(0), bean.getAccount().getUserIdDetails(), bean.getAccount().getId(), conn);
                                }
                            }
                        }
                        componentInstanceBeanList.add(componentInstanceBean);
                        int instanceID = ComponentInstanceUtil.addComponentInstance(componentInstanceBean, conn);
                        instanceIdAndComponentTypeMap.put(instanceID, componentInstanceBean.getMstComponentType());
                        newlyAddedInstancesAndConnections.add(instanceID);
                        if (componentInstanceBean.getIsHost() == 1)
                            autoDiscoveryDataService.setHostDiscoveryStatusTimeAccount(Long.toString(Instant.now().toEpochMilli()), DiscoveryStatus.ADDED_TO_SYSTEM.toString(), componentInstanceBean.getIdentifier(), bean.getAccount().getId(), conn);
                        else
                            autoDiscoveryDataService.setProcessDiscoveryStatusTimeAccount(Long.toString(Instant.now().toEpochMilli()), DiscoveryStatus.ADDED_TO_SYSTEM.toString(), componentInstanceBean.getIdentifier(), bean.getAccount().getId(), conn);
                    }
                } catch (Exception e) {
                    log.error("Error occurred while adding host/components for hostIdentifier. HostName: {}, HostIdentifier: {}", h.getHostname(), h.getHostIdentifier(), e);
                    throw new ControlCenterException(Throwables.getRootCause(e).getMessage());
                }

                CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
                for (Integer newInstanceId : newlyAddedInstancesAndConnections) {
                    try {
                        Integer newlyAddedJimAgentId = agentSubTypeNameAndIdMap.get(Constants.JIM_AGENT_SUB_TYPE);
                        Integer newlyAddedComponentAgentId = agentSubTypeNameAndIdMap.get(Constants.COMPONENT_AGENT_SUB_TYPE);
                        String createdTime = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
                        List<AgentCompInstMappingBean> agentCompInstanceMapping = new ArrayList<>();
                        if (newlyAddedComponentAgentId != null && newlyAddedComponentAgentId != 0) {
                            agentCompInstanceMapping.add(AgentCompInstMappingBean.builder().agentId(newlyAddedComponentAgentId).compInstanceId(newInstanceId).createdTime(createdTime).build());
                        }

                        if (instanceIdAndComponentTypeMap.get(newInstanceId).equalsIgnoreCase(Constants.APP_SERVER_COMPONENT_TYPE)) {
                            if (newlyAddedJimAgentId != null && newlyAddedJimAgentId != 0) {
                                agentCompInstanceMapping.add(AgentCompInstMappingBean.builder().agentId(newlyAddedJimAgentId).compInstanceId(newInstanceId).createdTime(createdTime).build());
                            }
                            createAgentInstanceMapping(conn, agentCompInstanceMapping, compInstanceDataService);
                        } else {
                            createAgentInstanceMapping(conn, agentCompInstanceMapping, compInstanceDataService);
                        }

                    } catch (Exception e) {
                        log.error("Error occurred while adding agents instance mapping for instance Id {}", newInstanceId);
                        throw new ControlCenterException(Throwables.getRootCause(e).getMessage());
                    }
                }

                try {
                    UtilityBean<List<ConnectionDetailsBean>> connList = addConnectionBL.serverValidation(UtilityBean.<List<ConnectionDetailsPojo>>builder()
                            .account(bean.getAccount())
                            .accountIdentifier(bean.getAccount().getIdentifier())
                            .userId(bean.getUserId())
                            .authToken(bean.getAuthToken())
                            .pojoObject(connectionRequestObj).build());
                    if (null != connList && !connList.getPojoObject().isEmpty()) {
                        int[] ids = ConnectionDetailsDataService.addConnection(connList.getPojoObject(), conn);
                        if (ids.length != 0) {
                            for (int id : ids)
                                newlyAddedInstancesAndConnections.add(id);
                        } else {
                            log.error("Unable to add connections. id(s): {}", ids);
                            throw new DataProcessingException("Unable to add connections");
                        }
                    }
                } catch (Exception e) {
                    log.error("Error occurred while adding connections for hostIdentifier. HostName: {}, HostIdentifier: {}", h.getHostname(), h.getHostIdentifier(), e);
                }
                addConfigurationEntities(requestObj, bean.getAccount().getId(), bean.getAccountIdentifier(), bean.getUserId(), h.getHostIdentifier(), conn);
                return null;
            });
            ComponentInstanceUtil.addInstancesToRedis(componentInstanceBeanList, bean.getAccountIdentifier());

        } catch (Exception e) {
            log.error("Error while add instance information. HostName: {}, HostIdentifier: {}", h.getHostname(), h.getHostIdentifier(), e);
            throw new ControlCenterException(Throwables.getRootCause(e).getMessage());
        }
        return newlyAddedInstancesAndConnections;
    }

    private static void createAgentInstanceMapping(Handle conn, List<AgentCompInstMappingBean> agentCompInstanceMapping, CompInstanceDataService compInstanceDataService) throws DataProcessingException {
        if (!agentCompInstanceMapping.isEmpty()) {
            int[] ids = compInstanceDataService.addAgentMappingToInstance(agentCompInstanceMapping, conn);
            if (ids == null || ids.length == 0) {
                log.error("Error while adding agent mapping to instance");
                throw new DataProcessingException("Error while adding agent mapping to instance");
            }
        }
    }

    public boolean addConfigurationEntities(List<ComponentInstancePojo> requestObj, int accountId, String accountIdentifier, String userId, String hostIdentifier, Handle handle) throws ControlCenterException {

        List<String> identifiersFromRequestPojo = requestObj.parallelStream()
                .map(ComponentInstancePojo::getIdentifier)
                .collect(Collectors.toList());
        List<ComponentInstanceBean> componentInstanceList = new MasterDataService().getCompInstForAccount(accountId, handle)
                .parallelStream()
                .filter(c -> identifiersFromRequestPojo.contains(c.getIdentifier()))
                .collect(Collectors.toList());

        Map<String, ComponentInstanceBean> componentInstanceBeanMap = componentInstanceList
                .parallelStream()
                .collect(Collectors.toMap(ComponentInstanceBean::getIdentifier, Function.identity()));

        AutoDiscoveryDataService autoDiscoveryDataService = new AutoDiscoveryDataService();

        int groupId = 0;
        ComponentRepo componentRepo = new ComponentRepo();

        try {
            List<AutoDiscoveredConfigurationEntities> configurationEntities = autoDiscoveryDataService.getDiscoveredConfigurationEntities(hostIdentifier, handle);

            for (AutoDiscoveredConfigurationEntities configurationEntity : configurationEntities) {

                ComponentInstanceBean componentInstanceBean = componentInstanceBeanMap.get(configurationEntity.getProcessIdentifier());

                if (componentInstanceBean != null) {

                    ComponentKpiEntity componentKpiEntity = componentRepo.getComponentKpiDetails(accountIdentifier, componentInstanceBean.getMstComponentName())
                            .parallelStream()
                            .filter(componentKpi -> componentKpi.getIdentifier().equals(configurationEntity.getKpiType()))
                            .findAny()
                            .orElse(null);
                    if (componentKpiEntity != null) {
                        groupId = componentKpiEntity.getGroupId();
                    }
                    UpdateMetricGroupAttributes updateMetricGroupAttributes = new UpdateMetricGroupAttributes();

                    List<Integer> instancesList = new ArrayList<>();
                    instancesList.add(componentInstanceBean.getId());

                    MetricDetailsRequest metricDetailsRequest = new MetricDetailsRequest();

                    metricDetailsRequest.setInstanceIds(instancesList);
                    metricDetailsRequest.setAccountIdentifier(accountIdentifier);
                    metricDetailsRequest.setAccountId(accountId);
                    metricDetailsRequest.setUserId(userId);
                    metricDetailsRequest.setComponentId(componentInstanceBean.getMstComponentId());
                    metricDetailsRequest.setGroupId(groupId);
                    MetricDetailsRequest.GroupAttributes groupAttributes = new MetricDetailsRequest.GroupAttributes();
                    groupAttributes.setValue(configurationEntity.getEntityPath());
                    groupAttributes.setOldValue(null);
                    groupAttributes.setAliasName(configurationEntity.getEntityPath());
                    groupAttributes.setAction(Actions.ADD);
                    groupAttributes.setStatus(1);

                    metricDetailsRequest.setGroupAttributes(Collections.singletonList(groupAttributes));
                    if (metricDetailsRequest.getGroupId() != 0) {
                        updateMetricGroupAttributes.updateMetricGroupAttributes(metricDetailsRequest, handle);
                    }
                }
            }
        } catch (ControlCenterException e) {
            log.error("Error adding entity configuration to file watch {}", e.getMessage(), e);
            throw new ControlCenterException(Throwables.getRootCause(e).getMessage());
        }
        return false;
    }
}
