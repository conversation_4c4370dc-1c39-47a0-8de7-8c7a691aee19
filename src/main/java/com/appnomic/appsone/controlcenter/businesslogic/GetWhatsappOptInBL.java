package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UserAttributesBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.UserDataService;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.OptInRequestPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.WhatsappOptInResponse;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.text.MessageFormat;

public class GetWhatsappOptInBL implements BusinessLogic<String, UtilityBean<UserAttributesBean>, WhatsappOptInResponse> {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetWhatsappOptInBL.class);

    @Override
    public UtilityBean<String> clientValidation(RequestObject requestObject) throws ClientException {
        LOGGER.debug("Inside Client validation");

        OptInRequestPojo optInRequestPojo;

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            optInRequestPojo = objectMapper.readValue(requestObject.getBody(), new TypeReference<OptInRequestPojo>() {});
        } catch (IOException e) {
            throw new ClientException(e, Constants.REQUEST_BODY_INVALID_ERROR_MESSAGE);
        }

        if (optInRequestPojo.getContactNumber() == null || optInRequestPojo.getContactNumber().isEmpty()) {
            LOGGER.error("Contact no is null or empty: [{}].", optInRequestPojo.getContactNumber());
            throw new ClientException(MessageFormat.format("Contact no. is null or empty: {0}.", optInRequestPojo.getContactNumber()));
        }

        return UtilityBean.<String>builder()
                .pojoObject(optInRequestPojo.getContactNumber())
                .build();
    }

    @Override
    public UtilityBean<UserAttributesBean> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        LOGGER.debug("Inside Server validation");

        UserAttributesBean userAttributesBean;

        try {
            UserDataService userDataService = new UserDataService();
            userAttributesBean = userDataService.getUserAttributesByContact(utilityBean.getPojoObject());

            if (userAttributesBean == null) {
                LOGGER.error("Invalid User {}", utilityBean.getPojoObject());
                throw new ServerException(MessageFormat.format("Invalid User {0}", utilityBean.getPojoObject()));
            }
        } catch (ControlCenterException e) {
            LOGGER.error("Invalid User {}", utilityBean.getPojoObject());
            throw new ServerException(MessageFormat.format("Invalid User {0}", utilityBean.getPojoObject()));
        }
        return UtilityBean.<UserAttributesBean>builder()
                .pojoObject(userAttributesBean)
                .build();
    }

    @Override
    public WhatsappOptInResponse process(UtilityBean<UserAttributesBean> bean) throws DataProcessingException {
        LOGGER.debug("Inside Process");

        return WhatsappOptInResponse.builder()
                .userId(bean.getPojoObject().getUserId())
                .optInStatus(bean.getPojoObject().getOptInStatus())
                .optInLastRequestTime(DateTimeUtil.getEpochTime(bean.getPojoObject().getOptInLastRequestTime()))
                .build();
    }
}
