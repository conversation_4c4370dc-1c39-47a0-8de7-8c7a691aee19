package com.appnomic.appsone.controlcenter.businesslogic.connectors;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.BusinessLogic;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.connectors.ConnectorDetails;
import com.appnomic.appsone.controlcenter.pojo.IdValuePojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.appnomic.appsone.controlcenter.dao.mysql.connectors.ConnectorDetailsDataService;

@Slf4j
public class GetConnectorsDetailsBL implements BusinessLogic<Object, Integer, List<ConnectorDetails>> {

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid account identifier");
        }

        return UtilityBean.builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .build();
    }

    @Override
    public Integer serverValidation(UtilityBean<Object> utilityBean) throws ServerException {

        String accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        int accountId = account.getId();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }
        return accountId;
    }

    @Override
    public List<ConnectorDetails> process(Integer accountId) throws DataProcessingException {
        List<ConnectorDetails> connectorDetailsList;
        try {
            connectorDetailsList = ConnectorDetailsDataService.getConnectorDetails(null);
            if(connectorDetailsList!=null)
                connectorDetailsList.removeIf(x -> x.getTemplateExists()==0);
            Map<Integer, Integer> isConfigured = ConnectorDetailsDataService.isConfiguredForAccount(accountId, null).stream().collect(Collectors.toMap(IdValuePojo::getId, IdValuePojo::getValue));

            if(connectorDetailsList == null)
                throw new DataProcessingException("No connectors available.");
            for(ConnectorDetails connectorDetails : connectorDetailsList)
            {
                if (isConfigured.containsKey(connectorDetails.getId()) && isConfigured.get(connectorDetails.getId()).equals(1)) {
                    connectorDetails.setConfigured(1);
                } else {
                    connectorDetails.setConfigured(0);
                }
                connectorDetails.setCommands(ConnectorDetailsDataService.getConnectorCommands(connectorDetails.getId(), null));
            }
            return connectorDetailsList;
        } catch (Exception e) {
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }
}
