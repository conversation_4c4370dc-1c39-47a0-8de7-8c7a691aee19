package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.exceptions.KpiException;
import com.appnomic.appsone.controlcenter.pojo.TagMappingDetails;
import com.appnomic.appsone.controlcenter.dao.mysql.TagsDataService;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

class TagMapping {

    private static final Logger LOGGER = LoggerFactory.getLogger(TagMapping.class);

    int addTagMappings(int tagIdentifier, int categoryId, String categoryIdentifier, String userId,
                        int accountId, int kpiId, Handle handle) throws KpiException {

        if(StringUtils.isEmpty(categoryIdentifier) || StringUtils.isEmpty(userId)) {
            LOGGER.error("categoryIdentifier is either empty or NULL");
            throw new KpiException("TagMapping data insertion failed. Reason: categoryIdentifier is invalid.");
        }

        TagMappingDetails tagMappingDetails = TagMappingDetails.builder()
                .tagId(tagIdentifier)
                .tagKey(String.valueOf(categoryId))
                .tagValue(categoryIdentifier)
                .objectRefTable("mst_kpi_details")
                .objectId(kpiId)
                .accountId(accountId)
                .userDetailsId(userId)
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .build();

        int tagId = TagsDataService.createDaoAndAddTagMappingDetails(tagMappingDetails, handle);

        if (-1 == tagId) {
            throw new KpiException("Error while adding TagMapping details.");
        }

        LOGGER.info("TagMapping successfully added.");

        return tagId;
    }
}
