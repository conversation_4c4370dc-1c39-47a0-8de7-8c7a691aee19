package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.AgentBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.Agent;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class UpdateAgentBL implements BusinessLogic<List<Agent>, List<AgentBean>, String> {

    @Override
    public UtilityBean<List<Agent>> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        List<Agent> agents;
        try {
            agents = objectMapper.readValue(requestObject.getBody(),
                    new TypeReference<List<Agent>>(){});
        } catch (IOException e) {
            log.error("IOException encountered while parsing request body. Details {}", e.getMessage());
            throw new ClientException(Constants.JSON_PARSE_ERROR);
        }

        for(Agent agent : agents) {
            if (!agent.validateForUpdate()) {
                log.error("Input validation failure");
                throw new ClientException("Input validation failure");
            }
        }

        return UtilityBean.<List<Agent>>builder()
                .authToken(authKey)
                .pojoObject(agents)
                .build();
    }

    @Override
    public List<AgentBean> serverValidation(UtilityBean<List<Agent>> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        List<Agent> agents = utilityBean.getPojoObject();
        List<AgentBean> agentBeanList = new ArrayList<>();

        for(Agent agent : agents) {
            AgentBean existingAgent = AgentDataService.fetchAgentAccountMapping(agent.getPhysicalAgentIdentifier());
            if (existingAgent == null) {
                log.error("Agent with physical agent [{}] unavailable", agent.getPhysicalAgentIdentifier());
                continue;
                //throw new ServerException(String.format("Agent with physical agent [%s] unavailable", agent.getPhysicalAgentIdentifier()));
            }

            AgentBean agentBean = new AgentBean();
            agentBean.setUniqueToken(existingAgent.getUniqueToken());
            agentBean.setVersion(agent.getVersion());
            agentBean.setUserDetailsId(userId);
            try {
                agentBean.setUpdatedTime(new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime()));
            } catch (ParseException e) {
                log.error("Error while creating updated time in GMT for agent [{}]. Details: ", agent.getPhysicalAgentIdentifier(), e);
                continue;
                //throw new ServerException(e.getMessage());
            }

            agentBeanList.add(agentBean);
        }
        if (agentBeanList.size() == 0){
            throw new ServerException("Invalid agent details exists in request, so update agent version will be skipped");
        }
        if(agentBeanList.size() != agents.size()){
            log.warn("Some of the agent details are wrong, total agents received :{}, valid agents:{}.", agents.size(), agentBeanList.size());
        }
        
        return agentBeanList;
    }

    @Override
    public String process(List<AgentBean> beans) throws DataProcessingException {
        log.debug("Processing agent update for agents: {}", beans);

        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        try {
            return dbi.inTransaction((conn, status) -> {
                int[] updatedRows;
                try {
                    updatedRows = new BindInDataService().updateAgentVersion(beans, null);
                } catch (ControlCenterException e) {
                    throw new DataProcessingException(e.getMessage());
                }

                if(updatedRows.length == 0 || updatedRows.length != beans.size()) {
                    log.error("Error while updating agent version");
                    throw new DataProcessingException("Error while updating agent version");
                }
                return "Agent version successfully updated";
            });
        } catch (Exception e) {
            log.error("Error while updating agent details. Details: ", e);

            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
            } else {
                throw e;
            }
        }
    }
}
