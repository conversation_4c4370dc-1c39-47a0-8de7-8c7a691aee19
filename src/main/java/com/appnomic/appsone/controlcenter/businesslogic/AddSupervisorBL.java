package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.SupervisorDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.SupervisorBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.Supervisor;
import com.appnomic.appsone.controlcenter.service.KeyCloakAuthService;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.appnomic.appsone.model.JWTData;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.UUID;

public class AddSupervisorBL implements BusinessLogic<Supervisor, SupervisorBean, String> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AddSupervisorBL.class);

    public UtilityBean<Supervisor> clientValidation(RequestObject requestObject) throws ClientException {
        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        AccountBean account = ValidationUtils.validAndGetAccount(identifier);
        if (account == null) {
            LOGGER.error("Account identifier is invalid");
            throw new ClientException("Account identifier is invalid");
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);

        if (authKey == null || authKey.trim().isEmpty()) {
            LOGGER.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

        Supervisor supervisor;
        try {
            supervisor = objectMapper.readValue(requestObject.getBody(),
                    new TypeReference<Supervisor>() {
                    });
        } catch (IOException e) {
            LOGGER.error("IOException encountered while parsing request body. Details {}", e.getMessage());
            throw new ClientException("Error while parsing request body");
        }

        String invalidParameters = supervisor.validate();
        if (!invalidParameters.trim().isEmpty()) {
            LOGGER.error("Input parameter(s) '[{}]' are invalid", invalidParameters);
            throw new ClientException(String.format("Input parameter(s) '%s' invalid", invalidParameters));
        }

        return UtilityBean.<Supervisor>builder()
                .account(account)
                .authToken(authKey)
                .pojoObject(supervisor)
                .build();
    }

    @Override
    public SupervisorBean serverValidation(UtilityBean<Supervisor> utilityBean) throws ServerException {
        JWTData jwtData;
        try {
            jwtData = KeyCloakAuthService.extractUserDetails(utilityBean.getAuthToken());
        } catch (ControlCenterException e) {
            LOGGER.error("Exception encountered while fetching the user identifier");
            throw new ServerException("Error in retrieving user identifier");
        }

        String userId = jwtData.getSub();

        Supervisor supervisor = utilityBean.getPojoObject();

        List<SupervisorBean> supervisorBeans = new SupervisorDataService().getAccountWiseSupervisorDetailsWithGlobalAccount(utilityBean.getAccount().getId(), null);

        boolean supNameExists = supervisorBeans.parallelStream()
                .anyMatch(bean -> supervisor.getName().equalsIgnoreCase(bean.getName()));

        if (supNameExists) {
            LOGGER.error("Supervisor with name [{}] already exists", supervisor.getName());
            throw new ServerException(String.format("Supervisor with name %s already exists", supervisor.getName()));
        }

        String supervisorIdentifier;
        if (StringUtils.isEmpty(supervisor.getSupervisorId())) {
            supervisorIdentifier = UUID.randomUUID().toString();
        } else {
            supervisorIdentifier = supervisor.getSupervisorId();
        }

        boolean supIdExists = supervisorBeans.parallelStream()
                .anyMatch(bean -> supervisorIdentifier.equalsIgnoreCase(bean.getIdentifier()));

        if (supIdExists) {
            LOGGER.error("Supervisor with identifier [{}] already exists", supervisorIdentifier);
            throw new ServerException(String.format("Supervisor with identifier %s already exists", supervisorIdentifier));
        }

        boolean supHostAddressExists = supervisorBeans.parallelStream()
                .anyMatch(bean -> supervisor.getHostAddress().equalsIgnoreCase(bean.getHostAddress()));

        if (supHostAddressExists) {
            LOGGER.error("Supervisor already exists for host address [{}]", supervisor.getHostAddress());
            throw new ServerException(String.format("Supervisor already exists for host address %s", supervisor.getHostAddress()));
        }

        ViewTypes supervisorType = MasterCache.getMstTypeForSubTypeName(Constants.SUPERVISOR_TYPE, supervisor.getSupervisorTypeName());

        if (supervisorType == null) {
            LOGGER.error("Supervisor type [{}] is invalid", supervisor.getSupervisorTypeName());
            throw new ServerException(String.format("Supervisor type %s is invalid", supervisor.getSupervisorTypeName()));
        }

        int supervisorTypeId = supervisorType.getSubTypeId();

        if (Constants.SUPERVISOR_REMOTE_MODE.equalsIgnoreCase(supervisor.getMode())) {
            boolean doesModeExist = supervisorBeans.parallelStream()
                    .anyMatch(sup -> sup.getMode().equalsIgnoreCase(supervisor.getMode()));

            if (doesModeExist) {
                LOGGER.error("Supervisor with mode [{}] already exists", supervisor.getMode());
                throw new ServerException(String.format("Supervisor with mode %s already exists", supervisor.getMode()));
            }
        }

        return SupervisorBean.builder()
                .identifier(supervisorIdentifier)
                .name(supervisor.getName())
                .supervisorType(supervisorTypeId)
                .hostAddress(supervisor.getHostAddress())
                .hostBoxName(supervisor.getHostBoxName())
                .version(supervisor.getVersion())
                .accountId(utilityBean.getAccount().getId())
                .userId(userId)
                .status(supervisor.isStatus())
                .mode(supervisor.getMode().toUpperCase())
                .createdTime(new Timestamp(new Date().getTime()))
                .updatedTime(new Timestamp(new Date().getTime()))
                .build();
    }

    @Override
    public String process(SupervisorBean bean) throws DataProcessingException {

        int result = new SupervisorDataService().addSupervisor(bean, null);

        if (result <= 0) {
            LOGGER.error("Error while adding supervisor");
            throw new DataProcessingException("Error while adding supervisor");
        }
        return "Supervisor successfully added";
    }
}
