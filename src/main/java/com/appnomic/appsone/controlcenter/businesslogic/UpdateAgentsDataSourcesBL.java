package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.TagsDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.TagMappingBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Kudva on 28/04/2022
 */
public class UpdateAgentsDataSourcesBL implements BusinessLogic<List<AgentsDataSources>, AgentsDataSourcesBean, String> {
    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateAgentsDataSourcesBL.class);

    @Override
    public UtilityBean<List<AgentsDataSources>> clientValidation(RequestObject requestObject) throws ClientException {
        String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }

        if (StringUtils.isEmpty(requestObject.getBody())) {
            LOGGER.error("Request body is empty.");
            throw new ClientException("Request body is empty.");
        }

        List<AgentsDataSources> agentsDataSources;
        try {
            agentsDataSources = new ObjectMapper().readValue(requestObject.getBody(),
                    new TypeReference<List<AgentsDataSources>>() {
                    });
            for(AgentsDataSources agentDataSource : agentsDataSources) {
                if (!agentDataSource.validate()) {
                    LOGGER.error("Request validation failure.");
                    throw new ClientException("Request validation failure. Kindly check the logs.");
                }
            }
        } catch (IOException e) {
            LOGGER.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }catch (Exception e) {
            throw new ClientException(e.getMessage());
        }
        return UtilityBean.<List<AgentsDataSources>>builder()
                .accountIdentifier(identifier)
                .pojoObject(agentsDataSources)
                .authToken(authToken)
                .build();
    }

    @Override
    public AgentsDataSourcesBean serverValidation(UtilityBean<List<AgentsDataSources>> utilityBean) throws ServerException {
        UserAccountBean userAccBean;
        try {
            userAccBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            throw new ServerException(e.getMessage());
        }

        List<AgentsDataSources> agentsDataSources = utilityBean.getPojoObject();
        int accountId = userAccBean.getAccount().getId();
        List<TagMappingBean> tagMappingToAddBeanList = new ArrayList<>();
        List<TagMappingBean> tagMappingToDeleteBeanList = new ArrayList<>();
        for(AgentsDataSources dataSources : agentsDataSources) {
            boolean agentIdExists = AgentDataService.isAgentIdExists(dataSources.getAgentId());
            if (!agentIdExists) {
                LOGGER.error("Agent to be updated has invalid Id: {}", dataSources.getAgentId());
                throw new ServerException(String.format("Agent to be updated has invalid Id:: [%s]", dataSources.getAgentId()));
            }
            TagDetailsBean tagDetails = MasterCache.getTagDetails(Constants.AGENT_DATA_SOURCES);

            tagMappingToAddBeanList.addAll(dataSources.getAddedDataSources()
                    .parallelStream().map(dataSource -> {
                        String date = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
                        return TagMappingBean.builder()
                            .tagId(tagDetails.getId())
                            .objectId(dataSources.getAgentId())
                            .objectRefTable(Constants.AGENT_TABLE)
                            .tagKey("Name")
                            .tagValue(dataSource)
                            .accountId(accountId)
                            .userDetailsId(userAccBean.getUserId())
                            .createdTime(date)
                            .updatedTime(date)
                            .build();
                    }).collect(Collectors.toList()));

            tagMappingToDeleteBeanList.addAll(dataSources.getDeletedDataSources()
                    .parallelStream().map(dataSource -> {
                        String date = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
                        return TagMappingBean.builder()
                            .tagId(tagDetails.getId())
                            .objectId(dataSources.getAgentId())
                            .objectRefTable(Constants.AGENT_TABLE)
                            .tagKey("Name")
                            .tagValue(dataSource)
                            .accountId(accountId)
                            .userDetailsId(userAccBean.getUserId())
                            .createdTime(date)
                            .updatedTime(date)
                            .build();
                    }).collect(Collectors.toList()));
        }
        return AgentsDataSourcesBean.builder()
                .addedAgentDataSources(tagMappingToAddBeanList)
                .deletedAgentDataSources(tagMappingToDeleteBeanList)
                .build();
    }

    @Override
    public String process(AgentsDataSourcesBean agentsDataSourcesBean) throws DataProcessingException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            return dbi.inTransaction((conn, status) -> updateAgentDataSources(agentsDataSourcesBean, conn));
        } catch (Exception e) {
            LOGGER.error("Unable to update agent data source. Details: ", e);
            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
            } else {
                throw e;
            }
        }
    }

    private String updateAgentDataSources(AgentsDataSourcesBean agentConfigBean, Handle handle) throws DataProcessingException {
        agentConfigBean.getAddedAgentDataSources().parallelStream()
                .forEach(tagMappingBean -> {
                    AgentDataService.addAgentDataSource(tagMappingBean, handle);
                });
        agentConfigBean.getDeletedAgentDataSources().parallelStream()
                .forEach(tagMappingBean -> {
                    TagsDataService.deleteTagMappingByTagValue(tagMappingBean.getObjectId(), tagMappingBean.getTagId(), tagMappingBean.getTagValue(), handle);
                });
        return "Agent Data Source details updated successfully";
    }
}