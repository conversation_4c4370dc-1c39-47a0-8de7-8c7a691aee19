package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.UserDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserNotificationDetailsBean;
import com.appnomic.appsone.controlcenter.dao.redis.UsersRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.UserNotificationDetails;
import com.appnomic.appsone.controlcenter.service.NotificationPreferencesDataService;
import com.appnomic.appsone.controlcenter.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.User;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class UpdateUserNotificationDetails implements BusinessLogic<List<UserNotificationDetails>, List<UserNotificationDetailsBean>, String> {

    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateUserNotificationDetails.class);

    @Override
    public UtilityBean<List<UserNotificationDetails>> clientValidation(RequestObject request) throws ClientException {

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String requestBody = request.getBody();
        if (StringUtils.isEmpty(requestBody)) {
            LOGGER.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
        String decryptedRequestBody;
        try {
            decryptedRequestBody = new AECSBouncyCastleUtil().decrypt(requestBody);
        } catch (InvalidCipherTextException e) {
            throw new ClientException(e, e.getMessage());
        }
        List<UserNotificationDetails> userNotificationDetails;
        try {
            userNotificationDetails = CommonUtils.getObjectMapperWithHtmlEncoder()
                    .readValue(decryptedRequestBody, new TypeReference<List<UserNotificationDetails>>() {
                    });
        } catch (IOException e) {
            LOGGER.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        if (userNotificationDetails == null || userNotificationDetails.isEmpty()) {
            LOGGER.error("User notification details are not provided in the body.");
            throw new ClientException("User notification details are not provided in the body.");
        }

        if (userNotificationDetails.parallelStream().anyMatch(u -> StringUtils.isEmpty(u.getUserId()))) {
            LOGGER.error("Request validation failure - Invalid userId (null or empty userId).");
            throw new ClientException(UIMessages.INVALID_REQUEST);
        }

        return UtilityBean.<List<UserNotificationDetails>>builder()
                .pojoObject(userNotificationDetails)
                .authToken(authToken)
                .build();
    }

    @Override
    public List<UserNotificationDetailsBean> serverValidation(UtilityBean<List<UserNotificationDetails>> utilityBean) throws ServerException {

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            LOGGER.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token.");
            throw new ServerException("Error while extracting user details from authorization token.");
        }

        List<UserNotificationDetailsBean> list = new ArrayList<>();
        Set<String> userIdentifiers = new UserDataService().getUsers().parallelStream().map(UserDetailsBean::getId).collect(Collectors.toSet());
        Timestamp timestampInGMT = DateTimeUtil.getCurrentTimestampInGMT();

        for (UserNotificationDetails details : utilityBean.getPojoObject()) {

            if (!userIdentifiers.contains(details.getUserId())) {
                LOGGER.error("Invalid userId specified in the request body. {}", details.getUserId());
                throw new ServerException("Invalid userId specified in the request body.");
            }

            list.add(UserNotificationDetailsBean.builder()
                    .emailEnabled(details.getEmailNotification() == 1)
                    .smsEnabled(details.getSmsNotification() == 1)
                    .forensicEnabled(details.getForensicNotification())
                    .applicableUserId(details.getUserId())
                    .userDetailsId(userId)
                    .updatedTime(timestampInGMT)
                    .build());
        }

        return list;

    }

    @Override
    public String process(List<UserNotificationDetailsBean> beans) throws DataProcessingException {
        try {
            NotificationPreferencesDataService.updateNotificationDetailsForUsers(beans, null);
            UpdateUserNotificationInRedis(beans);
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        return "User Notification Details updated successfully by user - " + beans.get(0).getUserDetailsId() + ".";
    }

    public void UpdateUserNotificationInRedis(List<UserNotificationDetailsBean> beans) {
        for ( UserNotificationDetailsBean bean : beans) {
            User existingUsers = new UsersRepo().getUser(bean.getApplicableUserId());
            if (existingUsers != null) {
                existingUsers.setForensicEnabled(bean.getForensicEnabled());
                existingUsers.setEmailEnabled(bean.isEmailEnabled() ? 1 : 0);
                existingUsers.setSmsEnabled(bean.isSmsEnabled() ? 1 : 0);
            }
            new UsersRepo().addUser(existingUsers);
        }
    }
}

