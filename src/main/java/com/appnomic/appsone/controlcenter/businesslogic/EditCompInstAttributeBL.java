package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.exception.AppsOneException;
import com.appnomic.appsone.common.util.Commons;
import com.appnomic.appsone.controlcenter.beans.CompInstAttributesBean;
import com.appnomic.appsone.controlcenter.beans.HierarchyBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AccountDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ComponentDataService;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.EditCompInstAttributePojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.service.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.util.AECSBouncyCastleUtil;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.InstanceAttributes;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.security.Security;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class EditCompInstAttributeBL implements BusinessLogic<HierarchyBean, List<CompInstAttributesBean>, List<CompInstAttributesBean>> {
    private static final Logger logger = LoggerFactory.getLogger(EditCompInstAttributeBL.class);
    InstanceRepo instanceRepo = new InstanceRepo();

    @Override
    public UtilityBean<HierarchyBean> clientValidation(RequestObject requestObject) throws ClientException {
        CompInstanceAttributesBL compInstanceAttributesBL = new CompInstanceAttributesBL();
        UtilityBean<HierarchyBean> hierarchyBeanUtilityBean = compInstanceAttributesBL.clientValidation(requestObject);
        HierarchyBean hierarchyBean = hierarchyBeanUtilityBean.getPojoObject();
        List<EditCompInstAttributePojo> editCompInstAttributePojos;
        try {
            editCompInstAttributePojos = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestObject.getBody(), new TypeReference<List<EditCompInstAttributePojo>>() {
            });
        } catch (Exception e) {
            logger.error("Exception encountered when parsing request body. : {}", e.getMessage());
            throw new ClientException("Exception encountered when parsing request body");
        }
        hierarchyBean.setEditCompInstAttributePojos(editCompInstAttributePojos);
        return UtilityBean.<HierarchyBean>builder()
                .pojoObject(hierarchyBean)
                .authToken(hierarchyBeanUtilityBean.getAuthToken())
                .build();
    }

    @Override
    public List<CompInstAttributesBean> serverValidation(UtilityBean<HierarchyBean> utilityBean) throws ServerException {
        List<CompInstAttributesBean> compInstAttributesBeans = instanceValidation(utilityBean);
        Map<String, CompInstAttributesBean> compInstAttributesBeanMap = compInstAttributesBeans.stream().collect(Collectors.toMap(t -> t.getAttributeKey().toLowerCase(), t -> t));
        HierarchyBean hierarchyBean = utilityBean.getPojoObject();
        List<EditCompInstAttributePojo> editCompInstAttributePojos = hierarchyBean.getEditCompInstAttributePojos();
        List<CompInstAttributesBean> updateAttributesList = new ArrayList<>();
        for (EditCompInstAttributePojo editCompInstAttributePojo : editCompInstAttributePojos) {
            int attributeId = editCompInstAttributePojo.getAttributeId();
            CompInstAttributesBean compInstAttributesBean = compInstAttributesBeanMap.get(editCompInstAttributePojo.getAttributeKey());
            if (compInstAttributesBean == null) {
                throw new ServerException("The attribute key '" + editCompInstAttributePojo.getAttributeKey() + "' doesn't exist to this instance");
            }
            if (compInstAttributesBean.getAttributeId() != attributeId) {
                throw new ServerException("The attributeId '" + attributeId + "' doesn't map to this attribute key " + compInstAttributesBean.getAttributeKey());
            }
            if (editCompInstAttributePojo.getAttributeKey().equals("hostaddress")) {
                throw new ServerException("The attribute key '" + editCompInstAttributePojo.getAttributeKey() + "' is not editable");
            }
            String attributeValue = editCompInstAttributePojo.getAttributeValue();
            if (!attributeValue.isEmpty()) {
                if (compInstAttributesBean.getType().equalsIgnoreCase("password")) {
                    try {
                        attributeValue = new AECSBouncyCastleUtil().decrypt(attributeValue);
                    } catch (InvalidCipherTextException e) {
                        String err = "Password is not properly encrypted.";
                        logger.error(err + " Details: {}", e.getMessage(), e);
                        throw new ServerException(err);
                    }
                    try {
                        attributeValue = Commons.encrypt(attributeValue);
                    } catch (AppsOneException e) {
                        logger.error("Error while encrypting. Details: {}", e.getMessage(), e);
                        throw new ServerException("Error while encrypting");
                    }
                }
            }
            compInstAttributesBean.setAttributeValue(attributeValue);
            compInstAttributesBean.setAccountIdentifier(hierarchyBean.getAccountIdString());
            int accountId = AccountDataService.getAccountByIdentifier(hierarchyBean.getAccountIdString(), null);
            int instanceId = Integer.parseInt(hierarchyBean.getInstanceIdString());
            String compInstanceIdentifier;
            try {
                compInstanceIdentifier = new com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService().getCompInstanceIdentifierFromId(instanceId, accountId, null);
                if (compInstanceIdentifier == null) {
                    logger.error("Validation failure. Invalid instance ID: [{}]", hierarchyBean.getInstanceIdString());
                    throw new ServerException(UIMessages.INVALID_INSTANCE);
                }
            } catch (ControlCenterException e) {
                logger.error("Exception occurred while getting instances details ");
                throw new ServerException("Exception occurred while getting instances details ");
            }
            compInstAttributesBean.setInstanceIdentifier(compInstanceIdentifier);
            compInstAttributesBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            updateAttributesList.add(compInstAttributesBean);
        }
        return updateAttributesList;
    }

    @Override
    public List<CompInstAttributesBean> process(List<CompInstAttributesBean> beans) throws DataProcessingException {
        try {
            for (CompInstAttributesBean compInstAttributesBean : beans) {
                if (compInstAttributesBean.getAttributeId() == 0) {
                    //create
                     compInstAttributesBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                     int attributeId = CompInstanceDataService.addCompInstanceAttribute(compInstAttributesBean, null);
                     compInstAttributesBean.setAttributeId(attributeId);
                     addInstanceAttributeDetails(compInstAttributesBean);
                } else {
                    //update
                    CompInstanceDataService.updateCompInstanceAttribute(compInstAttributesBean, null);
                    updateInstanceAttributeDetails(compInstAttributesBean);
                }
            }
        } catch (Exception e) {
            throw new DataProcessingException("Exception occurred while updating instance attributes" + e);
        }
        for (CompInstAttributesBean compInstAttributesBean : beans) {
            if (compInstAttributesBean.getType().equalsIgnoreCase("password")) {
                String attributeValue = compInstAttributesBean.getAttributeValue();
                if (!attributeValue.isEmpty()) {
                    try {
                        Security.removeProvider(Constants.BC_PROVIDER_NAME);
                        attributeValue = Commons.decrypt(attributeValue);
                    } catch (Exception e) {
                        logger.error("Exception encountered while decrypting the password. Details: {}", e.getMessage(), e);
                        throw new DataProcessingException("Error while decrypting the password from the database.");
                    }
                    try {
                        attributeValue = new AECSBouncyCastleUtil().encrypt(attributeValue);
                    } catch (Exception e) {
                        logger.error("Exception encountered while encrypting the password. Details: {}", e.getMessage(), e);
                        throw new DataProcessingException("Error while encrypting the password from the database.");
                    }
                }
                compInstAttributesBean.setAttributeValue(attributeValue);
            }
        }
        return beans;
    }
    public  List<CompInstAttributesBean> instanceValidation(UtilityBean<HierarchyBean> utilityBean)throws ServerException
    {
        HierarchyBean hierarchyBean = utilityBean.getPojoObject();
        int accountId = AccountDataService.getAccountByIdentifier(hierarchyBean.getAccountIdString(), null);
        if (accountId == 0) {
            logger.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }
        hierarchyBean.setAccountId(accountId);

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            logger.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        int instanceId = Integer.parseInt(hierarchyBean.getInstanceIdString());
        try {
            String compInstanceIdentifier = new com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService().getCompInstanceIdentifierFromId(instanceId, accountId, null);
            if (compInstanceIdentifier == null) {
                logger.error("Validation failure. Invalid instance ID: [{}]", hierarchyBean.getInstanceIdString());
                throw new ServerException(UIMessages.INVALID_INSTANCE);
            }

        } catch (ControlCenterException e) {
            logger.error("Exception occurred while getting instances details ");
            throw new ServerException("Exception occurred while getting instances details ");
        }

        List<CompInstAttributesBean> allValidInstanceAttributes = ComponentDataService.getInstanceMappingAttributes(instanceId, null);
        List<CompInstAttributesBean> instanceAttributes = ComponentDataService.getInstanceAttributes(instanceId, null);
        Map<String, CompInstAttributesBean> compInstAttributesBeanMap = instanceAttributes.stream().collect(Collectors.toMap(CompInstAttributesBean::getAttributeKey, t -> t));
        for (CompInstAttributesBean compInstAttributesBean : allValidInstanceAttributes) {
            CompInstAttributesBean instAttributesBean = compInstAttributesBeanMap.get(compInstAttributesBean.getAttributeKey());
            if (instAttributesBean != null) {
                compInstAttributesBean.setAttributeId(instAttributesBean.getAttributeId());
                compInstAttributesBean.setAttributeValue(instAttributesBean.getAttributeValue());
                compInstAttributesBean.setCreatedTime(instAttributesBean.getCreatedTime());
                compInstAttributesBean.setUpdatedTime(instAttributesBean.getUpdatedTime());
            }
            if (compInstAttributesBean.getUserDetailsId() == null) compInstAttributesBean.setUserDetailsId(userId);
        }

        return allValidInstanceAttributes;
    }
    public void updateInstanceAttributeDetails(CompInstAttributesBean compInstAttributesBean){
        List<InstanceAttributes> attributeDetails = instanceRepo.getAttributeDetails(compInstAttributesBean.getAccountIdentifier(), compInstAttributesBean.getInstanceIdentifier());
        if (attributeDetails.isEmpty()) {
            logger.error("Attribute details not found for instanceId:{} and account:{}", compInstAttributesBean.getInstanceIdentifier(), compInstAttributesBean.getAccountIdentifier());
            return;
        }
        attributeDetails.parallelStream().forEach(attributeDetail -> {
            if(compInstAttributesBean.getAttributeId() == attributeDetail.getAttributeId()){
                attributeDetail.setAttributeValue(compInstAttributesBean.getAttributeValue());
                instanceRepo.updateAttributeDetails(compInstAttributesBean.getAccountIdentifier(), compInstAttributesBean.getInstanceIdentifier(), attributeDetails);
            }
        });
    }
    private void addInstanceAttributeDetails(CompInstAttributesBean compInstAttributesBean){
        InstanceAttributes attributeList = InstanceAttributes.builder()
                .attributeId(compInstAttributesBean.getAttributeId())
                .attributeName(compInstAttributesBean.getAttributeName())
                .attributeValue(compInstAttributesBean.getAttributeValue())
                .attributeType(compInstAttributesBean.getType())
                .isCustom(compInstAttributesBean.getIsCustom())
                .status(compInstAttributesBean.getStatus())
                .compInstanceId(compInstAttributesBean.getCompInstanceId())
                .build() ;
        List<InstanceAttributes> existingAttributeList = instanceRepo.getAttributeDetails(compInstAttributesBean.getAccountIdentifier(), compInstAttributesBean.getInstanceIdentifier());
        if(existingAttributeList.isEmpty()){
            existingAttributeList = new ArrayList<>();
        }
        if(existingAttributeList.parallelStream().anyMatch(f -> f.getAttributeId() == compInstAttributesBean.getAttributeId())) {
            logger.error("The attribute details are already present for the attributeId: {}, instanceId: {} and accountId: {} ",compInstAttributesBean.getAttributeId(), compInstAttributesBean.getInstanceIdentifier(), compInstAttributesBean.getInstanceIdentifier());
            return;
        }
        existingAttributeList.add(attributeList);
        instanceRepo.updateAttributeDetails(compInstAttributesBean.getAccountIdentifier(), compInstAttributesBean.getInstanceIdentifier(), existingAttributeList);
    }

}
