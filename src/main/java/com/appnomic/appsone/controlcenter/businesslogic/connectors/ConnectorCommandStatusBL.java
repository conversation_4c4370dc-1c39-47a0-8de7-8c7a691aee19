package com.appnomic.appsone.controlcenter.businesslogic.connectors;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.BusinessLogic;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.connectors.ConnectorDetailsDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.connectors.ConnectorDetails;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class ConnectorCommandStatusBL implements BusinessLogic<Object, UtilityBean<Object>, List<ConnectorDetails.ConnectorCommandStatus>> {

    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {

        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }
        String token = requestObject.getHeaders().get("Authorization");
        if (token == null || token.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }
        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid account identifier");
        }
        return UtilityBean.builder()
                .accountIdentifier(identifier)
                .authToken(token)
                .build();
    }

    @Override
    public UtilityBean<Object> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {

        String accountIdentifier = utilityBean.getAccountIdentifier();
        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        utilityBean.setPojoObject(userId);
        return utilityBean;
    }

    @Override
    public List<ConnectorDetails.ConnectorCommandStatus> process(UtilityBean<Object> bean) throws DataProcessingException {
        List<ConnectorDetails.ConnectorCommandStatus> response;
        try {
            response = ConnectorDetailsDataService.getConnectorCommandStatus(null);
        } catch (Exception e)
        {
            log.error("Error while fetching connector command status. Details: " , e);
            throw new DataProcessingException("Error while fetching connector command status.");
        }
        return response;
    }
}
