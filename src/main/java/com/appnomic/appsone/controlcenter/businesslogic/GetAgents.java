package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.AgentBean;
import com.appnomic.appsone.controlcenter.beans.UserAccessDetails;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.UserAccessDataService;
import com.appnomic.appsone.controlcenter.dao.redis.AgentRepo;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.UserValidationUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.pojos.BasicEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * <AUTHOR> Kudva on 08/10/2021
 */
public class GetAgents implements BusinessLogic<Integer, AgentTypePojo, List<Agent>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AgentType.class);
    private String userIdentifier;

    @Override
    public UtilityBean<Integer> clientValidation(RequestObject request) throws ClientException {
        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }

        String[] agentTypeString = request.getQueryParams().get("agentTypeId");

        int agentTypeId = 0;
        if (agentTypeString != null && agentTypeString.length != 0) {
            if (agentTypeString.length > 1) {
                LOGGER.error("Multiple AgentTypeIds provided: [{}]", String.join(",", agentTypeString));
                throw new ClientException("Multiple AgentTypeIds provided.");
            } else {
                try {
                    agentTypeId = Integer.parseInt(agentTypeString[0].trim());
                    if (agentTypeId < 1) {
                        LOGGER.error("AgentTypeId {} should be a positive non-zero integer.", agentTypeId);
                        throw new ClientException("AgentTypeId should be a positive non-zero integer.");
                    }
                } catch (NumberFormatException e) {
                    LOGGER.error("AgentTypeId should be an integer. Details: {}", e.getMessage());
                    throw new ClientException("AgentTypeId should be positive integer.");
                }
            }
        }

        return UtilityBean.<Integer>builder()
                .accountIdentifier(identifier)
                .pojoObject(agentTypeId)
                .authToken(authToken)
                .build();
    }

    @Override
    public AgentTypePojo serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        UserAccountBean userAccBean;
        try {
            userAccBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            throw new ServerException(e.getMessage());
        }
        userIdentifier = userAccBean.getUserId();
        AgentTypePojo agentTypePojo = AgentTypePojo.builder()
                .id(utilityBean.getPojoObject())
                .account(userAccBean.getAccount())
                .build();

        //all agentTypes are considered
        if (agentTypePojo.getId() == 0) {
            return agentTypePojo;
        }
        int id = AgentDataService.checkIfAgentTypeExists(agentTypePojo.getId());
        if (id == 0) {
            LOGGER.error("Invalid agent type Id: {}", agentTypePojo.getId());
            throw new ServerException(String.format("Invalid agent type Id Id: [%d]", agentTypePojo.getId()));
        }

        return agentTypePojo;
    }

    @Override
    public List<Agent> process(AgentTypePojo agentTypePojo) throws DataProcessingException {
        long st = System.currentTimeMillis();
        try {
            long start = System.currentTimeMillis();
            List<AgentBean> agentBeans = new ArrayList<>();

            String accountIdentifier = agentTypePojo.getAccount().getIdentifier();
            UserAccessDetails userAccessDetails = UserValidationUtil.getUserAccessDetails(userIdentifier, accountIdentifier);
            if (userAccessDetails == null) {
                LOGGER.error("User access details is invalid for user [{}] and account [{}]", userIdentifier, accountIdentifier);
            } else {
                if (userAccessDetails.getApplicationIdentifiers() == null || userAccessDetails.getApplicationIdentifiers().isEmpty()) {
                    LOGGER.error("Applications unavailable for user [{}] and account [{}]", userIdentifier, accountIdentifier);
                    throw new DataProcessingException("applications not found");
                }

                if (userAccessDetails.getAgents() == null || userAccessDetails.getAgents().isEmpty()) {
                    LOGGER.warn("Agents unavailable for user [{}] and account [{}]", userIdentifier, accountIdentifier);
                } else {
                    agentBeans = userAccessDetails.getAgents();

                    if(agentTypePojo.getId() != 0) {
                        agentBeans = agentBeans.parallelStream().filter(a -> a.getAgentTypeId() == agentTypePojo.getId()).collect(toList());
                    }
                }
            }

            LOGGER.debug("Time taken to get the agents from db is {} ms.", (System.currentTimeMillis() - start));
            start = System.currentTimeMillis();

            AgentRepo agentRepo = new AgentRepo();
            ServiceRepo serviceRepo = new ServiceRepo();
            InstanceRepo instanceRepo = new InstanceRepo();

            Map<String, String> hostAddressMap = instanceRepo.getInstances(agentTypePojo.getAccount().getIdentifier()).parallelStream()
                    .filter(i -> i.getComponentTypeId() == 1)
                    .filter(i -> !i.isCluster())
                    .filter(i -> i.getStatus() == 1)
                    .collect(groupingBy(com.heal.configuration.pojos.CompInstClusterDetails::getHostAddress,
                            mapping(com.heal.configuration.pojos.CompInstClusterDetails::getComponentTypeName, joining(","))));

            Map<String, String> usersMap = UserAccessDataService.getActiveUsers().parallelStream().collect(Collectors.toMap(IdPojo::getIdentifier, IdPojo::getName));
            Map<Integer, BasicEntity> servicesMap = serviceRepo.getAllServicesDetails(agentTypePojo.getAccount().getIdentifier()).parallelStream().collect(Collectors.toMap(BasicEntity::getId, Function.identity()));

            LOGGER.debug("Time taken to get the agent metadata from redis is {} ms.", (System.currentTimeMillis() - start));

            List<Agent> agents = agentBeans.parallelStream()
                    .map(agent -> {
                        try {
                            com.heal.configuration.pojos.Agent agentDetails = agentRepo.getAgentDetails(agent.getUniqueToken());

                            if(agentDetails == null) {
                                LOGGER.error("Agent details unavailable in redis for agent identifier [{}]", agent.getUniqueToken());
                                return null;
                            }

                            ComponentAgent componentAgent = null;
                            if (agentDetails.getTypeName().equalsIgnoreCase(Constants.COMPONENT_AGENT_SUB_TYPE) &&
                                    agentDetails.getAgentDataCollectionDetails() != null) {

                                Network network = Network.builder()
                                        .id(agentDetails.getAgentDataCollectionDetails().getId())
                                        .name(agentDetails.getAgentDataCollectionDetails().getName())
                                        .host(agentDetails.getAgentDataCollectionDetails().getDataAddress())
                                        .port(agentDetails.getAgentDataCollectionDetails().getDataPort())
                                        .protocol(agentDetails.getAgentDataCollectionDetails().getDataProtocol())
                                        .type("Communication_Endpoint")
                                        .description(agentDetails.getAgentDataCollectionDetails().getName())
                                        .endpoint(agentDetails.getAgentDataCollectionDetails().getDataEndPoint())
                                        .build();

                                componentAgent = ComponentAgent.builder()
                                        .agentIdentifier(agent.getUniqueToken())
                                        .timeoutMultiplier(agentDetails.getAgentDataCollectionDetails().getTimeoutMultiplier())
                                        .configOperationMode(agentDetails.getConfigOperationMode())
                                        .dataOperationMode(agentDetails.getDataOperationMode())
                                        .dataCommunication(network)
                                        .build();
                            }

                            List<String> dataSources = agentDetails.getTags()
                                    .parallelStream()
                                    .filter(t -> t.getType().equalsIgnoreCase(Constants.AGENT_DATA_SOURCES))
                                    .map(com.heal.configuration.pojos.Tags::getValue)
                                    .distinct()
                                    .collect(toList());

                            List<String> instanceIds = agentRepo.getAgentInstanceMappingDetails(agentTypePojo.getAccount().getIdentifier(), agent.getUniqueToken())
                                    .parallelStream()
                                    .map(i -> i.getId() + "")
                                    .distinct()
                                    .collect(toList());

                            BasicEntity service = null;
                            if(agent.getServiceIds() != null && !agent.getServiceIds().isEmpty()) {
                                service = servicesMap.get(agent.getServiceIds().get(0));
                            }

                            return Agent.builder()
                                    .id(agent.getId())
                                    .name(agent.getName())
                                    .uniqueToken(agent.getUniqueToken())
                                    .physicalAgentIdentifier(agent.getPhysicalAgentIdentifier())
                                    .subType(agentDetails.getTypeName())
                                    .description(agent.getDescription())
                                    .mode(agent.getMode())
                                    .hostAddress(agent.getHostAddress())
                                    .version(agent.getVersion())
                                    .hostType(hostAddressMap.getOrDefault(agent.getHostAddress(), ""))
                                    .status(agent.getStatus())
                                    .serviceId(service != null ? service.getId() : 0)
                                    .serviceName(service != null ? service.getName() : "")
                                    .compInstIdentifiers(Collections.singletonList(AgentCompInstMappingDetails.builder()
                                            .compInstIds(instanceIds).build()))
                                    .agentMappingDetails(componentAgent)
                                    .addedDataSources(dataSources)
                                    .updatedTime(agent.getUpdatedTime())
                                    .updatedBy(usersMap.getOrDefault(agent.getUserDetailsId(), null))
                                    .build();
                        } catch (Exception e) {
                            LOGGER.error("Error while populating the agent details. Agent:{}", agent, e);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparing(Agent::getUpdatedTime, Comparator.reverseOrder())
                            .thenComparing(Agent::getStatus, Comparator.reverseOrder())
                            .thenComparing(Agent::getName))
                    .collect(Collectors.toList());

            if(agents.size() != agentBeans.size()) {
                throw new DataProcessingException("Error while populating agents list");
            }

            return agents;
        } catch (Exception e) {
            LOGGER.error("Error while getting agent data: Details: ", e);
            throw new DataProcessingException(e.getMessage());
        } finally {
            LOGGER.debug("Time taken for fetching agent details is {} ms.", System.currentTimeMillis() - st);
        }
    }
}