package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.ServiceTransactionSettingBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.ClientValidations;
import com.appnomic.appsone.controlcenter.common.CronExpressionUtil;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.TransactionDataService;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.AutoAcceptanceSettingsPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.Service;
import com.heal.configuration.pojos.TransactionAutoAcceptance;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AddAutoAcceptanceSettingsBL implements BusinessLogic<AutoAcceptanceSettingsPojo, UtilityBean<AutoAcceptanceSettingsPojo>, String> {
    private final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    @Override
    public UtilityBean<AutoAcceptanceSettingsPojo> clientValidation(RequestObject request) throws ClientException {
        ClientValidations.requestNullCheck(request);

        String accountIdentifier = ClientValidations.accountNullCheck(request);
        String authToken = ClientValidations.authTokenNullCheck(request);
        String serviceId = ClientValidations.serviceNullCheck(request);

        ClientValidations.requestBodyNullCheck(request.getBody());
        String acceptanceSettingsPayload = request.getBody();
        AutoAcceptanceSettingsPojo autoAcceptanceSettingsPojos;
        try {
            autoAcceptanceSettingsPojos = objectMapper.readValue(acceptanceSettingsPayload, new TypeReference<>() {
            });
        } catch (JsonProcessingException e) {
            log.error("Exception while parsing input request body. Details: {}", e.getMessage());
            throw new ClientException("Error while parsing input");
        }

        AutoAcceptanceSettingsPojo.validateAutoAcceptanceSettingsPayload(autoAcceptanceSettingsPojos);

        return UtilityBean.<AutoAcceptanceSettingsPojo>builder()
                .accountIdentifier(accountIdentifier)
                .serviceId(serviceId)
                .authToken(authToken)
                .pojoObject(autoAcceptanceSettingsPojos)
                .build();
    }

    @Override
    public UtilityBean<AutoAcceptanceSettingsPojo> serverValidation(UtilityBean<AutoAcceptanceSettingsPojo> utilityBean) throws ServerException {
        UserAccountBean userAccountBean;
        try {
            userAccountBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
            utilityBean.setUserId(userAccountBean.getUserId());
            utilityBean.setAccount(userAccountBean.getAccount());
        } catch (RequestException e) {
            log.error("Error while validating user and account details", e);
            throw new ServerException("Error while validating user and account details");
        }

        ControllerBean serviceDetails = new ControllerDataService().getControllerById(Integer.parseInt(utilityBean.getServiceId()), userAccountBean.getAccount().getId(), null);
        if (serviceDetails == null) {
            log.error("Service details unavailable for serviceID [{}]", utilityBean.getServiceId());
            throw new ServerException("Service details unavailable for serviceID [{}]" + utilityBean.getServiceId());
        }

        utilityBean.setServiceIdentifier(serviceDetails.getIdentifier());

        return utilityBean;
    }


    @Override
    public String process(UtilityBean<AutoAcceptanceSettingsPojo> bean) throws DataProcessingException {
        try {
            // Update to percona
            MySQLConnectionManager.getInstance().getHandle().inTransaction((conn, status) -> {
                TransactionDataService transactionDataService = new TransactionDataService();
                // Update transaction settings table
                try {
                    transactionDataService.updateTransactionSettingTable(bean.getPojoObject(), Integer.parseInt(bean.getServiceId()), conn);
                } catch (ControlCenterException e) {
                    throw new DataProcessingException("Exception while updating transaction auto acceptance settings in transaction settings table");
                }

                // Updating Scheduler Details table with cron expression
                try {
                    ServiceTransactionSettingBean transactionSetting = transactionDataService.getTransactionSetting(bean.getAccount().getId(), Integer.parseInt(bean.getServiceId()));
                    String cron_expression = CronExpressionUtil.generateCronExpression(bean.getPojoObject().getHoldDuration(), DateTimeUtil.getHourTime(transactionSetting.getLastCommitedTime()));
                    transactionDataService.updateSchedulerDetailsTable(cron_expression, transactionSetting.getSchedulerDetailsId(), conn);
                } catch (Exception e) {
                    throw new DataProcessingException("Error while generating cron expression or updating cron expression");
                }

                return "success";
            });

            // Update to redis
            ServiceRepo serviceRepo = new ServiceRepo();
            Service service = serviceRepo.getServiceConfigurationByIdentifier(bean.getAccountIdentifier(), bean.getServiceIdentifier());
            if (service == null) {
                log.error("Service is unavailable in redis. Unable to set auto acceptance settings for service identifier {}", bean.getServiceIdentifier());
                return "Auto acceptance settings updated in only percona";
            }

            TransactionAutoAcceptance transactionAutoAcceptance = service.getTransactionAutoAcceptance();
            if (transactionAutoAcceptance == null) {
                log.error("TransactionAutoAcceptance is unavailable in redis. Unable to update auto acceptance settings in redis for service identifier : {}", bean.getServiceIdentifier());
                return "Auto acceptance settings updated in only percona";
            }

            AutoAcceptanceSettingsPojo autoAcceptanceSettingsPojos = bean.getPojoObject();
            if (autoAcceptanceSettingsPojos.getMinRequestCount() != transactionAutoAcceptance.getMinVolumeCount()) {
                transactionAutoAcceptance.setMinVolumeCount(autoAcceptanceSettingsPojos.getMinRequestCount());
            }
            if (autoAcceptanceSettingsPojos.getMaxAutoAcceptedRequests() != transactionAutoAcceptance.getMaxTxnLimit()) {
                transactionAutoAcceptance.setMaxTxnLimit(autoAcceptanceSettingsPojos.getMaxAutoAcceptedRequests());
            }
            if (autoAcceptanceSettingsPojos.getHoldDuration() != transactionAutoAcceptance.getAutoCommitDuration()) {
                transactionAutoAcceptance.setAutoCommitDuration(autoAcceptanceSettingsPojos.getHoldDuration());
            }

            service.setTransactionAutoAcceptance(transactionAutoAcceptance);

            serviceRepo.updateServiceConfigurationByServiceIdentifier(bean.getAccountIdentifier(), bean.getServiceIdentifier(), service);

            return "Successfully updated data into percona and redis";
        } catch (Exception e) {
            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
            } else {
                throw e;
            }
        }
    }
}
