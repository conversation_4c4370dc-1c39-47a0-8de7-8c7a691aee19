package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.CategoryDetailBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.CategoryType;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CategoryDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.CategoryRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.CategoryDetails;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.Category;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;
import java.util.UUID;

@Slf4j
public class AddCategory implements BusinessLogic<CategoryDetails, CategoryDetailBean, IdPojo> {

    CategoryDataService categoryDataService = new CategoryDataService();
    CategoryRepo categoryRepo = new CategoryRepo();

    @Override
    public UtilityBean<CategoryDetails> clientValidation(RequestObject request) throws ClientException {

        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(request.getBody())) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }

        String requestBody = request.getBody();
        CategoryDetails categoryDetails;
        try {
            categoryDetails = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestBody,
                    new TypeReference<CategoryDetails>() {
                    });
        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        if (!categoryDetails.validate()) {
            log.error("Validation failure of details provided");
            throw new ClientException(UIMessages.INVALID_REQUEST);
        }

        return UtilityBean.<com.appnomic.appsone.controlcenter.pojo.CategoryDetails>builder()
                .authToken(authToken)
                .accountIdentifier(identifier)
                .pojoObject(categoryDetails)
                .build();
    }

    @Override
    public CategoryDetailBean serverValidation(UtilityBean<CategoryDetails> utilityBean) throws ServerException {

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be " +
                    "invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        int accountId = account.getId();
        CategoryDetails categoryDetails = utilityBean.getPojoObject();
        categoryDetails.setName(categoryDetails.getName().trim());

        List<CategoryDetailBean> categories;
        try {
            categories = categoryDataService.getCategoriesForAccount(accountId);
        } catch (ControlCenterException e) {
            throw new ServerException(e.getMessage());
        }

        if (categories.stream().anyMatch(c -> c.getName().equalsIgnoreCase(categoryDetails.getName()))) {
            log.error("Category name should be unique in the given account.");
            throw new ServerException("Category name should be unique in the given account.");
        }

        return CategoryDetailBean.builder()
                .name(categoryDetails.getName())
                .accountId(accountId)
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .userDetailsId(userId)
                .description(categoryDetails.getDescription() != null ? categoryDetails.getDescription() : "")
                .identifier(categoryDetails.getIdentifier() != null ? categoryDetails.getIdentifier() : UUID.randomUUID().toString())
                .status(categoryDetails.getSubType() != null ? categoryDetails.getStatus() : Constants.STATUS_ACTIVE)
                .isCustom(Constants.STATUS_ACTIVE)
                .isInformative((categoryDetails.getSubType() != null && categoryDetails.getSubType().trim()
                        .equalsIgnoreCase(CategoryType.INFO.getType())) ? 1 : 0)
                .isWorkLoad(((categoryDetails.isWorkLoad() && categoryDetails.getSubType() == null) ||
                        categoryDetails.getSubType().trim().equalsIgnoreCase(CategoryType.WORKLOAD.getType()) ? 1 : 0))
                .accountIdentifier(utilityBean.getAccountIdentifier())

                .build();
    }

    @Override
    public IdPojo process(CategoryDetailBean categoryBean) throws DataProcessingException {

        int id = categoryDataService.addCategory(categoryBean, null);
        if (id < 1) {
            throw new DataProcessingException("Unable to add category");
        }

        addCategoryInRedis(categoryBean, id);
        return IdPojo.builder()
                .id(id)
                .identifier(categoryBean.getIdentifier())
                .name(categoryBean.getName())
                .build();
    }

    public void addCategoryInRedis(CategoryDetailBean categoryDetailBean, int id) {
        List<Category> existingCategoryDetails = categoryRepo.getCategoryDetails(categoryDetailBean.getAccountIdentifier());

        Category newCategory = Category.builder()
                .id(id)
                .name(categoryDetailBean.getName())
                .identifier(categoryDetailBean.getIdentifier())
                .createdTime(categoryDetailBean.getCreatedTime())
                .updatedTime(categoryDetailBean.getUpdatedTime())
                .lastModifiedBy(categoryDetailBean.getUserDetailsId())
                .status(categoryDetailBean.getStatus())
                .workload(categoryDetailBean.getIsWorkLoad())
                .informative(categoryDetailBean.getIsInformative())
                .custom(categoryDetailBean.getIsCustom())
                .description(categoryDetailBean.getDescription())
                .build();

        existingCategoryDetails.add(newCategory);
        categoryRepo.updateCategoryDetails(categoryDetailBean.getAccountIdentifier(),existingCategoryDetails);
        categoryRepo.updateCategory(categoryDetailBean.getAccountIdentifier(), newCategory);

    }
}

