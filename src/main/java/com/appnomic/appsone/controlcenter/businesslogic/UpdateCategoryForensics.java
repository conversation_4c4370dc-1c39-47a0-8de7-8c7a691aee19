package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ComponentInstanceBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ActionScriptDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CategoryForensicsDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CommandDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.Actions;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandDetailArgumentBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompInstanceForensicCategoryBean;
import com.appnomic.appsone.controlcenter.dao.redis.CategoryRepo;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.dao.redis.MasterDataRepo;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.CategoryForensicDetails;
import com.appnomic.appsone.controlcenter.pojo.CategoryForensicsPOJO;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import com.heal.configuration.entities.ActionBean;
import com.heal.configuration.entities.CommandArgumentsBean;
import com.heal.configuration.entities.ScriptDetailsBean;
import com.heal.configuration.pojos.Action;
import com.heal.configuration.pojos.Category;
import com.heal.configuration.pojos.CompInstClusterDetails;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.google.common.collect.MoreCollectors.onlyElement;

public class UpdateCategoryForensics implements BusinessLogic<CategoryForensicsPOJO, CategoryForensicsPOJO, String> {

    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateCategoryForensics.class);
    CategoryForensicsDataService dataService = new CategoryForensicsDataService();
    InstanceRepo instanceRepo = new InstanceRepo();
    CategoryRepo categoriesRepo = new CategoryRepo();

    @Override
    public UtilityBean<CategoryForensicsPOJO> clientValidation(RequestObject request) throws ClientException {
        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }

        String requestBody = request.getBody();
        if (StringUtils.isEmpty(requestBody)) {
            LOGGER.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        CategoryForensicsPOJO categoryForensicsPOJO;
        try {
            categoryForensicsPOJO = CommonUtils.getObjectMapperWithHtmlEncoder()
                    .readValue(requestBody, new TypeReference<CategoryForensicsPOJO>() {
                    });
        } catch (IOException e) {
            LOGGER.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        if (!categoryForensicsPOJO.validate()) {
            LOGGER.error("Request validation failure.");
            throw new ClientException(UIMessages.INVALID_REQUEST);
        }

        return UtilityBean.<CategoryForensicsPOJO>builder()
                .accountIdentifier(identifier)
                .pojoObject(categoryForensicsPOJO)
                .authToken(authToken)
                .build();
    }

    @Override
    public CategoryForensicsPOJO serverValidation(UtilityBean<CategoryForensicsPOJO> utilityBean) throws ServerException {

        UserAccountBean userAccBean;
        try {
            userAccBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            throw new ServerException(e.getMessage());
        }

        int accountId = userAccBean.getAccount().getId();
        List<ComponentInstanceBean> instances = new ArrayList<>();
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();

        CategoryForensicsPOJO request = utilityBean.getPojoObject();

        Set<Integer> instanceIds = new HashSet<>(request.getInstanceIds());
        for (int instanceId : instanceIds) {
            ComponentInstanceBean bean = compInstanceDataService.getComponentInstanceByIdAndAccount(instanceId, accountId);
            if (bean == null) {
                throw new ServerException("Invalid instance Id : " + instanceId);
            }
            instances.add(bean);
        }

        Set<Integer> componentIds = instances.parallelStream().map(ComponentInstanceBean::getMstComponentId)
                .collect(Collectors.toSet());
        if (componentIds.size() != 1) {
            LOGGER.error("InstanceIds specified are mapped to different components.");
            throw new ServerException("InstanceIds specified are mapped to different components.");
        }

        request.setUserId(userAccBean.getUserId());
        request.setAccountId(accountId);
        request.setComponentId(new ArrayList<>(componentIds).get(0));
        request.setAccountIdentifier(utilityBean.getAccountIdentifier());

        return request;
    }

    @Override
    public String process(CategoryForensicsPOJO bean) throws DataProcessingException {
        try {
            return MySQLConnectionManager.getInstance().getHandle().inTransaction((conn, status) ->
                    updateActionCategoryInstanceMappingDetails(bean, conn));
        } catch (Exception e) {
            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw (DataProcessingException) Throwables.getRootCause(e);
            } else {
                throw e;
            }
        }
    }

    public String updateActionCategoryInstanceMappingDetails(CategoryForensicsPOJO request, Handle handle) throws ControlCenterException {

        String userId = request.getUserId();
        String timestamp = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
        List<Integer> instanceIds = request.getInstanceIds();
        List<CompInstanceForensicCategoryBean> compInstanceForensicCategoryDetailsToAdd = new ArrayList<>();
        List<CompInstanceForensicCategoryBean> compInstanceForensicCategoryDetailsToUpdate = new ArrayList<>();

        for (CategoryForensicDetails mapping : request.getCategoryForensicDetails()) {

            for (int instanceId : instanceIds) {
                CompInstanceForensicCategoryBean bean = dataService.getCompInstanceForensicCategoryDetailsForInstanceId
                        (instanceId, mapping.getCategoryId(), mapping.getForensicId(), null);

                if (bean == null) {
                    CompInstanceForensicCategoryBean actionCategoryDetails = dataService.getActionCategoryMappingDetails
                            (mapping.getCategoryId(), mapping.getForensicId(), null);
                    if (actionCategoryDetails == null) {
                        LOGGER.error("Unable to fetch action-category mapping details.");
                        throw new ControlCenterException("Unable to fetch action-category mapping details.");
                    }

                    compInstanceForensicCategoryDetailsToAdd.add(CompInstanceForensicCategoryBean.builder()
                            .compInstanceId(instanceId)
                            .categoryId(mapping.getCategoryId())
                            .actionId(mapping.getForensicId())
                            .shouldTrigger(mapping.getTriggerForensicStatus())
                            .timeWinInSec(actionCategoryDetails.getTimeWinInSec())
                            .actionExecTypeId(actionCategoryDetails.getActionExecTypeId())
                            .downloadTypeId(actionCategoryDetails.getDownloadTypeId())
                            .retries(actionCategoryDetails.getRetries())
                            .ttlInSec(actionCategoryDetails.getTtlInSec())
                            .commandExecTypeId(actionCategoryDetails.getCommandExecTypeId())
                            .suppressionInterval(mapping.getSuppressionInterval())
                            .objectId(actionCategoryDetails.getObjectId())
                            .objectRefTable(actionCategoryDetails.getObjectRefTable())
                            .userDetailsId(userId)
                            .createdTime(timestamp)
                            .updatedTime(timestamp)
                            .accountId(request.getAccountId())
                            .accountIdentifier(request.getAccountIdentifier())
                            .build());
                } else {
                    bean.setShouldTrigger(mapping.getTriggerForensicStatus());
                    bean.setSuppressionInterval(mapping.getSuppressionInterval());
                    bean.setUserDetailsId(userId);
                    bean.setAccountId(request.getAccountId());
                    bean.setUpdatedTime(timestamp);
                    compInstanceForensicCategoryDetailsToUpdate.add(bean);
                    bean.setAccountIdentifier(request.getAccountIdentifier());
                }
            }
        }

        if (!compInstanceForensicCategoryDetailsToAdd.isEmpty()) {
            dataService.addCompInstanceForensicCategoryDetails(compInstanceForensicCategoryDetailsToAdd, handle);
            //adding forensic details into the redis cache
            addCompInstanceForensicCategoryDetails(compInstanceForensicCategoryDetailsToAdd);

        }
        if (!compInstanceForensicCategoryDetailsToUpdate.isEmpty()) {
            dataService.updateCompInstanceForensicCategoryDetails(compInstanceForensicCategoryDetailsToUpdate, handle);
            //updating forensic details into the redis cache.
            updateCompInstanceForensicCategoryDetails(timestamp, compInstanceForensicCategoryDetailsToUpdate);
        }
        return "Forensic Category Instance mapping details updated successfully.";
    }

    private String updateCompInstanceForensicCategoryDetails(String timestamp, List<CompInstanceForensicCategoryBean> updateCompInstanceForensicCategoryDetails) {
        for (CompInstanceForensicCategoryBean forensicCategoryBean : updateCompInstanceForensicCategoryDetails)
        {
            CompInstClusterDetails instClusterDetail = instanceRepo.getInstances(forensicCategoryBean.getAccountIdentifier()).parallelStream().filter(f -> f.getId() == forensicCategoryBean.getCompInstanceId()).findAny().orElse(null);
            if(instClusterDetail == null) {
                LOGGER.error("Could not find the instance details from redis cache. AccountId:{}, instanceId:{}", forensicCategoryBean.getAccountIdentifier(), forensicCategoryBean.getCompInstanceId());
                return "";
            }
            String instanceIdentifier = instClusterDetail.getIdentifier();

            Category categoryDetail = categoriesRepo.getCategoryDetails(forensicCategoryBean.getAccountIdentifier()).parallelStream().filter(m -> m.getId() == forensicCategoryBean.getCategoryId()).findAny().orElse(null);
            if(categoryDetail == null) {
                LOGGER.error("Could not find the category detail from redis cache. AccountId:{}, instanceId:{}", forensicCategoryBean.getAccountIdentifier(), forensicCategoryBean.getCompInstanceId());
                return "";
            }
            String categoryIdentifier = categoryDetail.getIdentifier();

            List<Action> categoryForensicDetails = instanceRepo.getInstanceCategoriesForensicDetails(forensicCategoryBean.getAccountIdentifier(), instanceIdentifier, categoryIdentifier);
            if(categoryForensicDetails.parallelStream().noneMatch(m -> m.getId() == forensicCategoryBean.getActionId() )) {
              LOGGER.error("The forensic details are not found for the given category [{}] and action Id [{}]",categoryIdentifier, forensicCategoryBean.getActionId());
              return "";
            }
            updateForensicDetail(timestamp, forensicCategoryBean, instanceIdentifier, categoryIdentifier, categoryForensicDetails);
        }
        return "";
    }

    private String addCompInstanceForensicCategoryDetails(List<CompInstanceForensicCategoryBean> addCompInstanceForensicCategoryDetails) {
        for (CompInstanceForensicCategoryBean forensicCategoryBean : addCompInstanceForensicCategoryDetails)
        {
            CompInstClusterDetails instClusterDetail = instanceRepo.getInstances(forensicCategoryBean.getAccountIdentifier())
                    .parallelStream()
                    .filter(f -> f.getId() == forensicCategoryBean.getCompInstanceId())
                    .findAny()
                    .orElse(null);

            if(instClusterDetail == null) {
                LOGGER.error("Could not find the instance details from redis cache. AccountId:{}, instanceId:{}", forensicCategoryBean.getAccountIdentifier(), forensicCategoryBean.getCompInstanceId());
                return "";
            }

            Category categoryDetail = categoriesRepo.getCategoryDetails(forensicCategoryBean.getAccountIdentifier()).parallelStream().filter(m -> m.getId() == forensicCategoryBean.getCategoryId()).findAny().orElse(null);
            if(categoryDetail == null) {
                LOGGER.error("Could not find the category details from redis cache. AccountId:{}, instanceId:{}", forensicCategoryBean.getAccountIdentifier(), forensicCategoryBean.getCompInstanceId());
                return "";
            }
            List<Action> forensicDetails = instanceRepo.getInstanceCategoriesForensicDetails(forensicCategoryBean.getAccountIdentifier(), instClusterDetail.getIdentifier(), categoryDetail.getIdentifier());
            if(!forensicDetails.isEmpty() && forensicDetails.parallelStream().anyMatch(m -> m.getId()==forensicCategoryBean.getActionId()) )
            {
                LOGGER.debug("The forensic details for given instance: [{}] and category: [{}] already exists", instClusterDetail.getIdentifier(), forensicCategoryBean.getActionId());
                String timestamp = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
                updateForensicDetail(timestamp, forensicCategoryBean, instClusterDetail.getIdentifier(), forensicCategoryBean.getAccountIdentifier(), forensicDetails );
            }
            addForensicDetail(forensicCategoryBean, forensicCategoryBean.getAccountIdentifier(), instClusterDetail.getIdentifier(), categoryDetail.getIdentifier(), forensicDetails);

        }
        return "";
    }

    private void updateForensicDetail(String timestamp, CompInstanceForensicCategoryBean forensicCategoryBean, String instanceIdentifier, String categoryIdentifier,  List<Action> forensicDetails) {
        String accountIdentifier = forensicCategoryBean.getAccountIdentifier();
        forensicDetails.forEach(bean->{
            if(bean.getId() == forensicCategoryBean.getActionId()){
                bean.setSuppressionInterval(forensicCategoryBean.getSuppressionInterval());
                bean.setStatus(forensicCategoryBean.getShouldTrigger());
                bean.setUpdatedTime(timestamp);
                bean.setLastModifiedBy(forensicCategoryBean.getUserDetailsId());
            }
        });
        instanceRepo.updateForensicDetails(accountIdentifier, instanceIdentifier, categoryIdentifier, forensicDetails);
    }

    private void addForensicDetail(CompInstanceForensicCategoryBean forensicCategoryBean, String accountIdentifier, String instanceIdentifier, String categoriesIdentifier, List<Action> forensicDetails) {
        List<Actions> actions = ActionScriptDataService.getActionScriptDetails();
        List<com.heal.configuration.pojos.ViewTypes> viewTypes = new MasterDataRepo().getTypes();
        Actions actionDetails = actions.parallelStream().filter(a ->a.getId()== forensicCategoryBean.getActionId()).collect(onlyElement());
        CommandDetailsBean commandDetailsBean = CommandDataService.getCommandDetail(forensicCategoryBean.getObjectId(), null);
        ScriptDetailsBean scriptDetails = ScriptDetailsBean.builder()
                .id(commandDetailsBean.getId())
                .name(commandDetailsBean.getName())
                .identifier(commandDetailsBean.getIdentifier())
                .scriptName(commandDetailsBean.getCommandName())
                .timeoutInSecs(commandDetailsBean.getTimeOutInSecs())
                .outputTypeId(commandDetailsBean.getOutputTypeId())
                .outputType(viewTypes.parallelStream().filter(a -> a.getSubTypeId() == commandDetailsBean.getOutputTypeId()).collect(onlyElement()).getSubTypeName())
                .commandTypeId(commandDetailsBean.getCommandTypeId())
                .commandType(viewTypes.parallelStream().filter(a -> a.getSubTypeId() == commandDetailsBean.getOutputTypeId()).collect(onlyElement()).getTypeName())
                .producerTypeId(commandDetailsBean.getProducerTypeId())
                .producerType(viewTypes.parallelStream().filter(a -> a.getSubTypeId() == commandDetailsBean.getProducerTypeId()).collect(onlyElement()).getSubTypeName())
                .createdTime(commandDetailsBean.getCreatedTime().toString())
                .updatedTime(commandDetailsBean.getUpdatedTime().toString())
                .lastModifiedBy(commandDetailsBean.getUserDetails())
                .build();
        if (scriptDetails == null) {
            LOGGER.debug("The command script details not found for the object: [{}]", forensicCategoryBean.getObjectId());
        }
        List<CommandDetailArgumentBean> arguments = CommandDataService.getCommandArguments(forensicCategoryBean.getObjectId());
        List<CommandArgumentsBean> newArguments = new ArrayList<>();
        for ( CommandDetailArgumentBean bean : arguments)
        {
            CommandArgumentsBean buildArguments = CommandArgumentsBean.builder()
                    .id(bean.getId())
                    .key(bean.getArgumentKey())
                    .value(bean.getArgumentValue())
                    .defaultValue(bean.getDefaultValue())
                    .valueType(Integer.toString(bean.getArgumentValueTypeId()))
                    .build();

            newArguments.add(buildArguments);
        }

        if (!newArguments.isEmpty()) {
            scriptDetails.setArguments(newArguments);
        }
        ActionBean newForensic = ActionBean.builder()
                .id(forensicCategoryBean.getActionId())
                .name(actionDetails.getName())
                .identifier(actionDetails.getIdentifier())
                .status(forensicCategoryBean.getShouldTrigger())
                .standardTypeId(actionDetails.getStandardType())
                .standardType( viewTypes.parallelStream().filter(a -> a.getSubTypeId() == actionDetails.getStandardType()).collect(onlyElement()).getSubTypeName())
                .agentTypeId(actionDetails.getAgentType())
                .agentType(viewTypes.parallelStream().filter(a -> a.getSubTypeId() == actionDetails.getAgentType()).collect(onlyElement()).getSubTypeName())
                .actionTypeId(actionDetails.getActionTypeId())
                .actionType(viewTypes.parallelStream().filter(a -> a.getSubTypeId() == actionDetails.getActionTypeId()).collect(onlyElement()).getSubTypeName())
                .actionExecutionTypeId(forensicCategoryBean.getActionExecTypeId())
                .actionExecutionType(viewTypes.parallelStream().filter(a -> a.getSubTypeId() == forensicCategoryBean.getActionExecTypeId()).collect(onlyElement()).getSubTypeName())
                .downloadTypeId(forensicCategoryBean.getDownloadTypeId())
                .downloadType(viewTypes.parallelStream().filter(a -> a.getSubTypeId() == forensicCategoryBean.getDownloadTypeId()).collect(onlyElement()).getSubTypeName())
                .commandExecutionTypeId(forensicCategoryBean.getCommandExecTypeId())
                .commandExecutionType(viewTypes.parallelStream().filter(a -> a.getSubTypeId() == forensicCategoryBean.getCommandExecTypeId()).collect(onlyElement()).getSubTypeName())
                .suppressionInterval(forensicCategoryBean.getSuppressionInterval())
                .categoryIdentifier(categoriesIdentifier)
                .commandId(forensicCategoryBean.getObjectId())
                .scriptDetailsBean(scriptDetails)
                .retries(forensicCategoryBean.getRetries())
                .supCtrlTtlInSecs(forensicCategoryBean.getTtlInSec())
                .createdTime(forensicCategoryBean.getCreatedTime())
                .updatedTime(forensicCategoryBean.getUpdatedTime())
                .lastModifiedBy(forensicCategoryBean.getUserDetailsId())
                .build();
        Action newForensicDetails = newForensic.mapToAction();
        List<Action> newList = new ArrayList<>();
        newList.add(newForensicDetails);
        newList.addAll(forensicDetails);
        instanceRepo.updateForensicDetails(accountIdentifier, instanceIdentifier, categoriesIdentifier, newList);
    }
}
