package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AccountDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.AccountRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.pojos.Account;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class GetActiveLicenseHostsBL implements BusinessLogic<List<String>, List<AccountBean>, Map<String, Object>> {

    @Override
    public UtilityBean<List<String>> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }
        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            log.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }

        List<String> a1AccountIdentifier = Arrays.stream(ConfProperties.getString(Constants.A1_HEALTH_ACCOUNT)
                .trim().split(",")).collect(Collectors.toList());

        a1AccountIdentifier = a1AccountIdentifier.stream().distinct().filter(c-> !StringUtils.isEmpty(c)).collect(Collectors.toList());
        return UtilityBean.<List<String>>builder()
                .authToken(authToken)
                .pojoObject(a1AccountIdentifier)
                .build();
    }

    @Override
    public List<AccountBean> serverValidation(UtilityBean<List<String>> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        List<AccountBean> a1AccDetails = new ArrayList<>();
        if (utilityBean.getPojoObject().size() > 0) {
            for (String accIdentifier : utilityBean.getPojoObject()) {
                AccountBean accountBean = AccountDataService.getAccountDetailsByNameOrIdentifier(accIdentifier, null);
                if (accountBean == null) {
                    log.warn("No account exist with identifier [{}].", accIdentifier);
                } else {
                    a1AccDetails.add(accountBean);
                }
            }
        }
        return a1AccDetails;
    }

    @Override
    public Map<String, Object> process(List<AccountBean> bean) throws DataProcessingException {

        List<Account> allAccounts = new AccountRepo().getAccounts().parallelStream()
                .filter(account -> bean.stream().noneMatch(b -> b.getId() == account.getId()))
                .collect(Collectors.toList());

        Map<String, Object> data = new HashMap<>();
        int count = 0;
        for (Account acc : allAccounts) {
            count += new CompInstanceDataService().activeLicenseHostsCount(acc.getId(), 1, 1, 0, null);
        }
        data.put("hosts", count);
        return data;
    }
}
