package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.RulesDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.TagsDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.RulesDao;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.TagsDao;
import com.appnomic.appsone.controlcenter.dao.redis.MasterDataRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.AddRulesPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.TagMappingDetails;
import com.appnomic.appsone.controlcenter.pojo.TagRequestPojo;
import com.appnomic.appsone.controlcenter.service.KeyCloakAuthService;
import com.appnomic.appsone.controlcenter.service.TagsService;
import com.appnomic.appsone.controlcenter.util.*;
import com.appnomic.appsone.model.JWTData;
import com.heal.configuration.pojos.PairData;
import com.heal.configuration.pojos.RegexTypeDetail;
import com.heal.configuration.pojos.RequestTypeDetail;
import com.heal.configuration.pojos.Rule;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.appnomic.appsone.controlcenter.common.Constants.RULES_TABLE;

@Slf4j
public class AddRulesBL implements BusinessLogic<AddRulesPojo, UtilityBean<AddRulesPojo>, Integer> {

    private static final Integer maxTagsAllowed = ConfProperties.getInt(Constants.MAX_TAGS_ALLOWED, Constants.MAX_TAGS);
    public static MasterDataRepo masterDataRepo = new MasterDataRepo();

    @Override
    public UtilityBean<AddRulesPojo> clientValidation(RequestObject requestObject) throws ClientException {

        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if(requestObject.getBody() == null) {
            log.error(UIMessages.REQUEST_BODY_NULL_EMPTY);
            throw new ClientException(UIMessages.REQUEST_BODY_NULL_EMPTY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String accountIdentifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (accountIdentifier == null || StringUtils.isEmpty(accountIdentifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String serviceIdStr = requestObject.getParams().get(Constants.SERVICE_ID.toLowerCase());
        if (serviceIdStr == null || StringUtils.isEmpty(serviceIdStr)) {
            log.error(UIMessages.EMPTY_SERVICE_IDENTIFIER);
            throw new ClientException(UIMessages.EMPTY_SERVICE_IDENTIFIER);
        }

        try {
            Integer.parseInt(serviceIdStr);
        } catch (NumberFormatException e) {
            log.error("Service ID {} provided is invalid. It is not a valid integer", serviceIdStr);
            throw new ClientException("Invalid service ID");
        }

        AddRulesPojo addRule;

        try {
            addRule = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestObject.getBody(), AddRulesPojo.class);
            addRule.validate();

            if (!addRule.getError().isEmpty()) {
                log.error(addRule.getError().toString());
                throw new ClientException(addRule.getError().toString());
            }
        } catch (Exception e) {
            log.error(Constants.JSON_PARSE_ERROR, e);
            throw new ClientException(Constants.JSON_PARSE_ERROR);
        }

        return UtilityBean.<AddRulesPojo>builder()
                .accountIdentifier(accountIdentifier)
                .serviceId(serviceIdStr)
                .authToken(authKey)
                .pojoObject(addRule)
                .build();
    }

    @Override
    public UtilityBean<AddRulesPojo> serverValidation(UtilityBean<AddRulesPojo> utilityBean) throws ServerException {
        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error(UIMessages.INVALID_ACCOUNT_MESSAGE, utilityBean.getAccountIdentifier());
            throw new ServerException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }

        String userId;

        try {
            JWTData jwtData = KeyCloakAuthService.extractUserDetails(utilityBean.getAuthToken());
            userId = jwtData.getSub();
        } catch (ControlCenterException e) {
            throw new ServerException(e.getMessage());
        }

        ControllerBean serviceDetails = new ControllerDataService().getControllerById(Integer.parseInt(utilityBean.getServiceId()), account.getId(), null);
        if (serviceDetails == null) {
            log.error(UIMessages.INVALID_SERVICE, utilityBean.getServiceId());
            throw new ServerException(UIMessages.INVALID_SERVICE);
        }

        utilityBean.setServiceIdentifier(serviceDetails.getIdentifier());
        utilityBean.setUserId(userId);
        utilityBean.setAccount(account);

        return utilityBean;
    }

    @Override
    public Integer process(UtilityBean<AddRulesPojo> bean) throws DataProcessingException {

        AddRulesPojo addRule = bean.getPojoObject();
        String serviceId = bean.getServiceId();
        AccountBean account = bean.getAccount();

        TagDetailsBean tagDetailsBean = MasterCache.getTagDetails(Constants.CONTROLLER_TAG);
        if (tagDetailsBean == null) {
            log.error("Tag detail is not found for given tag name" + Constants.CONTROLLER_TAG + " and account id:" + account.getId());
            throw new DataProcessingException("Tag details is not found for tag name" + Constants.CONTROLLER_TAG);
        }

        ViewTypes ruleType = MasterCache.getMstTypeForSubTypeName(Constants.RULES_TYPE, addRule.getRuleType());
        if (ruleType == null) {
            log.error("No any sub type is found for given rule type." + addRule.getRuleType());
            throw new DataProcessingException("No any sub type is found for given rule type. " + addRule.getRuleType());
        }

        ViewTypes httpMethodType = MasterCache.getMstTypeForSubTypeName(Constants.HTTP_METHOD_TYPE, addRule.getHttpMethodType());
        if (httpMethodType == null) {
            log.error(" No any sub type is found for given http method type. " + addRule.getHttpMethodType());
            throw new DataProcessingException(" No any sub type is found for given http method type. " + addRule.getHttpMethodType());
        }

        ViewTypes payLoadType = MasterCache.getMstTypeForSubTypeName(Constants.PAY_LOAD_TYPE, addRule.getPayloadType());
        if (payLoadType == null) {
            log.error(" No any sub type is found for given payload type. " + addRule.getPayloadType());
            throw new DataProcessingException(" No any sub type is found for given payload type. " + addRule.getPayloadType());
        }

        int ruleName = ValidationUtils.getRuleName(account.getId(), Integer.parseInt(serviceId), tagDetailsBean.getId(), addRule.getRuleName().trim());
        if (ruleName > 0) {
            log.error("Rule name -" + addRule.getRuleName() + "  already exists");
            throw new DataProcessingException("Rule name -" + addRule.getRuleName() + "  already exists");
        }


        List<AddRulesPojo> ruleUniquePatterns = ValidationUtils.getCompletePattern(Constants.RULES_TABLE, account.getId());
        AddRulesPojo existsRule = ruleUniquePatterns.stream()
                .filter(addRulesPojo -> addRulesPojo.getCompletePattern().equalsIgnoreCase(addRule.getUniquePatter()) && addRulesPojo.getServiceId().equals(serviceId))
                .findAny()
                .orElse(null);
        if (existsRule != null) {
            log.error("Pattern already exists with rule name as " + existsRule.getRuleName());
            throw new DataProcessingException("Pattern already exists with rule name as " + existsRule.getRuleName());
        }

        addRule.setCompletePattern(addRule.getUniquePatter());
        addRule.setRuleType(String.valueOf(ruleType.getSubTypeId()));
        addRule.setHttpMethodType(String.valueOf(httpMethodType.getSubTypeId()));
        addRule.setPayloadType(String.valueOf(payLoadType.getSubTypeId()));
        addRule.setIsDefault(0);
        addRule.setServiceId(serviceId);
        addRule.setUserDetails(bean.getUserId());
        addRule.setAccountId(account.getId());
        addRule.setUserDetailsId(account.getUserIdDetails());
        if (bean.getPojoObject().getMonitorEnabled() != null) {
            addRule.setMonitorEnabled(bean.getPojoObject().getMonitorEnabled());
        } else {
            addRule.setMonitorEnabled(Integer.valueOf(Constants.RULES_MONITORING_ENABLED_DEFAULT));
        }
        if (bean.getPojoObject().getDiscoveryEnabled() != null) {
            addRule.setDiscoveryEnabled(bean.getPojoObject().getDiscoveryEnabled());
        } else {
            addRule.setDiscoveryEnabled(Integer.valueOf(Constants.RULES_DISCOVERY_ENABLED_DEFAULT));
        }


        if (!addRule.getError().isEmpty()) {
            log.error(addRule.getError().toString() + " Status code = " + Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            throw new DataProcessingException(addRule.getError().toString() + " Status code = " + Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
        }

        log.debug("addRule: {}", addRule);
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();

        Integer ruleId = dbi.inTransaction((handle, status) -> addRulesDetails(account, addRule, bean.getServiceIdentifier(), handle));

        if (ruleId == null) {
            log.error("Unable to insert data into rules table");
            throw new DataProcessingException("Unable to insert data into rules table");
        }
        return ruleId;
    }

    private int addRulesDetails(AccountBean account, AddRulesPojo addRule, String serviceIdentifier, Handle handle) throws DataProcessingException {
        TagsDao tagsDao = handle.attach(TagsDao.class);
        RulesDao rulesDao = handle.attach(RulesDao.class);

        try {
            TagDetailsBean controllerTag = MasterCache.getTagDetails(Constants.CONTROLLER_TAG);

            RulesBean rulesBean = new RulesBean();
            Timestamp date = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
            rulesBean.setCreatedTime(date);
            rulesBean.setUpdatedTime(date);
            rulesBean.setAccountId(addRule.getAccountId());
            rulesBean.setName(addRule.getRuleName());
            rulesBean.setRuleTypeId(Integer.parseInt(addRule.getRuleType()));
            rulesBean.setMonitorEnabled(addRule.getMonitorEnabled());
            rulesBean.setDiscoveryEnabled(addRule.getDiscoveryEnabled());
            rulesBean.setIsDefault(addRule.getIsDefault());
            rulesBean.setUserDetails(addRule.getUserDetails());


            if (controllerTag == null) {
                log.error("No Tag details found");
                throw new DataProcessingException("No Tag details found");
            }
            List<RuleDetailsBean> ruleDetailsBeans = RulesDataService.getRuleList(Integer.parseInt(addRule.getServiceId()), controllerTag.getId(), addRule.getAccountId());
            if (ruleDetailsBeans != null && !ruleDetailsBeans.isEmpty()) {
                rulesBean.setOrder(ruleDetailsBeans.size() + 1);
            } else {
                rulesBean.setOrder(1);
            }

            int ruleId = rulesDao.addRulesDetails(rulesBean);

            if (ruleId <= 0) {
                log.error("Rules value not updated in 'Rules' table");
                throw new DataProcessingException("Rules value not updated in 'Rules' table");
            }

            log.info(" Rules value is successfully inserted into 'Rules' table");

            RequestTypeDetailBean requestTypeDetailBean = new RequestTypeDetailBean();
            requestTypeDetailBean.setCreatedTime(date);
            requestTypeDetailBean.setUpdatedTime(date);
            requestTypeDetailBean.setAccountId(addRule.getAccountId());
            requestTypeDetailBean.setUserDetails(addRule.getUserDetails());
            requestTypeDetailBean.setRuleId(ruleId);
            requestTypeDetailBean.setHttpMethodType(Integer.parseInt(addRule.getHttpMethodType()));

            switch (addRule.getSegmentURIType().toLowerCase()) {
                case "first":
                    requestTypeDetailBean.setFirstUriSegments(Integer.parseInt(addRule.getSegmentValue()));
                    break;
                case "last":
                    requestTypeDetailBean.setLastUriSegments(Integer.parseInt(addRule.getSegmentValue()));
                    break;
                case "complete":
                    requestTypeDetailBean.setCompleteURI(Boolean.parseBoolean(addRule.getSegmentValue()));
                    break;
                case "custom":
                    requestTypeDetailBean.setCustomSegments(addRule.getSegmentValue());
                    break;
            }

            requestTypeDetailBean.setCompletePattern(addRule.getCompletePattern());
            requestTypeDetailBean.setPayloadType(Integer.parseInt(addRule.getPayloadType()));

            int httpPatternId = rulesDao.addSegmentsDetails(requestTypeDetailBean);

            if (httpPatternId <= 0) {
                log.error("Segments value not inserted in 'http_patterns' table");
                throw new DataProcessingException("Segments value not inserted in 'http_patterns' table");
            }

            log.info("Segments value is successfully inserted into 'http_patterns' table");
            TagsService tagsService = new TagsService();

            TagMappingDetails tagMappingDetails = tagsService.createTagMappingDetailsObj(controllerTag.getId(), ruleId, Constants.RULES_TABLE,
                    addRule.getServiceId(), serviceIdentifier, addRule.getUserDetails(), addRule.getAccountId());
            int tagId = TagsDataService.addTagMappingDetails(tagMappingDetails, tagsDao);


            if (tagId <= 0) {
                log.error("Tag details not inserted in 'tag_mapping' table");
                throw new DataProcessingException("Tag details not inserted in 'tag_mapping' table");
            }

            List<PairDataBean> pairDataBeanList = new ArrayList<>();

            Map<String, String> queryList = addRule.getQueryParameters();
            if (queryList != null && !queryList.isEmpty()) {
                pairDataBeanList.addAll(getPairDataBeans(ruleId, httpPatternId, Constants.QUERY_PARAM, date, queryList, addRule));
            }
            Map<String, String> payloadParameters = addRule.getPayloadParameters();
            if (payloadParameters != null && !payloadParameters.isEmpty()) {
                pairDataBeanList.addAll(getPairDataBeans(ruleId, httpPatternId, Constants.PAY_LOAD_TYPE, date, payloadParameters, addRule));
            }
            Map<String, String> httpHeaderParameters = addRule.getHttpHeaderParameters();
            if (httpHeaderParameters != null && !httpHeaderParameters.isEmpty()) {
                pairDataBeanList.addAll(getPairDataBeans(ruleId, httpPatternId, Constants.HTTP_HEADER_TYPE, date, httpHeaderParameters, addRule));
            }

            if (!pairDataBeanList.isEmpty()) {
                int[] ids = rulesDao.addHttpPatternsDetails(pairDataBeanList);

                if (ids.length == 0) {
                    log.error("Pair details not inserted into 'http_pair_data' table ");
                    throw new DataProcessingException("Pair details not inserted into 'http_pair_data' table ");
                }
                List<Integer> newlyAddedPairDataIds = Arrays.stream(ids)
                        .boxed()
                        .collect(Collectors.toList());

                List<PairDataBean> httpPairDataDetails = rulesDao.getHttpPairDataDetails(newlyAddedPairDataIds);

                for (PairDataBean pairDataBean : pairDataBeanList) {
                    for (PairDataBean httpPairDataDetail : httpPairDataDetails) {
                        if (pairDataBean.getPairTypeId() == httpPairDataDetail.getPairTypeId() && pairDataBean.getParamKey().equalsIgnoreCase(httpPairDataDetail.getParamKey())) {
                            pairDataBean.setId(httpPairDataDetail.getId());
                            break;
                        }
                    }
                }
            }
            if (addRule.getAddDiscoveryTags().size() > maxTagsAllowed) {
                throw new ControlCenterException("Maximum " + maxTagsAllowed + " tags can be added.");
            } else {
                TagRequestPojo tagRequest = new TagRequestPojo();
                tagRequest.getObjectIds().add(ruleId);
                tagRequest.setAddDiscoveryTags(addRule.getAddDiscoveryTags());
                tagRequest.setServiceId(addRule.getServiceId());
                tagRequest.validate();
                TagRequestDetailsBL.removeAddTagValidation(account.getId(), tagRequest, RULES_TABLE);
                TagRequestDetailsBL.discoveryTagsBL(account, tagRequest, RULES_TABLE, handle);

                addNewRuleInRedisCache(account, serviceIdentifier, addRule, rulesBean, requestTypeDetailBean, pairDataBeanList);
            }
            return ruleId;
        } catch (Exception e) {
            log.error("Exception encountered while adding rules.", e);
            throw new DataProcessingException("Error while adding rules");
        }
    }

    private List<PairDataBean> getPairDataBeans(int ruleId, int httpPatternId, String pairSubType, Timestamp date, Map<String, String> queryList, AddRulesPojo addRule)
            throws DataProcessingException {
        ViewTypes subType = MasterCache.getMstTypeForSubTypeName(Constants.PAIR_TYPE, pairSubType);

        if (subType == null) {
            throw new DataProcessingException("Unable to fetch subType information for provided tag value for " + pairSubType);
        }

        List<PairDataBean> pairDataBeanList = new ArrayList<>();

        queryList.forEach((key, value) -> {
            PairDataBean pairDataBean = new PairDataBean();
            pairDataBean.setParamKey(key);
            pairDataBean.setParamValue(value);
            pairDataBean.setCreatedTime(date);
            pairDataBean.setUpdatedTime(date);
            pairDataBean.setAccountId(addRule.getAccountId());
            pairDataBean.setUserDetails(addRule.getUserDetails());
            pairDataBean.setRuleId(ruleId);
            pairDataBean.setHttpPatternId(httpPatternId);
            pairDataBean.setPairTypeId(subType.getSubTypeId());
            pairDataBean.setPairTypeName(subType.getSubTypeName());
            pairDataBeanList.add(pairDataBean);
        });

        return pairDataBeanList;
    }

    public void addNewRuleInRedisCache(AccountBean account, String serviceIdentifier, AddRulesPojo addRule, RulesBean rulesBean,
                                       RequestTypeDetailBean requestTypeDetailBean, List<PairDataBean> pairDataBeanList) {

        ServiceRepo serviceRepo = new ServiceRepo();
        RequestTypeDetail requestTypeDetail = new RequestTypeDetail();
        RegexTypeDetail regexTypeDetail = new RegexTypeDetail();
        List<PairData> pairDataList = new ArrayList<>();

        try {

            List<com.heal.configuration.pojos.ViewTypes> types = masterDataRepo.getTypes();
            com.heal.configuration.pojos.ViewTypes viewTypesForRule = types.parallelStream().filter(f -> f.getSubTypeId() == rulesBean.getRuleTypeId()).findAny().orElse(null);
            if (viewTypesForRule == null) {
                log.error("Could not find view types for typeId [{}] and ruleId [{}]", rulesBean.getRuleTypeId(), requestTypeDetailBean.getRuleId());
                return;
            }
            com.heal.configuration.pojos.ViewTypes viewTypesForPayload = types.parallelStream().filter(f -> f.getSubTypeId() == requestTypeDetailBean.getPayloadType()).findAny().orElse(null);
            if (viewTypesForPayload == null) {
                log.error("Could not find view types for typeId [{}] and ruleId [{}]", requestTypeDetailBean.getPayloadType(), requestTypeDetailBean.getRuleId());
                return;
            }

            //For time being this object is set to default values in future it will be handled.
            regexTypeDetail.setId(0);
            regexTypeDetail.setLength(0);
            regexTypeDetail.setInitialPattern(null);
            regexTypeDetail.setEndPattern(null);

            requestTypeDetail.setId(requestTypeDetailBean.getRuleId());
            requestTypeDetail.setPayloadTypeId(requestTypeDetailBean.getPayloadType());
            requestTypeDetail.setPayloadTypeName(viewTypesForPayload.getSubTypeName());
            requestTypeDetail.setCustomSegments(requestTypeDetailBean.getCustomSegments());
            requestTypeDetail.setFirstUriSegments(requestTypeDetailBean.getFirstUriSegments());
            requestTypeDetail.setLastUriSegments(requestTypeDetailBean.getLastUriSegments());
            requestTypeDetail.setCompleteURI(requestTypeDetailBean.isCompleteURI());

            for (PairDataBean pairDataBean : pairDataBeanList) {
                PairData pairData = new PairData();
                pairData.setId(pairDataBean.getId());
                pairData.setPairTypeId(pairDataBean.getPairTypeId());
                pairData.setPairTypeName(pairDataBean.getPairTypeName());
                pairData.setParamKey(pairDataBean.getParamKey());
                pairData.setParamValue(pairDataBean.getParamValue());
                pairDataList.add(pairData);
            }

            requestTypeDetail.setPairData(pairDataList);

            Rule newRule = Rule.builder()
                    .id(requestTypeDetailBean.getRuleId())
                    .name(rulesBean.getName())
                    .monitoringEnabled(rulesBean.getMonitorEnabled() == 1)
                    .discoveryEnabled(rulesBean.getDiscoveryEnabled() == 1)
                    .order(rulesBean.getOrder())
                    .ruleTypeId(rulesBean.getRuleTypeId())
                    .ruleType(viewTypesForRule.getSubTypeName())
                    .isDefault(addRule.getIsDefault())
                    .regexTypeDetails(regexTypeDetail)
                    .requestTypeDetails(requestTypeDetail)
                    .transactionGroups(Collections.emptyList())
                    .maxTags(rulesBean.getMaxTags())
                    .build();

            List<Rule> serviceRules = serviceRepo.getServiceRules(account.getIdentifier(), serviceIdentifier);
            if (serviceRules.isEmpty()) {
                serviceRules = new ArrayList<>();
            }
            serviceRules.add(newRule);
            serviceRepo.updateServiceRules(account.getIdentifier(), serviceIdentifier, serviceRules);
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Data successfully inserted in Percona but failed to update Redis Cache", e);
        }
    }
}