package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.TagDetailsBean;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.TagsDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.TagMappingBean;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.AddingTagEnum;
import com.appnomic.appsone.controlcenter.pojo.AddingTagsPojo;
import com.appnomic.appsone.controlcenter.pojo.Controller;
import com.appnomic.appsone.controlcenter.pojo.EntryPointObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;


public class TagMappingBL {

    private TagMappingBL() {
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(TagMappingBL.class);

    public static int addTagMapping(int tagId, int objId, String objectRefTable, String tagKey, String tagValue, String userId, int accountId, Handle handle) {

        int tagMappingId = TagsDataService.getTagMappingId(tagId, objId, objectRefTable, tagKey, tagValue, accountId);
        if (tagMappingId != 0) {
            LOGGER.error("Data is already available in tag_mapping table for tagId- {} ,objId - {} ,objectName - {} ,tagKey:- {},tagValue- {} ,accountId :-{}", tagId, objId, objectRefTable, tagKey, tagValue, accountId);
            return -1;
        }

        TagMappingBean bean = new TagMappingBean();
        bean.setTagId(tagId);
        bean.setObjectId(objId);
        bean.setObjectRefTable(objectRefTable);
        bean.setTagKey(tagKey);
        bean.setTagValue(tagValue);
        bean.setUserDetailsId(userId);
        bean.setAccountId(accountId);

        String date = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
        bean.setCreatedTime(date);
        bean.setUpdatedTime(date);

        tagMappingId = addTagMapping(bean, handle);
        if (tagMappingId != -1) {
            LOGGER.info("Tag mapping data is added successfully tagId: {} accountId: {}", tagId, accountId);
        } else {
            LOGGER.error("Failed to add the Tag mapping data tagId: {} accountId: {}", tagId, accountId);
        }

        return tagMappingId;
    }

    private static int addTagMapping(TagMappingBean bean, Handle handle) {
        return TagsDataService.addTagMappingDetails(bean, handle);
    }

    public static EntryPointObject clientValidation(Request request) throws RequestException, ControlCenterException {
        if (null == request || null == request.body() || request.body().trim().isEmpty()) {
            LOGGER.error("Validation failure. Request or request body cannot be NULL or empty.");
            throw new RequestException("Request or request body cannot be NULL or empty.");
        }

        EntryPointObject entryPointObject = new EntryPointObject();

        String accountIdString = request.params(UIMessages.ACCOUNT_IDENTIFIER);
        AccountBean account = ValidationUtils.validAndGetAccount(accountIdString);
        if (account == null) {
            LOGGER.error("Invalid account id: {}", accountIdString);
            throw new RequestException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }
        entryPointObject.setAccount(account);

        String userId;
        try {
            userId = CommonUtils.getUserId(request);
        } catch (ControlCenterException e) {
            LOGGER.error("Invalid user identifier. Reason: {}", e.getMessage(), e);
            throw new RequestException("Invalid user identifier.");
        }
        entryPointObject.setUserId(userId);

        String tagName = request.queryParams("tagName");
        if (tagName == null || tagName.trim().length() == 0) {
            LOGGER.error("tagName should not be empty or NULL");
            throw new RequestException("tagName should not be empty or NULL");
        }
        entryPointObject.setTagName(tagName);

        List<AddingTagsPojo> addingTagsPojoList;
        try {
            addingTagsPojoList = Arrays.asList(CommonUtils.getObjectMapperWithHtmlEncoder().readValue(request.body(), AddingTagsPojo[].class));
        } catch (IOException e) {
            LOGGER.error("Exception encountered while retrieving add tags. Reason: {}", e.getMessage());
            throw new ControlCenterException("Error while converting request object into tag object.");
        }

        if (addingTagsPojoList.isEmpty()) {
            LOGGER.error("tags are invalid.");
            throw new ControlCenterException("tags are unavailable.");
        }

        for(AddingTagsPojo tags : addingTagsPojoList) {
            if(!tags.validate()) {
                LOGGER.error("Validation of entry-level tagging failed.");
                throw new ControlCenterException("Validation of the input failed");
            }

            if("POST".equalsIgnoreCase(request.requestMethod()) && "0".equals(tags.getTagValue().trim())) {
                LOGGER.error("Trying to delete an entry point in POST request");
                throw new ControlCenterException("Trying to delete an entry point in POST request");
            } else if("DELETE".equalsIgnoreCase(request.requestMethod()) && "1".equals(tags.getTagValue().trim())) {
                LOGGER.error("Trying to add an entry point in DELETE request");
                throw new ControlCenterException("Trying to add an entry point in DELETE request");
            }
        }

        entryPointObject.setAddingTagsPojoList(addingTagsPojoList);

        return entryPointObject;
    }

    public static EntryPointObject serverTagValidation(String whomToTag, String referenceId, EntryPointObject entryPointObject) throws ControlCenterException {

        TagDetailsBean tagDetailsBean = MasterCache.getTagDetails(String.valueOf(AddingTagEnum.findByTagName(entryPointObject.getTagName())));

        if (Objects.isNull(tagDetailsBean)) {
            LOGGER.error("Tag detail is not found for given tag name [{}]", entryPointObject.getTagName());
            throw new ControlCenterException(UIMessages.INVALID_TAG_DETAILS);
        }

        List<Controller> controlList = CommonUtils.getControllersByTypeBypassCache(String.valueOf(AddingTagEnum.findByTagName(whomToTag)),
                entryPointObject.getAccount().getId());

        Controller serviceDetails = controlList.stream()
                .filter(con -> con.getIdentifier().equals(referenceId.trim())
                        || con.getName().equals(referenceId.trim()))
                .findAny().orElse(null);

        if (Objects.isNull(serviceDetails)) {
            LOGGER.error("Invalid service identifier provided [{}]. Reason: Provided service identifier is unavailable in controller table.", referenceId);
            throw new ControlCenterException("Invalid service identifier");
        }

        entryPointObject.setTagDetailsBean(tagDetailsBean);
        entryPointObject.setServiceDetails(serviceDetails);

        return entryPointObject;
    }

    public static void addTagMapping(String tagValue, String userId, int accountId, TagDetailsBean tagDetailsBean, Controller serviceDetails) throws ControlCenterException {
        TagMappingBean tagMappingDetails = createTag(serviceDetails, tagDetailsBean, tagValue);
        TagMappingBL.addingTags(tagMappingDetails, userId, accountId);
    }

    public static void deleteTagMapping(String tagValue, String userId, int accountId, TagDetailsBean tagDetailsBean, Controller serviceDetails) throws ControlCenterException {
        TagMappingBean tagMappingDetails = createTag(serviceDetails, tagDetailsBean, tagValue);
        if (tagValue.equals("0")) {
            deleteTags(userId, accountId, serviceDetails, tagMappingDetails);
        }
    }

    private static void deleteTags(String userId, int accountId, Controller serviceDetails, TagMappingBean tagMappingDetails) throws ControlCenterException {
        int tagMappingId = TagsDataService.getTagMappingId(tagMappingDetails.getTagId(), tagMappingDetails.getObjectId(),
                tagMappingDetails.getObjectRefTable(), tagMappingDetails.getTagKey(), "1", accountId);
        if (tagMappingId == 0) {
            LOGGER.error("Entry point tag does not exist for the service [{}]", serviceDetails.getIdentifier());
            throw new ControlCenterException("Entry point tag does not exist for the service " + serviceDetails.getIdentifier());
        }
        int id = TagsDataService.deleteTags(tagMappingDetails.getTagId(), tagMappingDetails.getObjectId(), tagMappingDetails.getTagKey(),
                tagMappingDetails.getObjectRefTable(), accountId, userId, null);
        if(id == -1) {
            LOGGER.info("Entry point tag successfully removed for service [{}]", serviceDetails.getIdentifier());
        }
    }

    private static TagMappingBean createTag(Controller serviceDetails, TagDetailsBean tagDetailsBean, String tagValue) {
        TagMappingBean tags = new TagMappingBean();

        if (Objects.nonNull(serviceDetails)) {
            String refTable = Constants.CONTROLLER;
            String tagKey = Constants.DEFAULT_TAG_VALUE;
            tags.setTagId(tagDetailsBean.getId());
            tags.setObjectId(Integer.parseInt(serviceDetails.getAppId()));
            tags.setTagKey(tagKey);
            tags.setTagValue(tagValue);
            tags.setObjectRefTable(refTable);
        }
        return tags;
    }

    private static void addingTags(TagMappingBean tagMappingDetails, String userId, int accountId) throws ControlCenterException {
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        try {
            dbi.inTransaction((conn, status) -> addingTagMappingDetails(tagMappingDetails, userId, accountId, conn));
        } catch (Exception e) {
            LOGGER.error("Unable to add tags :", e);
            if (Throwables.getRootCause(e) instanceof ControlCenterException) {
                throw (ControlCenterException) Throwables.getRootCause(e);
            } else {
                throw e;
            }
        }
    }

    private static TagMappingBean addingTagMappingDetails(TagMappingBean tagMappingBean, String userId, int accountId, Handle conn) throws ControlCenterException {
        if (Objects.nonNull(tagMappingBean)) {
            int id = addTagMapping(tagMappingBean.getTagId(), tagMappingBean.getObjectId(), tagMappingBean.getObjectRefTable(), tagMappingBean.getTagKey(), tagMappingBean.getTagValue()
                    , userId, accountId, conn);
            if(id <= 0) {
                throw new ControlCenterException("Service is already tagged as entry point.");
            }
        }
        return tagMappingBean;
    }

    public static int updateTagMapping(int tagMappingId, String tagKey, String tagValue, String userId, Handle handle) {
        TagMappingBean bean = new TagMappingBean();
        bean.setId(tagMappingId);
        bean.setTagKey(tagKey);
        bean.setTagValue(tagValue);
        bean.setUserDetailsId(userId);

        String date = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
        bean.setUpdatedTime(date);

        int id = TagsDataService.updateTagMappingDetails(bean, handle);
        if (id != -1) {
            LOGGER.info("Tag mapping data is updated successfully for Id: {}", id);
        } else {
            LOGGER.error("Failed to update tag mapping data for Id: {}", id);
        }
        return id;
    }
}
