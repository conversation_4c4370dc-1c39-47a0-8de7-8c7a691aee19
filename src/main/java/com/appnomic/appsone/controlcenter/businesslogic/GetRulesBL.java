package com.appnomic.appsone.controlcenter.businesslogic;


import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.RuleDetailsBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.MasterDataRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.service.TransactionService;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.pojos.Rule;
import com.heal.configuration.pojos.TransactionGroup;
import com.heal.configuration.pojos.ViewTypes;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class GetRulesBL implements BusinessLogic<Integer, UtilityBean<Integer>, List<Map<String, Object>>> {
    @Override
    public UtilityBean<Integer> clientValidation(RequestObject request) throws ClientException {

        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }
        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (authToken == null || StringUtils.isEmpty(authToken)) {
            log.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }
        String accountIdString = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (accountIdString == null || StringUtils.isEmpty(accountIdString)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String serviceId = request.getParams().get(Constants.SERVICE_ID);

        if(serviceId == null || StringUtils.isEmpty(serviceId)) {
            log.error(UIMessages.EMPTY_SERVICE_IDENTIFIER);
            throw new ClientException(UIMessages.EMPTY_SERVICE_IDENTIFIER);
        }

        return UtilityBean.<Integer>builder()
                .accountIdentifier(accountIdString)
                .authToken(authToken)
                .pojoObject(Integer.parseInt(serviceId))
                .build();
    }

    @Override
    public UtilityBean<Integer> serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }
        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        //Check account details
        if (account == null) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ServerException(UIMessages.ACCOUNT_EMPTY);
        }

        ControllerBean serviceDetails = new ControllerDataService().getControllerById(utilityBean.getPojoObject(), account.getId(), null);

        if(serviceDetails == null) {
            log.error(UIMessages.INVALID_SERVICE,utilityBean.getServiceId());
            throw new ServerException(UIMessages.INVALID_SERVICE);
        }

        utilityBean.setAccount(account);
        utilityBean.setServiceId(serviceDetails.getIdentifier());

        return utilityBean;
    }

    @Override
    public List<Map<String, Object>> process(UtilityBean<Integer> bean) throws DataProcessingException {


        List<Map<String, Object>> rulesList = new ArrayList<>();

        String accIdentifier = bean.getAccountIdentifier();
        String serviceIdentifier = bean.getServiceId();
        ServiceRepo services = new ServiceRepo();

        List<Rule> rulesDetails = services.getServiceRules(accIdentifier, serviceIdentifier);

        if (rulesDetails.isEmpty()) {
            log.error("No rules available for the provided service: {}", serviceIdentifier);
            return Collections.emptyList();
        }
        MasterDataRepo masterDataRepo = new MasterDataRepo();
        ViewTypes ejbRuleType = masterDataRepo.getTypes().stream()
                .filter(type -> Constants.RULES_TYPE.trim().equalsIgnoreCase(type.getTypeName()))
                .filter(it -> Constants.AGENT_RULES_TYPE_EJB_DATA.trim().equalsIgnoreCase(it.getSubTypeName())).findAny().orElse(null);

        int ejbTypeId = ejbRuleType != null ? ejbRuleType.getSubTypeId() : 0;

        rulesDetails = rulesDetails.stream()
                .filter(rule -> rule.getRuleTypeId() != ejbTypeId)
                .collect(Collectors.toList())
                .stream()
                .sorted(Comparator.comparing(Rule::getOrder).reversed())
                .collect(Collectors.toList());


        if (rulesDetails.isEmpty()) {
            log.error("No rules available for the provided service: {}", serviceIdentifier);
            return Collections.emptyList();
        }

        rulesDetails.forEach(ruleDetail -> {

            Map<String, Object> rulesData = new HashMap<>();
            rulesData.put(Constants.ID, ruleDetail.getId());
            rulesData.put(Constants.NAME, ruleDetail.getName());
            rulesData.put(Constants.RULE_TYPE, (ruleDetail.getIsDefault() == 0 ? Constants.RULES_TYPE_CUSTOM : Constants.RULES_TYPE_STANDARD));
            rulesData.put(Constants.ORDER, ruleDetail.getOrder());
            rulesData.put(Constants.MONITOR_STATUS, ruleDetail.isMonitoringEnabled() ? 1 : 0);
            rulesData.put(Constants.DISCOVERY_STATUS, ruleDetail.isDiscoveryEnabled() ? 1 : 0);

            List<RuleDetailsBean> totalRequestsCount = TransactionService.getLastDiscoveredRequest(ruleDetail.getId(), bean.getAccount().getId());
            if (!totalRequestsCount.isEmpty()) {
                rulesData.put(Constants.RULE_REQUESTS_COUNT, totalRequestsCount.size());
                totalRequestsCount.sort(Comparator.comparing(RuleDetailsBean::getRequestDiscoveredTime, Comparator.reverseOrder()));
                String requestDiscoveredTime = totalRequestsCount.get(0).getRequestDiscoveredTime();
                rulesData.put(Constants.RULE_REQUEST_DISCOVERED_TIME, DateTimeUtil.getGMTToEpochTime(requestDiscoveredTime));
            }

            List<String> tGroupName = ruleDetail.getTransactionGroups().stream().map(TransactionGroup::getTransactionGroupName).collect(Collectors.toList());
            rulesData.put(Constants.DISCOVERY_TAGS, (0 == ruleDetail.getId()) ? Collections.emptySet() : tGroupName);

            rulesList.add(rulesData);
        });

        return rulesList;
    }
}