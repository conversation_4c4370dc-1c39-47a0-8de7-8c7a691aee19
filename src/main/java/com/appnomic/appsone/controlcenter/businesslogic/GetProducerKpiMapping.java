package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ProducerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.KPIComponentPojo;
import com.appnomic.appsone.controlcenter.pojo.ProducerKPIMappingDetailsPojo;
import com.appnomic.appsone.controlcenter.pojo.ProducerPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class GetProducerKpiMapping implements BusinessLogic<Integer, ProducerPojo, List<KPIComponentPojo>> {

    public UtilityBean<Integer> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            log.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }

        String accountIdentifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(accountIdentifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String producerIdString = request.getParams().get(":producerid");
        int producerId;
        try {
            producerId = Integer.parseInt(producerIdString);
            if (producerId < 1) {
                log.error("ProducerId {} should be a positive non-zero integer.", producerId);
                throw new ClientException("ProducerId should be a positive non-zero integer.");
            }
        } catch (NumberFormatException e) {
            log.error("ProducerId should be an integer. Details: {}", e.getMessage());
            throw new ClientException("ProducerId should be positive integer.");
        }

        return UtilityBean.<Integer>builder()
                .accountIdentifier(accountIdentifier)
                .authToken(authToken)
                .pojoObject(producerId)
                .build();
    }

    @Override
    public ProducerPojo serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        UserAccountBean userAccBean;

        try {
            userAccBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            throw new ServerException(e.getMessage());
        }

        String userId = userAccBean.getUserId();
        AccountBean account = userAccBean.getAccount();
        Integer producerId = utilityBean.getPojoObject();

        int count = ProducerDataService.getProducer(account.getId(), producerId);
        if (count == 0) {
            log.error("Invalid producer Id: {}", producerId);
            throw new ServerException(String.format("Invalid producer Id: [%d]", producerId));
        }

        return ProducerPojo.builder()
                .userId(userId)
                .account(account)
                .producerId(producerId)
                .build();
    }

    @Override
    public List<KPIComponentPojo> process(ProducerPojo bean) throws DataProcessingException {
        List<ProducerKPIMappingDetailsPojo> producerKpiMappingData = ProducerDataService.getProducerKPIMappingDetails(bean.getAccount().getId(), bean.getProducerId(), null);

        if (producerKpiMappingData == null || producerKpiMappingData.isEmpty()) {
            log.info("There are no KPIs mapped to this producer {}", bean.getProducerId());
            return new ArrayList<>();
        }

        return producerKpiMappingData.parallelStream()
                .map(c -> KPIComponentPojo.builder()
                        .id(c.getProducerId())
                        .kpiIdentifier(c.getKpiName())
                        .componentType(c.getComponentTypeName())
                        .componentName(c.getComponentName())
                        .componentVersion(c.getComponentVersionName())
                        .isDefault(c.getIsDefault())
                        .build()).sorted(Comparator.comparingInt(KPIComponentPojo::getId))
                .collect(Collectors.toList());
    }
}
