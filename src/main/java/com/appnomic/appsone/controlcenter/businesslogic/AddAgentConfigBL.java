package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.PhysicalAgentBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.TagMappingBean;
import com.appnomic.appsone.controlcenter.dao.redis.AgentRepo;
import com.appnomic.appsone.controlcenter.dao.redis.MasterDataRepo;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.Agent;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.service.ComponentAgentConfigService;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.ViewTypes;
import com.heal.configuration.pojos.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.*;

@Slf4j
public class AddAgentConfigBL implements BusinessLogic<Agent, AgentBean, IdPojo> {

    AgentRepo agentRepo = new AgentRepo();

    @Override
    public UtilityBean<Agent> clientValidation(RequestObject requestObject) throws ClientException {
        String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            log.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }

        if (StringUtils.isEmpty(requestObject.getBody())) {
            log.error("Request body is empty.");
            throw new ClientException("Request body is empty.");
        }

        Agent agent;
        try {
            agent = new ObjectMapper().readValue(requestObject.getBody(),
                    new TypeReference<Agent>() {
                    });
            agent.validate();
        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        } catch (Exception e) {
            throw new ClientException(e.getMessage());
        }

        if (agent.getSubType().equalsIgnoreCase(Constants.COMPONENT_AGENT_SUB_TYPE)) {
            if (agent.getAgentMappingDetails() == null) {
                log.error("Component Agent details cannot be null for agent- {}", agent.getName());
                throw new ClientException("Component Agent details cannot be null for agent-" + agent.getName());
            }
            if (agent.getAgentMappingDetails().getDataCommunication() == null) {
                log.error("Data communication can not be null for agent- {}", agent.getName());
                throw new ClientException("Data communication can not be null for agent-" + agent.getName());
            }
        }

        return UtilityBean.<Agent>builder()
                .accountIdentifier(identifier)
                .pojoObject(agent)
                .authToken(authToken)
                .build();
    }

    @Override
    public AgentBean serverValidation(UtilityBean<Agent> utilityBean) throws ServerException {
        UserAccountBean userAccBean;
        try {
            userAccBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            throw new ServerException(e.getMessage());
        }

        Agent agent = utilityBean.getPojoObject();
        int accountId = userAccBean.getAccount().getId();
        String accountIdentifier = userAccBean.getAccount().getIdentifier();

        if (AgentDataService.getAgentBeanDataForName(agent.getName()) != null) {
            log.error("Agent name: {} already exists", agent.getName());
            throw new ServerException(String.format("Agent name: [%s] already exists.", agent.getName()));
        }
        if (AgentDataService.getAgentBeanData(agent.getUniqueToken()) != null) {
            log.error("Agent identifier: {} already exists", agent.getUniqueToken());
            throw new ServerException(String.format("Agent identifier: [%s] already exists.", agent.getUniqueToken()));
        }
        if (!ValidationUtils.isValidServiceId(accountIdentifier, agent.getServiceId())) {
            log.error("Service Id {} is invalid.", agent.getServiceId());
            throw new ServerException(String.format("Service Id [%s] is invalid.", agent.getServiceId()));
        }

        String serviceIdentifier = null;
        String serviceName = null;
        if (agent.getServiceId() != 0) {
            ControllerBean controllerBean = new ControllerDataService().getControllerById(agent.getServiceId(), accountId, null);
            serviceIdentifier = controllerBean.getIdentifier();
            serviceName = controllerBean.getName();
        }
        return AgentBean.builder()
                .name(agent.getName())
                .uniqueToken(agent.getUniqueToken())
                .subType(agent.getSubType())
                .status(agent.getStatus())
                .hostAddress(agent.getHostAddress())
                .mode(agent.getMode())
                .description(agent.getDescription())
                .serviceId(agent.getServiceId())
                .serviceIdentifier(serviceIdentifier)
                .serviceName(serviceName)
                .userDetailsId(userAccBean.getUserId())
                .accountId(accountId)
                .agentMappingDetails(agent.getAgentMappingDetails())
                .addedDataSources(agent.getAddedDataSources())
                .deletedDataSources(agent.getDeletedDataSources())
                .accountIdentifier(accountIdentifier)
                .build();
    }

    @Override
    public IdPojo process(AgentBean agentConfigBean) throws DataProcessingException {
        try {
            String uniqueToken = UUID.randomUUID().toString();
            if (agentConfigBean.getUniqueToken() == null) {
                agentConfigBean.setUniqueToken(uniqueToken);
            }
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            return dbi.inTransaction((conn, status) -> addAgentConfig(agentConfigBean, conn));
        } catch (Exception e) {
            log.error("Unable to add agent data. Details: ", e);
            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
            } else {
                throw e;
            }
        }
    }

    public IdPojo addAgentConfig(AgentBean agentConfigBean, Handle handle) throws DataProcessingException {
        int physicalIdentifierId;
        int dataCommunicationId = 0;
        ViewTypes subTypes = new MasterDataRepo().getTypes()
                .stream()
                .filter(a -> a.getTypeName().equalsIgnoreCase(Constants.AGENT_TYPE))
                .filter(a -> a.getSubTypeName().equalsIgnoreCase(agentConfigBean.getSubType()))
                .findAny().orElse(null);

        if(subTypes == null) {
            log.error("Type details unavailable for type [{}] and subtype [{}]", Constants.AGENT_TYPE, agentConfigBean.getSubType());
            throw new DataProcessingException("Type details unavailable for type " + Constants.AGENT_TABLE + " and subtype " + agentConfigBean.getSubType());
        }

        agentConfigBean.setAgentTypeId(subTypes.getSubTypeId());

        List<AgentBean> agentBeanList = AgentDataService.getAgentsByTypeNHostAddress(agentConfigBean.getAgentTypeId(), agentConfigBean.getHostAddress());
        if (agentBeanList == null || agentBeanList.isEmpty()) {
            agentConfigBean.setPhysicalAgentIdentifier(agentConfigBean.getUniqueToken() + "_Physical");
            physicalIdentifierId = addPhysicalAgent(agentConfigBean, handle).getId();
        } else {
            PhysicalAgentBean physicalAgentBean = AgentDataService.getPhysicalAgentDetailsUsingId(agentBeanList.get(0).getPhysicalAgentId());
            if (physicalAgentBean == null) {
                log.error("Physical agent details unavailable for physical agent ID [{}]", agentBeanList.get(0).getPhysicalAgentId());
                throw new DataProcessingException(String.format("Physical agent details unavailable for physical agent ID %s", agentBeanList.get(0).getPhysicalAgentId()));
            }

            agentConfigBean.setPhysicalAgentIdentifier(physicalAgentBean.getIdentifier());
            physicalIdentifierId = agentBeanList.get(0).getPhysicalAgentId();
        }
        agentConfigBean.setPhysicalAgentId(physicalIdentifierId);
        int agentId = addAgent(agentConfigBean, handle);
        agentConfigBean.setId(agentId);
        addAccountMappingDetails(agentConfigBean, handle);
        if (agentConfigBean.getServiceId() != 0) {
            addAgentServiceMapping(agentConfigBean, handle);
        }

        if (agentConfigBean.getSubType().equalsIgnoreCase(Constants.COMPONENT_AGENT_SUB_TYPE)) {
            dataCommunicationId = addComponentAgent(agentConfigBean, handle);
        }

        List<Tags> newTagDetails = new ArrayList<>();
        Tags serviceTag = Tags.builder()
                .key(String.valueOf(agentConfigBean.getServiceId()))
                .value(agentConfigBean.getServiceIdentifier())
                .type(Constants.CONTROLLER_TAG)
                .build();
        newTagDetails.add(serviceTag);

        TagDetailsBean tagDetails = MasterCache.getTagDetails(Constants.AGENT_DATA_SOURCES);
        if (tagDetails == null) {
            log.error("Tag details unavailable for tag name [{}]", Constants.AGENT_DATA_SOURCES);
            throw new DataProcessingException("Tag details unavailable for tag name " + Constants.AGENT_DATA_SOURCES);
        }

        if (!CollectionUtils.isEmpty(agentConfigBean.getAddedDataSources())) {
            agentConfigBean.getAddedDataSources().parallelStream()
                    .forEach(dataSource -> {
                        String date = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());
                        AgentDataService.addAgentDataSource(TagMappingBean.builder()
                                .tagId(tagDetails.getId())
                                .objectId(agentId)
                                .objectRefTable(Constants.AGENT_TABLE)
                                .tagKey("Name")
                                .tagValue(dataSource)
                                .accountId(agentConfigBean.getAccountId())
                                .userDetailsId(agentConfigBean.getUserDetailsId())
                                .createdTime(date)
                                .updatedTime(date)
                                .build(), handle);
                        newTagDetails.add(Tags.builder()
                                .key("Name")
                                .value(dataSource)
                                .type(tagDetails.getName())
                                .build());
                    });
        }
        //add agent details in the redis cache.
        addAgentForAccount(agentConfigBean);
        addAgentForAgentIdentifier(agentConfigBean, newTagDetails, dataCommunicationId);
        addAgentServiceMappingInRedis(agentConfigBean);

        return IdPojo.builder()
                .id(agentId)
                .name(agentConfigBean.getName())
                .identifier(agentConfigBean.getUniqueToken())
                .build();
    }

    private void addAgentServiceMappingInRedis(AgentBean agentConfigBean) {
        List<BasicEntity> existingServiceDetails = agentRepo.getServices(agentConfigBean.getAccountIdentifier(), agentConfigBean.getUniqueToken());
        if (existingServiceDetails.parallelStream().anyMatch(f -> agentConfigBean.getServiceId() == f.getId())) {
            log.debug("The service id: {} is already exist for the given accountIdentifier: {} and agentIdentifier: {}", agentConfigBean.getServiceId(), agentConfigBean.getAccountIdentifier(), agentConfigBean.getUniqueToken());
            return;
        }
        BasicEntity serviceDetail = BasicEntity.builder()
                .id(agentConfigBean.getServiceId())
                .status(agentConfigBean.getStatus())
                .name(agentConfigBean.getServiceName())
                .accountId(agentConfigBean.getAccountId())
                .identifier(agentConfigBean.getServiceIdentifier())
                .lastModifiedBy(agentConfigBean.getUserDetailId())
                .createdTime(String.valueOf(agentConfigBean.getCreatedTime()))
                .updatedTime(String.valueOf(agentConfigBean.getUpdatedTime()))
                .build();
        existingServiceDetails.add(serviceDetail);
        agentRepo.updateServices(agentConfigBean.getAccountIdentifier(), agentConfigBean.getUniqueToken(), existingServiceDetails);

    }

    private void addAgentForAgentIdentifier(AgentBean agentConfigBean, List<Tags> tagsList, int dataCommunicationId) {
        Timestamp timeStamp = new Timestamp(new Date().getTime());
        AgentDataCollectionDetails agentDataCollectionDetails = null;
        if (agentConfigBean.getAgentMappingDetails() != null) {

            agentDataCollectionDetails = AgentDataCollectionDetails.builder()
                    .id(dataCommunicationId)
                    .dataAddress(agentConfigBean.getAgentMappingDetails().getDataCommunication().getHost())
                    .dataPort(agentConfigBean.getAgentMappingDetails().getDataCommunication().getPort())
                    .dataProtocol(agentConfigBean.getAgentMappingDetails().getDataCommunication().getProtocol())
                    .timeoutMultiplier(agentConfigBean.getAgentMappingDetails().getTimeoutMultiplier())
                    .dataEndPoint(agentConfigBean.getAgentMappingDetails().getDataCommunication().getEndpoint())
                    .build();
        }
        com.heal.configuration.pojos.Agent newAgentDetail = com.heal.configuration.pojos.Agent.builder()
                .accountId(agentConfigBean.getAccountId())
                .id(agentConfigBean.getId())
                .identifier(agentConfigBean.getUniqueToken())
                .name(agentConfigBean.getName())
                .status(agentConfigBean.getStatus())
                .createdTime(String.valueOf(timeStamp))
                .updatedTime(String.valueOf(timeStamp))
                .lastModifiedBy(agentConfigBean.getUserDetailId())
                .physicalAgentId(agentConfigBean.getPhysicalAgentId())
                .physicalAgentIdentifier(agentConfigBean.getPhysicalAgentIdentifier())
                .mode(agentConfigBean.getMode())
                .typeId(agentConfigBean.getAgentTypeId())
                .typeName(agentConfigBean.getSubType())
                .version(agentConfigBean.getVersion())
                .communicationInterval(agentConfigBean.getCommunicationInterval())
                .agentDataCollectionDetails(agentDataCollectionDetails)
                .accountIdentifiers(Collections.singletonList(agentConfigBean.getAccountIdentifier()))
                .tags(tagsList)
                .build();

        if (agentConfigBean.getAgentMappingDetails() != null) {
            newAgentDetail.setConfigOperationMode(agentConfigBean.getAgentMappingDetails().getConfigOperationMode());
            newAgentDetail.setDataOperationMode(agentConfigBean.getAgentMappingDetails().getDataOperationMode());
        }

        agentRepo.updateAgentDetailsForAgentIdentifier(newAgentDetail);
    }

    private void addAgentForAccount(AgentBean agentConfigBean) {
        List<BasicAgentEntity> existingAgentDetails = agentRepo.getAgents(agentConfigBean.getAccountIdentifier());

        if (existingAgentDetails.isEmpty()) {
            existingAgentDetails = new ArrayList<>();
        }

        if (existingAgentDetails.parallelStream().anyMatch(agent -> agent.getId() == agentConfigBean.getId())) {
            log.error("The agent Detail already present for the given accountIdentifier [{}] and agentId [{}]", agentConfigBean.getAccountIdentifier(), agentConfigBean.getId());
            return;
        }
        BasicAgentEntity newAgent = BasicAgentEntity.builder()
                .id(agentConfigBean.getId())
                .name(agentConfigBean.getName())
                .identifier(agentConfigBean.getUniqueToken())
                .createdTime(String.valueOf(agentConfigBean.getCreatedTime()))
                .updatedTime(String.valueOf(agentConfigBean.getUpdatedTime()))
                .lastModifiedBy(agentConfigBean.getUserDetailId())
                .status(agentConfigBean.getStatus())
                .accountId(agentConfigBean.getAccountId())
                .type(agentConfigBean.getSubType())
                .typeId(agentConfigBean.getAgentTypeId())
                .physicalAgentIdentifier(agentConfigBean.getPhysicalAgentIdentifier())
                .physicalAgentId(agentConfigBean.getPhysicalAgentId())
                .accountId(agentConfigBean.getAccountId())
                .communicationInterval(agentConfigBean.getCommunicationInterval())
                .version(agentConfigBean.getVersion())
                .supervisorId(agentConfigBean.getSupervisorId())
                .mode(agentConfigBean.getMode())
                .hostAddress(agentConfigBean.getHostAddress())
                .build();
        existingAgentDetails.add(newAgent);
        agentRepo.updateAgents(agentConfigBean.getAccountIdentifier(), existingAgentDetails);
    }

    private int addAgent(AgentBean agentConfigBean, Handle handle) throws DataProcessingException {
        Timestamp timestamp;
        try {
            timestamp = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
        } catch (ParseException e) {
            log.error("Error in adding agent. Details: {}", e.getMessage(), e);
            throw new DataProcessingException("Error in adding agent.");
        }

        agentConfigBean.setCreatedTime(timestamp);
        agentConfigBean.setUpdatedTime(timestamp);

        int agentId = AgentDataService.addAgent(agentConfigBean, handle);

        if (agentId == 0) {
            throw new DataProcessingException("Unable to add agent data into DB for agent name-" + agentConfigBean.getName());
        }
        return agentId;
    }

    private PhysicalAgentBean addPhysicalAgent(AgentBean agentConfigBean, Handle handle) throws DataProcessingException {
        Timestamp timestamp;
        try {
            timestamp = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
        } catch (ParseException e) {
            log.error("Error in adding physical agent. Details: {}", e.getMessage(), e);
            throw new DataProcessingException("Error in adding physical agent.");
        }
        PhysicalAgentBean physicalAgentBean = PhysicalAgentBean.builder()
                .identifier(agentConfigBean.getPhysicalAgentIdentifier())
                .userDetailsId(agentConfigBean.getUserDetailsId())
                .createdTime(timestamp)
                .updatedTime(timestamp)
                .lastCommandExecuted(1)
                .lastJobId(null)
                .lastStatusId(null)
                .build();

        int physicalAgentId = AgentDataService.addPhysicalAgentDetails(physicalAgentBean, handle);

        if (physicalAgentId < 0) {
            log.error("Physical agent insertion failed.");
            throw new DataProcessingException("Physical agent insertion failed.");
        }

        physicalAgentBean.setId(physicalAgentId);
        return physicalAgentBean;
    }

    private void addAccountMappingDetails(AgentBean agentConfigBean, Handle handle) throws DataProcessingException {
        AgentAccountMappingBean agentAccountMappingBean = new AgentAccountMappingBean();
        try {
            agentAccountMappingBean.setAgentId(agentConfigBean.getId());
            agentAccountMappingBean.setAccountId(agentConfigBean.getAccountId());
            agentAccountMappingBean.setUserDetailsId(agentConfigBean.getUserDetailsId());
            agentAccountMappingBean.setCreatedTime(new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime()));
        } catch (ParseException e) {
            log.error("Error in adding agent-account mapping. Details: {}", e.getMessage(), e);
            throw new DataProcessingException("Error in adding agent-account mapping.");
        }
        int id = new CompInstanceDataService().addAgentAccountMapping(agentAccountMappingBean, handle);
        if (id == -1) {
            log.error("Error while adding agent account mapping.");
            throw new DataProcessingException("Error while adding agent account mapping.");
        }
        log.info("Agent is mapped for a given account and agent id {}, {}-", agentConfigBean.getAccountId(), agentConfigBean.getId());
    }

    private void addAgentServiceMapping(AgentBean agentConfigBean, Handle handle) throws DataProcessingException {
        TagDetailsBean tagDetailsBean = MasterCache.getTagDetails(Constants.CONTROLLER_TAG);
        if (tagDetailsBean == null) {
            log.error("Tag details unavailable for tag name [{}]", Constants.CONTROLLER_TAG);
            throw new DataProcessingException("Tag details unavailable for tag name " + Constants.CONTROLLER_TAG);
        }
        int tagId = tagDetailsBean.getId();

        int id = TagMappingBL.addTagMapping(tagId, agentConfigBean.getId(), Constants.AGENT_TABLE, String.valueOf(agentConfigBean.getServiceId()), agentConfigBean.getServiceIdentifier(), agentConfigBean.getUserDetailsId(), agentConfigBean.getAccountId(), handle);
        if (id == -1) {
            log.error("Error in adding agent service mapping details for agent Id {} and service Id {}", agentConfigBean.getId(), agentConfigBean.getServiceId());
            throw new DataProcessingException("Error in adding agent service mapping details.");
        }
    }

    private int addComponentAgent(AgentBean agentConfigBean, Handle handle) throws DataProcessingException {
        try {
            return new ComponentAgentConfigService().addComponentAgent(agentConfigBean, handle);
        } catch (ControlCenterException e) {
            log.error("Error in adding component agent. Details: {}", e.getMessage(), e);
            throw new DataProcessingException("Error in adding component agent.");
        }
    }
}