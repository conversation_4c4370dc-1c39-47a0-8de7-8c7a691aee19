package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Actions;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProducerMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserProfileBean;
import com.appnomic.appsone.controlcenter.dao.redis.*;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.Tags;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.service.TagsService;
import com.appnomic.appsone.controlcenter.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.heal.configuration.entities.ParentApplicationBean;
import com.heal.configuration.pojos.Application;
import com.heal.configuration.pojos.*;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class Applications {

    private static final ObjectMapper OBJECT_MAPPER = CommonUtils.getObjectMapperWithHtmlEncoder();
    private static final Logger LOGGER = LoggerFactory.getLogger(Applications.class);
    private static boolean clusterDataRequired = false;
    private static String accountIdentifier;

    private static final int anomalyDestinationTypeId = ConfProperties.getInt(Constants.ANOMALY_DESTINATION_TYPE_ID, Constants.ANOMALY_DESTINATION_TYPE_ID_DEFAULT);
    private static final int ewSignalCloseTime = ConfProperties.getInt(Constants.EW_SIGNAL_CLOSE_TIME, Constants.EW_SIGNAL_CLOSE_TIME_DEFAULT);
    private static final int problemSignalCloseTime = ConfProperties.getInt(Constants.PROBLEM_SIGNAL_CLOSE_TIME, Constants.PROBLEM_SIGNAL_CLOSE_TIME_DEFAULT);
    private static final int infoSignalCloseTime = ConfProperties.getInt(Constants.INFO_SIGNAL_CLOSE_TIME, Constants.INFO_SIGNAL_CLOSE_TIME_DEFAULT);

    public static UserAccountIdentifiersBean getClientValidations(Request request) throws RequestException {
        String accountIdentifier = request.params(Constants.ACCOUNT_IDENTIFIER);
        if (accountIdentifier == null || accountIdentifier.trim().isEmpty()) {
            LOGGER.error("Account Identifier is null or empty.");
            throw new RequestException(UIMessages.INVALID_IDENTIFIER);
        }

        String userId;
        try {
            userId = CommonUtils.getUserId(request);
        } catch (ControlCenterException e) {
            LOGGER.error("Invalid authentication token. User identifier extraction failed.");
            throw new RequestException("Invalid authentication token. User identifier extraction failed.");
        }

        clusterDataRequired = Boolean.parseBoolean(request.queryParams("clusterDataRequired"));

        return new UserAccountIdentifiersBean(userId, accountIdentifier);
    }

    public static AccountBean getServerValidations(String accountIdentifier) throws RequestException {
        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            LOGGER.error("Invalid account identifier [{}]", accountIdentifier);
            throw new RequestException("Invalid Account Identifier");
        }
        return account;
    }

    public static List<GetApplication> get(AccountBean account, String userId) throws ControlCenterException {
        long time = System.currentTimeMillis();
        try {
            ControllerDataService controllerDataService = new ControllerDataService();

            List<Controller> accessibleApplications = UserValidationUtil.getAccessibleApplicationsForUser(userId, account.getIdentifier());

            if (null == accessibleApplications || accessibleApplications.isEmpty()) {
                LOGGER.error("Applications unavailable for the user [{}] for account [{}]", userId, account.getIdentifier());
                throw new ControlCenterException("Applications unavailable for the user");
            }
            LOGGER.trace("Time taken to fetch accessible applications for user {} ms", System.currentTimeMillis() - time);
            time = System.currentTimeMillis();

            Map<String, String> usersMap = UserAccessDataService.getActiveUsers().parallelStream().collect(Collectors.toMap(IdPojo::getIdentifier, IdPojo::getName));
            Map<String, List<GetApplication.ClusterComponentDetails>> servHostClusterMap = new HashMap<>();
            Map<String, List<GetApplication.ClusterComponentDetails>> servCompClusterMap = new HashMap<>();
            if (clusterDataRequired) {
                List<GetApplication.ClusterComponentDetails> instClusterComponentDetails =
                        controllerDataService.getInstClusterComponentDetailsForService(null);
                LOGGER.trace("Time taken to fetch instClusterComponentDetails {} ms", System.currentTimeMillis() - time);
                time = System.currentTimeMillis();

                servHostClusterMap = instClusterComponentDetails.parallelStream()
                        .filter(c -> c.getComponentTypeId() == 1)
                        .collect(Collectors.groupingBy(GetApplication.ClusterComponentDetails::getServiceIdentifier));

                servCompClusterMap = instClusterComponentDetails.parallelStream()
                        .filter(c -> c.getComponentTypeId() != 1)
                        .collect(Collectors.groupingBy(GetApplication.ClusterComponentDetails::getServiceIdentifier));
            }

            Map<String, List<GetApplication.ClusterComponentDetails>> finalServHostClusterMap = servHostClusterMap;
            Map<String, List<GetApplication.ClusterComponentDetails>> finalServCompClusterMap = servCompClusterMap;
            Map<String, List<GetApplication.ServiceClusterDetails>> appServiceMap =
                    controllerDataService.getApplicationServiceMapping(account.getId()).parallelStream()
                            .collect(Collectors.groupingBy(ViewApplicationServiceMappingBean::getApplicationIdentifier))
                            .entrySet().parallelStream()
                            .collect(Collectors.toMap(Map.Entry::getKey, c -> c.getValue().parallelStream().distinct()
                                    .map(d -> GetApplication.ServiceClusterDetails.builder()
                                            .id(d.getServiceId())
                                            .name(d.getServiceName())
                                            .identifier(d.getServiceIdentifier())
                                            .hostCluster(finalServHostClusterMap.getOrDefault(d.getServiceIdentifier(), new ArrayList<>()))
                                            .componentCluster(finalServCompClusterMap.getOrDefault(d.getServiceIdentifier(), new ArrayList<>()))
                                            .build())
                                    .collect(Collectors.toList())));

            LOGGER.trace("Time taken to fetch appServiceMap {} ms", System.currentTimeMillis() - time);
            time = System.currentTimeMillis();

            List<com.heal.configuration.pojos.Application> applicationList = new ApplicationRepo().getApplicationsForAccount(account.getIdentifier());
            Map<String, List<com.heal.configuration.pojos.Tags>> appVsTags = applicationList.stream().filter(app -> app.getTags() != null)
                    .collect(Collectors.toMap(BasicEntity::getIdentifier, com.heal.configuration.pojos.Application::getTags));
            Map<String, ParentApplication> appVsParent = applicationList.parallelStream()
                    .filter(a -> a.getParentApplication() != null)
                    .collect(Collectors.toMap(BasicEntity::getIdentifier, com.heal.configuration.pojos.Application::getParentApplication));

            List<GetApplication> accessibleApps = accessibleApplications.parallelStream()
                    .map(c -> GetApplication.builder()
                            .id(Integer.parseInt(c.getAppId()))
                            .identifier(c.getIdentifier())
                            .name(c.getName())
                            .lastModifiedBy(usersMap.getOrDefault(c.getCreatedBY(), null))
                            .lastModifiedOn(DateTimeUtil.getGMTToEpochTime(c.getUpdatedTime()))
                            .parentApplication(appVsParent.getOrDefault(c.getIdentifier(), null))
                            .services(appServiceMap.getOrDefault(c.getIdentifier(), new ArrayList<>()))
                            .tags(appVsTags.getOrDefault(c.getIdentifier(), new ArrayList<>()))
                            .build())
                    .collect(Collectors.toList());

            LOGGER.trace("Time taken to fetch GetApplication list {} ms", System.currentTimeMillis() - time);

            return accessibleApps;
        } catch (ControlCenterException e) {
            throw new ControlCenterException("Exception while getting Application details. Reason:" + e.getMessage());
        }
    }

    private static List<ObjPojo> getServicesByAppId(int appId, int accountId, List<Controller> list) {
        int tagId = MasterCache.getTagDetails(Constants.CONTROLLER_TAG).getId();

        int typeId = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT,
                Constants.SERVICES_CONTROLLER_TYPE).getSubTypeId();

        Map<String, String> serviceNameMap = list.parallelStream()
                .filter(c -> c.getControllerTypeId() == typeId)
                .collect(Collectors.toMap(Controller::getAppId, Controller::getName));

        return MasterDataService
                .getTagMappingDetails(accountId)
                .parallelStream()
                .filter(t -> t.getObjectRefTable().equalsIgnoreCase(Constants.CONTROLLER) &&
                        t.getTagId() == tagId && t.getObjectId() == appId)
                .map(t -> ObjPojo.builder()
                        .id(Integer.parseInt(t.getTagKey()))
                        .name(serviceNameMap.get(t.getTagKey()))
                        .build())
                .collect(Collectors.toList());
    }

    public static String getUserID(Request request) throws RequestException {
        if (null == request.body() || (request.body().trim().isEmpty())) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            throw new RequestException(UIMessages.REQUEST_NULL);
        }

        return ValidationUtils.getUserId(request.headers(Constants.AUTHORIZATION));
    }

    // Add application and tag services
    public static AddApplication addClientValidations(Request request) throws RequestException {
        AddApplication app;

        if (null == request.body() || (request.body().trim().isEmpty())) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            throw new RequestException(UIMessages.REQUEST_NULL);
        }

        String user = getUserID(request);
        try {
            app = OBJECT_MAPPER.readValue(request.body(),
                    new TypeReference<>() {
                    });
            app.setUserId(user);
            app.setAccountIdentifier(request.params(Constants.ACCOUNT_IDENTIFIER));
        } catch (IOException e) {
            LOGGER.error(Constants.JSON_PARSE_ERROR + " : {}", e.getMessage());
            throw new RequestException(Constants.JSON_PARSE_ERROR);
        }
        if (!app.validate()) {
            throw new RequestException(UIMessages.INVALID_REQUEST);
        }
        return app;
    }

    public static ApplicationBean addServerValidations(AddApplication app) throws RequestException, ControlCenterException, IOException {
        UsersBL usersBL = new UsersBL();
        UserInfo user = usersBL.getUserDetails(app.getUserId());
        Set<Integer> validUserProfileIds = UserAccessDataService.getUserProfiles().parallelStream()
                .filter(p -> p.getName().equalsIgnoreCase("Super Admin") || p.getName().equalsIgnoreCase("Heal Admin"))
                .map(UserProfileBean::getId)
                .collect(Collectors.toSet());

        if (!validUserProfileIds.contains(user.getProfileId())) {
            throw new RequestException("User is not allowed to create an application. " +
                    "Only 'Super Admin' and 'Heal Admin' can create applications. " + app.getUserId());
        }

        AccountBean account = ValidationUtils.validAndGetAccount(app.getAccountIdentifier());
        if (account == null) {
            LOGGER.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new RequestException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }
        accountIdentifier = app.getAccountIdentifier();
        int accountId = account.getId();
        int appTypeId = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT,
                Constants.APPLICATION_CONTROLLER_TYPE).getSubTypeId();

        if (app.getParentApplication() == null || app.getParentApplication().getName().trim().isEmpty()) {
            LOGGER.warn("Parent Application name is Empty. Adding Application name:{} as Parent Application.", app.getName());
            app.setParentApplication(ParentApplication.builder().name(app.getName()).build());
        }

        List<Controller> list = MasterDataService.getAllControllerList();

        if (app.getIdentifier() == null || app.getIdentifier().trim().isEmpty())
            app.setIdentifier(UUID.randomUUID().toString());
        else {
            if (list.parallelStream().anyMatch(c -> c.getIdentifier().equalsIgnoreCase(app.getIdentifier()))) {
                LOGGER.error("Identifier already exists. {}", app.getIdentifier());
                throw new RequestException("Identifier already exists.");
            }
        }
        List<Controller> controllerList = list.parallelStream().filter(c -> c.getAccountId() == accountId).collect(Collectors.toList());

        if (controllerList.parallelStream().filter(c -> c.getControllerTypeId() == appTypeId)
                .anyMatch(c -> c.getName().equalsIgnoreCase(app.getName()))) {
            LOGGER.error("Application already exists. {}", app.getName());
            throw new RequestException("Application already exists.");
        }

        Optional<TimezoneDetail> timezone = MasterCache.getTimezones().parallelStream().
                filter(t -> t.getTimeZoneId().equalsIgnoreCase(app.getTimezone()))
                .findFirst();

        if (timezone.isEmpty()) {
            LOGGER.error("The given timezone is invalid : {}", app.getTimezone());
            throw new RequestException("Invalid timezone. Kindly check the logs.");
        }
        if (app.getTags() != null && !app.getTags().isEmpty()) {
            List<TagDetails> tagDetailsList = new TagDetailsRepo().getTagMappingDetails();
            for (ApplicationTags tag : app.getTags()) {
                TagDetails tagDetails = tagDetailsList.parallelStream()
                        .filter(tagDetail -> tagDetail.getName().equalsIgnoreCase(tag.getType()))
                        .findAny()
                        .orElse(null);
                if (tagDetails == null) {
                    LOGGER.error("Invalid Tag Type provided : {}", tag.getType());
                    throw new RequestException("Invalid Tag Type. Kindly check the logs.");
                }
            }
        }
        return ApplicationBean.builder()
                .name(app.getName().trim())
                .identifier(app.getIdentifier())
                .parentApplication(app.getParentApplication())
                .addServices(app.getServices() == null ? null : validateServicesAdd(app.getServices(), controllerList))
                .timezone(timezone.get())
                .tags(app.getTags() == null ? null : app.getTags())
                .accountId(accountId)
                .userId(app.getUserId())
                .build();
    }


    private static List<Controller> validateServicesAdd(List<String> serviceNameList, List<Controller> controllerList) throws RequestException {

        int serviceTypeId = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT,
                Constants.SERVICES_CONTROLLER_TYPE).getSubTypeId();

        Map<String, Controller> controllerMap = controllerList.parallelStream()
                .filter(c -> c.getControllerTypeId() == serviceTypeId)
                .collect(Collectors.toMap(c -> c.getIdentifier().toLowerCase(), c -> c));

        List<Controller> services = new ArrayList<>();
        List<String> invalidServices = serviceNameList.parallelStream()
                .filter(s -> {
                    if (controllerMap.containsKey(s.toLowerCase().trim())) {
                        services.add(controllerMap.get(s.toLowerCase().trim()));
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toList());

        if (!invalidServices.isEmpty()) {
            LOGGER.error("The following services are invalid : {}", invalidServices);
            throw new RequestException("Invalid add service list. Kindly check the logs.");
        }
        return services;
    }

    public IdPojo add(ApplicationBean bean) throws ControlCenterException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            IdPojo idPojo = dbi.inTransaction((conn, status) -> addApplication(bean, conn));
            if (idPojo != null) {
                addApplicationInRedis(bean, idPojo.getId());
            }
            return idPojo;
        } catch (Exception e) {
            throw new ControlCenterException(Throwables.getRootCause(e).getMessage());
        }
    }

    private IdPojo addApplication(ApplicationBean bean, Handle handle) throws ControlCenterException {
        TagsService tagsService = new TagsService();

        int id = tagsService.addController(bean.getAccountId(), bean.getName(), bean.getIdentifier(), bean.getUserId(),
                MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT,
                        Constants.APPLICATION_CONTROLLER_TYPE).getSubTypeId(), handle);

        int tagId = MasterCache.getTagDetails(Constants.CONTROLLER_TAG).getId();
        if (bean.getAddServices() != null) {
            bean.getAddServices()
                    .forEach(s -> TagMappingBL.addTagMapping(tagId, id, Constants.CONTROLLER, s.getAppId(),
                            s.getIdentifier(), bean.getUserId(), bean.getAccountId(), handle));
        }

        int parentId = bean.getParentApplication().getId();
        if (parentId == 0) {
            ParentApplicationBean parentApplicationBean =
                    ParentApplicationBean.builder()
                            .name(bean.getParentApplication().getName())
                            .identifier(UUID.randomUUID().toString())
                            .accountId(bean.getAccountId())
                            .userId(bean.getUserId())
                            .createdTime(new Timestamp(System.currentTimeMillis()))
                            .updatedTime(new Timestamp(System.currentTimeMillis()))
                            .status(1)
                            .build();
            parentId = addParentApplicationForApplication(parentApplicationBean, bean.getIdentifier(), accountIdentifier, handle);
            bean.setParentApplication(ParentApplication.builder()
                    .id(parentId)
                    .name(parentApplicationBean.getName())
                    .identifier(parentApplicationBean.getIdentifier())
                    .build());
        } else {
            addApplicationToExistingParentApplicationInRedis(bean, accountIdentifier);
        }

        if (bean.getParentApplication() != null && !bean.getParentApplication().getName().isEmpty()) {
            TagMappingBL.addTagMapping(Constants.PARENT_APPLICATION_TAG_ID, parentId, Constants.PARENT_APPLICATION_TAG,
                    id + "", bean.getIdentifier(), bean.getUserId(), bean.getAccountId(), handle);
        }

        List<Tags> tags = new ArrayList<>();
        tags.add(Tags.builder()
                .tagId(MasterCache.getTagDetails(Constants.TIME_ZONE_TAG).getId())
                .identifier(bean.getTimezone().getTimeZoneId())
                .name(Constants.TIME_ZONE_TAG)
                .value(String.valueOf(bean.getTimezone().getOffset()))
                .subTypeName(Constants.ACCOUNT_TAG)
                .build());

        tagsService.addTags(tags, bean.getAccountId(), id, Constants.CONTROLLER, bean.getUserId(), handle);

        if (bean.getTags() != null && !bean.getTags().isEmpty()) {
            bean.getTags().forEach(t ->
                    TagMappingBL.addTagMapping(Constants.SERVICE_TYPE_TAG_ID, id, Constants.CONTROLLER_TAG,
                            t.getKey(), t.getValue(), bean.getUserId(), bean.getAccountId(), handle));

        }

        long stTime = System.currentTimeMillis();
        addApplicationInstance(id, bean, handle);
        LOGGER.debug("Total time taken to insert application instance into DB and populate redis keys for serviceName: [{}], time: [{}]ms", bean.getName(), System.currentTimeMillis() - stTime);

        stTime = System.currentTimeMillis();
        addApplicationSettings(id, bean, handle);
        LOGGER.debug("Total time taken to insert application settings data into DB and populate redis keys for application: [{}], time: [{}]ms", bean.getName(), System.currentTimeMillis() - stTime);

        return IdPojo.builder()
                .id(id)
                .name(bean.getName())
                .identifier(bean.getIdentifier())
                .build();
    }

    private void addApplicationSettings(int id, ApplicationBean bean, Handle handle) throws ControlCenterException {
        List<com.heal.configuration.pojos.ViewTypes> types = new MasterDataRepo().getTypes();

        com.heal.configuration.pojos.ViewTypes viewType = types.stream()
                .filter(type -> type.getTypeName().equals(Constants.ANOMALY_SIGNAL_TYPE_NAME_DEFAULT) &&
                        type.getSubTypeName().equals(Constants.ANOMALY_SIGNAL_DESTINATION_DEFAULT))
                .findAny().orElse(null);

        ApplicationSettings applicationSettings = ApplicationSettings.builder()
                .applicationId(id)
                .ewSignalCloseTime(ewSignalCloseTime)
                .problemSignalCloseTime(problemSignalCloseTime)
                .infoSignalCloseTime(infoSignalCloseTime)
                .signalAnomalyTypeId(viewType != null ? viewType.getSubTypeId() : anomalyDestinationTypeId)
                .typeName(viewType != null ? viewType.getSubTypeName() : Constants.ANOMALY_SIGNAL_DESTINATION_DEFAULT)
                .status(1)
                .userDetailsId(bean.getUserId())
                .accountId(bean.getAccountId())
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .build();

        try {
            int appSettingsId = new ApplicationSettingsDataService().addApplicationSettings(applicationSettings, handle);
            if (appSettingsId == 0) {
                LOGGER.error("Error while inserting ApplicationSettings: [{}]", applicationSettings);
                throw new ControlCenterException("Error while inserting ApplicationSettings for application " + bean.getName());
            }

            applicationSettings.setId(appSettingsId);
            new ApplicationRepo().addApplicationSettings(accountIdentifier, bean.getIdentifier(), applicationSettings);
        } catch (Exception e) {
            throw new ControlCenterException(e.getMessage());
        }
    }

    private static List<Tags> getTags(List<Controller> addServices) {
        int tagId = MasterCache.getTagDetails(Constants.CONTROLLER_TAG).getId();
        return addServices.parallelStream()
                .map(c -> Tags.builder()
                        .tagId(tagId)
                        .identifier(c.getIdentifier())
                        .name(Constants.CONTROLLER_TAG)
                        .value(c.getIdentifier())
                        .subTypeName(Constants.SERVICES_CONTROLLER_TYPE)
                        .build())
                .collect(Collectors.toList());
    }

    public static EditController editClientValidations(Request request) throws RequestException {
        EditController app;

        if (request.body() == null || (null == request.body()) || (request.body().trim().isEmpty())) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            throw new RequestException(UIMessages.REQUEST_NULL);
        }

        String user = getUserID(request);
        try {
            app = OBJECT_MAPPER.readValue(request.body(),
                    new TypeReference<>() {
                    });
            app.setUserId(user);
            app.setAccountIdentifier(request.params(Constants.ACCOUNT_IDENTIFIER));
            app.setIdentifier(request.params(":applicationIdentifier"));
        } catch (IOException e) {
            LOGGER.error(Constants.JSON_PARSE_ERROR + " : {}", e.getMessage());
            throw new RequestException(Constants.JSON_PARSE_ERROR);
        } catch (Exception e) {
            LOGGER.error("Error in processing the request for : {} , {}", request.params(Constants.ACCOUNT_IDENTIFIER), request.params(":applicationIdentifier"));
            throw new RequestException("Error in processing the request. Kindly check the logs.");
        }

        if (!app.validate()) {
            throw new RequestException(UIMessages.INVALID_REQUEST);
        }
        return app;
    }

    public static ApplicationBean editServerValidations(EditController app) throws RequestException {

        AccountBean account = ValidationUtils.validAndGetAccount(app.getAccountIdentifier());
        if (account == null) {
            LOGGER.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new RequestException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }
        int accountId = account.getId();
        accountIdentifier = app.getAccountIdentifier();
        ViewTypes type;
        Optional<ViewTypes> types = MasterCache.getSubTypeDetails(Constants.CONTROLLER_TYPE_NAME_DEFAULT)
                .parallelStream()
                .filter(t -> Constants.APPLICATION_CONTROLLER_TYPE.equalsIgnoreCase(t.getSubTypeName()))
                .findAny();
        if (types.isPresent()) type = types.get();
        else throw new RequestException("ControllerType should either be 'Services' or 'Application'.");

        List<Controller> controllerList = MasterDataService.getControllerList(accountId);
        List<Controller> list = controllerList.parallelStream()
                .filter(c -> c.getControllerTypeId() == type.getSubTypeId())
                .collect(Collectors.toList());

        final String[] id = new String[1];
        if (list.parallelStream().noneMatch(c -> {
            if (c.getIdentifier().equalsIgnoreCase(app.getIdentifier())) {
                id[0] = c.getAppId();
                return true;
            }
            return false;
        })) {
            LOGGER.error("{} doesn't exist for the specified account.", type.getSubTypeName());
            throw new RequestException(type.getSubTypeName() + " doesn't exist for the specified account.");
        }
        int controllerId = Integer.parseInt(id[0]);

        serviceEditValidate(type.getSubTypeName(), app);

        if (app.getParentApplications() != null && !app.getParentApplications().isEmpty()) {
            new Applications().parentApplicationEditValidate(app);
        }

        if (app.getName() != null && !app.getName().trim().isEmpty()) {
            if (list.parallelStream().anyMatch(x -> x.getName().equals(app.getName().trim()))) {
                LOGGER.error("{} exists with the same name for the specified account.", type.getSubTypeName());
                throw new RequestException("Invalid name : " + type.getSubTypeName() + " exists with the same name for the specified account.");
            } else app.setName(app.getName().trim());
        }

        if (app.getTags() != null && !app.getTags().isEmpty()) {
            new Applications().applicationTagEditValidate(app);
        }

        return ApplicationBean.builder()
                .id(controllerId)
                .name(app.getName())
                .identifier(app.getIdentifier())
                .parentApplications(app.getParentApplications())
                .tags(app.getTags())
                .addServices(validateServicesAdd(app.getAddServices(), controllerList))
                .deleteServiceIds(validateServicesDelete(app.getDeleteServices(), controllerId, accountId, controllerList))
                .accountId(accountId)
                .userId(app.getUserId())
                .build();

    }

    private void parentApplicationEditValidate(EditController app) throws RequestException {
        LOGGER.info("Validating Parent Application mapped to the Application {}", app.getIdentifier());

        ParentApplicationRepo parentApplicationRepo = new ParentApplicationRepo();
        List<ParentApplication> parentApplications = parentApplicationRepo.getAllParentApplications(app.getAccountIdentifier());
        List<String> existingParentApplications = parentApplications
                .parallelStream()
                .map(ParentApplication::getName)
                .collect(Collectors.toList());
        List<String> newlyRequestedParentApplications = app.getParentApplications().parallelStream()
                .filter(a -> a.getAction().equalsIgnoreCase(Actions.UPDATE.toString()))
                .map(ParentApplication::getName)
                .collect(Collectors.toList());
        boolean nameAlreadyExists = existingParentApplications.parallelStream().anyMatch(newlyRequestedParentApplications::contains);
        if (nameAlreadyExists) {
            LOGGER.error("Parent Application name already exists.");
            throw new RequestException("Parent Application name already exists.");
        }
        List<String> actionsList = app.getParentApplications().parallelStream().map(ParentApplication::getAction).collect(Collectors.toList());

        if (actionsList.contains(Actions.DELETE.toString()) && !actionsList.contains(Actions.ADD.toString())) {
            LOGGER.error("Application {} must be mapped to at least one Parent Application", app.getIdentifier());
            throw new RequestException("Error while editing Parent Application mapped to Application. Kindly check the logs");
        }

        if (actionsList.contains(Actions.UPDATE.toString()) && actionsList.contains(Actions.ADD.toString())) {
            LOGGER.error("Multiple Parent Application cant be mapped to single Application {}", app.getIdentifier());
            throw new RequestException("Error while editing Parent Application mapped to Application.");
        }
        int addCount = 0;

        for (ParentApplication parentApplication : app.getParentApplications()) {
            String action = parentApplication.getAction();
            String identifier = parentApplication.getIdentifier();

            if (!Actions.ADD.toString().equalsIgnoreCase(action)
                    && !Actions.DELETE.toString().equalsIgnoreCase(action)
                    && !Actions.UPDATE.toString().equalsIgnoreCase(action)) {
                LOGGER.info("Invalid Action Type provided {}", action);
                throw new RequestException("Invalid Action Type provided.");
            }

            boolean isMappedToApp = parentApplications.parallelStream()
                    .anyMatch(a -> a.getIdentifier().equals(identifier) && a.getApplicationIdentifiers().contains(app.getIdentifier()));

            if (Actions.ADD.toString().equalsIgnoreCase(action)) {
                if (addCount != 0) {
                    LOGGER.error("Multiple Parent Applications cannot be mapped to single Application {}", app.getIdentifier());
                    throw new RequestException("Multiple parent applications cannot be mapped to single application");
                } else if (isMappedToApp) {
                    LOGGER.error("Parent application {} is already mapped to application {}.", identifier, app.getIdentifier());
                    throw new RequestException("Parent application is already mapped to application");
                } else {
                    addCount++;
                }
            } else if (Actions.DELETE.toString().equalsIgnoreCase(action)
                    || Actions.UPDATE.toString().equalsIgnoreCase(action)) {
                if (!isMappedToApp) {
                    LOGGER.error("Parent application {} cannot be edited/deleted. Reason: It is either does not exist or it is not mapped to application {}.", parentApplication.getName(), app.getIdentifier());
                    throw new RequestException("Parent application either does not exist or it not mapped to application");
                }
            }
        }
    }

    private void applicationTagEditValidate(EditController app) throws RequestException {
        LOGGER.info("Validating Tags Mapped to the Application {}", app.getIdentifier());

        List<ApplicationTags> newTagsList = app.getTags();

        com.heal.configuration.pojos.Application application = new ApplicationRepo().getApplicationForIdentifier(app.getAccountIdentifier(), app.getIdentifier());
        List<com.heal.configuration.pojos.Tags> existingTagsForApplication = application.getTags();

        if (!newTagsList.isEmpty()) {
            List<TagDetails> tagDetailsList = new TagDetailsRepo().getTagMappingDetails();
            Map<String, TagDetails> tagTypeVsDetails = tagDetailsList.parallelStream().collect(Collectors.toMap(TagDetails::getName, Function.identity()));

            for (ApplicationTags tag : newTagsList) {
                if (!tag.getAction().equalsIgnoreCase(Actions.ADD.toString()) && !tag.getAction().equalsIgnoreCase(Actions.DELETE.toString())) {
                    LOGGER.error("Invalid tag action {} provided for the application {}", tag.getType(), app.getIdentifier());
                    throw new RequestException("Invalid request. Tags can either be added or deleted.");
                }

                TagDetails tagDetails = tagTypeVsDetails.getOrDefault(tag.getType(), null);
                if (tagDetails == null) {
                    LOGGER.error("Invalid tag type {} provided for the application {}", tag.getType(), app.getIdentifier());
                    throw new RequestException("Invalid request. Tag type is incorrect");
                }

                boolean isAppTagged = existingTagsForApplication.parallelStream()
                        .anyMatch(tags -> tags.getType().equalsIgnoreCase(tag.getType())
                                && tags.getKey().equalsIgnoreCase(tag.getKey())
                                && tags.getValue().equalsIgnoreCase(tag.getValue()));

                if (tag.getAction().equalsIgnoreCase(Actions.DELETE.toString()) && !isAppTagged) {
                    LOGGER.error("Tag {} cannot be deleted. Reason: It is not mapped to the application {}.", tag, app.getIdentifier());
                    throw new RequestException("Tag is not mapped to application");
                }

                if (tag.getAction().equalsIgnoreCase(Actions.ADD.toString()) && isAppTagged) {
                    LOGGER.error("Tag {} cannot be added. Reason: It is already mapped to the application {}.", tag, app.getIdentifier());
                    throw new RequestException("Tag is already mapped to application");
                }
            }
        }
    }

    private static void serviceEditValidate(String type, EditController app) throws RequestException {
        if (type.equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
            if (app.getName() == null || app.getName().trim().isEmpty())
                throw new RequestException("Name is not available in the request to modify the service.");
            if (!(app.getAddServices().isEmpty() && app.getDeleteServices().isEmpty()))
                throw new RequestException("AddServices and DeleteServices should not be present if " +
                        "ControllerType is 'Services'.");
        }
    }

    private static List<Integer> validateServicesDelete(List<String> deleteServicesList, int id, int accountId,
                                                        List<Controller> controllerList) throws RequestException {

        List<String> deleteServices = controllerList.parallelStream()
                .filter(c -> deleteServicesList.parallelStream().anyMatch(d -> d.equalsIgnoreCase(c.getIdentifier())))
                .map(Controller::getName)
                .collect(Collectors.toList());

        Map<String, Integer> map = getServicesByAppId(id, accountId, controllerList)
                .parallelStream()
                .collect(Collectors.toMap(c -> c.getName().toLowerCase(), ObjPojo::getId));
        List<Integer> services = new ArrayList<>();
        List<String> invalidServices = deleteServices.parallelStream()
                .filter(s -> {
                    if (map.containsKey(s.toLowerCase().trim())) {
                        services.add(map.get(s.toLowerCase().trim()));
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toList());

        if (!invalidServices.isEmpty()) {
            LOGGER.error("The following services are invalid : {}", invalidServices);
            throw new RequestException("Invalid delete service list. Kindly check the logs.");
        }
        return services;

    }

    public static int edit(ApplicationBean bean) throws ControlCenterException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            Integer id = dbi.inTransaction((conn, status) -> Applications
                    .editApp(bean, conn));
            if (id > 0) {
                editApplicationInRedis(bean);
                editParentApplicationInRedis(bean);
            }
            return id;
        } catch (Exception e) {
            throw new ControlCenterException(Throwables.getRootCause(e).getMessage());
        }
    }

    private static int editApp(ApplicationBean app, Handle handle) throws ControlCenterException {
        ControllerDataService controllerDataService = new ControllerDataService();
        Applications applications = new Applications();

        if (app.getParentApplications() != null && !app.getParentApplications().isEmpty()) {
            applications.editParentApplication(app, handle);
        }
        if (app.getName() != null && !app.getName().isEmpty())
            controllerDataService.editControllerName(app.getId(), app.getName(), DateTimeUtil.getTimeInGMT(System.currentTimeMillis()), handle);
        if (!app.getAddServices().isEmpty())
            new TagsService().addTags(getTags(app.getAddServices()), app.getAccountId(), app.getId(),
                    Constants.CONTROLLER, app.getUserId(), handle);
        if (!app.getDeleteServiceIds().isEmpty())
            new BindInDataService().deleteTagMapping(MasterCache.getTagDetails(Constants.CONTROLLER_TAG).getId(),
                    app.getId(), app.getDeleteServiceIds().parallelStream().mapToInt(i -> i).toArray(),
                    Constants.CONTROLLER, app.getAccountId(), handle);
        controllerDataService.updateControllerDetails(app.getId(), app.getUserId(),
                DateTimeUtil.getTimeInGMT(System.currentTimeMillis()), handle);

        if (app.getTags() != null && !app.getTags().isEmpty()) {
            applications.editTagsForApplication(app, handle);
        }

        return app.getId();
    }

    private void editTagsForApplication(ApplicationBean app, Handle handle) throws ControlCenterException {
        LOGGER.info("Editing tags mapped to the application {}", app.getIdentifier());

        try {
            List<ApplicationTags> tagsToBeRemoved = new ArrayList<>();

            for (ApplicationTags tag : app.getTags()) {
                if (tag.getAction().equalsIgnoreCase(Actions.ADD.toString())) {
                    TagMappingBL.addTagMapping(Constants.SERVICE_TYPE_TAG_ID, app.getId(), Constants.CONTROLLER_TAG,
                            tag.getKey(), tag.getValue(), app.getUserId(), app.getAccountId(), handle);
                }
                if (tag.getAction().equalsIgnoreCase(Actions.DELETE.toString())) {
                    TagsDataService.deleteAgentServiceTagMapping(Constants.SERVICE_TYPE_TAG_ID, app.getId(), Constants.CONTROLLER_TAG,
                            tag.getKey(), tag.getValue(), app.getAccountId(), handle);
                    tagsToBeRemoved.add(tag);
                }
            }
            app.getTags().removeAll(tagsToBeRemoved);

        } catch (Exception e) {
            LOGGER.error("Error while updating Tags for the Application {}. Reason : {}", app.getIdentifier(), e.getMessage());
            throw new ControlCenterException("Error while updating Tags for the Application");
        }
    }

    private void editParentApplication(ApplicationBean app, Handle handle) throws ControlCenterException {
        LOGGER.info("Editing parent application mapped to the application {}", app.getIdentifier());

        ParentApplicationDataService parentApplicationDataService = new ParentApplicationDataService();
        try {
            for (ParentApplication parentApplication : app.getParentApplications()) {
                app.setParentApplication(parentApplication);

                String action = parentApplication.getAction();

                if (Actions.UPDATE.toString().equalsIgnoreCase(action)) {
                    parentApplicationDataService.editParentApplication(parentApplication.getId(), parentApplication.getName(), app.getAccountId(), handle);
                }

                if (Actions.DELETE.toString().equalsIgnoreCase(action)) {
                    TagsDataService.deleteTagMappingByTagValueObjectIdAndTagId(Constants.PARENT_APPLICATION_TAG_ID, app.getParentApplication().getId(), app.getIdentifier(), app.getAccountId(), handle);
                }

                if (Actions.ADD.toString().equalsIgnoreCase(action)) {
                    int parentId = app.getParentApplication().getId();

                    if (parentId == 0) {
                        ParentApplicationBean parentApplicationBean = ParentApplicationBean.builder()
                                .name(parentApplication.getName())
                                .identifier(UUID.randomUUID().toString())
                                .accountId(app.getAccountId())
                                .userId(app.getUserId())
                                .createdTime(new Timestamp(System.currentTimeMillis()))
                                .updatedTime(new Timestamp(System.currentTimeMillis()))
                                .status(1)
                                .build();
                        parentId = addParentApplicationForApplication(parentApplicationBean, app.getIdentifier(), accountIdentifier, handle);
                        app.setParentApplication(ParentApplication.builder()
                                .id(parentId)
                                .name(parentApplicationBean.getName())
                                .identifier(parentApplicationBean.getIdentifier())
                                .build());
                    } else {
                        addApplicationToExistingParentApplicationInRedis(app, accountIdentifier);
                    }

                    TagMappingBL.addTagMapping(Constants.PARENT_APPLICATION_TAG_ID, parentId, Constants.PARENT_APPLICATION_TAG,
                            app.getId() + "", app.getIdentifier(), app.getUserId(), app.getAccountId(), handle);
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error while updating Parent Application for the Application {}", app.getIdentifier());
            throw new ControlCenterException("Error while updating Parent Application for the Application");
        }
    }

    private static void editParentApplicationInRedis(ApplicationBean app) {
        ParentApplicationRepo parentApplicationRepo = new ParentApplicationRepo();
        ApplicationRepo applicationRepo = new ApplicationRepo();
        List<com.heal.configuration.pojos.Application> applicationList = applicationRepo.getApplicationsForAccount(accountIdentifier);

        if (app.getParentApplications() == null) {
            LOGGER.warn("Parent application details not exists, update to redis will be skipped. Application:{}", app);
            return;
        }
        app.getParentApplications().parallelStream()
                .filter(parentApplication -> Actions.ADD.toString().equalsIgnoreCase(parentApplication.getAction())
                        || Actions.UPDATE.toString().equalsIgnoreCase(parentApplication.getAction()))
                .findFirst()
                .ifPresent(app::setParentApplication);

        List<ParentApplication> parentApplications = parentApplicationRepo.getAllParentApplications(accountIdentifier);

        parentApplications.parallelStream()
                .filter(e -> e.getId() == app.getParentApplication().getId())
                .findAny()
                .ifPresent(parentApp -> {
                    parentApp.setName(app.getParentApplication().getName());
                    parentApp.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                    parentApp.setLastModifiedBy(app.getUserId());
                    parentApplicationRepo.updateParentApplicationByIdentifier(parentApp, accountIdentifier);
                });

        app.getParentApplications().parallelStream()
                .filter(p -> p.getAction().equalsIgnoreCase(Actions.DELETE.toString()))
                .findAny()
                .flatMap(parentApplication -> parentApplications.parallelStream()
                        .filter(a -> a.getIdentifier().equalsIgnoreCase(parentApplication.getIdentifier()))
                        .findAny())
                .ifPresent(p -> p.getApplicationIdentifiers().remove(app.getIdentifier()));

        parentApplicationRepo.updateParentApplication(parentApplications, accountIdentifier);

        ParentApplication parentApplicationFromRepo = parentApplications
                .parallelStream()
                .filter(parentApplication -> parentApplication.getApplicationIdentifiers().contains(app.getIdentifier()))
                .findAny()
                .orElse(null);
        if (parentApplicationFromRepo != null) {
            LOGGER.info("Parent Application {} is mapped to the application {}", parentApplicationFromRepo.getName(), app.getName());
            for (String appIdentifier : parentApplicationFromRepo.getApplicationIdentifiers()) {
                LOGGER.info("Updating application {} redis key with the new parent application details", appIdentifier);
                com.heal.configuration.pojos.Application application = applicationRepo.getApplicationForIdentifier(accountIdentifier, appIdentifier);
                if (application != null) {
                    application.setParentApplication(parentApplicationFromRepo);
                    applicationList.parallelStream()
                            .filter(a -> a.getIdentifier().equalsIgnoreCase(appIdentifier))
                            .findFirst()
                            .ifPresent(a -> {
                                a.setParentApplication(parentApplicationFromRepo);
                                applicationRepo.updateApplicationDetailsForAccount(accountIdentifier, applicationList);
                            });
                    applicationRepo.updateApplication(accountIdentifier, application);
                } else {
                    LOGGER.error("The application detail not found for the application id:{}, name:{}", app.getId(), app.getName());
                }
            }
        }
    }

    private static void addApplicationInRedis(ApplicationBean bean, int id) throws ControlCenterException {
        ApplicationRepo applicationRepo = new ApplicationRepo();
        MasterDataRepo masterDataRepo = new MasterDataRepo();
        List<com.heal.configuration.pojos.Application> applicationDetails = applicationRepo.getApplicationsForAccount(accountIdentifier);
        if (applicationDetails.isEmpty()) {
            applicationDetails = new ArrayList<>();
        }

        List<com.heal.configuration.pojos.ViewTypes> types = masterDataRepo.getTypes();

        com.heal.configuration.pojos.ViewTypes viewType = types.parallelStream().filter(type -> type.getTypeName().equals(Constants.CONTROLLER_TYPE_NAME_DEFAULT) &&
                type.getSubTypeName().equals(Constants.APPLICATION_CONTROLLER_TYPE)).findAny().orElse(null);

        if (viewType == null) {
            LOGGER.error("Type details unavailable for type name {}", Constants.CONTROLLER_TYPE_NAME_DEFAULT);
            throw new ControlCenterException("Type details unavailable for type name " + Constants.CONTROLLER_TYPE_NAME_DEFAULT);
        }

        String percentileKpisSuffix = ConfProperties.getString(Constants.PERCENTILE_KPIS_IDENTIFIER_SUFFIX, Constants.PERCENTILE_KPIS_IDENTIFIER_SUFFIX_DEFAULT);
        String defaultPercentiles = ConfProperties.getString(Constants.APPLICATION_PERCENTILES_DEFAULT_CONFIGURATION, Constants.APPLICATION_PERCENTILES_DEFAULT_VALUES);
        String[] percentileKpis = defaultPercentiles.split(",");

        Map<String, Set<Double>> percentilesMap = new HashMap<>();
        for (String percentileKpiIdentifier : percentileKpis) {
            String[] percentileKpiIdentifierArr = percentileKpiIdentifier.trim().split("_");
            if (percentileKpiIdentifierArr[percentileKpiIdentifierArr.length - 1].equals(percentileKpisSuffix)) {
                Double percentile = Double.valueOf(percentileKpiIdentifierArr[percentileKpiIdentifierArr.length - 2]);

                String percentileKpi = Arrays.stream(percentileKpiIdentifierArr)
                        .limit(percentileKpiIdentifierArr.length - 2)
                        .collect(Collectors.joining("_"));

                percentilesMap.compute(percentileKpi, (o, n) -> {
                    if (n == null) {
                        n = new HashSet<>();
                    }

                    n.add(percentile);
                    return n;
                });
            }
        }

        Application application = Application.builder()
                .id(id)
                .name(bean.getName())
                .identifier(bean.getIdentifier())
                .parentApplication(bean.getParentApplication())
                .tags(bean.getTags() == null ? new ArrayList<>() : bean.getTags()
                        .stream()
                        .filter(Objects::nonNull)
                        .map(tag -> com.heal.configuration.pojos.Tags.builder()
                                .key(tag.getKey())
                                .type(tag.getType())
                                .value(tag.getValue())
                                .build())
                        .collect(Collectors.toList()))
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .lastModifiedBy(bean.getUserId())
                .status(1)
                .percentiles(percentilesMap)
                .timeOffset(bean.getTimezone().getOffset())
                .typeId(viewType.getSubTypeId())
                .build();

        applicationDetails.add(application);
        applicationRepo.updateApplicationDetailsForAccount(accountIdentifier, applicationDetails);

        applicationRepo.updateApplication(accountIdentifier, application);
        if (bean.getAddServices() != null && !bean.getAddServices().isEmpty()) {
            addServiceMappingForNewApplication(bean, application);
        }
    }

    private static void addApplicationToExistingParentApplicationInRedis(ApplicationBean bean, String accountIdentifier) {
        ParentApplicationRepo parentApplicationRepo = new ParentApplicationRepo();
        List<ParentApplication> parentApplications = parentApplicationRepo.getAllParentApplications(accountIdentifier);
        parentApplications.parallelStream().filter(e -> e.getId() == bean.getParentApplication().getId()).findAny().ifPresent(parentApp -> {
            List<String> applications = parentApp.getApplicationIdentifiers();
            applications.add(bean.getIdentifier());
            parentApp.setApplicationIdentifiers(applications);
            parentApplicationRepo.updateParentApplication(parentApplications, accountIdentifier);
            parentApplicationRepo.updateParentApplicationByIdentifier(parentApp, accountIdentifier);
        });
    }

    private static void addServiceMappingForNewApplication(ApplicationBean bean, BasicEntity newApplication) {
        ServiceRepo serviceRepo = new ServiceRepo();
        ApplicationRepo applicationRepo = new ApplicationRepo();
        List<BasicEntity> newMappingServiceList = new ArrayList<>();
        for (Controller serviceToAdd : bean.getAddServices()) {
            newMappingServiceList.add(BasicEntity.builder()
                    .status(serviceToAdd.getStatus())
                    .id(Integer.parseInt(serviceToAdd.getAppId()))
                    .name(serviceToAdd.getName())
                    .identifier(serviceToAdd.getIdentifier())
                    .accountId(serviceToAdd.getAccountId())
                    .lastModifiedBy(serviceToAdd.getCreatedBY())
                    .updatedTime(serviceToAdd.getUpdatedTime())
                    .createdTime(serviceToAdd.getCreatedOn())
                    .build());
            List<BasicEntity> applicationsByServiceIdentifier = serviceRepo.getApplicationsByServiceIdentifier(accountIdentifier, serviceToAdd.getIdentifier());
            if (applicationsByServiceIdentifier.isEmpty()) {
                applicationsByServiceIdentifier = new ArrayList<>();
            }
            applicationsByServiceIdentifier.add(newApplication);
            serviceRepo.updateApplicationsByServiceIdentifier(accountIdentifier, serviceToAdd.getIdentifier(), applicationsByServiceIdentifier);
        }
        applicationRepo.updateServiceApplication(accountIdentifier, newApplication.getIdentifier(), newMappingServiceList);
    }

    private static void editApplicationInRedis(ApplicationBean bean) {
        LOGGER.info("Updating application {} in redis", bean.getIdentifier());

        ApplicationRepo applicationRepo = new ApplicationRepo();
        ServiceRepo serviceRepo = new ServiceRepo();

        List<com.heal.configuration.pojos.Application> applicationDetails = applicationRepo.getApplicationsForAccount(accountIdentifier);
        com.heal.configuration.pojos.Application app = applicationRepo.getApplicationForIdentifier(accountIdentifier, bean.getIdentifier());
        if (app == null) {
            LOGGER.error("The application detail not found for the applicationId: {}", bean.getId());
            return;
        }

        app.setName((bean.getName() != null && !bean.getName().trim().isEmpty()) ? bean.getName() : app.getName());
        if (bean.getTags() != null && !bean.getTags().isEmpty()) {
            app.setTags(bean.getTags()
                    .stream()
                    .map(tag -> com.heal.configuration.pojos.Tags.builder()
                            .key(tag.getKey())
                            .type(tag.getType())
                            .value(tag.getValue())
                            .build())
                    .collect(Collectors.toList()));
        }

        app.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        app.setLastModifiedBy(bean.getUserId());

        if (bean.getParentApplication() != null) {
            app.setParentApplication(bean.getParentApplication());
        }

        if (bean.getParentApplications() != null) {
            bean.getParentApplications()
                    .parallelStream()
                    .filter(parentApp -> parentApp.getAction().equalsIgnoreCase(Actions.ADD.toString())).findAny().ifPresent(app::setParentApplication);
        }

        applicationRepo.updateApplication(accountIdentifier, app);

        applicationDetails.parallelStream()
                .filter(e -> e.getIdentifier().equals(bean.getIdentifier()))
                .findAny()
                .ifPresent(newApp -> {
                    if (bean.getParentApplication() != null) {
                        newApp.setParentApplication(bean.getParentApplication());
                    }
                    if (bean.getParentApplications() != null) {
                        bean.getParentApplications()
                                .parallelStream()
                                .filter(parentApp -> parentApp.getAction().equalsIgnoreCase(Actions.ADD.toString())).findAny().ifPresent(newApp::setParentApplication);
                    }

                    newApp.setName((bean.getName() != null && !bean.getName().trim().isEmpty()) ? bean.getName() : newApp.getName());

                    if (!bean.getTags().isEmpty()) {
                        newApp.setTags(bean.getTags()
                                .stream()
                                .map(tag -> com.heal.configuration.pojos.Tags.builder()
                                        .key(tag.getKey())
                                        .type(tag.getType())
                                        .value(tag.getValue())
                                        .build())
                                .collect(Collectors.toList()));
                    }
                });
        if (bean.getParentApplications() != null && bean.getParentApplication() != null && bean.getParentApplication().getAction().equalsIgnoreCase(Actions.UPDATE.toString())) {
            for (com.heal.configuration.pojos.Application a : applicationDetails) {
                if (a.getParentApplication() != null &&
                        a.getParentApplication().getIdentifier().equals(bean.getParentApplication().getIdentifier())) {
                    a.setParentApplication(bean.getParentApplication());
                }
            }
        }
        applicationRepo.updateApplicationDetailsForAccount(accountIdentifier, applicationDetails);


        List<BasicEntity> servicesMappedToApplication = applicationRepo.getServicesMappedToApplication(accountIdentifier, app.getIdentifier());

        for (BasicEntity service : servicesMappedToApplication) {
            List<BasicEntity> applicationsByServiceIdentifier = serviceRepo.getApplicationsByServiceIdentifier(accountIdentifier, service.getIdentifier());
            BasicEntity basicEntityOfApplication = applicationsByServiceIdentifier.parallelStream()
                    .filter(a -> a.getIdentifier().equalsIgnoreCase(app.getIdentifier())).findAny().orElse(null);
            if (basicEntityOfApplication != null) {
                basicEntityOfApplication.setName(bean.getName() != null ? bean.getName() : basicEntityOfApplication.getName());
                basicEntityOfApplication.setLastModifiedBy(bean.getUserId());
                basicEntityOfApplication.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));

                serviceRepo.updateApplicationsByServiceIdentifier(accountIdentifier, service.getIdentifier(), applicationsByServiceIdentifier);
            }
        }
        if (bean.getAddServices() != null && !bean.getAddServices().isEmpty()) {
            addServiceMappingToExistingApplication(bean, app);
        }
        if (bean.getDeleteServiceIds() != null && !bean.getDeleteServiceIds().isEmpty()) {
            deleteServiceMappingToApplication(bean, app);
        }
    }

    private static void addServiceMappingToExistingApplication(ApplicationBean bean, com.heal.configuration.pojos.Application application) {
        ApplicationRepo applicationRepo = new ApplicationRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        List<BasicEntity> servicesMappedToApplication = applicationRepo.getServicesMappedToApplication(accountIdentifier, application.getIdentifier());
        for (Controller serviceToAdd : bean.getAddServices()) {
            if (servicesMappedToApplication.parallelStream().anyMatch(f -> f.getIdentifier().equalsIgnoreCase(serviceToAdd.getIdentifier()))) {
                LOGGER.error("The given serviceIdentifier: {} already mapped to the applicationIdentifier: {} ", serviceToAdd.getIdentifier(), bean.getIdentifier());
            } else {
                servicesMappedToApplication.add(BasicEntity.builder()
                        .status(serviceToAdd.getStatus())
                        .id(Integer.parseInt(serviceToAdd.getAppId()))
                        .name(serviceToAdd.getName())
                        .identifier(serviceToAdd.getIdentifier())
                        .accountId(serviceToAdd.getAccountId())
                        .lastModifiedBy(serviceToAdd.getCreatedBY())
                        .updatedTime(serviceToAdd.getUpdatedTime())
                        .createdTime(serviceToAdd.getCreatedOn())
                        .build());
                List<BasicEntity> applicationsByServiceIdentifier = serviceRepo.getApplicationsByServiceIdentifier(accountIdentifier, serviceToAdd.getIdentifier());
                if (applicationsByServiceIdentifier.isEmpty()) {
                    applicationsByServiceIdentifier = new ArrayList<>();
                }
                if (applicationsByServiceIdentifier.parallelStream().anyMatch(appDetail -> appDetail.getIdentifier().equalsIgnoreCase(application.getIdentifier()))) {
                    LOGGER.error("The application details already mapped to the given serviceId : [{}]", serviceToAdd.getIdentifier());
                } else {
                    applicationsByServiceIdentifier.add(BasicEntity.builder()
                            .id(application.getId())
                            .name(application.getName())
                            .identifier(application.getIdentifier())
                            .build());
                    serviceRepo.updateApplicationsByServiceIdentifier(accountIdentifier, serviceToAdd.getIdentifier(), applicationsByServiceIdentifier);
                }
            }
        }
        applicationRepo.updateServiceApplication(accountIdentifier, application.getIdentifier(), servicesMappedToApplication);
    }

    private static void deleteServiceMappingToApplication(ApplicationBean bean, com.heal.configuration.pojos.Application application) {
        ApplicationRepo applicationRepo = new ApplicationRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        List<BasicEntity> servicesMappedToApplication = applicationRepo.getServicesMappedToApplication(accountIdentifier, application.getIdentifier());

        Map<Integer, BasicEntity> existingServiceDetails = serviceRepo.getAllServicesDetails(accountIdentifier).parallelStream().collect(Collectors.toMap(BasicEntity::getId, Function.identity()));

        for (Integer serviceId : bean.getDeleteServiceIds()) {
            servicesMappedToApplication.removeIf(service -> service.getId() == serviceId);
            BasicEntity service = existingServiceDetails.get(serviceId);
            if (service != null) {
                List<BasicEntity> serviceMappedApplications = serviceRepo.getApplicationsByServiceIdentifier(accountIdentifier, service.getIdentifier());
                if (!serviceMappedApplications.isEmpty()) {
                    serviceMappedApplications.removeIf(app -> app.getIdentifier().equalsIgnoreCase(application.getIdentifier()));
                    serviceRepo.updateApplicationsByServiceIdentifier(accountIdentifier, service.getIdentifier(), serviceMappedApplications);
                }
            }
        }
        applicationRepo.updateServiceApplication(accountIdentifier, application.getIdentifier(), servicesMappedToApplication);
    }

    public int addParentApplicationForApplication(ParentApplicationBean parentApplicationBean, String applicationIdentifier, String accountIdentifier, Handle handle) throws ControlCenterException {
        ParentApplicationDataService parentApplicationDataService = new ParentApplicationDataService();
        int id = parentApplicationDataService.addParentApplication(parentApplicationBean, handle);
        parentApplicationBean.setId(id);
        new AddParentApplicationBL().addParentApplicationInRedis(Collections.singletonList(parentApplicationBean), applicationIdentifier, accountIdentifier);
        return id;
    }

    public void addApplicationInstance(int applicationId, ApplicationBean bean, Handle handle) {

        long stTime = System.currentTimeMillis();
        try {
            ApplicationInstanceDataService applicationInstanceDataService = new ApplicationInstanceDataService();

            MasterComponentBean componentDetailsByName = ComponentDataService.getComponentDetailsByName(Constants.APPLICATION_KPIS_COMPONENT_NAME);

            if (componentDetailsByName == null) {
                LOGGER.warn("Component not found for componentName: [{}], skipping insertion in application instance related data", Constants.APPLICATION_KPIS_COMPONENT_NAME);
                return;
            }

            String createdTime = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());

            ApplicationInstanceDetails applicationInstanceDetails = ApplicationInstanceDetails.builder()
                    .applicationId(applicationId)
                    .accountId(bean.getAccountId())
                    .applicationIdentifier(bean.getIdentifier())
                    .name(bean.getName())
                    .identifier(String.format("%s_%s", bean.getIdentifier(), Constants.APPLICATION_KPIS_COMPONENT_NAME))
                    .commonVersionId(componentDetailsByName.getCommonVersionId())
                    .componentVersionId(componentDetailsByName.getComponentVersionId())
                    .componentId(componentDetailsByName.getId())
                    .componentName(Constants.APPLICATION_KPIS_COMPONENT_NAME)
                    .componentTypeId(componentDetailsByName.getComponentTypeId())
                    .componentTypeName(componentDetailsByName.getComponentTypeName())
                    .createdTime(createdTime)
                    .updatedTime(createdTime)
                    .status(1)
                    .lastModifiedBy(bean.getUserId())
                    .build();


            int appInstanceId = applicationInstanceDataService.insertApplicationInstance(applicationInstanceDetails, handle);

            if (appInstanceId == 0) {
                LOGGER.error("Error while inserting ApplicationInstance: [{}]", applicationInstanceDetails);
                throw new ControlCenterException("Error while inserting ApplicationInstance");
            }
            applicationInstanceDetails.setId(appInstanceId);

            KPIDataService kpiDataService = new KPIDataService();

            List<ViewCommonVersionKPIsBean> viewCommonVersionGroupKPIsData = MasterDataService.getViewCommonVersionGroupKPIsData(componentDetailsByName.getCommonVersionId(), bean.getAccountId());
            List<ViewCommonVersionKPIsBean> viewCommonVersionNonGroupKPIsData = MasterDataService.getViewCommonVersionNonGroupKPIsData(componentDetailsByName.getCommonVersionId(), bean.getAccountId());
            if (viewCommonVersionGroupKPIsData.isEmpty() && viewCommonVersionNonGroupKPIsData.isEmpty()) {
                LOGGER.warn("Kpi details not found for commonVersionId: [{}], accountId: [{}] ", componentDetailsByName.getCommonVersionId(), bean.getAccountId());
                return;
            }

            List<Integer> groupKpiIds = viewCommonVersionGroupKPIsData.stream()
                    .map(ViewCommonVersionKPIsBean::getKpiId)
                    .collect(Collectors.toList());

            List<Integer> nonGroupKpiIds = viewCommonVersionNonGroupKPIsData.stream()
                    .map(ViewCommonVersionKPIsBean::getKpiId)
                    .collect(Collectors.toList());

            nonGroupKpiIds.addAll(groupKpiIds);

            List<ProducerMapping> producerMappings = kpiDataService.getMappingsByKpiDetailsIds(nonGroupKpiIds, null);
            Map<Integer, ProducerMapping> producerVskpiMap = producerMappings.stream()
                    .collect(Collectors.toMap(ProducerMapping::getKpiId, Function.identity()));

            if (producerVskpiMap.isEmpty()) {
                LOGGER.warn("ProducerMapping not found for kpiDetailsIds: [{}]", groupKpiIds);
                return;
            }
            List<ApplicationInstanceKpiEntity> applicationInstanceKpiEntities = new ArrayList<>();
            if (viewCommonVersionNonGroupKPIsData.isEmpty()) {
                LOGGER.debug("Non group kpi not found for commonVersionId: [{}]", componentDetailsByName.getCommonVersionId());
            } else {
                applicationInstanceKpiEntities = viewCommonVersionNonGroupKPIsData.stream()
                        .map(kpi -> {
                            ProducerMapping mappingsByKpiDetailsId = producerVskpiMap.getOrDefault(kpi.getKpiId(), null);
                            if (mappingsByKpiDetailsId == null) {
                                LOGGER.warn("Producer not found for kpiId: [{}]", kpi.getKpiId());
                                return null;
                            }

                            return ApplicationInstanceKpiEntity.builder()
                                    .applicationInstanceId(appInstanceId)
                                    .producerKpiMappingId(mappingsByKpiDetailsId.getProducerMappingId())
                                    .collectionInterval(kpi.getDefaultCollectionInterval())
                                    .status(kpi.getStatus())
                                    .createdTime(createdTime)
                                    .updatedTime(createdTime)
                                    .userDetailsId(bean.getUserId())
                                    .id(kpi.getKpiId())
                                    .name(kpi.getKpiName())
                                    .identifier(kpi.getKpiIdentifier())
                                    .producerId(mappingsByKpiDetailsId.getProducerId())
                                    .notificationsEnabled(1)
                                    .build();
                        }).filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
            if (!applicationInstanceKpiEntities.isEmpty()) {
                applicationInstanceDataService.insertApplicationInstanceKpiDetailsList(applicationInstanceKpiEntities, handle);
            }

            List<CompInstanceKpiGroupDetailsBean> groupKpiDetails = new ArrayList<>();
            if (viewCommonVersionGroupKPIsData.isEmpty()) {
                LOGGER.debug("group kpi not found for commonVersionId: [{}]", componentDetailsByName.getCommonVersionId());
            } else {
                groupKpiDetails = viewCommonVersionGroupKPIsData.stream()
                        .map(kpi -> {
                            ProducerMapping mappingsByKpiDetailsId = producerVskpiMap.getOrDefault(kpi.getKpiId(), null);
                            if (mappingsByKpiDetailsId == null) {
                                LOGGER.warn("Producer not found for kpiId: [{}]", kpi.getKpiId());
                                return null;
                            }

                            int groupId = kpi.getKpiGroupId();
                            MasterKpiGroupBean groupBean = MasterCache.getGroupKpiDetailList(bean.getAccountId(), groupId);
                            if (groupBean == null) {
                                LOGGER.warn("No any group kpi is found in master kpi group details table for group id: [{}]", groupId);
                                return null;
                            }

                            if (groupBean.getDiscovery() != 1) {
                                return null;
                            }

                            return CompInstanceKpiGroupDetailsBean.builder()
                                    .compInstanceId(appInstanceId)
                                    .mstProducerKpiMappingId(mappingsByKpiDetailsId.getProducerMappingId())
                                    .collectionInterval(kpi.getDefaultCollectionInterval())
                                    .status(kpi.getStatus())
                                    .createdTime(createdTime)
                                    .updatedTime(createdTime)
                                    .userDetailsId(bean.getUserId())
                                    .id(kpi.getKpiId())
                                    .mstProducerId(mappingsByKpiDetailsId.getProducerId())
                                    .notification(1)
                                    .mstKpiGroupId(kpi.getKpiGroupId())
                                    .kpiGroupName(groupBean.getIdentifier())
                                    .isDiscovery(groupBean.getDiscovery())
                                    .attributeValue("ALL")
                                    .attributeStatus(kpi.getStatus())
                                    .aliasName("ALL")
                                    .isGroup(1)
                                    .build();
                        }).filter(Objects::nonNull)
                        .collect(Collectors.toList());

            }

            if (!groupKpiDetails.isEmpty()) {
                applicationInstanceDataService.insertApplicationInstanceGroupKpiDetailsList(groupKpiDetails, handle);
            }

            CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
            List<AttributesViewBean> attributesViewBeanList = compInstanceDataService.
                    getAttributeViewDataByComponentAndCommonVersion(componentDetailsByName.getId(),
                            componentDetailsByName.getCommonVersionId(), null);

            if (attributesViewBeanList.isEmpty()) {
                LOGGER.warn("attribute details not found for componentId: [{}], and commonVersionId: [{}]",
                        componentDetailsByName.getId(), componentDetailsByName.getCommonVersionName());
                return;

            }

            List<ApplicationInstanceAttributeBean> applicationInstanceAttributeBean = attributesViewBeanList.parallelStream().map(attributesViewBean -> ApplicationInstanceAttributeBean.builder()
                    .applicationInstanceId(appInstanceId)
                    .attributeValue(attributesViewBean.getDefaultValue())
                    .componentAttributeMappingId(attributesViewBean.getMstComponentAttributeMappingId())
                    .createdTime(createdTime)
                    .updatedTime(createdTime)
                    .userDetailsId(bean.getUserId())
                    .mstCommonAttributesId(attributesViewBean.getAttributeId())
                    .attributeName(attributesViewBean.getAttributeName())
                    .build()).collect(Collectors.toList());


            applicationInstanceDataService.insertApplicationInstanceAttribute(applicationInstanceAttributeBean, handle);
            LOGGER.debug("Total time taken to create application instance into db for serviceName: [{}], time: [{}]ms", bean.getName(), System.currentTimeMillis() - stTime);

            pushServiceInstanceIntoRedis(accountIdentifier, applicationInstanceDetails, applicationInstanceKpiEntities, groupKpiDetails, attributesViewBeanList);
        } catch (Exception e) {
            LOGGER.error("Exception occurred while pushing application Instance Into db for service Name: [{}], Details: ", bean.getName(), e);

        }


    }

    public void pushServiceInstanceIntoRedis(String accountIdentifier, ApplicationInstanceDetails applicationInstanceDetails,
                                             List<ApplicationInstanceKpiEntity> applicationInstanceKpiEntities,
                                             List<CompInstanceKpiGroupDetailsBean> groupKpiDetails, List<AttributesViewBean> attributesViewBeanList) {

        long stTime = System.currentTimeMillis();
        try {
            ApplicationInstanceRepo applicationInstanceRepo = new ApplicationInstanceRepo();

            List<ApplicationInstanceDetails> applicationInstances = applicationInstanceRepo.getApplicationInstances(accountIdentifier)
                    .parallelStream().filter(inst -> inst.getApplicationId() != applicationInstanceDetails.getApplicationId()).collect(Collectors.toList());

            applicationInstances.add(applicationInstanceDetails);
            applicationInstanceRepo.updateApplicationInstances(accountIdentifier, applicationInstances);

            applicationInstanceRepo.updateApplicationInstance(accountIdentifier, applicationInstanceDetails);

            List<ApplicationInstanceKpiEntity> groupKpiEntities = groupKpiDetails.parallelStream()
                    .map(kpi -> ApplicationInstanceKpiEntity.builder()
                            .identifier(kpi.getKpiGroupName())
                            .applicationInstanceId(kpi.getCompInstanceId())
                            .producerKpiMappingId(kpi.getMstProducerKpiMappingId())
                            .collectionInterval(kpi.getCollectionInterval())
                            .status(kpi.getStatus())
                            .createdTime(kpi.getCreatedTime())
                            .updatedTime(kpi.getUpdatedTime())
                            .userDetailsId(kpi.getUserDetailsId())
                            .id(kpi.getId())
                            .name(kpi.getKpiGroupName())
                            .producerId(kpi.getMstProducerId())
                            .notificationsEnabled(1)
                            .discovery(kpi.getIsDiscovery())
                            .groupId(kpi.getMstKpiGroupId())
                            .groupName(kpi.getKpiGroupName())
                            .groupIdentifier(kpi.getKpiGroupName())
                            .build())
                    .collect(Collectors.toList());

            applicationInstanceKpiEntities.addAll(groupKpiEntities);

            applicationInstanceKpiEntities.forEach(kpi -> {
                applicationInstanceRepo.updateApplicationInstanceKpiById(accountIdentifier, applicationInstanceDetails.getIdentifier(), kpi);
                applicationInstanceRepo.updateApplicationInstanceKpiByIdentifier(accountIdentifier, applicationInstanceDetails.getIdentifier(), kpi);

            });

            applicationInstanceRepo.updateApplicationInstanceWiseKpis(accountIdentifier, applicationInstanceDetails.getIdentifier(), applicationInstanceKpiEntities);

            List<InstanceAttributes> attributes = attributesViewBeanList.parallelStream().map(attributesViewBean -> InstanceAttributes.builder()
                    .attributeId(attributesViewBean.getAttributeId())
                    .attributeName(attributesViewBean.getAttributeName())
                    .attributeValue(attributesViewBean.getDefaultValue())
                    .status(attributesViewBean.getStatus())
                    .isUiVisible(attributesViewBean.getIsUiVisible())
                    .isCustom(attributesViewBean.getIsCustom())
                    .attributeType(attributesViewBean.getAttributeType())
                    .build()).collect(Collectors.toList());

            applicationInstanceRepo.updateApplicationInstanceWiseAttributes(accountIdentifier, applicationInstanceDetails.getIdentifier(), attributes);

        } catch (Exception e) {
            LOGGER.error("Exception occurred while pushing application Instance related keys Into Redis for service Name: [{}], Details: ", applicationInstanceDetails.getName(), e);
        }

        LOGGER.debug("Total time taken to create application instance related keys for serviceName: [{}], time: [{}]ms", applicationInstanceDetails.getName(), System.currentTimeMillis() - stTime);

    }
}
