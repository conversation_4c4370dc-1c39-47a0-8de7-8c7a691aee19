package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.MasterComponentBean;
import com.appnomic.appsone.controlcenter.beans.MasterComponentVersionBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ComputedKpiBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.KpiCategoryDetails;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.KpiDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ServiceKpiThreshold;
import com.appnomic.appsone.controlcenter.dao.opensearch.ServiceRepo;
import com.appnomic.appsone.controlcenter.dao.opensearch.ThresholdDataServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.StaticThresholdException;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ComponentUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.KpiDetails;
import com.heal.configuration.pojos.KpiViolationConfig;
import com.heal.configuration.pojos.opensearch.ServiceKpiThresholds;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class StaticThreshold {
    private StaticThreshold() {
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(StaticThreshold.class);
    private static final String SERVICE_ID = ":serviceId";
    private static final String KPI_TYPE = "kpiType";
    private static final String THRESHOLD_TYPE = ":threshold-type";
    private static final String ACCOUNT_STR = ":identifier";
    private static final String SUCCESS_TRANS_MSG = "Threshold value is successfully inserted into 'service_kpi_thresholds' table - {}:";

    private static Map<Integer,String> computedKpiDetails;

    public static List<StaticThresholdRules> clientValidation(Request request) throws StaticThresholdException {
        LOGGER.debug("Inside Update Client validation");
        validateRequestParameters(request);
        if (request.body() == null) {
            LOGGER.error("Request body can not be null.");
            throw new StaticThresholdException("Request body can not be null.");
        }
        List<StaticThresholdRules> sorRuleList;
        Set<StaticThresholdRules> validSet;
        try {
            sorRuleList = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(request.body(), new TypeReference<List<StaticThresholdRules>>() {
            });
        } catch (Exception e) {
            LOGGER.error("Exception encountered when parsing request body", e);
            throw new StaticThresholdException("Exception encountered when parsing request body");
        }

        if (sorRuleList == null) {
            LOGGER.error("Received empty list of availability Rule objects");
            throw new StaticThresholdException("Static thresholds are unavailable");

        }
        validSet = new HashSet<>(sorRuleList);
        if (sorRuleList.size() != validSet.size()) {
            LOGGER.error("duplicate static thresholds are defined for the same KPI");
            throw new StaticThresholdException("duplicate static thresholds are defined for the same KPI");
        }

        return sorRuleList;
    }

    public static void validateRequestParameters(Request request) throws StaticThresholdException {
        LOGGER.debug("Inside Client validation");
        String accountIdString = request.params(ACCOUNT_STR);
        StringBuilder errors = new StringBuilder();
        if (accountIdString == null || accountIdString.trim().length() == 0) {
            LOGGER.error("Account id should not be empty or null, Account id:{}", accountIdString);
            errors.append(UIMessages.ACCOUNT_EMPTY);
        }
        String serviceIdStr = request.params(SERVICE_ID);
        if (serviceIdStr.trim().length() == 0) {
            LOGGER.error("Service id should not be empty or null");
            errors.append(UIMessages.SERVICE_EMPTY_ERROR);
        }
        try {
            Integer.parseInt(serviceIdStr);
        } catch (NumberFormatException e) {
            LOGGER.error("Error occurred while converting the service id [{}]. Reason: {}", serviceIdStr, e.getMessage());
            errors.append(UIMessages.INVALID_SERVICE);
        }
        String kpiType = request.queryParams(KPI_TYPE);
        if (kpiType == null || kpiType.trim().isEmpty()) {
            LOGGER.error("Invalid kpiType. Reason: kpiType is undefined in the request.");
            errors.append(UIMessages.INVALID_KPI_TYPE);
        }
        String thresholdType = request.params(THRESHOLD_TYPE);
        if (Objects.isNull(thresholdType) || thresholdType.trim().isEmpty() || !thresholdType.equalsIgnoreCase("static")) {
            LOGGER.error("Invalid thresholdType. Reason: thresholdType is undefined in the request.");
            errors.append(UIMessages.INVALID_THRESHOLD_TYPE);
        }
        if (errors.length() > 0) {
            throw new StaticThresholdException(errors.toString());
        }
    }

    public static Controller serverValidation(Request request) throws StaticThresholdException {
        LOGGER.debug("Inside Server validation");
        String accountIdString = request.params(ACCOUNT_STR);
        String kpiType = request.queryParams(KPI_TYPE);
        ViewTypes kpi = MasterCache.getMstTypeForSubTypeName(Constants.KPI_TYPE, kpiType);

        if (kpi == null) {
            LOGGER.error("Invalid KPI type [{}]. Reason: KPI type should be one of Availability or Core.", kpiType);
            throw new StaticThresholdException(UIMessages.INVALID_KPI_TYPE);
        }

        int accountId = ValidationUtils.validAndGetIdentifier(accountIdString);
        //account id validation
        if (accountId == -1) {
            LOGGER.error("Invalid account identifier provided.");
            throw new StaticThresholdException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }
        //* Check the service ID in Controller table *//*
        String serviceIdStr = request.params(SERVICE_ID);
        int serviceIdInt = Integer.parseInt(serviceIdStr);

        Controller serviceDetails = ValidationUtils.getServiceDetails(accountId, serviceIdInt);
        if (Objects.isNull(serviceDetails)) {
            LOGGER.error("Invalid service id {} provided.", serviceIdStr);
            throw new StaticThresholdException(UIMessages.INVALID_SERVICE);
        }

        return serviceDetails;
    }

    public static List<StaticThresholdRules> processStaticThresholds(Controller service, String subTypeKpi, String accountIdentifier) {
        LOGGER.debug("Inside process method");
        int accountId = service.getAccountId();
        int serviceId = Integer.parseInt(service.getAppId());
        Set<StaticThresholdRules> staticThresholdByKpiId = new HashSet<>();
        long sst = System.currentTimeMillis();
        List<CompInstClusterDetails> serviceCompInstances = CommonUtils.getComponentClusterList(serviceId, accountId);
        LOGGER.debug("Total time taken to fetch serviceCompInstances from DB for accountId: [{}], serviceId: {},  {} ms", accountId, serviceId, (System.currentTimeMillis() - sst));
        if (serviceCompInstances.isEmpty()) {
            LOGGER.info("Component instances unavailable for the serviceId [{}] provided.", serviceId);
            return Collections.emptyList();
        }
        //get comp_id and common version id to find the KPIs
        ViewTypes kpiType = MasterCache.getMstTypeForSubTypeName(Constants.KPI_TYPE, subTypeKpi);
        KPIDataService kpiDataService = new KPIDataService();
        Map<Integer, KpiCategoryDetails> kpiVsCatgoryMap = MasterDataService.getKpiCategoryDetails().stream()
                .collect(Collectors.toMap(KpiCategoryDetails::getKpiId, Function.identity(), (rep, exist) -> rep));

        List<ServiceKpiThreshold> kpiThresholdList = ImportServicesDataService.getKpiThresholds(accountId, Integer.parseInt(service.getAppId()));
        List<StaticThresholdRules> missingThresholdRules = new ArrayList<>();
        Set<String> thresholdKpiStrIds = kpiThresholdList.stream()
                .map(ServiceKpiThreshold::getKpiId)
                .map(String::valueOf)
                .collect(Collectors.toSet());

        TabularResults allConfigureThresholdsKpiListsFromOS = new ServiceRepo().getAllConfigureThresholdsKpiListsFromOS(accountIdentifier, service.getIdentifier(), thresholdKpiStrIds);
        Map<Integer, List<String>> configuredThresholdsFromOS = extractResult(allConfigureThresholdsKpiListsFromOS);

        serviceCompInstances.forEach(compInstance -> {
            List<KpiDetailsBean> kpiDetailBeans = kpiDataService.getKpiList(compInstance.getCommonVersionId(), compInstance.getCompId(), null);
            if (kpiDetailBeans == null || kpiDetailBeans.isEmpty()) {
                LOGGER.info("Kpi Bean list is null for commonVersionId: {}, compId: {}", compInstance.getCommonVersionId(), compInstance.getCompId());
                return;
            }

            List<KpiDetailsBean> kpiDetailsBeanList = kpiDetailBeans.stream().filter(kpi -> kpi.getTypeId() == kpiType.getSubTypeId()).collect(Collectors.toList());
            LOGGER.info("Add service threshold for component instance.InstanceId :{}, ComponentId: {}", compInstance.getInstanceId(), compInstance.getCompId());

            staticThresholdByKpiId.addAll(getStaticThresholdByKpiId(accountId, kpiDetailsBeanList, kpiType,
                    kpiThresholdList, missingThresholdRules, kpiVsCatgoryMap, configuredThresholdsFromOS));
        });

        if (!missingThresholdRules.isEmpty()) {
            long st = System.currentTimeMillis();
            new ThresholdDataServiceRepo().insertBulkServiceKpiThresholdsIntoOS(missingThresholdRules, accountIdentifier, service.getIdentifier());
            LOGGER.debug("Total time taken to insert missing thresholds into OS for data: {} ms", (System.currentTimeMillis() - st));
        }
        return staticThresholdByKpiId.stream().sorted(Comparator.comparing(StaticThresholdRules::getCategoryName).thenComparing(StaticThresholdRules::getKpiName).thenComparing(StaticThresholdRules::getKpiLevel)).collect(Collectors.toList());
    }

    private static Set<StaticThresholdRules> getStaticThresholdByKpiId(Integer accountId, List<KpiDetailsBean> kpiDetailsBeanList,
                                                                       ViewTypes kpiType, List<ServiceKpiThreshold> kpiThresholdList,
                                                                       List<StaticThresholdRules> missingThresholdRules,
                                                                       Map<Integer, KpiCategoryDetails> kpiVsCatgoryMap,
                                                                       Map<Integer, List<String>> configuredThresholdsFromOS) {

        computedKpiDetails = getComputedKpiDetailsMap(accountId);

        Set<StaticThresholdRules> resultRules = new HashSet<>();
        kpiDetailsBeanList.forEach(kpi -> {
            Integer kpiId = kpi.getId();
            KpiCategoryDetails category = kpiVsCatgoryMap.get(kpiId);
            if (category == null) {
                LOGGER.error("KPI Category details is not found in DB for KpiId: [{}]", kpiId);
                return;
            }

            List<ServiceKpiThreshold> matchingThresholds = kpiThresholdList.stream()
                    .filter(threshold -> kpiId.equals(threshold.getKpiId()))
                    .collect(Collectors.toList());

            if (!matchingThresholds.isEmpty()) {

                if (matchingThresholds.size() == 1 && kpiType.getSubTypeName().equals(Constants.CORE_KPI_TYPE)) {
                    boolean clusters = matchingThresholds.get(0).getApplicableTo().equalsIgnoreCase("Clusters");
                    resultRules.add(populateStaticThresholdRulesByKpiDetail(!clusters, Constants.CORE_KPI_TYPE, kpi, category));
                }

                for (ServiceKpiThreshold threshold : matchingThresholds) {
                    StaticThresholdRules rule = populateStaticThresholdRules(category, threshold, kpi);
                    populateOperationTypesAndThresholdMaps(threshold, rule);
                    List<String> applicableTo = configuredThresholdsFromOS.getOrDefault(kpiId, Collections.emptyList());
                    if (!applicableTo.contains(threshold.getApplicableTo()) && rule.isGenerateAnomaly()) {
                        LOGGER.trace("Threshold missing in OS, inserting for KPI [{}], applicableTo [{}]", kpiId, threshold.getApplicableTo());
                        missingThresholdRules.add(rule);
                    } else {
                        LOGGER.trace("Threshold already exists in OS, skipping for KPI [{}], applicableTo [{}]", kpiId, threshold.getApplicableTo());
                    }
                    resultRules.add(rule);
                }
            } else {
                // Unmatched KPIs, generate static threshold rules
                if (Constants.CORE_KPI_TYPE.equals(kpiType.getSubTypeName())) {
                    resultRules.add(populateStaticThresholdRulesByKpiDetail(true, Constants.CORE_KPI_TYPE, kpi, category));
                    resultRules.add(populateStaticThresholdRulesByKpiDetail(false, Constants.CORE_KPI_TYPE, kpi, category));
                } else {
                    resultRules.add(populateStaticThresholdRulesByKpiDetail(false, Constants.AVAIL_KPI_TYPE, kpi, category));
                }
            }
        });

        return resultRules;
    }

    private static Map<Integer, List<String>> extractResult(TabularResults results) {
        Map<Integer, List<String>> kpiIdVSApplicableToMap = new HashMap<>();

        if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
            for (TabularResults.ResultRow resultRow : results.getRowResults()) {
                int kpiId = 0;
                String applicableTo = "";
                for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                    if (resultRowColumn.getColumnName().equalsIgnoreCase("kpiId")) {
                        kpiId = Integer.parseInt(resultRowColumn.getColumnValue());

                    }
                    if (resultRowColumn.getColumnName().equalsIgnoreCase("applicableTo")) {
                        applicableTo = resultRowColumn.getColumnValue();
                    }
                }

                List<String> exists = kpiIdVSApplicableToMap.getOrDefault(kpiId, new ArrayList<>());
                exists.add(applicableTo);
                kpiIdVSApplicableToMap.put(kpiId, exists);

            }
        }
        return kpiIdVSApplicableToMap;
    }

    private static Map<Integer, String> getComputedKpiDetailsMap(Integer accountId) {
        try {
            return new KPIDataService().getComputedExpression(accountId, null).parallelStream()
                    .collect(Collectors.toMap(ComputedKpiBean::getComputedKpiId, ComputedKpiBean::getDisplayFormula));
        } catch (ControlCenterException e) {
            LOGGER.error("Error while fetching computed KPI details for account [{}]", accountId);
        }

        return new HashMap<>();
    }

    private static StaticThresholdRules populateStaticThresholdRulesByKpiDetail(boolean isCluster, String kpiType, KpiDetailsBean kpiDetailsBean, KpiCategoryDetails kpiCategoryDetailBean) {
        int temp = kpiDetailsBean.getId();
        StaticThresholdRules staticThresholdRules = new StaticThresholdRules();
        staticThresholdRules.setKpiName(kpiDetailsBean.getName());
        staticThresholdRules.setKpiId(Integer.toString(temp));
        staticThresholdRules.setDataId(0);
        if (isCluster) {
            staticThresholdRules.setKpiLevel("clusters");
        } else {
            staticThresholdRules.setKpiLevel("instances");
        }
        staticThresholdRules.setCategoryId(kpiCategoryDetailBean.getId());
        staticThresholdRules.setCategoryName(kpiCategoryDetailBean.getName());
        staticThresholdRules.setKpiAttribute(Constants.ALL);
        staticThresholdRules.setKpiDataType(kpiDetailsBean.getDataType());
        staticThresholdRules.setKpiUnit(kpiDetailsBean.getMeasureUnits());

        if (computedKpiDetails != null && computedKpiDetails.containsKey(temp)) {
            staticThresholdRules.setComputedDetails(StaticThresholdRules.ComputedDetails.builder()
                    .formula(computedKpiDetails.get(temp))
                    .build());
        }

        staticThresholdRules.setUserRuleSeverity(false);
        staticThresholdRules.setSystemRuleSeverity(false);

        if(Constants.AVAIL_KPI_TYPE.equalsIgnoreCase(kpiType)) {
            staticThresholdRules.setUserDefinedOperationType("not equals");
            staticThresholdRules.setGenerateAnomaly(false);
            staticThresholdRules.setUserDefinedSOR(true);
        } else {
            staticThresholdRules.setGenerateAnomaly(true);
            staticThresholdRules.setUserDefinedSOR(false);
        }

        return staticThresholdRules;
    }

    private static void populateOperationTypesAndThresholdMaps(ServiceKpiThreshold kpiThreshold, StaticThresholdRules result) {
        if (kpiThreshold.getOperationTypeId() != null) {
            ViewTypes customOperationType = MasterCache.getMstSubTypeForSubTypeId(kpiThreshold.getOperationTypeId());
            if (customOperationType == null) {
                LOGGER.error("Invalid Operation Type for {} id.", kpiThreshold.getOperationTypeId());
                result.getErrorMessage().put("INVALID_OPERATION_TYPE", "Invalid Operation Type for " + kpiThreshold.getOperationTypeId());
                return;
            }

            if (Objects.isNull(OperationTypeEnum.findByType(customOperationType.getSubTypeName()))) {
                LOGGER.error("Invalid Operation Type for {} name.", customOperationType.getSubTypeName());
                result.getErrorMessage().put("INVALID_OPERATION_TYPE_NAME", "Invalid Operation Type for " + customOperationType.getSubTypeName());
                return;
            }
            result.setUserDefinedOperationType(customOperationType.getSubTypeName());
        }
        Map<String, Double> customThresholds = new HashMap<>();
        customThresholds.put(Constants.MAX_VALUE, kpiThreshold.getMaxThreshold());
        customThresholds.put(Constants.MIN_VALUE, kpiThreshold.getMinThreshold());
        result.setUserThresholds(customThresholds);
        Map<String, Double> systemThresholds = new HashMap<>();
        systemThresholds.put(Constants.MAX_VALUE, kpiThreshold.getSorMaxThreshold());
        systemThresholds.put(Constants.MIN_VALUE, kpiThreshold.getSorMinThreshold());
        result.setSystemThresholds(systemThresholds);
        if (kpiThreshold.getSorOperationTypeId() != null) {
            ViewTypes systemOperationType = MasterCache.getMstSubTypeForSubTypeId(kpiThreshold.getSorOperationTypeId());
            if (systemOperationType == null) {
                LOGGER.error("Invalid SOR Operation Type for {} id.", kpiThreshold.getOperationTypeId());
                result.getErrorMessage().put("INVALID_SOR_OPERATION_TYPE", "Invalid SOR Operation Type for " + kpiThreshold.getOperationTypeId());
                return;
            }

            if (Objects.isNull(OperationTypeEnum.findByType(systemOperationType.getSubTypeName()))) {
                LOGGER.error("Invalid SOR Operation Type for {} name.", systemOperationType.getSubTypeName());
                result.getErrorMessage().put("INVALID_SOR_OPERATION_TYPE_NAME", "Invalid SOR Operation Type for " + systemOperationType.getSubTypeName());
                return;
            }
            result.setSystemOperationType(systemOperationType.getSubTypeName());
        }
    }

    private static StaticThresholdRules populateStaticThresholdRules(KpiCategoryDetails kpiCategoryDetailBean, ServiceKpiThreshold kpiThreshold, KpiDetailsBean kpiDetailsBean) {
        StaticThresholdRules staticThresholdRules = new StaticThresholdRules();
        staticThresholdRules.setKpiName(kpiDetailsBean.getName());
        staticThresholdRules.setKpiId(kpiThreshold.getKpiId().toString());
        staticThresholdRules.setDataId(kpiThreshold.getId());
        staticThresholdRules.setKpiLevel(kpiThreshold.getApplicableTo());
        staticThresholdRules.setCategoryId(kpiCategoryDetailBean.getId());
        staticThresholdRules.setCategoryName(kpiCategoryDetailBean.getName());
        staticThresholdRules.setKpiAttribute(kpiThreshold.getKpiAttribute());
        staticThresholdRules.setKpiDataType(kpiDetailsBean.getDataType());
        staticThresholdRules.setStartTime(kpiThreshold.getStartTime());

        if (computedKpiDetails != null && computedKpiDetails.containsKey(kpiDetailsBean.getId())) {
            staticThresholdRules.setComputedDetails(StaticThresholdRules.ComputedDetails.builder()
                    .formula(computedKpiDetails.get(kpiDetailsBean.getId()))
                    .build());
        }

        staticThresholdRules.setKpiUnit(kpiDetailsBean.getMeasureUnits());
        if (kpiThreshold.getDefinedBy().equalsIgnoreCase(Constants.THRESHOLD_DEFINED_BY_USER)) {
            staticThresholdRules.setUserDefinedSOR(true);
        } else if (kpiThreshold.getDefinedBy().equalsIgnoreCase(Constants.THRESHOLD_DEFINED_BY_SYSTEM)) {
            staticThresholdRules.setUserDefinedSOR(false);
        }
        if (kpiThreshold.getStatus() == 1) {
            staticThresholdRules.setGenerateAnomaly(true);
        }

        if (kpiThreshold.getUserSeverity() == 1) {
            staticThresholdRules.setUserRuleSeverity(true);
        }
        if (kpiThreshold.getSystemSeverity() == 1) {
            staticThresholdRules.setSystemRuleSeverity(true);
        }

        return staticThresholdRules;
    }

    public static List<StaticThresholdRules> updateProcess(Request request, Controller service, String userId, List<StaticThresholdRules> sorRuleList) throws StaticThresholdException {

        List<StaticThresholdRules> validRulePojo = new ArrayList<>();
        List<StaticThresholdRules> invalidJsonObjects = new ArrayList<>();
        String accountIdString = request.params(ACCOUNT_STR);
        String kpiType = request.queryParams(KPI_TYPE);

        String isSystemString = request.queryParams("isSystem");
        boolean isSystem = Boolean.parseBoolean(isSystemString);

        ViewTypes kpi = MasterCache.getMstTypeForSubTypeName(Constants.KPI_TYPE, kpiType);
        List<StaticThresholdRules> allKpiLists = processStaticThresholds(service, kpi.getSubTypeName(), accountIdString);

        try {
            sorRuleList.forEach(rulesData -> {
                rulesData.validate(kpi.getSubTypeName(), isSystem);
                validKpiCheck(rulesData, service, allKpiLists);
                if (rulesData.getErrorMessage().size() != 0) {
                    invalidJsonObjects.add(rulesData);
                    LOGGER.info(rulesData.getErrorMessage().toString());
                } else {
                    validRulePojo.add(rulesData);
                }
            });

            List<StaticThresholdRules> addRulesList = new ArrayList<>();
            List<StaticThresholdRules> updateRulesList = new ArrayList<>();

            validRulePojo.forEach(rulePojo -> {
                if (rulePojo.getDataId() > 0) {
                    updateRulesList.add(rulePojo);
                } else {
                    addRulesList.add(rulePojo);
                }
            });

            Timestamp echoMilli;
            Timestamp echoMilliPlusOneMin;

            boolean offlineHeal = checkForOfflineHeal(userId);
            if (offlineHeal) {
                LOGGER.info("Installation Mode is offline for  User id: {}", userId);
                echoMilli = new Timestamp(0L);
                echoMilliPlusOneMin = echoMilli;
            } else {
                echoMilli = DateTimeUtil.getCurrentTimestampInGMT();
                echoMilliPlusOneMin = new Timestamp(DateTimeUtil.getCurrentTimestampInGMT().getTime() + 60000);
            }

            addRulesList.forEach(sorRule -> {
                ServiceKpiThreshold thresholdBean = populateServiceKpiThreshold(sorRule, service, echoMilli, userId, kpi, isSystem);
                int thresholdId = ImportServicesDataService.addStaticThreshold(thresholdBean, isSystem);
                createThresholdForOS(accountIdString, service, sorRule, echoMilli, true);
                LOGGER.info(SUCCESS_TRANS_MSG, thresholdId);

            });
            ServiceRepo serviceRepo = new ServiceRepo();
            updateRulesList.forEach(updateSorRule -> {
                String kpiId = updateSorRule.getKpiId();
                String applicableTo = updateSorRule.getKpiLevel();

                ServiceKpiThreshold updateThresholdBean = populateServiceKpiThreshold(updateSorRule, service, echoMilli, userId, kpi, isSystem);
                int thresholdId = ImportServicesDataService.updateStaticThreshold(updateThresholdBean, isSystem);

                LOGGER.info(SUCCESS_TRANS_MSG, thresholdId);
                List<ServiceKpiThresholds> configureKpiList = new ServiceRepo().getConfigureKpiList(accountIdString, service.getIdentifier(), kpiId, applicableTo);

                if (!configureKpiList.isEmpty()) {
                    configureKpiList.sort(Comparator.comparing(ServiceKpiThresholds::getStartTime).reversed());
                    ServiceKpiThresholds configKpiDetails = configureKpiList.get(0);
                    serviceRepo.updateThreshold(accountIdString, service.getIdentifier(), updateSorRule, "", configKpiDetails, echoMilli);
                }
                if (!echoMilli.equals(echoMilliPlusOneMin)) {
                    createThresholdForOS(accountIdString, service, updateSorRule, echoMilli, true);
                }
            });

            updateServiceKpiThresholdsInRedis(accountIdString, service, sorRuleList, echoMilli);

        } catch (Exception e) {
            String errorMsg = e.getMessage();
            LOGGER.error("Exception encountered while processing thresholds. Details: {}", errorMsg);
            throw new StaticThresholdException(errorMsg);
        }
        return invalidJsonObjects;
    }

    private static boolean checkForOfflineHeal(String userId) {
        if (userId != null) {
            InstallationAttributesDataService installationAttributesDataService = new InstallationAttributesDataService();
            return installationAttributesDataService.checkForOfflineHeal(userId);
        }
        return false;
    }

    private static void createThresholdForOS(String accountIdString, Controller service, StaticThresholdRules sorRule, Timestamp echoMilliPlusOneMin, boolean flag) {
        if (flag && sorRule.isGenerateAnomaly()) {
            Map<String, Double> thresholds;
            if (sorRule.isUserDefinedSOR()) {
                thresholds = sorRule.getUserThresholds();
            } else {
                thresholds = sorRule.getSystemThresholds();
            }
            ServiceRepo.createThreshold(accountIdString, service.getIdentifier(), sorRule, echoMilliPlusOneMin, "", thresholds);
        }
    }

    private static void validKpiCheck(StaticThresholdRules staticThresholdRules, Controller service, List<StaticThresholdRules> validKpis) {
        ServiceKpiThreshold temp = ImportServicesDataService.getStaticThreshold(staticThresholdRules, service.getAccountId(), Integer.parseInt(service.getAppId()));
        if (temp != null) {
            if (temp.getId() != staticThresholdRules.getDataId()) {
                staticThresholdRules.setDataId(temp.getId());
            }
        } else {
            long count = validKpis.stream().filter(kpis -> kpis.getKpiId().equals(staticThresholdRules.getKpiId()) && kpis.getKpiLevel().equals(staticThresholdRules.getKpiLevel())).count();
            if (count == 0) {
                staticThresholdRules.getErrorMessage().put("KPI_ID_ABSENT", "Input Json is invalid, reason provided is kpi id " + staticThresholdRules.getKpiId() + " is not configured with the service " + service.getAppId() + " .");
            }
        }

    }

    private static ServiceKpiThreshold populateServiceKpiThreshold(StaticThresholdRules updateSorRule, Controller service, Timestamp echoMilli, String userId, ViewTypes kpiType, boolean isSystem) {
        ServiceKpiThreshold updateThresholdBean = new ServiceKpiThreshold();
        updateThresholdBean.setId(updateSorRule.getDataId());
        updateThresholdBean.setKpiId(Integer.parseInt(updateSorRule.getKpiId()));
        updateThresholdBean.setAccountId(service.getAccountId());
        updateThresholdBean.setServiceId(Integer.parseInt(service.getAppId()));
        updateThresholdBean.setUpdatedTime(echoMilli);
        updateThresholdBean.setKpiAttribute(updateSorRule.getKpiAttribute());
        updateThresholdBean.setUserDetailsId(userId);
        updateThresholdBean.setApplicableTo(updateSorRule.getKpiLevel());
        updateThresholdBean.setCreatedTime(echoMilli);
        updateThresholdBean.setStartTime(echoMilli);

        if (updateSorRule.isUserDefinedSOR()) {

            LOGGER.debug("User defined SOR threshold defined");

            updateThresholdBean.setDefinedBy(Constants.THRESHOLD_DEFINED_BY_USER);
            if (kpiType.getSubTypeName().equals(Constants.CORE_KPI_TYPE) && updateSorRule.getUserDefinedOperationType() != null) {
                LOGGER.debug("CORE kpi type provided");

                Map<String, Double> thresholds = updateSorRule.getUserThresholds();
                Double max = thresholds.get(Constants.MAX_VALUE);
                Double min = thresholds.get(Constants.MIN_VALUE);
                ViewTypes operationType = MasterCache.getMstTypeForSubTypeName(Constants.OPERATIONS_TYPE, updateSorRule.getUserDefinedOperationType());
                updateThresholdBean.setOperationTypeId(operationType.getSubTypeId());
                if (max != null && min != null && max > min && operationType.getSubTypeName().equals(OperationTypeEnum.NOT_BETWEEN.getType())) {
                    updateThresholdBean.setMaxThreshold(max);
                    updateThresholdBean.setMinThreshold(min);
                } else if (min != null) {
                    updateThresholdBean.setMinThreshold(min);
                }
            } else if (kpiType.getSubTypeName().equals(Constants.AVAIL_KPI_TYPE)) {
                ViewTypes operationType = MasterCache.getMstTypeForSubTypeName(Constants.AVAILABILITY_OPERATIONS_TYPE, updateSorRule.getUserDefinedOperationType());
                updateThresholdBean.setOperationTypeId(operationType.getSubTypeId());
            }
        } else {
            LOGGER.debug("System defined SOR threshold defined");

            updateThresholdBean.setDefinedBy(Constants.THRESHOLD_DEFINED_BY_SYSTEM);
            //This two checks are because of following reasons:
            //1: isSystem is a flag which can differentiates between pipeline and UI.
            //2: A user can toggle userDefined to false from UI that means he doesn't want any user defined values.So we are nilling user threshold values.
            //3: System value can be inserted by pipeline as well and this is silent feature that doesn't do any effect in user defined values, so we are just updating system threshold values.

            if (isSystem && updateSorRule.getSystemOperationType() != null) {
                ViewTypes operationType = MasterCache.getMstTypeForSubTypeName(Constants.OPERATIONS_TYPE, updateSorRule.getSystemOperationType());
                Map<String, Double> thresholds = updateSorRule.getSystemThresholds();
                setSystemOperationIdAndThresholds(thresholds, operationType, updateThresholdBean);
            } else if (!isSystem) {
                updateThresholdBean.setMaxThreshold(null);
                updateThresholdBean.setMinThreshold(null);
                updateThresholdBean.setOperationTypeId(null);
            }
        }

        if (updateSorRule.isGenerateAnomaly()) {
            updateThresholdBean.setStatus(1);
        } else {
            updateThresholdBean.setStatus(0);
        }

        if (updateSorRule.isUserRuleSeverity()) {
            updateThresholdBean.setUserSeverity(1);
        } else {
            updateThresholdBean.setUserSeverity(0);
        }

        if (updateSorRule.isSystemRuleSeverity()) {
            updateThresholdBean.setSystemSeverity(1);
        } else {
            updateThresholdBean.setSystemSeverity(0);
        }

        return updateThresholdBean;
    }

    private static void setSystemOperationIdAndThresholds(Map<String, Double> thresholds, ViewTypes operationType, ServiceKpiThreshold updateThresholdBean) {
        updateThresholdBean.setSorOperationTypeId(operationType.getSubTypeId());
        Double max = thresholds.get(Constants.MAX_VALUE);
        Double min = thresholds.get(Constants.MIN_VALUE);
        updateThresholdBean.setOperationTypeId(operationType.getSubTypeId());
        if (max != null && min != null && max > min && operationType.getSubTypeName().equals(OperationTypeEnum.NOT_BETWEEN.getType())) {
            updateThresholdBean.setSorMaxThreshold(max);
            updateThresholdBean.setSorMinThreshold(min);
        } else if (min != null) {
            updateThresholdBean.setSorMinThreshold(min);
        }
    }

    public static void createKpiThresholdForAvailabilityKpi(ComponentInstance componentInstance, int accountId, String userId, Handle conn,
                                                            Map<String,IdPojo> controllerMap, Set<ServiceKpiThreshold> serviceKpiThresholdSet) throws ControlCenterException, ParseException {
        LOGGER.debug("Inside create availability kpi threshold method");

        String mstComponentName = componentInstance.getMstComponentName();
        String mstComponentVersion = componentInstance.getMstComponentVersion();
        MasterComponentBean masterComponentBean = ComponentUtils.validateMasterComponent(mstComponentName, componentInstance.getErrorMessage(), String.valueOf(accountId));
        MasterComponentVersionBean masterComponentVersionBean = ComponentUtils.validateMasterComponentVersion(mstComponentVersion, masterComponentBean.getId(), componentInstance.getErrorMessage(), String.valueOf(accountId));
        List<KpiDetailsBean> kpiDetailBeans = new KPIDataService().getKpiList(masterComponentVersionBean.getMstCommonVersionId(), masterComponentVersionBean.getMstComponentId(), null);
        if (kpiDetailBeans == null || kpiDetailBeans.isEmpty()) {
            LOGGER.info("Kpi Bean list is null for commonVersionId: {}, compId: {}", masterComponentVersionBean.getMstCommonVersionId(), masterComponentVersionBean.getMstComponentId());
            return;
        }
        ViewTypes availKpiType = MasterCache.getMstTypeForSubTypeName(Constants.KPI_TYPE, Constants.AVAIL_KPI_TYPE);
        List<Integer> serviceIds = getServiceIdFromIdentifier(componentInstance.getTags(), controllerMap);
        for (Integer serviceId : serviceIds) {
            if (serviceId == 0) {
                LOGGER.error("Service not valid in input Json.");
                throw new ControlCenterException("Service not valid in input Json");
            }
            Set<KpiDetailsBean> kpiDetailsBeanList;
            kpiDetailsBeanList = kpiDetailBeans.stream().filter(kpi -> kpi.getTypeId() == availKpiType.getSubTypeId()).collect(Collectors.toSet());
            kpiDetailBeans = filterRequiredKpi(kpiDetailsBeanList, accountId, serviceId);
            insertAvailKpiThreshold(kpiDetailBeans, serviceKpiThresholdSet, accountId, serviceId, userId, conn);
        }

    }

    private static void insertAvailKpiThreshold(List<KpiDetailsBean> kpiDetailsBeanList, Set<ServiceKpiThreshold> serviceKpiThresholdSet,
                                                int accountId, int serviceId, String userId, Handle conn) throws ParseException, ControlCenterException {
        Timestamp echoMilli;
        boolean offlineHeal = checkForOfflineHeal(userId);
        if (offlineHeal) {
            LOGGER.info("Installation Mode is offline for  User id: {}", userId);
            echoMilli = new Timestamp(0L);
        } else {
            echoMilli = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());

        }
        List<ServiceKpiThreshold> serviceKpiThresholds = new ArrayList<>();

        ViewTypes availabilityOperationType = MasterCache.getMstSubTypeForSubTypeName(OperationTypeEnum.NOT_EQUALS.getType());

        if(null == availabilityOperationType) {
            LOGGER.error("Operation type for availability KPI is not found. None of the availability KPIs " +
                    "for service [{}] will be added to the thresholds list.", serviceId);
            return;
        }

        for (KpiDetailsBean kpiDetailsBean : kpiDetailsBeanList) {
            ServiceKpiThreshold serviceKpiThreshold = ServiceKpiThreshold.builder()
                    .accountId(accountId)
                    .serviceId(serviceId)
                    .kpiId(kpiDetailsBean.getId())
                    .applicableTo(InstanceType.INSTANCES.getConfigDBName())
                    .createdTime(echoMilli)
                    .updatedTime(echoMilli)
                    .startTime(echoMilli)
                    .userDetailsId(userId)
                    .kpiAttribute(Constants.ALL)
                    .definedBy(Constants.THRESHOLD_DEFINED_BY_USER)
                    .status(1)
                    .userSeverity(0)
                    .systemSeverity(0)
                    .operationTypeId(availabilityOperationType.getSubTypeId())
                    .build();

            if(!serviceKpiThresholdSet.contains(serviceKpiThreshold)) {
                serviceKpiThresholds.add(serviceKpiThreshold);
                LOGGER.debug("About to insert availability kpi to the service_kpi_thresholds where account: {}, service: {}, kpiId: {}, kpiLevel:{}", accountId, serviceId, kpiDetailsBean.getId(), InstanceType.INSTANCES.getConfigDBName());
            }
        }
        if (!serviceKpiThresholds.isEmpty()) {
            int[] thresholdIds = ImportServicesDataService.addAvailabilityStaticThreshold(serviceKpiThresholds, false, conn);
            if (thresholdIds.length != 0 && thresholdIds[0] == -1) {
                String err = "Unable to add Availability Kpi.";
                LOGGER.error(err);
                throw new ControlCenterException(err);
            }
            LOGGER.info(SUCCESS_TRANS_MSG, thresholdIds);

            serviceKpiThresholdSet.addAll(serviceKpiThresholds);
        }
    }


    private static List<Integer> getServiceIdFromIdentifier(List<Tags> tags, Map<String,IdPojo> controllerMap) throws ControlCenterException {
        List<Integer> serviceIds = new ArrayList<>();
        for (Tags tag : tags) {
            if (tag.getName().equalsIgnoreCase(Constants.CONTROLLER) && tag.getSubTypeName().equalsIgnoreCase(Constants.SERVICES_CONTROLLER_TYPE)) {
                ControllerBean controllerBean = new ControllerDataService().getControllerByIdentifierOrName(tag.getIdentifier(), null, null);
                if (controllerBean == null) {
                    if(!controllerMap.containsKey(tag.getIdentifier())) {
                        LOGGER.error("Service details unavailable for identifier {}", tag.getIdentifier());
                        throw new ControlCenterException("Service not exist for identifier: " + tag.getIdentifier());
                    }

                    IdPojo id = controllerMap.get(tag.getIdentifier());
                    if(null == id) {
                        LOGGER.error("Service details unavailable for identifier {}", tag.getIdentifier());
                        throw new ControlCenterException("Service not exist for identifier: " + tag.getIdentifier());
                    }

                    serviceIds.add(id.getId());
                } else {
                    serviceIds.add(controllerBean.getId());
                }
            }
        }
        return serviceIds;
    }

    private static List<KpiDetailsBean> filterRequiredKpi(Set<KpiDetailsBean> kpiDetailsBeanList, int accountId, int serviceId) {
        List<KpiDetailsBean> finalKpiDetailsList = new ArrayList<>();
        ServiceKpiThreshold serviceKpiThreshold = ServiceKpiThreshold.builder()
                .serviceId(serviceId)
                .accountId(accountId)
                .applicableTo(InstanceType.INSTANCES.getConfigDBName())
                .build();

        List<Integer> serviceKpiThresholdList = ImportServicesDataService.getConfigkpiList(serviceKpiThreshold);
        if (serviceKpiThresholdList == null || serviceKpiThresholdList.isEmpty()) {
            finalKpiDetailsList = new ArrayList<>(kpiDetailsBeanList);
        } else {
            for (KpiDetailsBean kpi : kpiDetailsBeanList) {
                long presentKpi = serviceKpiThresholdList
                        .stream()
                        .filter(kpiId -> kpiId.equals(kpi.getId())).count();
                if (presentKpi == 0) {
                    finalKpiDetailsList.add(kpi);
                }
            }
        }

        return finalKpiDetailsList;
    }

    public static void updateServiceKpiThresholdsInRedis(String accountIdString, Controller service, List<StaticThresholdRules> sorRuleList, Timestamp echoMilli) {

        Double min = null;
        Double max = null;
        com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo serviceRepo = new com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo();
        for (StaticThresholdRules staticThresholdRule : sorRuleList) {
            KpiDetails serviceKPI = serviceRepo.getServiceKPI(accountIdString, service.getIdentifier(), Integer.parseInt(staticThresholdRule.getKpiId()));
            int generateAnomaly = staticThresholdRule.isGenerateAnomaly() ? 1 : 0;

            if (serviceKPI == null) {
                LOGGER.error("Service KPI Details for KPI ID {} does not exist in redis", staticThresholdRule.getKpiId());
                return;
            }

            if (staticThresholdRule.getSystemThresholds() != null && staticThresholdRule.getSystemThresholds().get(Constants.MIN_VALUE) != null) {
                min = Double.parseDouble(String.valueOf(staticThresholdRule.getSystemThresholds().get(Constants.MIN_VALUE)));
            }
            if (staticThresholdRule.getSystemThresholds() != null && staticThresholdRule.getSystemThresholds().get(Constants.MAX_VALUE) != null) {
                max = Double.parseDouble(String.valueOf(staticThresholdRule.getSystemThresholds().get(Constants.MIN_VALUE)));
            }

            KpiViolationConfig kpiViolationConfig = serviceKPI.getKpiViolationConfig().parallelStream().filter(f -> f.getApplicableTo().equalsIgnoreCase(staticThresholdRule.getKpiLevel())).findAny().orElse(null);

            if (kpiViolationConfig == null) {
                KpiViolationConfig newKPIViolationConfigToBeAdded = buildKPIViolationConfigObject(echoMilli, staticThresholdRule, min, max);
                serviceKPI.getKpiViolationConfig().add(newKPIViolationConfigToBeAdded);
            } else {
                setIntoKPIViolationConfigObject(echoMilli, min, max, staticThresholdRule, generateAnomaly, kpiViolationConfig);
            }
            serviceRepo.updateServiceKpiById(accountIdString, service.getIdentifier(), serviceKPI.getId(), serviceKPI);
            serviceRepo.updateServiceKpiByIdentifier(accountIdString, service.getIdentifier(), serviceKPI.getIdentifier(), serviceKPI);
        }
    }

    private static void setIntoKPIViolationConfigObject(Timestamp echoMilli, Double min, Double max, StaticThresholdRules staticThresholdRule, int generateAnomaly, KpiViolationConfig kpiViolationConfig) {
        kpiViolationConfig.setStatus(generateAnomaly);
        kpiViolationConfig.setGenerateAnomaly(generateAnomaly);
        SimpleDateFormat formatter = new SimpleDateFormat(Constants.DATE_TIME);
        formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
        kpiViolationConfig.setStartTime(formatter.format(echoMilli.getTime()));
        kpiViolationConfig.setSeverity(staticThresholdRule.isUserRuleSeverity() ? 1 : 0);

        if (staticThresholdRule.isUserDefinedSOR()) {
            setUserDefinedThreshold(staticThresholdRule, kpiViolationConfig);
        }
        else{
            setSystemDefinedThreshold(staticThresholdRule, kpiViolationConfig, min, max);
        }
    }

    private static void setSystemDefinedThreshold(StaticThresholdRules staticThresholdRule, KpiViolationConfig kpiViolationConfig, Double min, Double max) {
        kpiViolationConfig.setOperation(staticThresholdRule.getSystemOperationType());
        kpiViolationConfig.setMinThreshold(min);
        kpiViolationConfig.setMaxThreshold(max);
        kpiViolationConfig.setDefinedBy(Constants.THRESHOLD_DEFINED_BY_SYSTEM);
    }

    private static void setUserDefinedThreshold(StaticThresholdRules staticThresholdRule, KpiViolationConfig kpiViolationConfig) {
        if (staticThresholdRule.getUserThresholds().get(Constants.MIN_VALUE) != null) {
            kpiViolationConfig.setMinThreshold(Double.parseDouble(String.valueOf(staticThresholdRule.getUserThresholds().get(Constants.MIN_VALUE))));
            if(staticThresholdRule.getUserDefinedOperationType().equalsIgnoreCase(Constants.OPERATIONS_TYPE_GREATER_THAN) || staticThresholdRule.getUserDefinedOperationType().equalsIgnoreCase(Constants.OPERATIONS_TYPE_LESSER_THAN)){
                 kpiViolationConfig.setMaxThreshold(null);
            }
        }
        if (staticThresholdRule.getUserThresholds().get(Constants.MAX_VALUE) != null) {
            kpiViolationConfig.setMaxThreshold(Double.parseDouble(String.valueOf(staticThresholdRule.getUserThresholds().get(Constants.MAX_VALUE))));
        }
        kpiViolationConfig.setOperation(staticThresholdRule.getUserDefinedOperationType());
        kpiViolationConfig.setDefinedBy(Constants.THRESHOLD_DEFINED_BY_USER);
    }

    private static KpiViolationConfig buildKPIViolationConfigObject(Timestamp echoMilli, StaticThresholdRules staticThresholdRule, Double min, Double max) {
        boolean userDefinedSOR = staticThresholdRule.isUserDefinedSOR();
        Map<String, Double> userThresholds = staticThresholdRule.getUserThresholds();

        if (userThresholds != null && userThresholds.get(Constants.MAX_VALUE) != null) {
            max = Double.parseDouble(String.valueOf(userThresholds.get(Constants.MAX_VALUE)));
        }
        if(userThresholds != null && userThresholds.get(Constants.MIN_VALUE) != null){
            min = Double.parseDouble(String.valueOf(userThresholds.get(Constants.MIN_VALUE)));
        }
        int generateAnomaly = staticThresholdRule.isGenerateAnomaly() ? 1 : 0;

        SimpleDateFormat formatter = new SimpleDateFormat(Constants.DATE_TIME);
        formatter.setTimeZone(TimeZone.getTimeZone("UTC"));

        return KpiViolationConfig.builder()
                .operation(userDefinedSOR ?
                    staticThresholdRule.getUserDefinedOperationType() :
                    staticThresholdRule.getSystemOperationType())
                .minThreshold(min)
                .maxThreshold(max)
                .startTime(formatter.format(echoMilli.getTime()))
                .status(generateAnomaly)
                .generateAnomaly(generateAnomaly)
                .severity(staticThresholdRule.isUserRuleSeverity() ? 1 : 0)
                .applicableTo(staticThresholdRule.getKpiLevel())
                .definedBy(staticThresholdRule.isUserDefinedSOR() ?
                    Constants.THRESHOLD_DEFINED_BY_USER :
                    Constants.THRESHOLD_DEFINED_BY_SYSTEM)
                .build();
    }
}

