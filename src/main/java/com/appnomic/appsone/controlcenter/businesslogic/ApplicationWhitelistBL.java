package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.WhitelistSettingsBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.WhitelistDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.ApplicationWhitelist;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.WhitelistPojo;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Slf4j
public class ApplicationWhitelistBL implements BusinessLogic<List<ApplicationWhitelist>, UtilityBean<List<ApplicationWhitelist>>, WhitelistPojo> {

    private static final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    @Override
    public UtilityBean<List<ApplicationWhitelist>> clientValidation(RequestObject requestObject) throws ClientException {
        CommonUtils.basicRequestValidation(requestObject);

        List<ApplicationWhitelist> applicationWhitelist = null;
        if (!StringUtils.isEmpty(requestObject.getBody())) {
            try {
                applicationWhitelist = objectMapper.readValue(requestObject.getBody(), new TypeReference<List<ApplicationWhitelist>>() {
                });

                Optional<ApplicationWhitelist> invalidApps = applicationWhitelist.stream().filter(app -> app.getApplicationSuppressionInterval() < 0).findAny();

                if (invalidApps.isPresent())    {
                    log.error("Whitelist suppression interval {} cannot be negative", invalidApps.get().getApplicationSuppressionInterval());
                    throw new ClientException("Whitelist suppression interval cannot be negative: " + invalidApps.get().getApplicationSuppressionInterval());
                }
            } catch (IOException e) {
                log.error("IOException encountered while parsing request body. Details {}", e.getMessage());
                throw new ClientException(e.getMessage());
            }
        }

        return UtilityBean.<List<ApplicationWhitelist>>builder()
                .accountIdentifier(requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER))
                .authToken(requestObject.getHeaders().get(Constants.AUTHORIZATION))
                .pojoObject(applicationWhitelist).build();
    }

    @Override
    public UtilityBean<List<ApplicationWhitelist>> serverValidation(UtilityBean<List<ApplicationWhitelist>> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }
        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }
        int accountId = account.getId();
        if (utilityBean.getPojoObject()!=null) {
            List<ApplicationWhitelist> appNamesList = utilityBean.getPojoObject();
            for (ApplicationWhitelist app : appNamesList) {
                ControllerBean application = new ControllerDataService().getControllerByIdentifierOrName(null, app.getApplicationName(), null);
                if (application == null) {
                    log.error("Application Name [{}] is unavailable for account [{}]", app.getApplicationName(), accountId);
                    throw new ServerException("Application Name is not present for the specified account.");
                }
            }
        }
        utilityBean.setAccount(account);
        return utilityBean;
    }

    @Override
    public WhitelistPojo process(UtilityBean<List<ApplicationWhitelist>> bean) throws DataProcessingException {
        WhitelistDataService whitelistDataService = new WhitelistDataService();
        WhitelistPojo whitelistData = null;
        List<ApplicationWhitelist> whiteList = whitelistDataService.getWhitelist(bean.getAccount().getId());
        if (whiteList != null) {
            whitelistData = populateWhiteList(whiteList);
        }
        return whitelistData;
    }

    public String addWhitelist(UtilityBean<List<ApplicationWhitelist>> whitelistBeans) throws DataProcessingException {
        int[] keys;
        try {
            keys = new WhitelistDataService().updateApplicationWhitelist(whitelistBeans.getPojoObject());
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        return Arrays.toString(keys);
    }

    public String deleteWhitelist(List<ApplicationWhitelist> whitelistedEntries) throws DataProcessingException {
        int[] keys;
        try {
            keys = new WhitelistDataService().deleteApplicationWhitelist(whitelistedEntries);
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        return Arrays.toString(keys);
    }

    private WhitelistPojo populateWhiteList(List<ApplicationWhitelist> whiteList) {
        WhitelistPojo whitelistData = new WhitelistPojo();
        WhitelistSettingsBean whitelistSettings = WhitelistSettingsBL.getWhitelistSettings();
        whitelistData.setWhiteList(whiteList);
        whitelistData.setWhitelistActive(whitelistSettings.isWhitelistActive());
        return whitelistData;
    }
}