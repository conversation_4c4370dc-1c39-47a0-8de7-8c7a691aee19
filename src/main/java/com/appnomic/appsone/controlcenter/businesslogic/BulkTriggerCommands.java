package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.appnomic.appsone.controlcenter.beans.BulkCmdTriggerWrapper;
import com.appnomic.appsone.controlcenter.beans.CommandArgumentsBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentStatusDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CommandDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.RulesDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandDetailArgumentBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandTriggerBean;
import com.appnomic.appsone.controlcenter.dao.opensearch.AgentHealthStatusRepo;
import com.appnomic.appsone.controlcenter.dao.redis.*;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.BulkForensicCommandsPojo;
import com.appnomic.appsone.controlcenter.pojo.ForensicCmdTriggeredStatus;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.service.QueuePublisher;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import com.heal.configuration.entities.BasicAgentBean;
import com.heal.configuration.pojos.Agent;
import com.heal.configuration.pojos.BasicEntity;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class BulkTriggerCommands implements BusinessLogic<BulkForensicCommandsPojo, UtilityBean<BulkForensicCommandsPojo>, List<ForensicCmdTriggeredStatus>> {
    private static final Logger log = LoggerFactory.getLogger(BulkTriggerCommands.class);

    @Override
    public UtilityBean<BulkForensicCommandsPojo> clientValidation(RequestObject requestObject) throws ClientException {
        BulkForensicCommandsPojo triggerJimForensicCommands;

        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (requestObject.getBody() == null) {
            log.error(UIMessages.REQUEST_BODY_NULL_EMPTY);
            throw new ClientException(UIMessages.REQUEST_BODY_NULL_EMPTY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String accountIdString = requestObject.getParams().get(UIMessages.ACCOUNT_IDENTIFIER);

        if (StringUtils.isEmpty(accountIdString)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        try {
            triggerJimForensicCommands = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestObject.getBody(), new TypeReference<BulkForensicCommandsPojo>() {
            });
        } catch (JsonProcessingException e) {
            log.error(Constants.JSON_PARSE_ERROR + ". Details: request body: {}", requestObject.getBody(), e);
            throw new ClientException(Constants.JSON_PARSE_ERROR);
        }

        if (triggerJimForensicCommands == null) {
            log.error("Invalid input: The bulk forensic command is null.");
            throw new ClientException("Invalid input: The bulk forensic command is null.");
        }

        if (!triggerJimForensicCommands.validate()) {
            log.error(UIMessages.INVALID_REQUEST + ". Details: {}", triggerJimForensicCommands);
            throw new ClientException(UIMessages.INVALID_REQUEST);
        }

        return UtilityBean.<BulkForensicCommandsPojo>builder()
                .accountIdentifier(accountIdString)
                .authToken(authKey)
                .pojoObject(triggerJimForensicCommands)
                .build();
    }

    @Override
    public UtilityBean<BulkForensicCommandsPojo> serverValidation(UtilityBean<BulkForensicCommandsPojo> utilityBean) throws ServerException {
        try {
            String accountIdentifier = utilityBean.getAccountIdentifier();
            BulkForensicCommandsPojo inputCommand = utilityBean.getPojoObject();

            AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

            if (account == null) {
                log.error("Account identifier: {} is invalid", accountIdentifier);
                throw new ServerException("Account identifier is invalid");
            }

            String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

            if (userId == null) {
                log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
                throw new ServerException("Error while extracting user details from authorization token");
            }
            utilityBean.setUserId(userId);

            //Function below fetches command details by Agent Type
            Map<Integer, Integer> commandDetailsByJimAgentType = getCommandDetailsByJimAgentType();

            boolean isMicroService = inputCommand.getMicroserviceIdentifiers() != null && !inputCommand.getMicroserviceIdentifiers().isEmpty();
            List<String> serviceIdentifiers;

            if (isMicroService) {
                //Function below fetches all the services mapped to microservice.
                serviceIdentifiers = fetchServicesMappedToMicroService(inputCommand.getMicroserviceIdentifiers(),
                        inputCommand.getCommandIdentifier(),
                        accountIdentifier);
            } else {
                serviceIdentifiers = inputCommand.getServiceIdentifiers();
            }

            // Function below fetches JIM agents for services provided.
            List<String> jimAgentIdentifiers = fetchJimAgentsMappedToServices(serviceIdentifiers, accountIdentifier);

            long suppressionInMillis = (long) commandDetailsByJimAgentType.get(inputCommand.getCommandId()) * 60 * 1000;

            //Function below fetches instances mapped to the JIM agents provided.
            //Also verifies if the instances fall within the suppression interval. If it's within the interval, the command should be ignored; otherwise, the command should be triggered.
            Map<String, List<String>> agentInstancesMap = fetchInstancesMappedToAgents(jimAgentIdentifiers,
                    inputCommand.getCommandIdentifier(),
                    suppressionInMillis,
                    accountIdentifier);

            if(agentInstancesMap.isEmpty()) {
                log.error("The instances mapped to the following JIM agents {} are either inactive or fall within the command's suppression interval of {} ms. Please review the logs for further details.", jimAgentIdentifiers, suppressionInMillis);
                throw new ServerException(String.format("The instances mapped to the following JIM agents %s are either inactive or fall within the command's suppression interval of %s ms", jimAgentIdentifiers, suppressionInMillis));
            }

            inputCommand.setAgentInstances(agentInstancesMap);

            //Validate the incoming forensic action
            try {
                Map<String, String> argumentsMap = new HashMap<>();
                List<CommandArgumentsBean> requestCommandArguments = inputCommand.getCommandArguments();

                if (!commandDetailsByJimAgentType.containsKey(inputCommand.getCommandId())) {
                    log.error("Requested forensic action {} is not part of the actions configured for JIM agent", inputCommand.getCommandIdentifier());
                    throw new ServerException(String.format("Requested forensic action %s is not part of the actions configured for JIM agent", inputCommand.getCommandIdentifier()));
                }

                Map<Integer, List<CommandDetailArgumentBean>> commandIdVsArguments = CommandDataService.getAllCommandArguments()
                        .stream().collect(Collectors.groupingBy(CommandDetailArgumentBean::getCommandId));

                List<CommandDetailArgumentBean> cmdSpecificArgs = commandIdVsArguments.getOrDefault(inputCommand.getCommandId(), new ArrayList<>());

                //If cmdArgs exist for this commandId then check if input command arguments size and cmdArgs size are same.
                if (inputCommand.getCommandArguments().size() != cmdSpecificArgs.size()) {
                    log.error("Invalid number of command arguments provided in the request for the command {}. Please ensure all the command arguments are provided in the request.", inputCommand.getCommandId());
                    throw new ServerException(String.format("Invalid number of command arguments provided in the request for the command %s. Please ensure all the command arguments are provided in the request.", inputCommand.getCommandId()));
                }

                requestCommandArguments.forEach(args -> argumentsMap.put(args.getKey(), args.getValue()));
                inputCommand.setArgumentsMap(argumentsMap);
            } catch (ServerException e) {
                log.error("Request validation failed at server level, Examine logs for more details.", e);
                throw new ServerException("Request validation failed at server level, Examine logs for more details.");
            }

            utilityBean.setPojoObject(inputCommand);
            return utilityBean;
        } catch (Exception e) {
            log.error("Error occurred while validating the input request to trigger forensic commands", e);
            throw new ServerException("Error occurred while validating the input request to trigger forensic commands");
        }
    }

    @Override
    public List<ForensicCmdTriggeredStatus> process(UtilityBean<BulkForensicCommandsPojo> commands) throws DataProcessingException {
        String userId = commands.getUserId();
        String accountIdentifier = commands.getAccountIdentifier();
        BulkForensicCommandsPojo triggerCommand = commands.getPojoObject();
        List<ForensicCmdTriggeredStatus> forensicCommandTriggeredStatusBeanList;
        List<CommandRequestProtos.CommandRequest> commandRequests = new ArrayList<>();

        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        try {
            forensicCommandTriggeredStatusBeanList = dbi.inTransaction((conn, status) -> {
                List<BulkCmdTriggerWrapper> wrapperBean;
                List<ForensicCmdTriggeredStatus> beansList = new ArrayList<>();
                wrapperBean = processCommand(accountIdentifier, triggerCommand, userId, conn);

                for (BulkCmdTriggerWrapper forensicCommandTriggerWrapperBean : wrapperBean) {
                    beansList.add(forensicCommandTriggerWrapperBean.getForensicCmdTriggeredStatus());
                    commandRequests.add(forensicCommandTriggerWrapperBean.getCommandRequest());
                }

                long startTime = System.currentTimeMillis();
                log.info("Number of command requests are pushing into RMQ is {}.", commandRequests.size());

                for (CommandRequestProtos.CommandRequest commandRequest : commandRequests) {
                    QueuePublisher.sendAgentCommandMessage(commandRequest);
                }

                log.debug("Time taken to push {} command requests into RMQ is {}", commandRequests.size(), System.currentTimeMillis() - startTime);
                return beansList;
            });
        } catch (Exception e) {
            log.error("Error while triggering command. Reason: ", e);
            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw (DataProcessingException) Throwables.getRootCause(e);
            } else {
                throw e;
            }
        }

        return forensicCommandTriggeredStatusBeanList;
    }

    private List<BulkCmdTriggerWrapper> processCommand(String accountIdentifier, BulkForensicCommandsPojo bulkCommandPojo,
                                                       String userId, Handle conn) throws DataProcessingException {
        long ct = System.currentTimeMillis();
        try {
            CommandDetailsBean commandDetailsBean = CommandDataService.getCommandDetail(bulkCommandPojo.getCommandId(), conn);
            if (commandDetailsBean == null) {
                log.error("Default/Selected command not found for provided agentType {} to process the request.", bulkCommandPojo.getAgentType());
                throw new DataProcessingException("Default/Selected command not found to process the request.");
            }

            return bulkCommandPojo.getAgentInstances().keySet().stream().map(jimIdentifier -> {
                long iterationTime = System.currentTimeMillis();
                try {
                    return processAgentCommands(accountIdentifier, bulkCommandPojo, userId, jimIdentifier, commandDetailsBean, conn);
                } catch (Exception e) {
                    log.error("Error occurred while trying to process the command for jim agent {} of account {}", jimIdentifier, accountIdentifier, e);
                    return null;
                } finally {
                    log.debug("Time taken to process forensic command for jim agent {} is {}", jimIdentifier, System.currentTimeMillis() - iterationTime);
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());

        } finally {
            log.debug("Time taken by the processCommand function to execute bulk forensic commands: {}", System.currentTimeMillis() - ct);
        }
    }

    private BulkCmdTriggerWrapper processAgentCommands(String accountIdentifier, BulkForensicCommandsPojo bulkCommandPojo,
                                                       String userId, String agentIdentifier, CommandDetailsBean commandDetailsBean, Handle conn) throws DataProcessingException, ControlCenterException {
        long startTime = System.currentTimeMillis();

        AgentRepo agentRepo = new AgentRepo();
        Agent agentDetails = agentRepo.getAgentDetails(agentIdentifier);
        if (agentDetails == null) {
            log.error("Agent details for identifier {} not found in the Redis.", agentIdentifier);
            throw new DataProcessingException(String.format("Agent details for identifier %s not found in the Redis.", agentIdentifier));
        }

        int physicalAgentId = agentDetails.getPhysicalAgentId();
        String physicalAgentIdentifier = agentDetails.getPhysicalAgentIdentifier();

        String commandIdentifier = bulkCommandPojo.getCommandIdentifier();
        Map<String, List<String>> agentInstances = bulkCommandPojo.getAgentInstances();
        List<String> compInstanceIdentifier = agentInstances.get(agentIdentifier);

        log.info("Processing command Identifier {} triggered for agent {}", commandIdentifier, physicalAgentId);

        CommandTriggerBean commandTriggerBean = new CommandTriggerBean();
        commandTriggerBean.setCommandStatus(1);
        commandTriggerBean.setUserDetailsId(userId);
        commandTriggerBean.setAgentId(agentDetails.getId());
        commandTriggerBean.setPhysicalAgentIdentifier(physicalAgentId);
        commandTriggerBean.setCommandId(bulkCommandPojo.getCommandId());
        commandTriggerBean.setCommandJobId(String.valueOf(UUID.randomUUID()));
        commandTriggerBean.setTriggerTime(new Timestamp(new Date().getTime()));

        int agentTypeId = agentDetails.getTypeId();
        String agentType = agentDetails.getTypeName();

        Map<String, String> argumentsMap = bulkCommandPojo.getArgumentsMap();
        argumentsMap.put("Command", commandDetailsBean.getName());

        Map<String, String> metaData = new HashMap<>();
        metaData.put("accountId", accountIdentifier);
        metaData.put("instanceIdentifiers", compInstanceIdentifier.toString()); //Setting the instance identifier
        metaData.put("agentType", bulkCommandPojo.getAgentType()); //This flag is utilized in the supervisor controller to independently process Jim forensic actions.
        metaData.put("commandId", commandDetailsBean.getIdentifier());
        metaData.put("status", Constants.FORENSIC_ACTION_TRIGGER_STATUS); //Setting the status of jim forensic action to 'in-progress'

        ViewTypes commandTypeTypes = MasterCache.getMstTypeById(commandDetailsBean.getCommandTypeId());
        if (commandTypeTypes == null) {
            log.error("CommandType [{}] is invalid", commandDetailsBean.getCommandTypeId());
            throw new DataProcessingException(String.format("commandType %s is invalid", commandDetailsBean.getCommandTypeId()));
        }

        String commandType = commandTypeTypes.getTypeName();
        String commandExecType = commandTypeTypes.getSubTypeName();
        String commandOutputType = RulesDataService.getNameFromMSTSubType(commandDetailsBean.getOutputTypeId());

        CommandRequestProtos.Command command =
                CommandRequestProtos.Command.newBuilder()
                        .setRetryNumber(ConfProperties.getInt(Constants.COMMAND_RETRY_NUMBER, Constants.COMMAND_RETRY_NUMBER_DEFAULT))
                        .setSupervisorCtrlTTL(ConfProperties.getLong(Constants.SUPERVISOR_CONTROLLER_TTL, Constants.SUPERVISOR_CONTROLLER_TTL_DEFAULT))
                        .setCommandType(commandType)
                        .putAllArguments(argumentsMap)
                        .setCommandExecType(commandExecType)
                        .setCommand(commandDetailsBean.getCommandName())
                        .setCommandJobId(commandTriggerBean.getCommandJobId())
                        .setCommandTimeout(commandDetailsBean.getTimeOutInSecs())
                        .setCommandOutputType((null == commandOutputType) ? "" : commandOutputType)
                        .build();

        //Adding list of instances in place of supervisorIdentifiers
        CommandRequestProtos.CommandRequest commandDetails = CommandRequestProtos.CommandRequest.newBuilder().addAllSupervisorIdentifiers(compInstanceIdentifier)
                .addCommands(command)
                .setAgentType(agentType)
                .putAllMetadata(metaData)
                .setTriggerSource("ControlCenter")
                .setTriggerTime(new Date().getTime())
                .setViolationTime(new Date().getTime())
                .setAgentIdentifier(physicalAgentIdentifier)
                .setUserDetailsID(commandDetailsBean.getUserDetails())
                .build();

        AgentStatusDataService.addAgentCommandTrigger(commandTriggerBean, conn);

        new AgentHealthStatusRepo().insertCommandDetails(accountIdentifier, physicalAgentIdentifier, String.valueOf(commandTriggerBean.getCommandId()),
                commandTriggerBean.getCommandJobId(), Constants.FORENSIC_ACTION_TRIGGER_STATUS, System.currentTimeMillis(), metaData);

        log.debug("Time taken to insert data into Opensearch and DB for the agent {} is {}", agentIdentifier, System.currentTimeMillis() - startTime);

        ForensicCmdTriggeredStatus bean = new ForensicCmdTriggeredStatus();
        bean.setAgentTypeId(agentTypeId);
        bean.setInstanceIdentifier(compInstanceIdentifier);
        bean.setAgentIdentifier(agentDetails.getIdentifier());
        bean.setCommandJobId(commandTriggerBean.getCommandJobId());
        bean.setAgentId(commandTriggerBean.getPhysicalAgentIdentifier());
        bean.setPhysicalAgentIdentifier(agentDetails.getPhysicalAgentIdentifier());

        log.trace("Created BulkForensicCmdTriggeredStatus object: {}", bean);

        populateTriggerTimeInRedis(accountIdentifier, commandIdentifier, compInstanceIdentifier);

        return new BulkCmdTriggerWrapper(bean, commandDetails);
    }

    private Map<Integer, Integer> getCommandDetailsByJimAgentType() throws ServerException {
        List<com.heal.configuration.pojos.ViewTypes> types = new MasterDataRepo().getTypes();
        if (types.isEmpty()) {
            log.error("Obtained empty result from Redis when queried for view types");
            throw new ServerException("Obtained empty result from Redis when queried for view types");
        }

        Map<String, com.heal.configuration.pojos.ViewTypes> viewTypesMap = types.stream()
                .filter(f -> f.getSubTypeName().equals(Constants.JIM_AGENT_SUB_TYPE) || f.getTypeName().equals(Constants.FORENSIC_COMMAND_TYPE))
                .collect(Collectors.toMap(com.heal.configuration.pojos.ViewTypes::getTypeName, Function.identity()));

        int jimSubTypeId = viewTypesMap.get(Constants.AGENT_TYPE).getSubTypeId();
        int forensicTypeId = viewTypesMap.get(Constants.FORENSIC_COMMAND_TYPE).getTypeId();

        List<CommandDetailsBean> forensicActionDetailsByAgentType = CommandDataService.getCommandDetailsByAgentType(jimSubTypeId, forensicTypeId, null);
        if (forensicActionDetailsByAgentType.isEmpty()) {
            log.error("Commands unavailable for JIM agent subtype: {} and forensicTypeId: {}", jimSubTypeId, forensicTypeId);
            throw new ServerException("Commands unavailable for JIM agent");
        }

        return forensicActionDetailsByAgentType
                .parallelStream().collect(Collectors.toMap(CommandDetailsBean::getId, CommandDetailsBean::getSuppression));
    }


    private Map<String, List<String>> fetchInstancesMappedToAgents(List<String> jimAgentIdentifiers, String commandIdentifier, long suppressionInMillis, String accountIdentifier) throws ServerException {
        log.debug("Fetching instances which are mapped to these jim agents {}, Also verifying if the instances are within the command's suppression interval of {}", jimAgentIdentifiers, suppressionInMillis);
        Map<String, List<String>> agentInstancesMap = new HashMap<>();

        ForensicCmdRepo forensicCmdRepo = new ForensicCmdRepo();
        Map<String, Long> instancesLvlCmdTriggerTime = forensicCmdRepo.getInstancesCmdTriggerTime(accountIdentifier, commandIdentifier);

        // This condition checks whether the retrieved data is null
        // If data is null, it indicates an issue occurred while fetching the key from Redis
        if (instancesLvlCmdTriggerTime == null) {
            log.error("Obtained NULL when queried for instances command trigger time details for command {} of account {}", commandIdentifier, accountIdentifier);
            throw new ServerException(String.format("Obtained NULL when queried for instances command trigger time details for command %s of account %s", commandIdentifier, accountIdentifier));
        }

        InstanceRepo instanceRepo = new InstanceRepo();
        jimAgentIdentifiers.forEach(jim -> {
            List<BasicEntity> agentInstances = instanceRepo.getAgentInstances(accountIdentifier, jim);
            if (agentInstances.isEmpty()) {
                log.warn("Obtained empty results when queries for agent instances data from Redis. Skipping the agent. Details:- Account {}, Jim Agent {}", accountIdentifier, jim);
                return;
            }

            List<String> activeInstances = agentInstances.stream()
                    .filter(f -> f.getStatus() == 1)
                    .map(BasicEntity::getIdentifier)
                    .collect(Collectors.toList());

            if (activeInstances.isEmpty()) {
                log.warn("There are no active instances mapped to this jim agent {}. Hence skipping it", jim);
                return;
            }

            log.debug("Active instance mapped to JIM agent {} are {}", jim, activeInstances);

            List<String> instancesForensicCommands = getInstancesForensicCommands(accountIdentifier, commandIdentifier, activeInstances,
                    suppressionInMillis, instancesLvlCmdTriggerTime);

            if (instancesForensicCommands.isEmpty()) {
                log.error("The active instances {} mapped to JIM agent {} will not be processed as they fall within the command's suppression interval. Command: {}, Suppression Interval (ms): {}", activeInstances, jim, commandIdentifier, suppressionInMillis);
                return;
            }
            agentInstancesMap.put(jim, activeInstances);
        });

        return agentInstancesMap;
    }

    private List<String> fetchJimAgentsMappedToServices(List<String> serviceIdentifiers, String accountIdentifier) throws ServerException {
        log.debug("Fetching all the jim agents mapped to these services {} of account {}", serviceIdentifiers, accountIdentifier);
        ServiceRepo serviceRepo = new ServiceRepo();
        List<String> jimAgentIdentifiers = new ArrayList<>();

        for (String serviceIdentifier : serviceIdentifiers) {
            List<BasicAgentBean> serviceAgents = serviceRepo.getAgentsByServiceIdentifier(accountIdentifier, serviceIdentifier);
            if (serviceAgents.isEmpty()) {
                log.warn("Obtained NULL from Redis when queried for service agents for the service {} of account {}. Skipping the service.", serviceIdentifier, accountIdentifier);
                continue;
            }

            jimAgentIdentifiers.addAll(serviceAgents.parallelStream()
                    .filter(f -> f.getStatus() == 1)
                    .filter(f -> f.getType().equals(Constants.JIM_AGENT_SUB_TYPE))
                    .map(BasicEntity::getIdentifier)
                    .distinct()
                    .collect(Collectors.toList()));
        }

        if (jimAgentIdentifiers.isEmpty()) {
            log.error("None of the agents mapped to services {} of account {} are JIM agents.", serviceIdentifiers, accountIdentifier);
            throw new ServerException("JIM agent is unavailable for provided services");
        }

        return jimAgentIdentifiers;
    }

    private void populateTriggerTimeInRedis(String accountIdentifier, String commandIdentifier, List<String> instanceIdentifiers) throws DataProcessingException {
        long triggerTimeInGMT = Instant.now().toEpochMilli();
        log.debug("Saving instances command trigger time in GMT timezone in Redis. Details: Command {}, Trigger time {}, Instance Identifiers {}, Account {}", commandIdentifier, triggerTimeInGMT, instanceIdentifiers, accountIdentifier);

        ForensicCmdRepo forensicCmdRepo = new ForensicCmdRepo();
        Map<String, Long> instancesCmdTriggerTime = forensicCmdRepo.getInstancesCmdTriggerTime(accountIdentifier, commandIdentifier);

        if (instancesCmdTriggerTime == null) {
            log.error("Obtained NULL when queried for microservices command trigger time details for command {} of account {}", commandIdentifier, accountIdentifier);
            throw new DataProcessingException(String.format("Obtained NULL when queried for microservice command trigger time details for command %s of account %s", commandIdentifier, accountIdentifier));
        }

        instanceIdentifiers.forEach(instance -> instancesCmdTriggerTime.put(instance, triggerTimeInGMT));
        forensicCmdRepo.updateInstancesCmdTriggerTime(accountIdentifier, commandIdentifier, instancesCmdTriggerTime);
    }

    private List<String> getInstancesForensicCommands(String accountIdentifier, String commandIdentifier, List<String> instanceIdentifiers,
                                                      long suppressionInMillis, Map<String, Long> instancesLvlCmdTriggerTime) {

        log.trace("Verifying whether the instances {} fall within the command's suppression interval of {} ms.", instanceIdentifiers, suppressionInMillis);

        return instanceIdentifiers.parallelStream().map(instance -> {
            long commandTriggerTime = instancesLvlCmdTriggerTime.getOrDefault(instance, 0L);
            if (commandTriggerTime == 0) {
                log.debug("The trigger time retrieved from Redis for command {} for instance {} in account {} is 0. Proceeding to trigger the command.", commandIdentifier, instance, accountIdentifier);
                return instance;
            } else {
                log.debug("Verifying whether the current input command request falls within the suppression interval. Details: Command {}, Instance {}, Suppression Interval {}, Account {}", commandIdentifier, instance, suppressionInMillis, accountIdentifier);

                if (commandTriggerTime + suppressionInMillis < Instant.now().toEpochMilli()) {
                    log.debug("Input request command {} for instance {} is out of suppression interval, Hence this request will be processed.", commandIdentifier, instance);
                    return instance;
                } else {
                    log.warn("Input request command {} for instance {} is within the suppression interval, so the request will not be processed.", commandIdentifier, instance);
                    return null;
                }
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<String> fetchServicesMappedToMicroService(List<String> microserviceIdentifiers, String commandIdentifier, String accountIdentifier) {
        List<String> serviceIdentifiers = new ArrayList<>();
        ApplicationRepo applicationRepo = new ApplicationRepo();

        log.info("Initiating command at microservice level. Details:- Account {}, Command {}, Microservices {}", accountIdentifier, commandIdentifier, microserviceIdentifiers);
        for (String microserviceIdentifier : microserviceIdentifiers) {
            List<BasicEntity> servicesMappedToApplication = applicationRepo.getServicesMappedToApplication(accountIdentifier, microserviceIdentifier);

            if (servicesMappedToApplication.isEmpty()) {
                log.warn("Obtained empty results when queried for application mapped services from Redis. Skipping the application. Details:- Account {}, Application {}", accountIdentifier, microserviceIdentifier);
                continue;
            }

            serviceIdentifiers.addAll(servicesMappedToApplication.stream().map(BasicEntity::getIdentifier).collect(Collectors.toList()));
        }
        return serviceIdentifiers;
    }
}
