package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.redis.MasterDataRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.service.TransactionService;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.pojos.Rule;
import com.heal.configuration.pojos.TransactionGroup;
import com.heal.configuration.pojos.ViewTypes;
import lombok.extern.slf4j.Slf4j;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class GetRulesDetailsBL implements BusinessLogic<Integer, UtilityBean<Integer>, RuleDetailsPojo> {
    private static final String RULE_ID = ":ruleId";
    private int accountId;

    public UtilityBean<Integer> clientValidation(RequestObject requestObject) throws ClientException {

        int ruleId;

        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String accountIdentifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (accountIdentifier == null || StringUtils.isEmpty(accountIdentifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }
        String serviceIdStr = requestObject.getParams().get(Constants.SERVICE_ID.toLowerCase());
        if (serviceIdStr == null || StringUtils.isEmpty(serviceIdStr)) {
            log.error(UIMessages.EMPTY_SERVICE_IDENTIFIER);
            throw new ClientException(UIMessages.EMPTY_SERVICE_IDENTIFIER);
        }
        String ruleIdStr = requestObject.getParams().get(RULE_ID.toLowerCase());

        if (ruleIdStr == null || StringUtils.isEmpty(ruleIdStr)) {
            log.error(UIMessages.EMPTY_RULE_IDENTIFIER);
            throw new ClientException(UIMessages.EMPTY_RULE_IDENTIFIER);
        }
        try {
            ruleId = Integer.parseInt(ruleIdStr);
        } catch (NumberFormatException e) {
            log.error(MessageFormat.format(UIMessages.INVALID_VALUE, RULE_ID, ruleIdStr));
            throw new ClientException(MessageFormat.format(UIMessages.INVALID_VALUE, RULE_ID, ruleIdStr));
        }

        return UtilityBean.<Integer>builder()
                .accountIdentifier(accountIdentifier)
                .serviceId(serviceIdStr)
                .authToken(authKey)
                .pojoObject(ruleId)
                .build();
    }

    public UtilityBean<Integer> serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        UserAccountBean userAccountBean;
        try {
            userAccountBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            log.error("Invalid user and account details", e);
            throw new ServerException("Invalid user and account details");
        }
        accountId = userAccountBean.getAccount().getId();

        ControllerBean serviceDetails = new ControllerDataService().getControllerById(Integer.parseInt(utilityBean.getServiceId()), userAccountBean.getAccount().getId(), null);
        if (serviceDetails == null) {
            log.error("Service details unavailable for serviceID [{}]", utilityBean.getServiceId());
            throw new ServerException("Service details unavailable for serviceID [{}]" + utilityBean.getServiceId());
        }

        utilityBean.setServiceId(serviceDetails.getIdentifier());
        return utilityBean;
    }

    public RuleDetailsPojo process(UtilityBean<Integer> bean) throws DataProcessingException {
        RuleDetailsPojo ruleDetailsPojo = new RuleDetailsPojo();

        int ruleId = bean.getPojoObject();

        MasterDataRepo masterDataRepo = new MasterDataRepo();
        ViewTypes ejbRuleType = masterDataRepo.getTypes().stream()
                .filter(type -> Constants.RULES_TYPE.trim().equalsIgnoreCase(type.getTypeName()))
                .filter(it -> Constants.AGENT_RULES_TYPE_EJB_DATA.trim().equalsIgnoreCase(it.getSubTypeName())).findAny().orElse(null);

        int ejbTypeId = ejbRuleType != null ? ejbRuleType.getSubTypeId() : 0;

        ServiceRepo serviceRepo = new ServiceRepo();

        List<Rule> rulesDetails = serviceRepo.getServiceRules(bean.getAccountIdentifier(), bean.getServiceId());

        if (rulesDetails == null || rulesDetails.isEmpty()) {
            log.error("No Rules available for mentioned accountId: {} and serviceId: {}", bean.getAccountIdentifier(), bean.getServiceId());
            throw new DataProcessingException("No Rules available for mentioned accountId:" + bean.getAccountIdentifier() + " and serviceId:" + bean.getServiceId());
        }
        Rule ruleData = rulesDetails.stream().filter(rule -> rule.getId() == ruleId).findAny().orElse(null);

        if (ruleData == null || ruleData.getRuleTypeId() == ejbTypeId) {
            log.error("Rules unavailable with ruleId: [{}] and accountId:[{}]", ruleId, bean.getAccount().getId());
            throw new DataProcessingException("Rules unavailable with ruleId: " + ruleId + " and account id:" + bean.getAccount().getId());
        }

        RuleDetailsBean.UriSegments uriSegments = new RuleDetailsBean.UriSegments();

        if (!(ruleData.getRequestTypeDetails().getFirstUriSegments() == 0)) {
            uriSegments.setType(Constants.URI_SEGMENT_FIRST);
            uriSegments.setValue(String.valueOf(ruleData.getRequestTypeDetails().getFirstUriSegments()));
        } else if (!(ruleData.getRequestTypeDetails().getLastUriSegments() == 0)) {
            uriSegments.setType(Constants.URI_SEGMENT_LAST);
            uriSegments.setValue(String.valueOf(ruleData.getRequestTypeDetails().getLastUriSegments()));
        } else {
            uriSegments.setType(Constants.URI_SEGMENT_CUSTOM);
            uriSegments.setValue(ruleData.getRequestTypeDetails().getCustomSegments());
        }

        ArrayList<RuleDetailsBean.QueryParameter> queryParameters = new ArrayList<>();
        ArrayList<RuleDetailsBean.PayloadParameters> payloadParameters = new ArrayList<>();
        ArrayList<RuleDetailsBean.HttpHeaderParameters> httpHeaderParameters = new ArrayList<>();

        ruleData.getRequestTypeDetails().getPairData().forEach( pairDataBean -> {
            if(pairDataBean.getPairTypeName().equals(Constants.QUERY_PARAM)) {
                RuleDetailsBean.QueryParameter query = new RuleDetailsBean.QueryParameter();
                query.setName(pairDataBean.getParamKey());
                query.setValue(pairDataBean.getParamValue());
                queryParameters.add(query);
            }
            if(pairDataBean.getPairTypeName().equals(Constants.HTTP_HEADER_TYPE)) {
                RuleDetailsBean.HttpHeaderParameters httpHeader = new RuleDetailsBean.HttpHeaderParameters();
                httpHeader.setName(pairDataBean.getParamKey());
                httpHeader.setValue(pairDataBean.getParamValue());
                httpHeaderParameters.add(httpHeader);
            }
            if(pairDataBean.getPairTypeName().equals(Constants.PAY_LOAD_TYPE)) {
                RuleDetailsBean.PayloadParameters payload = new RuleDetailsBean.PayloadParameters();
                payload.setName(pairDataBean.getParamKey());
                payload.setValue(pairDataBean.getParamValue());
                payloadParameters.add(payload);
            }
        });

        ruleDetailsPojo.setUriSegments(uriSegments);
        ruleDetailsPojo.setId(ruleData.getId());
        ruleDetailsPojo.setName(ruleData.getName());
        ruleDetailsPojo.setType((ruleData.getIsDefault() == 0) ? Constants.RULES_TYPE_CUSTOM : Constants.RULES_TYPE_STANDARD);
        ruleDetailsPojo.setOrder(ruleData.getOrder());
        ruleDetailsPojo.setStatus(ruleData.isMonitoringEnabled() ? 1 :0);
        ruleDetailsPojo.setRuleTypeId(ruleData.getRuleTypeId());
        ruleDetailsPojo.setRuleType(ruleData.getRuleType());
        ruleDetailsPojo.setUriSegments(uriSegments);
        ruleDetailsPojo.setQueryParameters(queryParameters);
        ruleDetailsPojo.setPayloadType(ruleData.getRequestTypeDetails().getPayloadTypeName());
        ruleDetailsPojo.setPayloadParameters(payloadParameters);
        ruleDetailsPojo.setHttpHeaderParameters(httpHeaderParameters);

        Set<String> transactionGroupNamesList = ruleData.getTransactionGroups().stream().map(TransactionGroup::getTransactionGroupName).collect(Collectors.toSet());
        ruleDetailsPojo.setDiscoveryTags(transactionGroupNamesList);

        List<RuleDetailsBean> totalRequestsCount = TransactionService.getLastDiscoveredRequest(ruleData.getId(), accountId);
        if (!totalRequestsCount.isEmpty()) {
            ruleDetailsPojo.setRequestsCount(totalRequestsCount.size());
            totalRequestsCount.sort(Comparator.comparing(RuleDetailsBean::getRequestDiscoveredTime, Comparator.reverseOrder()));
            ruleDetailsPojo.setRequestDiscoveredTime(DateTimeUtil.getGMTToEpochTime(totalRequestsCount.get(0).getRequestDiscoveredTime()));
        }

        return ruleDetailsPojo;
    }
}
