package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.NotificationDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.SMTPDetailsBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.SmtpDetails;
import com.appnomic.appsone.controlcenter.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.bouncycastle.crypto.DataLengthException;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.Map;

public class UpdateEmailBL implements BusinessLogic<SmtpDetails, SMTPDetailsBean, String> {
    private static final ObjectMapper OBJECT_MAPPER = CommonUtils.getObjectMapperWithHtmlEncoder();
    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateEmailBL.class);

    @Override
    public UtilityBean<SmtpDetails> clientValidation(RequestObject requestObject) throws ClientException {
        SmtpDetails smtpDetails;

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authKey)) {
            LOGGER.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }
        try {
            smtpDetails = OBJECT_MAPPER.readValue(requestObject.getBody(),
                    new TypeReference<SmtpDetails>() {
                    });
        } catch (IOException e) {
            LOGGER.error(UIMessages.JSON_INVALID + " Details: {}", e.getMessage());
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        Map<String, String> error = smtpDetails.validate();

        if (!error.isEmpty()) {
            String err = error.toString();
            LOGGER.error(err);
            throw new ClientException(err);
        }

        return UtilityBean.<SmtpDetails>builder().accountIdentifier(identifier).pojoObject(smtpDetails).authToken(authKey).build();
    }

    @Override
    public SMTPDetailsBean serverValidation(UtilityBean<SmtpDetails> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            LOGGER.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }

        SMTPDetailsBean smtpDetailsBeanExists = new NotificationDataService().getSMTPDetails(account.getId(), null);
        if (smtpDetailsBeanExists == null) {
            String err = "Email settings is not available for account ".concat(String.valueOf(account.getId()));
            LOGGER.error(err);
            throw new ServerException(err);
        }

        SmtpDetails smtpDetails = utilityBean.getPojoObject();
        smtpDetails.setId(smtpDetailsBeanExists.getId());

        ViewTypes securityType = MasterCache.getMstTypeForSubTypeName(Constants.SMTP_PROTOCOLS, smtpDetails.getSecurity());
        if (securityType == null) {
            String err = "Security type details is unavailable for security type: ".concat(smtpDetails.getSecurity());
            LOGGER.error(err);
            throw new ServerException(err);
        }

        Timestamp time = DateTimeUtil.getCurrentTimestampInGMT();
        String plainTxt = "";

        if (smtpDetails.getPassword() == null || smtpDetails.getPassword().trim().isEmpty()) {
            smtpDetails.setPassword("");
        } else {
            try {
                plainTxt = new AECSBouncyCastleUtil().decrypt(smtpDetails.getPassword());
                if (StringUtils.isEmpty(plainTxt)) {
                    String err = "Password is not properly encrypted.";
                    LOGGER.error(err);
                    throw new ServerException(err);
                }
            } catch (InvalidCipherTextException | DataLengthException e) {
                LOGGER.error("Exception encountered while decrypting the password. Details: {}", e.getMessage(), e);
                throw new ServerException("Error while decrypting the password");
            }
        }

        smtpDetails.setPassword(CommonUtils.encryptInBCEC(plainTxt));

        return CreateEmailBL.createSmtpDetailsBean(smtpDetails, userId, account, time, securityType.getSubTypeId());
    }

    @Override
    public String process(SMTPDetailsBean bean) throws DataProcessingException {
        if (Constants.SUCCESS.equals(new NotificationDataService().updateSMTPDetails(bean, null))) {
            return "SUCCESS";
        } else {
            throw new DataProcessingException("Unable to update email settings");
        }
    }
}
