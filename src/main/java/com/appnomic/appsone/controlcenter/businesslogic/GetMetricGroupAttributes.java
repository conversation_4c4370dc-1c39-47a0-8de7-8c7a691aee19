package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ComponentInstanceBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.MetricDetailsDataService;
import com.appnomic.appsone.controlcenter.dao.opensearch.KPIGroupAttributesRepo;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.MetricDetailsRequest;
import com.appnomic.appsone.controlcenter.pojo.MetricGroupAttribute;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

public class GetMetricGroupAttributes implements BusinessLogic<MetricDetailsRequest, MetricDetailsRequest, Set<MetricGroupAttribute>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(GetMetricGroupAttributes.class);
    MetricDetailsDataService dataService = new MetricDetailsDataService();
    private int COMP_INST_RANGE = ConfProperties.getInt(Constants.COMP_INST_RANGE, Constants.COMP_INST_RANGE_DEFAULT_VALUE);

    @Override
    public UtilityBean<MetricDetailsRequest> clientValidation(RequestObject request) throws ClientException {
        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }

        String groupIdString = request.getParams().get(Constants.GROUP_ID);
        if (StringUtils.isEmpty(groupIdString)) {
            LOGGER.error("groupId is null or empty. {}", groupIdString);
            throw new ClientException("groupId is null or empty.");
        }
        int groupId;
        try {
            groupId = Integer.parseInt(groupIdString.trim());
        } catch (NumberFormatException e) {
            LOGGER.error("groupId [{}] is not an integer. ", groupIdString);
            throw new ClientException("groupId is not an integer.");
        }

        if (request.getQueryParams().get("instanceIds").length == 0) {
            LOGGER.error("InstanceIds are not specified in query parameters.");
            throw new ClientException("InstanceIds are not specified in queryParams");
        }
        String[] instanceIdsString = request.getQueryParams().get("instanceIds");

        List<Integer> instanceIds = new ArrayList<>();
        if (instanceIdsString == null || instanceIdsString.length == 0) {
            LOGGER.error("InstanceIds is null or empty.");
            throw new ClientException("InstanceIds is null or empty.");
        } else {
            try {
                for (String s : instanceIdsString[0].split(",")) {
                    Integer parseInt = Integer.parseInt(s.trim());
                    instanceIds.add(parseInt);
                }
            } catch (NumberFormatException e) {
                LOGGER.error("InstanceIds should be positive integers.");
                throw new ClientException("InstanceIds should be positive integers.");
            }
            if (instanceIds.parallelStream().anyMatch(i -> i < 1)) {
                LOGGER.error("InstanceIds cannot be less than 1.");
                throw new ClientException("InstanceIds cannot be less than 1.");
            }
        }

        return UtilityBean.<MetricDetailsRequest>builder()
                .accountIdentifier(identifier)
                .pojoObject(MetricDetailsRequest.builder()
                        .groupId(groupId)
                        .instanceIds(instanceIds)
                        .build())
                .authToken(authToken)
                .build();
    }

    @Override
    public MetricDetailsRequest serverValidation(UtilityBean<MetricDetailsRequest> utilityBean) throws ServerException {

        UserAccountBean userAccBean;
        try {
            userAccBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            throw new ServerException(e.getMessage());
        }

        int accountId = userAccBean.getAccount().getId();
        List<ComponentInstanceBean> instances = new ArrayList<>();
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
        MetricDetailsRequest request = utilityBean.getPojoObject();

        Set<Integer> instanceIds = new HashSet<>(request.getInstanceIds());
        for (int instanceId : instanceIds) {
            ComponentInstanceBean bean = compInstanceDataService.getComponentInstanceByIdAndAccount(instanceId, accountId);
            if (bean == null) {
                throw new ServerException("Invalid instance Id : " + instanceId);
            }
            instances.add(bean);
        }

        Set<Integer> componentIds = instances.parallelStream().map(ComponentInstanceBean::getMstComponentId)
                .collect(Collectors.toSet());
        if (componentIds.size() != 1) {
            LOGGER.error("InstanceIds specified are mapped to different components.");
            throw new ServerException("InstanceIds specified are mapped to different components.");
        }

        int compId = new ArrayList<>(componentIds).get(0);
        Set<Integer> groups;
        try {
            groups = dataService.getNonDiscoveredKPIGroupsForComponentId
                    (compId, accountId, null).parallelStream().map(IdPojo::getId)
                    .collect(Collectors.toSet());
        } catch (ControlCenterException e) {
            throw new ServerException(e.getMessage());
        }

        if (!groups.contains(request.getGroupId())) {
            LOGGER.error("Invalid groupId.");
            throw new ServerException("Invalid groupId.");
        }

        request.setAccountId(accountId);
        request.setAccountIdentifier(userAccBean.getAccount().getIdentifier());
        request.setComponentId(compId);
        request.setInstances(instances.parallelStream().map(instance -> IdPojo.builder()
                .id(instance.getId())
                .identifier(instance.getIdentifier())
                .name(instance.getName())
                .build())
                .collect(Collectors.toList()));
        return request;
    }

    @Override
    public Set<MetricGroupAttribute> process(MetricDetailsRequest bean) throws DataProcessingException {

        long st = System.currentTimeMillis();
        Long range = COMP_INST_RANGE * 60 * 1000L;
        String accIdentifier = bean.getAccountIdentifier();
        int groupId = bean.getGroupId();
        List<IdPojo> instances = bean.getInstances();
        Set<MetricGroupAttribute> attributes = new HashSet<>();
        List<Set<MetricGroupAttribute>> instanceGroupAttributesList = new ArrayList<>();
        Set<MetricGroupAttribute> result = new HashSet<>();
        try {
            Set<Integer> KPIs = dataService.getKpiIdsForGroup(groupId, bean.getAccountId(), null);
            for (IdPojo instance : instances) {
                Set<MetricGroupAttribute> instanceGroupAttributes = dataService.getGroupAttributesForInstance
                        (instance.getId(), groupId, null);
                Set<String> attributesWithDataCollected = new HashSet<>();
                try{
                    attributesWithDataCollected = new KPIGroupAttributesRepo()
                            .getGroupKpiAttributesWithDataCollected(accIdentifier, instance.getIdentifier(), range, KPIs);
                }catch (ControlCenterException exe){
                    LOGGER.error("No attributes found for the instances - ", exe);
                }
                Set<String> finalAttributesWithDataCollected = attributesWithDataCollected;
                instanceGroupAttributes.stream().parallel().forEach(attribute -> {
                    if (finalAttributesWithDataCollected.contains(attribute.getValue())) {
                        attribute.setDataCollected(1);
                    }
                });
                attributes.addAll(instanceGroupAttributes);
                instanceGroupAttributesList.add(instanceGroupAttributes);
            }

            if (instances.size() == 1) {
                instanceGroupAttributesList.get(0).forEach(attribute -> attribute.setCommon(1));
                return instanceGroupAttributesList.get(0);
            }

            attributes.forEach(attribute -> {
                if (instanceGroupAttributesList.parallelStream().anyMatch(i -> !i.contains(attribute))) {
                    result.add(MetricGroupAttribute.builder()
                            .value(attribute.getValue())
                            .aliasName(attribute.getAliasName())
                            .build());
                } else {
                    attribute.setCommon(1);
                    result.add(attribute);
                }
            });

        } catch (ControlCenterException e) {
            throw new DataProcessingException(e, "Error occurred while processing attributes, Bean:"+ bean);
        }
        LOGGER.info("Time taken for GetMetricGroupAttributes.process method is {} ms.", (System.currentTimeMillis() - st));
        return identifyCommonAliasNames(result, instanceGroupAttributesList);
    }

    public Set<MetricGroupAttribute> identifyCommonAliasNames(Set<MetricGroupAttribute> result,
                                                              List<Set<MetricGroupAttribute>> instanceGroupAttributesList) {
        List<MetricGroupAttribute> metricGroupAttributeList = instanceGroupAttributesList.stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        result.parallelStream()
                .filter(res -> res.getCommon() == 1)
                .forEach(attribute -> {
                    Set<String> metricGroupAttributes = metricGroupAttributeList.parallelStream()
                            .filter(attrib -> attrib.getValue().equals(attribute.getValue()))
                            .map(MetricGroupAttribute :: getAliasName)
                            .collect(Collectors.toSet());
                    if(metricGroupAttributes.size() == 1) {
                        attribute.setCommonAliasName(1);
                    }
                    else {
                        attribute.setAliasName("");
                    }
                });

        return result;

    }
}
