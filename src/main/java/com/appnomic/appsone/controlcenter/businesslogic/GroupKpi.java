package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.MasterKpiGroupBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.DBTestCache;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.KpiException;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.service.KeyCloakAuthService;
import com.appnomic.appsone.controlcenter.dao.mysql.KPIDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.MasterDataService;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.model.JWTData;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

public class GroupKpi {

    private static final Logger LOGGER = LoggerFactory.getLogger(GroupKpi.class);
    private static final ObjectMapper OBJECT_MAPPER = CommonUtils.getObjectMapperWithHtmlEncoder();

    private static final String INVALID_VALUES_FOR_ONE_OR_MORE_ATTRIBUTES = "Invalid request. Reason: Invalid values " +
            "for one or more attributes. Refer more details, refer application log file.";
    private static final String INVALID_REQUEST_BODY = "Invalid request body. Reason: Request body is either NULL or empty. " +
            "Refer more details, refer application log file.";

    private KPIDataService kpiDataService = new KPIDataService();

    public com.appnomic.appsone.controlcenter.pojo.GroupKpi clientValidation(String requestBody) throws KpiException {
        LOGGER.info("Client validation: BEGIN");

        com.appnomic.appsone.controlcenter.pojo.GroupKpi groupKpiRequest;
        try {
            groupKpiRequest = OBJECT_MAPPER.readValue(requestBody, new TypeReference<com.appnomic.appsone.controlcenter.pojo.GroupKpi>() {
            });

            if (!groupKpiRequest.isValid()) {
                throw new KpiException(INVALID_VALUES_FOR_ONE_OR_MORE_ATTRIBUTES);
            }

            if (null == groupKpiRequest.getGroupKpiIdentifier()) {
                groupKpiRequest.setGroupKpiIdentifier(groupKpiRequest.getGroupKpiName().concat("-").concat(Long.toString(Instant.now().toEpochMilli())));
                LOGGER.info("groupKpiIdentifier not provided in the request. Auto-generated value is [{}].", groupKpiRequest.getGroupKpiIdentifier());
            }
        } catch (IOException e) {
            LOGGER.error("Exception encountered while retrieving Group KPI creation request. Reason: {}", e.getMessage());
            throw new KpiException(INVALID_REQUEST_BODY);
        }

        LOGGER.info("Client validation: END");
        return groupKpiRequest;
    }

    public boolean serverValidation(com.appnomic.appsone.controlcenter.pojo.GroupKpi groupKpiRequest, int accountId) throws KpiException {
        LOGGER.info("server validation: BEGIN");

        ViewTypes kpiTypes = MasterCache.getMstTypeForSubTypeName("KPI", groupKpiRequest.getKpiType());

        if (null == kpiTypes) {
            LOGGER.error("KpiType validation failure. Reason: kpiType should be one of Availability, Core, FileWatch or ConfigWatch.");
            throw new KpiException("KpiType validation failure");
        }

        validateIfKpiAlreadyExists(accountId, groupKpiRequest);

        LOGGER.info("server validation: END");
        return true;
    }

    public IdPojo processGroupKpi(com.appnomic.appsone.controlcenter.pojo.GroupKpi groupKpiRequest, String userId, int accountId) throws KpiException {
        int kpiTypeId = MasterCache.getMstTypeForSubTypeName("KPI", groupKpiRequest.getKpiType()).getSubTypeId();

        int groupKpiId;
        try {
            MasterKpiGroupBean groupKpiBean = MasterKpiGroupBean.builder()
                    .name(groupKpiRequest.getGroupKpiName())
                    .identifier(groupKpiRequest.getGroupKpiIdentifier())
                    .description(groupKpiRequest.getDescription())
                    .createdTime(DateTimeUtil.getCurrentTimestampInGMT())
                    .updatedTime(DateTimeUtil.getCurrentTimestampInGMT())
                    .userDetailsId(userId)
                    .accountId(accountId)
                    .kpiTypeId(kpiTypeId)
                    .discovery(groupKpiRequest.getDiscovery())
                    .isCustom(groupKpiRequest.getCustom())
                    .status(1)
                    .build();

            groupKpiId = kpiDataService.createGroupKpi(groupKpiBean, null);
        } catch (Exception e) {
            LOGGER.error("Encountered exception while add Group KPI details. Reason: {}", e.getMessage());
            throw new KpiException("Error while adding Group KPI details.");
        }

        DBTestCache.addToCache("mst_kpi_group", groupKpiId);

        LOGGER.info("Group KPI successfully added.");

        return IdPojo.builder()
                .id(groupKpiId)
                .identifier(groupKpiRequest.getGroupKpiIdentifier())
                .name(groupKpiRequest.getGroupKpiName())
                .build();
    }

    public String getUserId(String authorizationKey) {
        AtomicReference<String> atomicString = new AtomicReference<>();
        try {
            JWTData jwtData = KeyCloakAuthService.extractUserDetails(authorizationKey);
            atomicString.set(jwtData.getSub());
        } catch (ControlCenterException e) {
            LOGGER.error("Exception encountered while retrieving user ID from request header. Reason: {}", e.getSimpleMessage());
            return null;
        }
        return atomicString.get();
    }

    private void validateIfKpiAlreadyExists(int accountId, com.appnomic.appsone.controlcenter.pojo.GroupKpi groupKpiRequest) throws KpiException {

        if (null != groupKpiRequest.getGroupKpiIdentifier()) {
            List<MasterKpiGroupBean> groupKpiCount = kpiDataService.checkForGroupKpiUsingIdentifier(groupKpiRequest.getGroupKpiIdentifier(), null);

            if (!groupKpiCount.isEmpty()) {
                LOGGER.error("KPI Group with alias name [{}] already exists.", groupKpiRequest.getGroupKpiIdentifier());
                throw new KpiException("Kpi group with provided alias name already exists");
            }
        }

        if (!isGroupNameValid(groupKpiRequest.getGroupKpiName(), accountId)) {
            LOGGER.error("Adding custom group KPI failed. Reason: KPI with the provided groupKpiName already exists.");
            throw new KpiException("Kpi group with provided name already exists");
        }
    }

    private boolean isGroupNameValid(String groupKpiName, int accountId) {

        return MasterDataService.getMasterKpiGroupDetails(accountId).stream()
                .noneMatch(bean -> groupKpiName.equalsIgnoreCase(bean.getName()));
    }
}
