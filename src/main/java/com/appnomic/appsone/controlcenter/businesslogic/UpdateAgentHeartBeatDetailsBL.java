package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.SupervisorDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.SupervisorBean;
import com.appnomic.appsone.controlcenter.dao.opensearch.AgentHealthStatusRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class UpdateAgentHeartBeatDetailsBL implements BusinessLogic<String, String, String> {

    private String accountIdentifier;

    @Override
    public UtilityBean<String> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Account identifier is invalid");
            throw new ClientException("Account identifier is invalid");
        }

        String agentIdentifier = requestObject.getParams().get(Constants.AGENT_IDENTIFIER);
        if(agentIdentifier == null || agentIdentifier.trim().isEmpty()) {
            log.error("Agent identifier is invalid");
            throw new ClientException("Agent identifier is invalid");
        }

        return UtilityBean.<String>builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .pojoObject(agentIdentifier)
                .build();
    }

    @Override
    public String serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);
        if (account == null) {
            throw new ServerException("Account identifier is invalid");
        }

        SupervisorBean existingSupervisor = new SupervisorDataService().getSupervisorByIdentifier(utilityBean.getPojoObject(), null);
        if(existingSupervisor == null) {
            log.error("Agent/Supervisor with identifier [{}] unavailable", utilityBean.getPojoObject());
            throw new ServerException("Agent/Supervisor identifier is invalid");
        }

        return existingSupervisor.getIdentifier();
    }

    @Override
    public String process(String agentIdentifier) throws DataProcessingException {
        try {
            log.debug("Agent health status details added with agentIdentifier {}, commandId {}, commandjobid {}, status {}, lastDataReceivedTimeInGMT {}, metadata {}", agentIdentifier, "", "", "", System.currentTimeMillis(), null);
            new AgentHealthStatusRepo().insertAgentHeartBeatTime(accountIdentifier, agentIdentifier, System.currentTimeMillis());
        } catch (Exception e) {
            throw new DataProcessingException(e.getMessage());
        }
        return "Agent heartbeat time updated successfully";
    }
}
