package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentDataService;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.sql.Timestamp;
import java.text.ParseException;


public class AgentBL {

    private static final Logger logger = LoggerFactory.getLogger(AgentBL.class);

    public Agent remClientValidations(Request request) throws RequestException {

        if(StringUtils.isEmpty(request.headers(Constants.AUTHORIZATION))){
            logger.error(UIMessages.AUTH_KEY_EMPTY);
            throw new RequestException(UIMessages.AUTH_KEY_EMPTY);
        }

        String identifier = request.params(Constants.AGENT_IDENTIFIER);
        if(StringUtils.isEmpty(identifier)){
            logger.error(UIMessages.EMPTY_AGENT_IDENTIFIER);
            throw new RequestException(UIMessages.EMPTY_AGENT_IDENTIFIER);
        }

        Agent agent = new Agent();
        agent.setUniqueToken(identifier);
        return agent;
    }

    public AgentBean remServerValidations(Agent agent, String authToken) throws RequestException, ParseException {

        String userId = ValidationUtils.getUserId(authToken);
        if(userId == null){
            logger.error(UIMessages.AUTH_KEY_INVALID);
            throw new RequestException(UIMessages.AUTH_KEY_INVALID);
        }

        AgentBean agentBean = AgentDataService.getAgentBeanData(agent.getUniqueToken());

        if (agentBean == null || agentBean.getStatus() == 0) {
            String err = "Agent Identifier '"+agent.getUniqueToken()+"' " + Constants.DOES_NOT_EXIST;
            logger.error(err);
            throw new RequestException(err);
        }
        agentBean.setUserDetailsId(userId);
        agentBean.setUpdatedTime(new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime()));

        return agentBean;

    }

    public int remove(AgentBean bean) throws ControlCenterException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            return dbi.inTransaction((conn, status) -> remAgent(bean, conn));
        } catch (Exception e) {
            throw new ControlCenterException(Throwables.getRootCause(e).getMessage());
        }
    }

    private int remAgent(AgentBean bean, Handle handle) throws ControlCenterException {

        int res = AgentDataService.remAgent(bean, handle);
        if (res == -1) {
            String err = "Unable to remove Agent.";
            logger.error(err);
            throw new ControlCenterException(err);
        }
        return res;
    }
}
