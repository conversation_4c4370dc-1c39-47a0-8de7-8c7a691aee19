package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ComponentInstanceBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.AgentRepo;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.ComponentInstancePojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.BasicAgentEntity;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.CompInstClusterDetails;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class UpdateCompInstanceNameBL implements BusinessLogic<ComponentInstancePojo, UtilityBean<ComponentInstancePojo>, String> {

    private static final CompInstanceDataService COMP_INSTANCE_DATA_SERVICE = new CompInstanceDataService();

    @Override
    public UtilityBean<ComponentInstancePojo> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject.getBody() == null || requestObject.getBody().trim().isEmpty()) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Account identifier is invalid. Reason: It is either NULL or empty");
            throw new ClientException("Account identifier is invalid");
        }

        String compInstanceIdStr = requestObject.getParams().get(Constants.INSTANCE_ID);

        if (compInstanceIdStr == null || compInstanceIdStr.trim().isEmpty()) {
            log.error("Component instance ID is invalid. Reason: It is either NULL or empty");
            throw new ClientException("Component instance ID is invalid");
        }

        int compInstanceId;
        try {
            compInstanceId = Integer.parseInt(compInstanceIdStr);
        } catch (NumberFormatException e) {
            log.error("Component instance ID is invalid. Reason: Its is not a valid integer");
            throw new ClientException("Component instance ID is invalid");
        }

        ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

        ComponentInstancePojo compInstance;
        try {
            compInstance = objectMapper.readValue(requestObject.getBody(),
                    new TypeReference<ComponentInstancePojo>() {
                    });

            compInstance.setId(compInstanceId);
        } catch (IOException e) {
            log.error("IOException encountered while parsing request body. Details {}", e.getMessage());
            throw new ClientException("Error while parsing request body");
        }

        if (compInstance.getInstanceName() == null || compInstance.getInstanceName().trim().isEmpty()) {
            log.error("Invalid component instance name. Reason: It is either NULL or empty");
            throw new ClientException("Invalid component instance name");
        }

        return UtilityBean.<ComponentInstancePojo>builder()
                .authToken(authKey)
                .accountIdentifier(identifier)
                .pojoObject(compInstance)
                .build();
    }

    @Override
    public UtilityBean<ComponentInstancePojo> serverValidation(UtilityBean<ComponentInstancePojo> utilityBean) throws ServerException {
        ComponentInstancePojo componentInstance = utilityBean.getPojoObject();

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }

        ComponentInstanceBean compInst = COMP_INSTANCE_DATA_SERVICE.getComponentInstanceByIdAndAccount(componentInstance.getId(), account.getId());
        if (compInst == null) {
            log.error("Component instance with ID [{}] with account ID [{}] is unavailable", componentInstance.getId(), account.getId());
            throw new ServerException("Component instance unavailable");
        }

        if (compInst.getName().equals(componentInstance.getInstanceName())) {
            log.error("Provided component instance name [{}] is same as the existing name", componentInstance.getName());
            throw new ServerException("Provided component instance name is same as the existing name");
        }

        compInst = COMP_INSTANCE_DATA_SERVICE.getComponentInstanceByName(componentInstance.getInstanceName(), account.getId(), null);

        //"WHERE" clause is case insensitive in percona, so added an additional check for instance name
        if(compInst != null && compInst.getName().equals(componentInstance.getInstanceName())) {
            log.error("Provided component instance name [{}] already exists for the accountID [{}]", componentInstance.getName(), account.getId());
            throw new ServerException("Component instance name already exists");
        }

        return utilityBean;
    }

    @Override
    public String process(UtilityBean<ComponentInstancePojo> utilityBean) throws DataProcessingException {
        ComponentInstancePojo bean = utilityBean.getPojoObject();
        int updatedRowId = COMP_INSTANCE_DATA_SERVICE.editComponentInstance(bean.getInstanceName(), bean.getId(), null);

        if (updatedRowId == bean.getId()) {
            log.error("Error while updating component instance name with ID [{}]", bean.getId());
            throw new DataProcessingException("Error while updating component instance name");
        }

        updateInstanceNameInInstanceLevelKey(bean, utilityBean.getAccountIdentifier());

        updateInstanceNameInAgentLevelInstanceKey(bean, utilityBean.getAccountIdentifier());
        updateInstanceNameInServiceLevelInstanceKey(bean, utilityBean.getAccountIdentifier());

        return "Component instance name updated successfully";
    }

    private void updateInstanceNameInInstanceLevelKey(ComponentInstancePojo bean, String accountIdentifier) {
        InstanceRepo instanceRepo = new InstanceRepo();
        List<CompInstClusterDetails> instances = instanceRepo.getInstances(accountIdentifier);
        CompInstClusterDetails instanceObject = instances
                .parallelStream()
                .filter(f -> f.getId() == bean.getId())
                .findAny()
                .orElse(null);
        List<CompInstClusterDetails> updatedInstanceList = new ArrayList<>();

        if (instanceObject != null) {
            instanceObject.setName(bean.getInstanceName());
            instanceRepo.updateInstanceByIdentifier(accountIdentifier, instanceObject);
            updatedInstanceList = instances.parallelStream().map(m -> m.getId() == bean.getId() ? instanceObject : m).collect(Collectors.toList());
        }
        if (!updatedInstanceList.isEmpty()) {
            updatedInstanceList.parallelStream()
                    .filter(c -> c.getHostId() != 0 && c.getHostId() == (bean.getId()))
                    .peek(c -> {
                        c.setHostName(bean.getInstanceName());
                        instanceRepo.updateInstanceByIdentifier(accountIdentifier, c);
                    })
                    .collect(Collectors.toList());
            instanceRepo.updateInstances(accountIdentifier, updatedInstanceList);
        }
    }

    private void updateInstanceNameInServiceLevelInstanceKey(ComponentInstancePojo bean, String accountIdentifier) {
        ServiceRepo serviceRepo = new ServiceRepo();
        List<BasicEntity> allServicesDetails = serviceRepo.getAllServicesDetails(accountIdentifier);
        List<String> serviceIdentifiers = allServicesDetails.parallelStream().map(BasicEntity::getIdentifier).collect(Collectors.toList());
        serviceIdentifiers.forEach(serviceIdentifier -> {
            List<BasicInstanceBean> serviceInstances = serviceRepo.getServiceInstances(accountIdentifier, serviceIdentifier);
            BasicInstanceBean basicInstanceBean = serviceInstances.parallelStream().filter(f -> f.getId() == bean.getId()).findAny().orElse(null);
            if(basicInstanceBean != null){
                basicInstanceBean.setName(bean.getInstanceName());
                List<BasicInstanceBean> updatedServiceInstances = serviceInstances.parallelStream().map(m -> m.getId() == bean.getId() ? basicInstanceBean : m).collect(Collectors.toList());
                serviceRepo.updateServiceInstances(accountIdentifier, serviceIdentifier, updatedServiceInstances);
            }
        });
    }

    private void updateInstanceNameInAgentLevelInstanceKey(ComponentInstancePojo bean, String accountIdentifier) {
        AgentRepo agentRepo = new AgentRepo();
        List<BasicAgentEntity> agents = agentRepo.getAgents(accountIdentifier);
        List<String> agentIdentifiers = agents.parallelStream().map(BasicEntity::getIdentifier).collect(Collectors.toList());
        agentIdentifiers.forEach(agentIdentifier -> {
            List<BasicEntity> agentInstanceMappingDetails = agentRepo.getAgentInstanceMappingDetails(accountIdentifier, agentIdentifier);
            BasicEntity basicEntity = agentInstanceMappingDetails.parallelStream().filter(f -> f.getId() == bean.getId()).findAny().orElse(null);
            if(basicEntity != null){
                basicEntity.setName(bean.getInstanceName());
                List<BasicEntity> updatedAgentInstanceMapping = agentInstanceMappingDetails.parallelStream().map(m -> m.getId() == bean.getId() ? basicEntity : m).collect(Collectors.toList());
                agentRepo.updateAgentInstanceMappingDetails(accountIdentifier, agentIdentifier, updatedAgentInstanceMapping);
            }
        });
    }
}
