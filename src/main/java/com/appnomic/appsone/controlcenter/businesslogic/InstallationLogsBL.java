package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.Host;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.AutoDiscoveryDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.opensearch.AutoDiscoveryRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.pojos.opensearch.RawExternalData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;
import java.util.zip.GZIPInputStream;

public class InstallationLogsBL implements BusinessLogic<Object, String, String> {
    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateAgentConfigBL.class);
    AutoDiscoveryDataService autoDiscoveryDataService = new AutoDiscoveryDataService();
    AccountBean accountBean;

    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }
        String hostAddressStr = requestObject.getQueryParams().get("hostAddress")[0];
        if (StringUtils.isEmpty(hostAddressStr)) {
            LOGGER.error("Host address is null or empty.");
            throw new ClientException("Host address is null or empty.");
        }
        String process = requestObject.getQueryParams().get(Constants.PROCESS_NAME)[0];
        if (StringUtils.isEmpty(process) || !process.equalsIgnoreCase("Auto")) {
            LOGGER.error("Invalid process passed.");
            throw new ClientException("Invalid process passed.");
        }
        String dataSource = requestObject.getQueryParams().get("dataSource")[0];
        if (StringUtils.isEmpty(dataSource) || !dataSource.equalsIgnoreCase(Constants.DATA_SOURCE_VALUE)) {
            LOGGER.error("Invalid dataSource passed.");
            throw new ClientException("Invalid dataSource passed.");
        }
        String tag = requestObject.getQueryParams().get("tag")[0];
        if (StringUtils.isEmpty(tag) || !tag.equalsIgnoreCase(Constants.INSTALLATION_LOG_NAME)) {
            LOGGER.error("Invalid tag passed.");
            throw new ClientException("Invalid tag passed.");
        }
        return UtilityBean.builder()
                .pojoObject(hostAddressStr)
                .accountIdentifier(identifier)
                .build();
    }

    public String serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            LOGGER.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        accountBean = account;

        String hostAddress = utilityBean.getPojoObject().toString();
        List<Host> hostDetails = autoDiscoveryDataService.getHostDetailsByHostAddress(hostAddress);
        if (hostDetails.isEmpty()) {
            LOGGER.error("Invalid host address: {}", hostAddress);
            throw new ServerException("Invalid host address passed.");
        }
        return hostAddress;
    }

    public String process(String hostAddress) throws DataProcessingException {
        RawExternalData rawExternalData = new AutoDiscoveryRepo().getRawExternalData(hostAddress, accountBean.getIdentifier());
        if (rawExternalData == null) {
            return null;
        }
        try {
            return buildRawExternalData(rawExternalData);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting installation log detail");
            throw new RuntimeException(e);
        }
    }

    public String buildRawExternalData(RawExternalData rawExternalData) throws DataProcessingException {

        String decompressStr = null;
        try {
            byte[] decoded = Base64.getDecoder().decode(rawExternalData.getPayload());
            decompressStr = decompress(decoded);
        } catch (Exception e) {
            LOGGER.error("Improper installation log data");
            throw new DataProcessingException("Improper installation log data");
        }
        String reportData = decompressStr.replaceAll("#@#HEAL_NEWLINE#@#", "\n").replaceAll("#@#", " ");
        return "HostAddress: " + rawExternalData.getMetadata().get(Constants.HOST_ADDRESS) + "\n" +
                "Time: " + rawExternalData.getTimestamp() + "\n" +
                "ReportData: " + reportData + "\n";
    }

    private static String decompress(byte[] compressed) {
        StringBuffer stringBuffer = new StringBuffer();
        try (ByteArrayInputStream bis = new ByteArrayInputStream(compressed);
             GZIPInputStream gis = new GZIPInputStream(bis);
             BufferedReader br = new BufferedReader(new InputStreamReader(gis, StandardCharsets.UTF_8))) {
            int intC;
            while ((intC = br.read()) != -1) {
                char c = (char) intC;
                if (c != '\r') {
                    stringBuffer.append(c);
                }
            }
            if (intC == -1) {
                stringBuffer.append("\n");
            }

        } catch (Exception e) {
            LOGGER.error("Error occurred while decompressing file.", e);
        }
        return stringBuffer.toString();
    }
}
