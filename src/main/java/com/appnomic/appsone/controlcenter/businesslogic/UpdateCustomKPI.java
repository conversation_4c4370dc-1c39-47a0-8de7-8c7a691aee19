package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.CategoryDetailBean;
import com.appnomic.appsone.controlcenter.beans.TagDetailsBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.common.ValueType;
import com.appnomic.appsone.controlcenter.dao.mysql.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.*;
import com.appnomic.appsone.controlcenter.dao.redis.ComponentRepo;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.CategoryDetails;
import com.appnomic.appsone.controlcenter.pojo.ComputedKpiDetails;
import com.appnomic.appsone.controlcenter.pojo.CustomKpiFromUI;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.TagMappingDetails;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.KpiCategoryDetails;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

public class UpdateCustomKPI implements BusinessLogic<List<CustomKpiFromUI>, List<CustomKpiFromUI>, String> {

    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateCustomKPI.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final KPIDataService kpiDataService = new KPIDataService();
    private String userId;
    private int accountId;
    private String accountIdentifier;

    @Override
    public UtilityBean<List<CustomKpiFromUI>> clientValidation(RequestObject requestObject) throws ClientException {

        if (StringUtils.isEmpty(requestObject.getBody())) {
            LOGGER.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (identifier == null || identifier.trim().isEmpty()) {
            LOGGER.error("Account identifier is NULL or empty.");
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String requestBody = requestObject.getBody();
        List<CustomKpiFromUI> customKpiList;

        try {
            customKpiList = objectMapper.readValue(requestBody, new TypeReference<List<CustomKpiFromUI>>() {
            });
        } catch (IOException e) {
            LOGGER.error("Exception while parsing input request body. Details: {}", e.getMessage(), e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        for (CustomKpiFromUI customKpiFromUI : customKpiList) {
            if (!customKpiFromUI.validateForUpdate()) {
                LOGGER.error("Validation failure of the details provided");
                throw new ClientException("Validation failure of the details provided");
            }
        }

        return UtilityBean.<List<CustomKpiFromUI>>builder()
                .accountIdentifier(identifier)
                .authToken(authToken)
                .pojoObject(customKpiList)
                .build();
    }

    @Override
    public List<CustomKpiFromUI> serverValidation(UtilityBean<List<CustomKpiFromUI>> utilityBean) throws ServerException {
        userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        accountId = AccountDataService.getAccountByIdentifier(utilityBean.getAccountIdentifier(), null);

        if (accountId <= 0) {
            LOGGER.error("Account identifier [{}] is invalid", utilityBean.getAccountIdentifier());
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }

        List<CustomKpiFromUI> customKpiFromUIList = utilityBean.getPojoObject();
        accountIdentifier = utilityBean.getAccountIdentifier();

        for (CustomKpiFromUI customKpiFromUI : customKpiFromUIList) {
            if (customKpiFromUI.getId() <= 0) {
                LOGGER.error("kpiId is invalid. Reason: kpiId should be positive integer to add the custom KPI.");
                throw new ServerException("Invalid kpiId");
            }

            KpiBean existingKpi = kpiDataService.fetchKpiUsingKpiId(customKpiFromUI.getId(), accountId, null);

            if (existingKpi == null) {
                LOGGER.error("KPI with ID [{}] does not exist for the account [{}]", customKpiFromUI.getId(), accountId);
                throw new ServerException("KPI with ID does not exist for the provided account");
            }

            if (existingKpi.getIsCustom() != 1) {
                LOGGER.error("KPI with ID [{}] is not allowed to be modified as it is not a custom KPI.", customKpiFromUI.getId());
                throw new ServerException("KPI is not allowed to be modified as it is not a custom KPI.");
            }

            ComputedKpiDetails computedKpiDetails = customKpiFromUI.getComputedKpiDetails();
            List<Integer> newKpis = new ArrayList<>();
            ComputedKpiBean existingFormula = null;
            List<Integer> existingMappedKpis = new ArrayList<>();

            if (computedKpiDetails != null) {

                int kpisUsed = new BindInDataService().getKpisUsingId(computedKpiDetails.getKpisUsed(), accountId, null);

                if (kpisUsed != computedKpiDetails.getKpisUsed().size()) {
                    LOGGER.error("Some or all the KPIs used in the computation do not exist for the account [{}]", accountId);
                    throw new ServerException("Some or all the KPIs used in the computation do not exist for the provided account");
                }

                existingMappedKpis = kpiDataService.getComputedKpiMapping(customKpiFromUI.getId(), accountId, null);

                if (existingMappedKpis.isEmpty()) {
                    throw new ServerException("Supporting KPIs unavailable in database");
                }

                List<Integer> finalExistingMappedKpis = existingMappedKpis;

                newKpis = computedKpiDetails.getKpisUsed().parallelStream()
                        .filter(v -> !finalExistingMappedKpis.contains(v))
                        .collect(Collectors.toList());

                existingFormula = kpiDataService.fetchComputedKpiFormula(customKpiFromUI.getId(), accountId, null);
            }

            Optional<CompVersionKpiMappingBean> existing;
            if (customKpiFromUI.getComponentCommonVersionId() > 0 && customKpiFromUI.getComponentTypeId() > 0 && customKpiFromUI.getComponentId() > 0) {
                List<CompVersionKpiMappingBean> mappingBeans = MasterDataService.getComponentMappingForKpi(customKpiFromUI.getId(), null);

                existing = mappingBeans.parallelStream()
                        .filter(m -> m.getCommonVersionId() == customKpiFromUI.getComponentCommonVersionId()
                                && m.getComponentTypeId() == customKpiFromUI.getComponentTypeId()
                                && m.getComponentId() == customKpiFromUI.getComponentId()).findAny();

                if (existing.isPresent()) {
                    LOGGER.error("Custom KPI is already mapped to the component with ID [{}], type ID [{}] and common version ID [{}]",
                            customKpiFromUI.getComponentId(), customKpiFromUI.getComponentTypeId(), customKpiFromUI.getComponentCommonVersionId());
                    throw new ServerException("Duplicate KPI-component mapping");
                }
            }

            if (customKpiFromUI.getName() != null && !customKpiFromUI.getName().equalsIgnoreCase(existingKpi.getName())) {
                int kpiExists = kpiDataService.checkForKpiUsingName(customKpiFromUI.getName(), accountId, null);

                if (kpiExists != 0) {
                    LOGGER.error("KPI with name [{}] already exists", customKpiFromUI.getName());
                    throw new ServerException("KPI with provided name already exists");
                }
            }

            try {
                if (customKpiFromUI.getKpiCategoryDetails() != null) {
                    if (kpiDataService.getCategoryDetailsForKpi(existingKpi.getId()).getCategoryId() == customKpiFromUI.getKpiCategoryDetails().getId()) {
                        LOGGER.info("Category KPI mapping is not modified.");
                        customKpiFromUI.setKpiCategoryDetails(null);
                    } else {
                        CategoryDetailBean category = new CategoryDataService().getCategoryForKpiTypeAndCategoryId(accountId,
                                existingKpi.getKpiTypeId(), customKpiFromUI.getKpiCategoryDetails().getId());
                        if (category != null) {
                            customKpiFromUI.getKpiCategoryDetails().setIdentifier(category.getIdentifier());
                        } else {
                            LOGGER.error("Invalid category details provided.");
                            throw new ServerException("Invalid category details.");
                        }
                    }
                }
            } catch (ControlCenterException e) {
                throw new ServerException(e.getMessage());
            }

            ValidationUtils.validateOperationTypes(customKpiFromUI.getClusterOperation(), customKpiFromUI.getRollupOperation());

            ValidationUtils.validateAggregationTypes(customKpiFromUI.getClusterAggregation(), customKpiFromUI.getInstanceAggregation());

            ViewTypes kpiType = MasterCache.getMstSubTypeForSubTypeId(existingKpi.getKpiTypeId());

            if (customKpiFromUI.getDataType() != null) {
                ViewTypes dataTypes = ValidationUtils.getDataType(kpiType.getSubTypeName(), customKpiFromUI.getDataType());
                if (null == dataTypes) {
                    LOGGER.error("dataType validation failure. Reason: dataType [{}] is not available for kpiType [{}].", customKpiFromUI.getDataType(), customKpiFromUI.getKpiType());
                    throw new ServerException("dataType of the KPI is invalid");
                }
            }

            if (customKpiFromUI.getValueType() != null && !kpiType.getSubTypeName().equalsIgnoreCase(Constants.CORE_KPI_TYPE)
                    && customKpiFromUI.getValueType() == ValueType.DELTA) {
                LOGGER.error("valueType validation failure. Reason: valueType [{}] is not available for kpiType [{}].", customKpiFromUI.getValueType(), customKpiFromUI.getKpiType());
                throw new ServerException("valueType of the KPI is invalid");
            }

            if (customKpiFromUI.getComponents() != null && !customKpiFromUI.getComponents().isEmpty()) {
                validateKpiComponentMapping(customKpiFromUI.getId(), customKpiFromUI.getComponents(), accountId);
            }

            if (existingKpi.getName().equalsIgnoreCase(customKpiFromUI.getName())
                    && existingKpi.getDescription().equalsIgnoreCase(customKpiFromUI.getDescription())
                    && noChangeInFormula(computedKpiDetails, existingFormula)
                    && !newKpis.isEmpty() && existingMappedKpis.size() == computedKpiDetails.getKpisUsed().size()
                    && customKpiFromUI.getStatus() == existingKpi.getStatus()
                    && customKpiFromUI.getKpiCategoryDetails() == null && customKpiFromUI.getCollectionInterval() < 1
                    && (customKpiFromUI.getClusterOperation() == null || existingKpi.getClusterOperation().equalsIgnoreCase(customKpiFromUI.getClusterOperation()))
                    && (customKpiFromUI.getRollupOperation() == null || existingKpi.getRollupOperation().equalsIgnoreCase(customKpiFromUI.getRollupOperation()))
                    && (customKpiFromUI.getClusterAggregation() == null || MasterCache.getMstTypeForSubTypeName("AggregationType",
                    customKpiFromUI.getClusterAggregation()).getSubTypeId() == existingKpi.getClusterAggregation())
                    && (customKpiFromUI.getInstanceAggregation() == null || MasterCache.getMstTypeForSubTypeName("AggregationType",
                    customKpiFromUI.getInstanceAggregation()).getSubTypeId() == existingKpi.getInstanceAggregation())
                    && (customKpiFromUI.getDataType() == null || customKpiFromUI.getDataType().equalsIgnoreCase(existingKpi.getDataType()))
                    && (customKpiFromUI.getKpiUnit() == null || customKpiFromUI.getKpiUnit().equalsIgnoreCase(existingKpi.getMeasureUnits()))
                    && (customKpiFromUI.getValueType() == null || customKpiFromUI.getValueType().toString().equalsIgnoreCase(existingKpi.getValueType()))
                    && (customKpiFromUI.getComponents() == null || customKpiFromUI.getComponents().isEmpty())) {
                String err = "No modification in the input KPI name, description, category mapping, collection interval, " +
                        "dataType, valueType, status, kpiUnit, clusterOperation, rollupOperation, instanceAggregation, clusterAggregation" +
                        "supported KPIs,  formula or component mapping.";
                LOGGER.error(err);
                throw new ServerException(err);
            }
        }

        return customKpiFromUIList;
    }

    private boolean noChangeInFormula(ComputedKpiDetails computedKpiDetails, ComputedKpiBean existingFormula) {
        return existingFormula != null && computedKpiDetails.getFormula().equalsIgnoreCase(existingFormula.getFormula())
                && computedKpiDetails.getDisplayFormula().equalsIgnoreCase(existingFormula.getDisplayFormula());
    }

    private void validateKpiComponentMapping(int kpiId, List<CustomKpiFromUI.ComponentMapping> componentMappings,
                                             int accountId) throws ServerException {
        try {

            if (kpiDataService.getKPICountMappedToComputedKpi(kpiId, accountId, null) > 0) {
                LOGGER.error("The custom KPI - component mapping can't be modified as it mapped to computed KPI.");
                throw new ServerException("The custom KPI - component mapping can't be modified as it mapped to computed KPI.");
            }

            Map<Integer, Integer> instanceCountForComponentsMap = new CompInstanceDataService().getActiveInstanceCountForComponents(accountId, null)
                    .parallelStream().collect(Collectors.toMap(CountBean::getId, CountBean::getCount));
            List<Integer> compIds = new KPIDataService().getComponentIdsForKPI(kpiId, null);

            for (CustomKpiFromUI.ComponentMapping component : componentMappings) {
                if (!compIds.contains(component.getId())) {
                    String error = "The component - " + component.getId() + " is not mapped to the KPI - " + kpiId + ". ";
                    LOGGER.error(error);
                    throw new ServerException(error);
                }
                if (component.getUnmap() == 1 && instanceCountForComponentsMap.getOrDefault(component.getId(), 0) != 0) {
                    String error = "The component - " + component.getId() + " has active instances hence the mapping with the KPI can't be removed.";
                    LOGGER.error(error);
                    throw new ServerException(error);
                }
            }
        } catch (ControlCenterException e) {
            throw new ServerException(e.getMessage());
        }
    }

    @Override
    public String process(List<CustomKpiFromUI> beans) throws DataProcessingException {
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        String id;
        try {
            id = dbi.inTransaction((conn, status) -> updateKpiDetails(beans, userId, accountId, conn));
            updateKpiDetailsInRedis(beans);
        } catch (Exception e) {
            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw (DataProcessingException) Throwables.getRootCause(e);
            } else {
                throw e;
            }
        }
        return id;
    }

    private String updateKpiDetails(List<CustomKpiFromUI> customKpiFromUIList, String userId, int accountId, Handle handle) throws DataProcessingException {
        List<KpiBean> kpiBeans = new ArrayList<>();
        List<KpiBean> statusChangedNonGroupKpiIds = new ArrayList<>();
        List<KpiBean> statusChangedGroupKpiIds = new ArrayList<>();
        List<ComputedKpiToKpiMapping> kpisMappingList = new ArrayList<>();
        List<CompVersionKpiMappingBean> compVersionKpiMappingBeanList = new ArrayList<>();
        List<ComputedKpiBean> computedKpiBeans = new ArrayList<>();
        List<TagMappingDetails> kpiCategoryMappingDetails = new ArrayList<>();
        List<CompVersionKpiMappingBean> updateCollectionIntervalDetails = new ArrayList<>();

        for (CustomKpiFromUI customKpiFromUI : customKpiFromUIList) {
            KpiBean existingKpi = kpiDataService.fetchKpiUsingKpiId(customKpiFromUI.getId(), accountId, null);
            customKpiFromUI.setComponentId(existingKpi.getComponentId());
            int clusterAggType = customKpiFromUI.getClusterAggregation() != null ? MasterCache
                    .getMstTypeForSubTypeName("AggregationType", customKpiFromUI.getClusterAggregation())
                    .getSubTypeId() : existingKpi.getClusterAggregation();
            int instAggType = customKpiFromUI.getInstanceAggregation() != null ? MasterCache
                    .getMstTypeForSubTypeName("AggregationType", customKpiFromUI.getInstanceAggregation())
                    .getSubTypeId() : existingKpi.getInstanceAggregation();

            KpiBean kpiBean = KpiBean.builder()
                    .id(customKpiFromUI.getId())
                    .name(customKpiFromUI.getName())
                    .description(customKpiFromUI.getDescription())
                    .status(customKpiFromUI.getStatus())
                    .userId(userId)
                    .accountId(accountId)
                    .dataType(customKpiFromUI.getDataType())
                    .valueType(customKpiFromUI.getValueType() == null ? null : customKpiFromUI.getValueType().toString())
                    .measureUnits(customKpiFromUI.getKpiUnit())
                    .clusterOperation(customKpiFromUI.getClusterOperation())
                    .rollupOperation(customKpiFromUI.getRollupOperation())
                    .instanceAggregation(instAggType)
                    .clusterAggregation(clusterAggType)
                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .cronExpression(customKpiFromUI.getCronExpression())
                    .build();

            kpiBeans.add(kpiBean);

            if (existingKpi.getGroupKpiId() != 0 && existingKpi.getStatus() != customKpiFromUI.getStatus()) {
                statusChangedGroupKpiIds.add(KpiBean.builder().id(customKpiFromUI.getId()).status(customKpiFromUI.getStatus()).build());
            } else if (existingKpi.getGroupKpiId() == 0 && existingKpi.getStatus() != customKpiFromUI.getStatus()) {
                statusChangedNonGroupKpiIds.add(KpiBean.builder().id(customKpiFromUI.getId()).status(customKpiFromUI.getStatus()).build());
            }

            if (customKpiFromUI.getComponents() != null && !customKpiFromUI.getComponents().isEmpty()) {
                updateKpiComponentMapping(customKpiFromUI.getId(), customKpiFromUI.getComponents(), accountId, userId,
                        existingKpi.getGroupKpiId() == 0, handle);
            }

            List<Integer> existingMappedKpis = kpiDataService.getComputedKpiMapping(customKpiFromUI.getId(), accountId, null);
            int computedKpiDetailsId = kpiDataService.getComputedDetailsId(customKpiFromUI.getId(), null);

            ComputedKpiDetails computedKpiDetails = customKpiFromUI.getComputedKpiDetails();

            if (computedKpiDetails != null) {
                List<Integer> newKpis = computedKpiDetails.getKpisUsed().parallelStream()
                        .filter(v -> !existingMappedKpis.contains(v))
                        .collect(Collectors.toList());

                if (!newKpis.isEmpty() || existingMappedKpis.size() != computedKpiDetails.getKpisUsed().size()) {
                    int deletedIds = kpiDataService.deleteComputedKpiMapping(customKpiFromUI.getId(), accountId, handle);

                    if (deletedIds <= 0 || deletedIds != existingMappedKpis.size()) {
                        LOGGER.error("Error while deleting old computed KPI to supporting KPI mapping. Kindly check if the KPI being modified is a computed KPI mapped to the accountId [{}]", accountId);
                        throw new DataProcessingException("Error while removing supporting KPI mapping");
                    }

                    List<ComputedKpiToKpiMapping> mappingList = computedKpiDetails.getKpisUsed()
                            .parallelStream()
                            .map(m -> ComputedKpiToKpiMapping.builder()
                                    .accountId(accountId)
                                    .userDetailsId(userId)
                                    .computedKpiId(customKpiFromUI.getId())
                                    .computedKpiDetailsId(computedKpiDetailsId)
                                    .baseKpiId(m)
                                    .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                                    .build())
                            .collect(Collectors.toList());

                    kpisMappingList.addAll(mappingList);
                }

                ComputedKpiBean existingFormula = kpiDataService.fetchComputedKpiFormula(customKpiFromUI.getId(), accountId, null);

                if (!computedKpiDetails.getFormula().equalsIgnoreCase(existingFormula.getFormula())
                        && !computedKpiDetails.getDisplayFormula().equalsIgnoreCase(existingFormula.getDisplayFormula())) {
                    ComputedKpiBean computedKpiBean = ComputedKpiBean.builder().computedKpiId(customKpiFromUI.getId())
                            .displayFormula(computedKpiDetails.getDisplayFormula())
                            .formula(computedKpiDetails.getFormula())
                            .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                            .userDetailsId(userId)
                            .build();

                    computedKpiBeans.add(computedKpiBean);
                }
            }

            if (customKpiFromUI.getComponentCommonVersionId() > 0 && customKpiFromUI.getComponentTypeId() > 0 && customKpiFromUI.getComponentId() > 0) {
                List<CompVersionKpiMappingBean> mappingBeans = MasterDataService.getComponentMappingForKpi(customKpiFromUI.getId(), null);

                Optional<CompVersionKpiMappingBean> newMapping = mappingBeans.parallelStream()
                        .filter(m -> (m.getCommonVersionId() == customKpiFromUI.getComponentCommonVersionId())
                                && (m.getComponentTypeId() == customKpiFromUI.getComponentTypeId())
                                && (m.getComponentId() == customKpiFromUI.getComponentId())).findAny();

                if (!newMapping.isPresent()) {
                    CompVersionKpiMappingBean compVersionKpiMapping = CompVersionKpiMappingBean.builder()
                            .isCustom(1)
                            .kpiDetailsId(customKpiFromUI.getId())
                            .doAnalytics(customKpiFromUI.getAvailableForAnalytics())
                            .commonVersionId(customKpiFromUI.getComponentCommonVersionId())
                            .userDetailsId(userId)
                            .defaultCollectionInterval(customKpiFromUI.getCollectionInterval() == 0 ? 60 : customKpiFromUI.getCollectionInterval())
                            .status(1)
                            .componentId(customKpiFromUI.getComponentId())
                            .componentTypeId(customKpiFromUI.getComponentTypeId())
                            .createdTime(DateTimeUtil.getCurrentTimestampInGMT())
                            .updatedTime(DateTimeUtil.getCurrentTimestampInGMT())
                            .build();

                    compVersionKpiMappingBeanList.add(compVersionKpiMapping);
                }
            }

            if (customKpiFromUI.getKpiCategoryDetails() != null) {
                TagDetailsBean tagDetailsBean = MasterCache.getTagDetails("Category");

                if (null != tagDetailsBean) {
                    kpiCategoryMappingDetails.add(TagMappingDetails.builder()
                            .tagId(tagDetailsBean.getId())
                            .tagKey(String.valueOf(customKpiFromUI.getKpiCategoryDetails().getId()))
                            .tagValue(customKpiFromUI.getKpiCategoryDetails().getIdentifier())
                            .objectId(customKpiFromUI.getId())
                            .accountId(accountId)
                            .userDetailsId(userId)
                            .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                            .build());
                } else {
                    throw new DataProcessingException("Unable to fetch tag details.");
                }
            }

            if (customKpiFromUI.getCollectionInterval() > 0) {
                updateCollectionIntervalDetails.add(CompVersionKpiMappingBean.builder()
                        .kpiDetailsId(customKpiFromUI.getId())
                        .commonVersionId(customKpiFromUI.getComponentCommonVersionId())
                        .userDetailsId(userId)
                        .defaultCollectionInterval(customKpiFromUI.getCollectionInterval())
                        .componentId(customKpiFromUI.getComponentId())
                        .componentTypeId(customKpiFromUI.getComponentTypeId())
                        .updatedTime(DateTimeUtil.getCurrentTimestampInGMT())
                        .build());
            }
        }

        if (!kpiBeans.isEmpty()) {
            int[] updatedId = kpiDataService.updateKpiDetails(kpiBeans, handle);
            if (updatedId == null || updatedId.length != kpiBeans.size()) {
                LOGGER.error("Error while updating custom KPI details. Kindly check if the KPI being modified is a computed KPI mapped to the accountId [{}]", accountId);
                throw new DataProcessingException("Error while updating KPI details");
            }
        }

        if (!kpiCategoryMappingDetails.isEmpty()) {
            int[] updatedId = kpiDataService.updateKpiCategoryMapping(kpiCategoryMappingDetails, handle);
            if (updatedId == null || updatedId.length != kpiCategoryMappingDetails.size()) {
                LOGGER.error("Error while updating KPI category mapping details. Kindly check if the KPI being modified is a computed KPI mapped to the accountId [{}]", accountId);
                throw new DataProcessingException("Error while updating KPI category mapping details.");
            }
        }

        if (!updateCollectionIntervalDetails.isEmpty()) {
            int[] updatedId = kpiDataService.updateDefaultCollectionIntervalForKPI(updateCollectionIntervalDetails, handle);
            if (updatedId == null || updatedId.length != updateCollectionIntervalDetails.size()) {
                LOGGER.error("Error while updating KPI category mapping details. Kindly check if the KPI being modified is a computed KPI mapped to the accountId [{}]", accountId);
                throw new DataProcessingException("Error while updating KPI category mapping details.");
            }
        }

        if (!statusChangedGroupKpiIds.isEmpty()) {
            int[] instanceLevelStatusUpdateIds = kpiDataService.statusChangeInstanceLevelGroupKpi(statusChangedGroupKpiIds, handle);

            if (instanceLevelStatusUpdateIds == null || instanceLevelStatusUpdateIds.length != statusChangedGroupKpiIds.size()) {
                LOGGER.error("Error while updating status computed KPI at instance level");
                throw new DataProcessingException("Error while updating status computed KPI at instance level");
            }
        }

        if (!statusChangedNonGroupKpiIds.isEmpty()) {
            int[] instanceLevelStatusUpdateIds = kpiDataService.statusChangeInstanceLevelNonGroupKpi(statusChangedNonGroupKpiIds, handle);

            if (instanceLevelStatusUpdateIds == null || instanceLevelStatusUpdateIds.length != statusChangedNonGroupKpiIds.size()) {
                LOGGER.error("Error while updating status computed KPI at instance level");
                throw new DataProcessingException("Error while updating status computed KPI at instance level");
            }
        }

        if (!kpisMappingList.isEmpty()) {
            int[] mappingCount = kpiDataService.mapExistingComputedKpiToMstKpi(kpisMappingList, handle);

            if (mappingCount == null || mappingCount.length != kpisMappingList.size()) {
                throw new DataProcessingException(("Error while mapping computed KPI to supporting KPIs"));
            }
        }

        if (!computedKpiBeans.isEmpty()) {
            int[] updatedFormula = kpiDataService.updateComputedKpiFormula(computedKpiBeans, handle);

            if (updatedFormula == null || updatedFormula.length <= 0) {
                LOGGER.error("Error while updating formula for computed KPIs");
                throw new DataProcessingException("Error while updating formula for computed KPIs");
            }
        }

        if (!compVersionKpiMappingBeanList.isEmpty()) {
            int[] ids = MasterDataService.bulkInsertIntoComponentVersionKpiMapping(compVersionKpiMappingBeanList, handle);

            if (ids == null || ids.length <= 0) {
                throw new DataProcessingException("Error while mapping KPI to provided component version");
            }
        }
        return "Custom KPI(s) updated successfully";
    }

    private void updateKpiComponentMapping(int kpiId, List<CustomKpiFromUI.ComponentMapping> componentMappings, int accountId,
                                           String user, Boolean isGroup, Handle handle) throws DataProcessingException {
        try {
            CompInstanceDataService compInstanceDataService = new CompInstanceDataService();

            List<CompVersionKpiMappingBean> statusModifyCompLevelList = new ArrayList<>();
            List<CompVersionKpiMappingBean> statusModifyCompInstanceLevelList = new ArrayList<>();
            List<Integer> removeCompVersionKpiMappingList = new ArrayList<>();
            List<Integer> removeCompInstanceKpiMappingList = new ArrayList<>();

            componentMappings.forEach(comp -> {
                List<Integer> compInstanceIds = compInstanceDataService.getInstanceIdsForComponent(comp.getId(), accountId, handle);
                if (comp.getUnmap() == 1) {
                    removeCompVersionKpiMappingList.add(comp.getId());
                    removeCompInstanceKpiMappingList.addAll(compInstanceIds);
                } else {
                    statusModifyCompLevelList.add(CompVersionKpiMappingBean.builder()
                            .kpiDetailsId(kpiId)
                            .userDetailsId(user)
                            .componentId(comp.getId())
                            .updatedTime(DateTimeUtil.getCurrentTimestampInGMT())
                            .status(comp.getStatus())
                            .build());
                    compInstanceIds.forEach(compInstance ->
                            statusModifyCompInstanceLevelList.add(CompVersionKpiMappingBean.builder()
                                    .kpiDetailsId(kpiId)
                                    .userDetailsId(user)
                                    .compInstanceId(compInstance)
                                    .updatedTime(DateTimeUtil.getCurrentTimestampInGMT())
                                    .status(comp.getStatus())
                                    .build()));
                }
            });

            BindInDataService bindInDataService = new BindInDataService();
            if (!removeCompVersionKpiMappingList.isEmpty()) {
                bindInDataService.removeCompVersionKpiMapping(kpiId, removeCompVersionKpiMappingList, handle);
            }
            if (!removeCompInstanceKpiMappingList.isEmpty()) {
                if (Boolean.TRUE.equals(isGroup)) {
                    bindInDataService.removeCompInstanceGroupKpiMapping(kpiId, removeCompInstanceKpiMappingList, handle);
                } else {
                    bindInDataService.removeCompInstanceNonGroupKpiMapping(kpiId, removeCompInstanceKpiMappingList, handle);
                }
            }

            if (!statusModifyCompLevelList.isEmpty()) {
                kpiDataService.updateCompVersionKpiMappingStatus(statusModifyCompLevelList, handle);
            }
            if (!statusModifyCompInstanceLevelList.isEmpty()) {
                if (Boolean.TRUE.equals(isGroup)) {
                    kpiDataService.updateCompInstanceGroupKpiMappingStatus(statusModifyCompInstanceLevelList, handle);
                } else {
                    kpiDataService.updateCompInstanceNonGroupKpiMappingStatus(statusModifyCompInstanceLevelList, handle);
                }
            }

        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
    }
    private void updateKpiDetailsInRedis(List<CustomKpiFromUI> customKpiFromUIList) {
        ComponentRepo componentRepo = new ComponentRepo();
        Map<Integer, Component> componentMap = componentRepo.getComponentDetails(accountIdentifier).stream().collect(Collectors.toMap(Component::getId, Function.identity()));
        if(componentMap.isEmpty()){
            LOGGER.error("Component details not found for the given accountIdentifier: {}", accountIdentifier);
            return; 
        }
        for (CustomKpiFromUI customKpiFromUI : customKpiFromUIList) {
            String componentName = componentMap.get(customKpiFromUI.getComponentId()).getName();
            if (componentName == null) {
                LOGGER.error("component name not found for a given componentId: {}", customKpiFromUI.getComponentId());
                return;
            }
            List<ComponentKpiEntity> existingComponentKpiDetails = componentRepo.getComponentKpiDetails(accountIdentifier, componentName);
            if (existingComponentKpiDetails.isEmpty()) {
                LOGGER.error("kpi details not found for the given accountIdentifier: {} and componentName: {}", accountIdentifier, componentName);
                return;
            }
            ComponentKpiEntity kpiEntity = existingComponentKpiDetails.parallelStream().filter(kpi -> kpi.getId() == customKpiFromUI.getId()).findAny().orElse(null);
            if (kpiEntity == null) {
                LOGGER.error("Kpi details not found for the given KpiId: {}", customKpiFromUI.getId());
                return;
            }
            List<Integer> baseKpiList = new ArrayList<>();
            KpiCategoryDetails kpiCategoryDetails = new KpiCategoryDetails();
            if (customKpiFromUI.getComponents() != null) {
                kpiEntity.setStatus(customKpiFromUI.getComponents().get(0).getStatus());
            }
            kpiEntity.setName(customKpiFromUI.getName() != null ? customKpiFromUI.getName() : kpiEntity.getName());
            kpiEntity.setCronExpression(customKpiFromUI.getCronExpression() != null ? customKpiFromUI.getCronExpression() : kpiEntity.getCronExpression());
            kpiEntity.setUnit(customKpiFromUI.getKpiUnit() != null ? customKpiFromUI.getKpiUnit() : kpiEntity.getUnit());
            kpiEntity.setInstanceAggType(customKpiFromUI.getInstanceAggregation() != null ? customKpiFromUI.getInstanceAggregation() : kpiEntity.getInstanceAggType());
            kpiEntity.setClusterAggType(customKpiFromUI.getClusterAggregation() != null ? customKpiFromUI.getClusterOperation() : kpiEntity.getClusterAggType());
            kpiEntity.setClusterAggType(customKpiFromUI.getClusterAggregation() != null ? customKpiFromUI.getClusterAggregation() : kpiEntity.getClusterAggType());
            kpiEntity.getCommonVersionDetails().forEach(f-> f.setCollectionInterval(customKpiFromUI.getCollectionInterval() > 0 ? customKpiFromUI.getCollectionInterval() : f.getCollectionInterval()));

            if (customKpiFromUI.getClusterOperation() != null) {
                kpiEntity.setAggOperation(ClusterOperationEnum.findByClusterOperation(customKpiFromUI.getClusterOperation()));
            }
            if (customKpiFromUI.getValueType() != null) {
                kpiEntity.setValueType(customKpiFromUI.getValueType().getType());
            }
            if (customKpiFromUI.getDescription() != null) {
                kpiEntity.setDescription(customKpiFromUI.getDescription());
            }
            kpiEntity.setDataType(customKpiFromUI.getDataType() != null ? customKpiFromUI.getDataType() : kpiEntity.getDataType());
            if (customKpiFromUI.getValueType() != null) {
                kpiEntity.setValueType(customKpiFromUI.getValueType().getType());
            }
            if (customKpiFromUI.getRollupOperation() != null) {
                kpiEntity.setRollupOperation(ClusterOperationEnum.findByClusterOperation(customKpiFromUI.getRollupOperation()));
            }
            if (customKpiFromUI.getKpiCategoryDetails() != null) {
                kpiCategoryDetails = buildCategoryDetail(customKpiFromUI.getKpiCategoryDetails());
                kpiEntity.setCategoryDetails(kpiCategoryDetails);
            }
            List<Integer> removeBaseKpi = new ArrayList<>();
            if (customKpiFromUI.getComputedKpiDetails() != null) {
                if (!kpiEntity.getComputedKpiPojo().getBaseKpiIds().isEmpty()) {
                    try {
                        baseKpiList = kpiDataService.getBaseKpiIds(accountId, null);
                    } catch (ControlCenterException e) {
                        LOGGER.error("Base kpi details not found for the given accountId: {}", accountId);
                        throw new RuntimeException(e);
                    }
                    if(kpiEntity.getComputedKpiPojo() != null && !kpiEntity.getComputedKpiPojo().getBaseKpiIds().isEmpty()){
                        List<Integer> finalBaseKpiList = baseKpiList;
                        removeBaseKpi = kpiEntity.getComputedKpiPojo().getBaseKpiIds().parallelStream().filter(baseKpiId -> !finalBaseKpiList.contains(baseKpiId)).collect(Collectors.toList());
                    }
                    List<Integer> finalRemoveBaseKpi = removeBaseKpi;
                    existingComponentKpiDetails.forEach(kpi ->{
                        if(finalRemoveBaseKpi.contains(kpi.getId())){
                            kpi.setIsBaseMetric(0);
                        }else if (customKpiFromUI.getComputedKpiDetails().getKpisUsed().contains(kpi.getId())) {
                            kpi.setIsBaseMetric(1);
                        }
                    });
                }
                com.heal.configuration.pojos.ComputedKpiPojo computedKpiPojo = com.heal.configuration.pojos.ComputedKpiPojo.builder()
                        .computedKpiId(customKpiFromUI.getId())
                        .displayFormula(customKpiFromUI.getComputedKpiDetails().getFormula())
                        .formula(customKpiFromUI.getComputedKpiDetails().getDisplayFormula())
                        .baseKpiIds(customKpiFromUI.getComputedKpiDetails().getKpisUsed())
                        .build();
                kpiEntity.setComputedKpiPojo(computedKpiPojo);
            }
            componentRepo.updateComponentKpiDetails(existingComponentKpiDetails, accountIdentifier, componentName);
            updateCompInstanceKpi(customKpiFromUI, kpiCategoryDetails, removeBaseKpi);
        }
    }

    private KpiCategoryDetails buildCategoryDetail(CategoryDetails kpiCategoryDetails) {
        return KpiCategoryDetails.builder()
                .id(kpiCategoryDetails.getId())
                .identifier(kpiCategoryDetails.getIdentifier())
                .name(kpiCategoryDetails.getName())
                .isWorkLoad(kpiCategoryDetails.isWorkLoad())
                .build();
    }
    private void updateCompInstanceKpi(CustomKpiFromUI customKpiFromUI, KpiCategoryDetails kpiCategoryDetails, List<Integer> finalRemoveBaseKpi) {
        InstanceRepo instanceRepo = new InstanceRepo();
        List<CompInstClusterDetails> instances = instanceRepo.getInstances(accountIdentifier);
        if(instances.isEmpty()){
            LOGGER.error("instance details not found for the given accountIdentifier: {}", accountIdentifier);
            return;
        }
        instances.parallelStream().
                filter( instance -> instance.getComponentId() == customKpiFromUI.getComponentId()).
                forEach(instance -> {
                    List<CompInstKpiEntity> instanceWiseKpis = instanceRepo.getInstanceWiseKpis(accountIdentifier, instance.getIdentifier());
                    CompInstKpiEntity kpiEntity = instanceWiseKpis.parallelStream().filter(kpi -> kpi.getId() == customKpiFromUI.getId()).findAny().orElse(null);
                    if(kpiEntity != null){
                        instanceWiseKpis.remove(kpiEntity);
                        
                        if(customKpiFromUI.getComponents() != null){
                            kpiEntity.setStatus(customKpiFromUI.getComponents().get(0).getStatus());
                        }
                        kpiEntity.setName(customKpiFromUI.getName() != null? customKpiFromUI.getName() :  kpiEntity.getName());
                        kpiEntity.setCollectionInterval(customKpiFromUI.getCollectionInterval() > 0 ? customKpiFromUI.getCollectionInterval() : kpiEntity.getCollectionInterval());
                        kpiEntity.setCronExpression(customKpiFromUI.getCronExpression() != null? customKpiFromUI.getCronExpression() : kpiEntity.getCronExpression());
                        kpiEntity.setUnit(customKpiFromUI.getKpiUnit() != null? customKpiFromUI.getKpiUnit() : kpiEntity.getUnit());
                        kpiEntity.setInstanceAggType(customKpiFromUI.getInstanceAggregation() != null? customKpiFromUI.getInstanceAggregation() : kpiEntity.getInstanceAggType());
                        kpiEntity.setClusterAggType(customKpiFromUI.getClusterAggregation() != null? customKpiFromUI.getClusterOperation() : kpiEntity.getClusterAggType());
                        kpiEntity.setClusterAggType(customKpiFromUI.getClusterAggregation() != null ? customKpiFromUI.getClusterAggregation() : kpiEntity.getClusterAggType());
                        if(customKpiFromUI.getValueType() != null){
                            kpiEntity.setValueType(customKpiFromUI.getValueType().getType());
                        }
                        if(customKpiFromUI.getDescription() != null){
                            kpiEntity.setDescription(customKpiFromUI.getDescription());
                        }
                        if(customKpiFromUI.getKpiCategoryDetails() != null){
                            kpiEntity.setCategoryDetails(kpiCategoryDetails);
                        }
                        if(customKpiFromUI.getClusterOperation() != null){
                            kpiEntity.setAggOperation(ClusterOperationEnum.findByClusterOperation(customKpiFromUI.getClusterOperation()));
                        }
                        kpiEntity.setDataType(customKpiFromUI.getDataType() != null ? customKpiFromUI.getDataType() : kpiEntity.getDataType());
                        if ( customKpiFromUI.getValueType() != null){
                            kpiEntity.setValueType(customKpiFromUI.getValueType().getType());
                        }
                        if(customKpiFromUI.getRollupOperation() != null) {
                            kpiEntity.setRollupOperation(ClusterOperationEnum.findByClusterOperation(customKpiFromUI.getRollupOperation()));
                        }
                        if(customKpiFromUI.getComputedKpiDetails() != null){
                            if(!kpiEntity.getComputedKpiPojo().getBaseKpiIds().isEmpty()){
                                instanceWiseKpis.forEach(kpi ->{
                                    if(finalRemoveBaseKpi.contains(kpi.getId())){
                                        kpi.setIsBaseMetric(0);
                                    }else if (customKpiFromUI.getComputedKpiDetails().getKpisUsed().contains(kpi.getId())) {
                                        kpi.setIsBaseMetric(1);
                                    }
                                });
                            }
                            com.heal.configuration.pojos.ComputedKpiPojo  computedKpiPojo = com.heal.configuration.pojos.ComputedKpiPojo.builder()
                                    .computedKpiId(customKpiFromUI.getId())
                                    .displayFormula(customKpiFromUI.getComputedKpiDetails().getFormula())
                                    .formula(customKpiFromUI.getComputedKpiDetails().getDisplayFormula())
                                    .baseKpiIds(customKpiFromUI.getComputedKpiDetails().getKpisUsed())
                                    .build();
                            kpiEntity.setComputedKpiPojo(computedKpiPojo);
                        }

                        instanceWiseKpis.add(kpiEntity);

                        instanceRepo.updateKpiDetailsForKpiId(accountIdentifier, instance.getIdentifier(),kpiEntity);
                        instanceRepo.updateKpiDetailsForKpiIdentifier(accountIdentifier, instance.getIdentifier(), kpiEntity);
                        instanceRepo.updateKpiDetails(accountIdentifier, instance.getIdentifier(), instanceWiseKpis);
                    }
                });
    }
}
