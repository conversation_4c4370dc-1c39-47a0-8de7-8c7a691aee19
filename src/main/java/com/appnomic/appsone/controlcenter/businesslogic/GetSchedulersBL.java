package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.MasterDataRepo;
import com.appnomic.appsone.controlcenter.dao.redis.SchedulersRedisRepo;
import com.appnomic.appsone.controlcenter.dao.redis.UsersRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.entities.SchedulerDetails;
import com.heal.configuration.pojos.ScheduleArguments;
import com.heal.configuration.pojos.Schedulers;
import com.heal.configuration.pojos.ViewTypes;
import java8.util.Objects;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class GetSchedulersBL implements BusinessLogic<String, List<SchedulerDetails>, List<Schedulers>> {

    SchedulersRedisRepo schedulersRedisRepo = new SchedulersRedisRepo();
    MasterDataRepo masterDataRepo = new MasterDataRepo();

    @Override
    public UtilityBean<String> clientValidation(RequestObject requestObject) throws ClientException {
        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Account identifier is invalid");
            throw new ClientException("Account identifier is invalid");
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);

        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        return UtilityBean.<String>builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .build();
    }

    @Override
    public List<SchedulerDetails> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        String accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        List<SchedulerDetails> schedulerDetails = schedulersRedisRepo.getSchedulers(accountIdentifier);

        if (schedulerDetails.isEmpty()) {
            log.error("Scheduler details unavailable");
            throw new ServerException("Scheduler details unavailable");
        }

        return schedulerDetails;
    }

    @Override
    public List<Schedulers> process(List<SchedulerDetails> schedulerDetails) throws DataProcessingException {
        List<ViewTypes> viewTypes = masterDataRepo.getTypes();
        UsersRepo usersRepo = new UsersRepo();

        List<Schedulers> schedulers = schedulerDetails.parallelStream()
                .map(s -> {
                    ViewTypes jobType = viewTypes.parallelStream().filter(v -> v.getSubTypeId() == s.getSinkTypeId()).findFirst().orElse(null);
                    if (jobType == null) {
                        log.error("JobType unavailable for subtype ID [{}]", s.getSinkTypeId());
                        return null;
                    }

                    List<ScheduleArguments> arguments = s.getSchedulerArguments().parallelStream()
                            .map(a -> ScheduleArguments.builder()
                                    .id(a.getId())
                                    .argumentName(a.getArgumentName())
                                    .argumentValue(a.getArgumentValue())
                                    .defaultValue(a.getDefaultValue())
                                    .placeholder(a.getPlaceholder())
                                    .modifiedBy(a.getUserDetailsId())
                                    .lastModifiedTime(a.getUpdatedTime())
                                    .build())
                            .collect(Collectors.toList());

                    return Schedulers.builder()
                            .id(s.getId())
                            .name(s.getName())
                            .jobType(jobType.getTypeName())
                            .cronExpression(s.getCronExpression())
                            .startTime(s.getStartTime())
                            .endTime(s.getEndTime())
                            .modifiedBy(usersRepo.getUser(s.getUserDetailsId()).getUserName())
                            .lastModifiedTime(s.getUpdatedTime())
                            .lastJobStatus("FINISHED")
                            .lastJobTriggerTime(System.currentTimeMillis())
                            .arguments(arguments)
                            .build();
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());

        if(schedulers.size() != schedulerDetails.size()) {
            log.error("Error in processing schedulers list");
            throw new DataProcessingException("Error in processing schedulers list");
        }

        return schedulers;
    }
}
