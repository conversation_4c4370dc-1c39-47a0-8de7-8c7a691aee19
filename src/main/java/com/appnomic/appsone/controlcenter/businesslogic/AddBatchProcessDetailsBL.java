package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.BatchProcessMappingBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.BatchProcessDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProcessArgumentBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProcessDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProcessHostDetailsBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.BatchProcessDetails;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

public class AddBatchProcessDetailsBL implements BusinessLogic<BatchProcessDetails, ProcessDetailsBean, IdPojo> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AddBatchProcessDetailsBL.class);
    BatchProcessDataService batchProcessDataService = new BatchProcessDataService();

    @Override
    public UtilityBean<BatchProcessDetails> clientValidation(RequestObject request) throws ClientException {

        if (request == null) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(request.getBody())) {
            LOGGER.error("Invalid request body. Reason: Request body is either NULL or empty.");
            throw new ClientException("Invalid request body. Reason: Request body is either NULL or empty.");
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (authToken == null || authToken.trim().isEmpty()) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            LOGGER.error(UIMessages.ACCOUNT_NULL_OR_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_NULL_OR_EMPTY);
        }

        String requestBody = request.getBody();
        BatchProcessDetails batchProcessDetails;
        try {
            batchProcessDetails = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestBody,
                    new TypeReference<BatchProcessDetails>() {
                    });
        } catch (IOException e) {
            LOGGER.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        if (!batchProcessDetails.validateForAdd()) {
            LOGGER.error("Validation failure of batch process details provided");
            throw new ClientException(UIMessages.INVALID_REQUEST);
        }

        return UtilityBean.<com.appnomic.appsone.controlcenter.pojo.BatchProcessDetails>builder()
                .authToken(authToken)
                .accountIdentifier(identifier)
                .pojoObject(batchProcessDetails)
                .build();
    }

    @Override
    public ProcessDetailsBean serverValidation(UtilityBean<BatchProcessDetails> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            LOGGER.error("Error while extracting userIdentifier from authorization token. Reason: Invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            LOGGER.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }
        int accountId = account.getId();

        BatchProcessDetails batchProcessDetails = utilityBean.getPojoObject();

        if (batchProcessDetails.getProcessIdentifier() != null) {
            try {
                ProcessDetailsBean processDetailsBean = batchProcessDataService.getBatchProcessDetailsByIdentifier(batchProcessDetails.getProcessIdentifier());
                if (processDetailsBean != null) {
                    LOGGER.error("Process with identifier [{}] already exists", batchProcessDetails.getProcessIdentifier());
                    throw new ServerException("'processIdentifier' should be unique across all accounts");
                }
            } catch (ControlCenterException e) {
                LOGGER.error("Error while fetching process details using identifier [{}]", batchProcessDetails.getProcessIdentifier());
                throw new ServerException(String.format("Error while fetching process details using identifier [%s]", batchProcessDetails.getProcessIdentifier()));
            }
        } else {
            batchProcessDetails.setProcessIdentifier(UUID.randomUUID().toString());
        }

        try {
            ProcessDetailsBean processDetailsBean = batchProcessDataService.getBatchProcessDetailsByNameAndAccount(accountId, batchProcessDetails.getProcessName());
            if(processDetailsBean != null) {
                LOGGER.error("Process with name [{}] already exists for accountId [{}]", batchProcessDetails.getProcessIdentifier(), accountId);
                throw new ServerException(String.format("Process with name [%s] already exists for accountId [%d]", batchProcessDetails.getProcessIdentifier(), accountId));
            }
        } catch (ControlCenterException e) {
            LOGGER.error("Error while fetching process details using name [{}] for accountId [{}]", batchProcessDetails.getProcessIdentifier(), accountId);
            throw new ServerException(String.format("Error while fetching process details using name [%s] for accountId [%s]", batchProcessDetails.getProcessIdentifier(), accountId));
        }

        return ProcessDetailsBean.builder()
                .name(batchProcessDetails.getProcessName().trim())
                .identifier(batchProcessDetails.getProcessIdentifier().trim())
                .accountId(accountId)
                .batchJobs(batchProcessDetails.getBatchJobs())
                .hostDetails(getHostDetailsWithArguments(batchProcessDetails, userId))
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .userDetailsId(userId)
                .status(Constants.STATUS_ACTIVE)
                .build();
    }

    private List<ProcessHostDetailsBean> getHostDetailsWithArguments(BatchProcessDetails batchProcessDetails, String userId) {
        return batchProcessDetails.getHostDetails().parallelStream().map(host -> {
            List<ProcessArgumentBean> args = host.getArguments().parallelStream().map(arg -> ProcessArgumentBean.builder()
                    .name(arg.getName().trim())
                    .value(arg.getValue().trim())
                    .order(arg.getOrder())
                    .defaultValue(arg.getDefaultValue())
                    .isPlaceholder(arg.getPlaceHolder())
                    .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .userDetailsId(userId)
                    .status(Constants.STATUS_ACTIVE)
                    .build()).collect(Collectors.toList());

            return ProcessHostDetailsBean.builder()
                    .hostAddress(host.getHostAddress().trim())
                    .directoryPath(host.getDirectoryPath().trim())
                    .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .userDetailsId(userId)
                    .status(Constants.STATUS_ACTIVE)
                    .arguments(args)
                    .build();

        }).collect(Collectors.toList());
    }

    @Override
    public IdPojo process(ProcessDetailsBean processDetailsBean) throws DataProcessingException {
        try {
            return MySQLConnectionManager.getInstance().getHandle().inTransaction((conn, status) -> {

                int batchProcessId = batchProcessDataService.addBatchProcessDetails(processDetailsBean, conn);

                List<BatchProcessMappingBean> processMappingBeans = new ArrayList<>();

                if(processDetailsBean.getBatchJobs() != null && !processDetailsBean.getBatchJobs().isEmpty()) {
                    for (String batchJob : processDetailsBean.getBatchJobs()) {
                        processMappingBeans.add(BatchProcessMappingBean.builder()
                                .processId(batchProcessId)
                                .batchJobName(batchJob)
                                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                                .userDetailsId(processDetailsBean.getUserDetailsId())
                                .build());
                    }

                    int[] mappingIds = batchProcessDataService.addProcessBatchMapping(processMappingBeans, conn);

                    if (mappingIds.length != processDetailsBean.getBatchJobs().size()) {
                        LOGGER.error("Batch process mappings insertion failed");
                        throw new DataProcessingException("Batch process mappings insertion failed");
                    }
                }

                for (ProcessHostDetailsBean host : processDetailsBean.getHostDetails()) {
                    host.setProcessDetailsId(batchProcessId);
                    int processHostDetailsId = batchProcessDataService.addBatchProcessHostDetails(host, conn);

                    host.getArguments().forEach(arg -> {
                        arg.setProcessDetailsId(batchProcessId);
                        arg.setProcessHostDetailsId(processHostDetailsId);
                    });
                    batchProcessDataService.addBatchProcessHostArguments(host.getArguments(), conn);
                }

                return IdPojo.builder()
                        .id(batchProcessId)
                        .identifier(processDetailsBean.getIdentifier())
                        .name(processDetailsBean.getName())
                        .build();
            });
        } catch (Exception e) {
            LOGGER.error("Error while adding process details. Details: ", e);
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }
}
