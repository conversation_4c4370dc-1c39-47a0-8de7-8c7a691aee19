package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserProfileBean;
import com.appnomic.appsone.controlcenter.pojo.UserProfilePojo;
import com.appnomic.appsone.controlcenter.dao.mysql.UserAccessDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;

public class UserProfileBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserProfileBL.class);

    public List<UserProfilePojo> processRequestAndGetUserProfileList() throws ControlCenterException {
        List<UserProfilePojo> userProfileList = new ArrayList<>();
        List<UserProfileBean> userProfileBeans = UserAccessDataService.getUserProfiles();

        if (Objects.isNull(userProfileBeans) || userProfileBeans.isEmpty()) {
            LOGGER.debug("User profiles bean list is NULL or empty.");
            return new ArrayList<>();
        }

        for (UserProfileBean ub : userProfileBeans) {
            UserProfilePojo userProfilePojo = new UserProfilePojo();
            userProfilePojo.setUserProfileId(ub.getId());
            userProfilePojo.setUserProfileName(ub.getName());
            userProfilePojo.setRole(ub.getRoleName());
            userProfilePojo.setAccessibleFeatures(new HashSet<>(UserAccessDataService.getAccessProfileMapping(ub.getId())));
            userProfileList.add(userProfilePojo);
        }

        return userProfileList;
    }
}
