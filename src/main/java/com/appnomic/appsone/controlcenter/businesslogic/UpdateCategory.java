package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.CategoryDetailBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.CategoryType;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CategoryDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.CategoryRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.CategoryDetails;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.Category;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
@Slf4j
public class UpdateCategory implements BusinessLogic<CategoryDetails, CategoryDetailBean, IdPojo> {
    private static final ObjectMapper OBJECT_MAPPER = CommonUtils.getObjectMapperWithHtmlEncoder();

    CategoryDataService categoryDataService = new CategoryDataService();
    CategoryRepo categoryRepo = new CategoryRepo();

    @Override
    public UtilityBean<CategoryDetails> clientValidation(RequestObject request) throws ClientException {

        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(request.getBody())) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }

        String categoryIdString = request.getParams().get(Constants.CATEGORY_ID);
        if (categoryIdString == null || categoryIdString.trim().isEmpty()) {
            log.error("Category Id is null or empty.");
            throw new ClientException("Category Id is null or empty.");
        }

        int categoryId;
        try {
            categoryId = Integer.parseInt(categoryIdString);
        } catch (NumberFormatException e) {
            log.error("Category Id should be a positive integer.");
            throw new ClientException("Category Id should be a positive integer.");
        }

        String requestBody = request.getBody();
        CategoryDetails categoryDetails;
        try {
            categoryDetails = OBJECT_MAPPER.readValue(requestBody, new TypeReference<CategoryDetails>() {
            });
        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        if (!categoryDetails.validate()) {
            log.error("Validation failure of details provided");
            throw new ClientException(UIMessages.INVALID_REQUEST);
        }
        categoryDetails.setId(categoryId);

        return UtilityBean.<com.appnomic.appsone.controlcenter.pojo.CategoryDetails>builder()
                .authToken(authToken)
                .accountIdentifier(identifier)
                .pojoObject(categoryDetails)
                .build();
    }

    @Override
    public CategoryDetailBean serverValidation(UtilityBean<CategoryDetails> utilityBean) throws ServerException {

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be " +
                    "invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        int accountId = account.getId();
        CategoryDetails categoryUpdateDetails = utilityBean.getPojoObject();
        categoryUpdateDetails.setName(categoryUpdateDetails.getName().trim());

        List<CategoryDetailBean> categories;
        try {
            categories = categoryDataService.getCategoriesForAccount(accountId);
        } catch (ControlCenterException e) {
            throw new ServerException(e.getMessage());
        }

        if (categories.isEmpty()) {
            log.error("No category available for account identifier: [{}]", account.getIdentifier());
            throw new ServerException("No category available for account identifier: [{}]" +account.getIdentifier());
        }

        Optional<CategoryDetailBean> optional = categories.parallelStream().filter(c -> c.getId() == categoryUpdateDetails.getId()).findAny();

        CategoryDetailBean category;
        if (optional.isPresent()) {
            category = optional.get();
        } else {
            log.error("Category is not present for the specified account.");
            throw new ServerException("Category is not present in the specified account.");
        }

        if (category.getIsCustom() == 1 && categories.stream().filter(c -> c.getId() != categoryUpdateDetails.getId()).anyMatch(c -> c.getName()
                .equalsIgnoreCase(categoryUpdateDetails.getName()))) {
            log.error("Category name should be unique in the specified account.");
            throw new ServerException("Category name should be unique in the specified account.");
        }

        boolean updateStatus = category.getStatus() != categoryUpdateDetails.getStatus();

        try {
            categoryUpdateDetails.setKpiCount(categoryDataService.getKpiCountForCategory(category.getId()));
            if (updateStatus && categoryUpdateDetails.getKpiCount() != 0) {
                log.error("Category status can only be modified if no KPIs are mapped to the category.");
                throw new ServerException("Category status can only be modified if no KPIs are mapped to the category.");
            }
        } catch (ControlCenterException e) {
            throw new ServerException(e.getSimpleMessage());
        }

        if (category.getIsCustom() == 0) {
            if ((categoryUpdateDetails.getSubType() != null && !categoryUpdateDetails.getSubType().isEmpty() &&
                    !categoryUpdateDetails.getSubType().trim().equalsIgnoreCase(getSubType(category).getType())) ||
                    updateStatus || !categoryUpdateDetails.getName().trim().equalsIgnoreCase(category.getName())) {
                throw new ServerException("Only category description can be modified for Standard categories.");
            }

            if (categoryUpdateDetails.getDescription() == null || categoryUpdateDetails.getDescription().isEmpty()) {
                throw new ServerException("Description provided is null or empty for the standard category.");
            }

            return CategoryDetailBean.builder()
                    .id(category.getId())
                    .name(category.getName())
                    .isCustom(category.getIsCustom())
                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .description(categoryUpdateDetails.getDescription())
                    .status(category.getStatus())
                    .isInformative(category.getIsInformative())
                    .isWorkLoad(category.getIsWorkLoad())
                    .accountIdentifier(account.getIdentifier())
                    .build();
        }

        int isInformative = (categoryUpdateDetails.getSubType().trim().equalsIgnoreCase(CategoryType.INFO.getType())) ? 1 : 0;

        return CategoryDetailBean.builder()
                .id(category.getId())
                .name(categoryUpdateDetails.getName().trim())
                .isCustom(category.getIsCustom())
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .description(categoryUpdateDetails.getDescription() != null ? categoryUpdateDetails.getDescription() : "")
                .status(categoryUpdateDetails.getStatus())
                .isInformative(isInformative)
                .accountIdentifier(account.getIdentifier())
                .infoModified(isInformative != category.getIsInformative())
                .isWorkLoad((categoryUpdateDetails.getSubType().trim().equalsIgnoreCase(CategoryType.WORKLOAD.getType()) ? 1 : 0))
                .build();
    }

    @Override
    public IdPojo process(CategoryDetailBean categoryBean) throws DataProcessingException {
        try {
            IdPojo idPojo = MySQLConnectionManager.getInstance().getHandle()
                    .inTransaction((conn, status) -> updateCategoryDetails(categoryBean, conn));

            updateCategoriesDetailsInRedis(categoryBean);
            return idPojo;

        } catch (Exception e) {
            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw (DataProcessingException) Throwables.getRootCause(e);
            } else {
                throw e;
            }
        }
    }

    private IdPojo updateCategoryDetails(CategoryDetailBean categoryBean, Handle handle) throws ControlCenterException {
        categoryDataService.updateCategory(categoryBean, handle);

        if (categoryBean.isInfoModified()) {
            List<Integer> kpiIds = categoryDataService.getKPIIdsForCategory(categoryBean.getId());
            if (!kpiIds.isEmpty()) {
                new BindInDataService().updateInfoForMappedKPIs(kpiIds, categoryBean.getIsInformative(), handle);
            }
        }

        return IdPojo.builder()
                .id(categoryBean.getId())
                .identifier(categoryBean.getIdentifier())
                .name(categoryBean.getName())
                .build();

    }

    private void updateCategoriesDetailsInRedis(CategoryDetailBean categoryBean) {
        List<Category> existingDetails = categoryRepo.getCategoryDetails(categoryBean.getAccountIdentifier());

            Category categoryDetail = existingDetails.parallelStream().filter(f -> f.getId() == categoryBean.getId()).findAny().orElse(null);
            if (categoryDetail == null){
                log.error("Categories details not found for accountIdentifier: {}",categoryBean.getAccountIdentifier());
            }else{
                categoryDetail.setWorkload(categoryBean.getIsWorkLoad());
                categoryDetail.setInformative(categoryBean.getIsInformative());
                categoryDetail.setName(categoryBean.getName());
                categoryDetail.setDescription(categoryBean.getDescription());

                categoryRepo.updateCategory(categoryBean.getAccountIdentifier(), categoryDetail);
            }

        categoryRepo.updateCategoryDetails(categoryBean.getAccountIdentifier(), existingDetails);
    }

    CategoryType getSubType(CategoryDetailBean category) {
        if (category.getIsWorkLoad() == 1) {
            return CategoryType.WORKLOAD;
        }

        if (category.getIsInformative() == 1) {
            return CategoryType.INFO;
        }

        return CategoryType.NON_INFO;
    }
}