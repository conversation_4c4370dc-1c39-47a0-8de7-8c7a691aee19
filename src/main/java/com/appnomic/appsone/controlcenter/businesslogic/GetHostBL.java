package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.enums.DiscoveryStatus;
import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.AutoDiscoveryDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ComponentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.autodiscovery.HostWiseConnections;
import com.appnomic.appsone.controlcenter.dao.opensearch.AutoDiscoveryRepo;
import com.appnomic.appsone.controlcenter.dao.redis.AccountRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.AgentInstanceMappingPojo;
import com.appnomic.appsone.controlcenter.pojo.InstanceComponentMappingDetails;
import com.appnomic.appsone.controlcenter.pojo.InstanceServiceApplicationDetailsPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.AgentDetails;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.GetHost;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.HostProcessCountPojo;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.IdNamePojo;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.EnvironmentHelper;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.Account;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class GetHostBL implements BusinessLogic<Object, AccountBean, List<GetHost>> {

    private int hostComponentTypeId;

    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid account identifier");
        }

        return UtilityBean.builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .build();
    }

    public AccountBean serverValidation(UtilityBean<Object> utilityBean) throws ServerException {

        String accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        int accountId = account.getId();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        MasterComponentTypeBean componentTypeBean = MasterCache.getMasterComponentTypeUsingName(Constants.COMPONENT_TYPE_HOST, String.valueOf(accountId));
        if (componentTypeBean == null) {
            String err = "Component with type '" + Constants.HOST + "' doesn't exist.";
            log.error(err);
            throw new ServerException(err);
        }
        hostComponentTypeId = componentTypeBean.getId();
        return account;
    }

    public List<GetHost> process(AccountBean accountBean) throws DataProcessingException {
        try {
            AccountRepo accountRepo = new AccountRepo();
            Account healHealthAccount = accountRepo.getAccountWithAccountIdentifier(Constants.HEAL_HEALTH_ACCOUNT_IDENTIFIER);
            int healHealthAccountId = 0;
            if (healHealthAccount == null) {
                log.warn("heal_health account not found in redis");
            } else {
                healHealthAccountId = healHealthAccount.getId();
            }

            AutoDiscoveryDataService autoDiscoveryDataService = new AutoDiscoveryDataService();
            Map<String, HostWiseConnections> hostIpAddressWiseConnections = autoDiscoveryDataService.getNumberOfConnectionsForHost(accountBean.getId(),healHealthAccountId, null).stream()
                    .collect(Collectors.toMap(HostWiseConnections::getHostAddress, Function.identity()));

            Map<String, HostWiseConnections> hostIdentifierWiseConnections = autoDiscoveryDataService.getNumberOfConnectionsForHostIdentifier(accountBean.getId(),healHealthAccountId, null).stream()
                    .collect(Collectors.toMap(HostWiseConnections::getHostIdentifier, Function.identity()));

            Map<String, String> hostIdErrorMap = new AutoDiscoveryRepo().getAutoDiscoveryHostErrorLogs();
            List<GetHost> preExistingData = preExistingData(accountBean.getId(), hostIdErrorMap, hostIpAddressWiseConnections, hostIdentifierWiseConnections);
            List<GetHost> stagingTableData = autoDiscoveryPart(hostIdErrorMap, hostIpAddressWiseConnections, hostIdentifierWiseConnections);

            List<GetHost> duplicatesFromSDMTable = new ArrayList<>();
            List<GetHost> duplicatesFromStagingTable = new ArrayList<>();

            removeDuplicates(preExistingData, stagingTableData, duplicatesFromSDMTable, duplicatesFromStagingTable);

            stagingTableData.removeAll(duplicatesFromStagingTable);
            preExistingData.removeAll(duplicatesFromSDMTable);
            preExistingData.addAll(stagingTableData);

            preExistingData.sort(Comparator.comparing(GetHost::getStatus, Comparator.reverseOrder())
                    .thenComparing(GetHost::getLastDiscoveryRunTime, Comparator.reverseOrder())
                    .thenComparing(GetHost::getHostName, Comparator.comparingInt(o -> Character.toLowerCase(o.charAt(0)))));

            /*
            In case new processes discovered by ad agent, then set flag newInstanceDiscovered to true
             */
            List<HostProcessCountPojo> newProcessList = new AutoDiscoveryDataService().getNewProcessCount(null);

            if (newProcessList != null && newProcessList.size() > 0 && newProcessList.get(0).getHostIdentifier() != null) {
                preExistingData.forEach(one -> newProcessList.forEach(two -> {
                    if (two.getHostIdentifier().equals(one.getHostIdentifier()) && two.getCountOfNewProcess() > 0)
                        one.setNewInstanceDiscovered(true);
                }));
            }

            return preExistingData;
        } catch (Exception e) {
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }

    public void removeDuplicates(List<GetHost> preExistingData, List<GetHost> stagingTableData, List<GetHost> duplicatesFromSDMTable, List<GetHost> duplicatesFromStagingTable) {
        preExistingData.forEach(one -> stagingTableData
                        .forEach(two -> {
                            if (one.getHostIdentifier().equals(two.getHostIdentifier()) && one.getService().size() < two.getService().size()) {
                                duplicatesFromSDMTable.add(one);
                            }
                            if ((one.getHostIdentifier().equals(two.getHostIdentifier()) && one.getService().size() >= two.getService().size())
                                    || (one.getHostAddress().equals(two.getHostAddress()) && two.getStatus() == DiscoveryStatus.DISCOVERED_NOT_ADDED_TO_SYSTEM)) {
                                duplicatesFromStagingTable.add(two);
                            }
                        }));
    }

    private List<GetHost> preExistingData(int accountId, Map<String, String> hostIdErrorMap, Map<String, HostWiseConnections> hostIpAddressWiseConnections,
                                          Map<String, HostWiseConnections> hostIdentifierWiseConnections) throws ControlCenterException {
        long time = System.currentTimeMillis();
        try {
            CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
            EnvironmentHelper envHelper = new EnvironmentHelper();

            List<ViewComponentInstanceBean> instanceBeans =
                    compInstanceDataService.getActiveInstanceDetailsForAccount(accountId, null);
            log.trace("Time taken for instanceBeans:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            List<ViewComponentInstanceBean> hostInstanceBeans = instanceBeans.parallelStream()
                    .filter(c -> c.getMstComponentTypeId() == hostComponentTypeId)
                    .collect(Collectors.toList());
            log.trace("Time taken for hostInstanceBeans:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            Map<Integer, List<ViewComponentInstanceBean>> hostComponentInstanceMap = instanceBeans
                    .parallelStream().filter(c -> c.getMstComponentTypeId() != hostComponentTypeId)
                    .collect(Collectors.groupingBy(ViewComponentInstanceBean::getHostId));
            log.trace("Time taken for hostComponentInstanceMap:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            List<CompClusterMappingBean> instClusterMapping = ComponentDataService.getInstanceClusterMapping(accountId, null);
            Map<Integer, List<CompClusterMappingBean>> instClusterMap = instClusterMapping.parallelStream()
                    .collect(Collectors.groupingBy(CompClusterMappingBean::getCompInstanceId));
            log.trace("Time taken for hostClusterMap:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            List<ViewClusterServicesBean> clusterServicesBeans = compInstanceDataService.getAllClusterServices(null);
            Map<Integer, List<IdNamePojo>> clusterServicesBeanMap = clusterServicesBeans.parallelStream()
                    .collect(Collectors.groupingBy(ViewClusterServicesBean::getClusterId)).entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, c -> c.getValue().stream().distinct().map(d -> IdNamePojo.builder()
                            .id(d.getServiceId())
                            .name(d.getServiceName())
                            .identifier(d.getServiceIdentifier()).build()).collect(Collectors.toList())));
            log.trace("Time taken for clusterServicesBeanMap:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            List<ViewApplicationServiceMappingBean> serviceApplicationsBeans =
                    compInstanceDataService.getAllServiceApplication(accountId, null);
            Map<Integer, List<IdNamePojo>> serviceApplicationsBeanMap = serviceApplicationsBeans.parallelStream()
                    .collect(Collectors.groupingBy(ViewApplicationServiceMappingBean::getServiceId)).entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, c -> c.getValue().stream().distinct().map(d -> IdNamePojo.builder()
                            .id(d.getApplicationId())
                            .name(d.getApplicationName())
                            .identifier(d.getApplicationIdentifier()).build()).collect(Collectors.toList())));
            log.trace("Time taken for serviceApplicationsBeanMap:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            List<AgentInstanceMappingPojo> agentBeanList = AgentDataService.getInstanceAgentMapping(accountId, null);
            Map<Integer, List<AgentInstanceMappingPojo>> agentBeanMap = agentBeanList.parallelStream()
                    .collect(Collectors.groupingBy(AgentInstanceMappingPojo::getInstanceId));
            log.trace("Time taken for agentBeanMap:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            List<CompInstanceAttributesBean> instCompAttributesDetails =
                    compInstanceDataService.getInstAttributeMapping("MonitorPort", null);
            Map<Integer, List<CompInstanceAttributesBean>> instCompAttributesMap = instCompAttributesDetails.parallelStream()
                    .collect(Collectors.groupingBy(CompInstanceAttributesBean::getCompInstanceId));
            log.trace("Time taken for instCompAttributesMap:{} ms", (System.currentTimeMillis() - time));
            time = System.currentTimeMillis();

            /*
             * Create GetHost Details List
             */
            List<GetHost> getHostList = hostInstanceBeans.parallelStream()
                    .map(c -> {
                        HostWiseConnections hostWiseConnections = hostIdentifierWiseConnections.getOrDefault(c.getIdentifier(), hostIpAddressWiseConnections.get(c.getHostAddress()));
                        int newHostsCount = hostWiseConnections == null ? 0 : hostWiseConnections.getNoOfConnections();
                        List<String> newHostIps = hostWiseConnections == null ? new ArrayList<>() : new ArrayList<>(Arrays.asList(hostWiseConnections.getListOfRemoteIps().split(",")));

                        GetHost getHost = GetHost.builder()
                                .id(String.valueOf(c.getId()))
                                .hostName(c.getName())
                                .hostIdentifier(c.getIdentifier())
                                .hostAddress(Collections.singletonList(c.getHostAddress()))
                                .environment(envHelper.getOrDefaultEnvironmentName(c.getIsDR()))
                                .status(DiscoveryStatus.ADDED_TO_SYSTEM)
                                .newHosts(newHostsCount)
                                .newHostIps(newHostIps)
                                .process(c.getDiscovery() == 0 ? "Manual" : "Auto")
                                .lastDiscoveryRunTime(DateTimeUtil.getGMTToEpochTime(String.valueOf(c.getUpdatedTime())))
                                .componentId(c.getMstComponentId())
                                .componentName(c.getMstComponentName())
                                .componentVersionId(c.getMstComponentVersionId())
                                .componentVersionName(c.getComponentVersionName())
                                .commonVersionId(c.getCommonVersionId())
                                .commonVersionName(c.getCommonVersionName())
                                .componentTypeId(c.getMstComponentTypeId())
                                .componentTypeName(c.getMstComponentTypeName())
                                .build();

                        List<IdNamePojo> services = new ArrayList<>();
                        List<IdNamePojo> applications = new ArrayList<>();

                        instClusterMap.getOrDefault(c.getId(), new ArrayList<>()).forEach(clu ->
                                services.addAll(clusterServicesBeanMap
                                        .getOrDefault(clu.getClusterId(), new ArrayList<>())));

                        services.forEach(srv -> applications.addAll(serviceApplicationsBeanMap
                                .getOrDefault(srv.getId(), new ArrayList<>())));

                        getHost.setService(services.stream().distinct().sorted(Comparator.comparingInt(IdNamePojo::getId)).collect(Collectors.toList()));
                        getHost.setApplication(applications.stream().distinct().sorted(Comparator.comparingInt(IdNamePojo::getId)).collect(Collectors.toList()));


                        /*
                         * Get Agent details for each Host
                         */
                        getHost.setMappedAgents(agentBeanMap.getOrDefault(c.getId(), new ArrayList<>())
                                .parallelStream().distinct().map(agentInstance -> AgentDetails.builder()
                                        .agentId(agentInstance.getAgentId())
                                        .agentName(agentInstance.getAgentName())
                                        .agentIdentifier(agentInstance.getUniqueToken())
                                        .physicalAgentId(agentInstance.getPhysicalAgentId())
                                        .physicalAgentIdentifier(agentInstance.getPhysicalAgentIdentifier())
                                        .agentTypeId(agentInstance.getAgentTypeId())
                                        .agentTypeName(agentInstance.getAgentTypeName())
                                        .status(agentInstance.getStatus())
                                        .build()).collect(Collectors.toList()));

                        /*
                         * Get Error log for each Host
                         */
                        getHost.setErrors(hostIdErrorMap.get(getHost.getId()) == null ?
                                hostIdErrorMap.get(getHost.getHostIdentifier()) : hostIdErrorMap.get(getHost.getId()));

                        /*
                         * Get discovered entities for each Host
                         */
                        List<String> discoveredEntities = new ArrayList<>();
                        hostComponentInstanceMap.getOrDefault(c.getId(), new ArrayList<>())
                                .forEach(dis -> {
                                            String monitorPort = instCompAttributesMap.get(dis.getId()) == null ?
                                                    "" : instCompAttributesMap.get(dis.getId()).get(0).getAttributeValue();
                                            String sb = dis.getMstComponentName() + ":" + monitorPort;
                                            discoveredEntities.add(sb);
                                        }
                                );
                        getHost.setDiscoveredEntities(discoveredEntities);
                        return getHost;
                    })
                    .collect(Collectors.toList());
            log.trace("Time taken for getHostList:{} ms", (System.currentTimeMillis() - time));
            return getHostList;
        } catch (Exception e) {
            throw new ControlCenterException("Error while getting HEAL System side host data:" + e);
        }
    }

    private List<GetHost> autoDiscoveryPart(Map<String, String> hostIdErrorMap, Map<String, HostWiseConnections> hostIpAddressWiseConnections,
                                            Map<String, HostWiseConnections> hostIdentifierWiseConnections) throws ControlCenterException {
        try {
            AutoDiscoveryDataService autoDiscoveryDataService = new AutoDiscoveryDataService();
            EnvironmentHelper envHelper = new EnvironmentHelper();
            List<InstanceServiceApplicationDetailsPojo> hostBeanList = autoDiscoveryDataService.getHostInstanceServiceAppDetails(null);
            List<InstanceComponentMappingDetails> instCompAttributesDetails = autoDiscoveryDataService.getInstCompAttributeMapping(null);
            /*
             * Create GetHost Details List
             */
            List<GetHost> getHostList = hostBeanList.stream()
                    .map(c -> {
                        HostWiseConnections hostWiseConnections = hostIdentifierWiseConnections.getOrDefault(c.getHostIdentifier(), hostIpAddressWiseConnections.get(c.getHostAddress()));
                        int newHostsCount = hostWiseConnections == null ? 0 : hostWiseConnections.getNoOfConnections();
                        List<String> newHostIps = hostWiseConnections == null ? new ArrayList<>() : new ArrayList<>(Arrays.asList(hostWiseConnections.getListOfRemoteIps().split(",")));

                        return GetHost.builder()
                                .id(String.valueOf(c.getInstanceIdentifier()))
                                .hostName(c.getInstanceName())
                                .hostIdentifier(c.getInstanceIdentifier())
                                .hostAddress(Collections.singletonList(c.getHostAddress()))
                                .environment(envHelper.getOrDefaultEnvironmentName(c.getIsDR()))
                                .status(c.getStatus())
                                .newHosts(newHostsCount)
                                .newHostIps(newHostIps)
                                .process("Auto")
                                .lastDiscoveryRunTime(c.getLastDiscoveryRunTime() == null ? 0 : Long.parseLong(c.getLastDiscoveryRunTime()))
                                .componentId(c.getComponentId())
                                .componentName(c.getComponentName())
                                .componentVersionId(c.getComponentVersionId())
                                .componentVersionName(c.getComponentVersionName())
                                .commonVersionId(c.getCommonVersionId())
                                .commonVersionName(c.getCommonVersionName())
                                .componentTypeId(c.getComponentTypeId())
                                .componentTypeName(c.getComponentTypeName())
                                .service(Collections.singletonList(IdNamePojo.builder()
                                        .id(c.getServiceId())
                                        .name(c.getServiceName())
                                        .identifier(c.getServiceIdentifier())
                                        .build()))
                                .application(Collections.singletonList(IdNamePojo.builder()
                                        .id(c.getApplicationId())
                                        .name(c.getApplicationName())
                                        .identifier(c.getApplicationIdentifier())
                                        .build()))
                                .build();
                    }).sorted(Comparator.comparing(GetHost::getId)).collect(Collectors.toList());

            /*
             * Remove Duplicates
             */
            for (int i = 0; i < getHostList.size() - 1; i++) {
                List<IdNamePojo> temp = new ArrayList<>();
                if (getHostList.get(i).getId().equals(getHostList.get(i + 1).getId())) {
                    if (!getHostList.get(i).getService().containsAll(getHostList.get(i + 1).getService())) {
                        /*
                        Remove Instances on basis of Service duplicates
                         */
                        temp.addAll(getHostList.get(i).getService());
                        temp.addAll(getHostList.get(i + 1).getService());
                        getHostList.get(i).setService(temp);
                        temp = new ArrayList<>();
                    }

                    /*
                    Remove Instances on basis of Application duplicates
                     */
                    if (!getHostList.get(i).getApplication().containsAll(getHostList.get(i + 1).getApplication())) {
                        temp.addAll(getHostList.get(i).getApplication());
                        temp.addAll(getHostList.get(i + 1).getApplication());
                        getHostList.get(i).setApplication(temp);
                    }

                    getHostList.remove(i + 1);
                    i--;
                }
            }

            for (GetHost getHost : getHostList) {
                List<String> discoveredEntities = new ArrayList<>();

                /*
                 * Get Error log for each Host
                 */
                getHost.setErrors(hostIdErrorMap.get(getHost.getId()) == null ?
                        hostIdErrorMap.get(getHost.getHostIdentifier()) : hostIdErrorMap.get(getHost.getId()));

                /*
                 * Get discovered entities for each Host
                 */
                List<String> instanceIdentifier = new ArrayList<>();
                for (InstanceComponentMappingDetails hostInstComponentMappingDetails : instCompAttributesDetails) {

                    if (String.valueOf(hostInstComponentMappingDetails.getHostIdentifier()).equals(getHost.getHostIdentifier())) {
                        if (!instanceIdentifier.contains(hostInstComponentMappingDetails.getCompInstanceIdentifier())
                                && !discoveredEntities.contains(hostInstComponentMappingDetails.getComponentName())) {
                            discoveredEntities.add(hostInstComponentMappingDetails.getComponentName());
                            instanceIdentifier.add(hostInstComponentMappingDetails.getCompInstanceIdentifier());
                        }
                        if (hostInstComponentMappingDetails.getAttributeName().equalsIgnoreCase("MonitorPort")) {
                            discoveredEntities.remove(hostInstComponentMappingDetails.getComponentName());
                            discoveredEntities.add(hostInstComponentMappingDetails.getComponentName() + ":" + hostInstComponentMappingDetails.getAttributeValue());
                        }
                    }
                }
                getHost.setDiscoveredEntities(discoveredEntities);
            }

            return getHostList;
        } catch (Exception e) {
            throw new ControlCenterException("Error while getting AutoDiscovery data:" + e);
        }
    }
}
