package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.beans.MaintenanceWindowBean;
import com.appnomic.appsone.common.exception.AppsOneException;
import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.MaintenanceDetails;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.ComptInstancePojo;
import com.appnomic.appsone.controlcenter.pojo.MaintenanceWindowPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class PostAdhocMaintenanceWindowBL implements BusinessLogic<MaintenanceWindowPojo, List<MaintenanceDetails>, MaintenanceWindowBean> {

    @Override
    public UtilityBean<MaintenanceWindowPojo> clientValidation(RequestObject request) throws ClientException {

        String accountIdentifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        String serviceIdStr = request.getParams().get(Constants.SERVICE_ID);
        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

        if (StringUtils.isEmpty(authToken)) {
            log.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }

        if (StringUtils.isEmpty(accountIdentifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        if (StringUtils.isEmpty(serviceIdStr)) {
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        }

        int serviceId;
        try {
            serviceId = Integer.parseInt(serviceIdStr);
        } catch (Exception e) {
            throw new ClientException(MessageFormat.format(UIMessages.INVALID_VALUE, Constants.SERVICE_IDENTIFIER, serviceIdStr));
        }

        if (StringUtils.isEmpty(request.getBody())) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        MaintenanceWindowPojo maintenanceWindowPojo;

        try {
            maintenanceWindowPojo = objectMapper.readValue(request.getBody(),
                    MaintenanceWindowPojo.class);
        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        maintenanceWindowPojo.validateAdhoc();

        if (!maintenanceWindowPojo.getError().isEmpty()) {
            throw new ClientException(maintenanceWindowPojo.getError().toString());
        }

        maintenanceWindowPojo.setServiceId(serviceId);

        return UtilityBean.<MaintenanceWindowPojo>builder()
                .accountIdentifier(accountIdentifier)
                .authToken(authToken)
                .pojoObject(maintenanceWindowPojo)
                .build();
    }

    @Override
    public List<MaintenanceDetails> serverValidation(UtilityBean<MaintenanceWindowPojo> utilityBean) throws ServerException {
        List<MaintenanceDetails> successMaintenanceDetailsList = new ArrayList<>();
        List<MaintenanceWindowBean> errorMaintenanceDetailsList = new ArrayList<>();

        String authToken = utilityBean.getAuthToken();

        UserAccountBean userAccBean;
        try {
            userAccBean = ValidationUtils.commonServerValidations(authToken, utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            log.error(e.getMessage());
            throw new ServerException(e.getMessage());
        }

        String userId = userAccBean.getUserId();
        int accountId = userAccBean.getAccount().getId();

        MaintenanceWindowPojo maintenanceBean = utilityBean.getPojoObject();

        ControllerBean controller = new ControllerDataService().getControllerById(maintenanceBean.getServiceId(), accountId, null);
        if (controller == null) {
            log.error("Service with id [{}] is unavailable for account id [{}]", maintenanceBean.getServiceId(), accountId);
            throw new ServerException(String.format("Service with id [%d] is unavailable for account id [%d]", maintenanceBean.getServiceId(), accountId));
        }

        long endTime = maintenanceBean.getEndTime().getTime() / 1000;

        int serviceId = maintenanceBean.getServiceId();

        ViewTypes maintenanceType = MasterCache.getMstTypeForSubTypeName(Constants.MST_TYPE_MAINTENANCE, Constants.MST_SUB_TYPE_SCHEDULED);
        Timestamp date = DateTimeUtil.getCurrentTimestampInGMT();

        for (int instanceId : maintenanceBean.getInstances()) {
            ComptInstancePojo comptInstancePojo;
            try {
                comptInstancePojo = validateInstance(accountId, serviceId, instanceId);
            } catch (ControlCenterException e) {
                log.error("Error while fetching component/host instances mapped to service id [{}] in account [{}]", serviceId, accountId);
                throw new ServerException(String.format("Error while fetching component/host instances mapped to service id [%d] in account [%d]", serviceId, accountId));
            }

            if (comptInstancePojo == null) {
                String error1 = "Invalid InstanceId : " + instanceId;
                throw new ServerException(error1);
            }

            MaintenanceWindowBean instanceMaintenanceWindowBean;
            try {
                instanceMaintenanceWindowBean = MaintenanceWindowUtility.getMaintenanceConflictByCompInstanceId(instanceId,
                        maintenanceBean.getId(), maintenanceBean.getStartTime(), new Timestamp(endTime));
            } catch (AppsOneException e) {
                log.error("Error while checking for adhoc maintenance window conflicts for instance [{}] mapped to service [{}]", instanceId, serviceId);
                throw new ServerException(String.format("Error while checking for maintenance window conflicts for service [%d]", serviceId));
            }

            if (instanceMaintenanceWindowBean != null) {
                instanceMaintenanceWindowBean.setName(comptInstancePojo.getInstanceName());
                instanceMaintenanceWindowBean.setId(comptInstancePojo.getInstanceId());
                errorMaintenanceDetailsList.add(instanceMaintenanceWindowBean);
            } else
                successMaintenanceDetailsList.add(MaintenanceDetails.builder()
                        .accountId(accountId)
                        .typeId(maintenanceType.getSubTypeId())
                        .status(true)
                        .instanceId(instanceId)
                        .instanceName(comptInstancePojo.getInstanceName())
                        .instanceIdentifier(comptInstancePojo.getInstanceIdentifier())
                        .startTime(maintenanceBean.getStartTime())
                        .maintenanceType(maintenanceType.getSubTypeName())
                        .endTime(maintenanceBean.getEndTime())
                        .accountIdentifier(userAccBean.getAccount().getIdentifier())
                        .serviceIdentifier(controller.getIdentifier())
                        .createdTime(date)
                        .updatedTime(date)
                        .name(controller.getName() + "_Adhoc")
                        .serviceId(serviceId)
                        .userDetails(userId)
                        .isCustom(1)
                        .build());
        }

        if (!errorMaintenanceDetailsList.isEmpty()) {
            log.error("Some or all the instances are already under maintenance. Details of instances: {}", errorMaintenanceDetailsList);
            throw new ServerException("Some or all the instances are already under maintenance");
        }

        return successMaintenanceDetailsList;
    }

    private ComptInstancePojo validateInstance(int accountId, int serviceId, int id) throws ControlCenterException {
        CompInstanceBL compInstanceBL = new CompInstanceBL();
        List<ComptInstancePojo> componentInstanceBeanList = compInstanceBL.getAllInstances(accountId, serviceId);
        for (ComptInstancePojo comptInstancePojo : componentInstanceBeanList) {
            if (comptInstancePojo.getInstanceId() == id)
                return comptInstancePojo;
        }
        return null;
    }

    @Override
    public MaintenanceWindowBean process(List<MaintenanceDetails> maintenanceDetailBeans) throws DataProcessingException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();

            return dbi.inTransaction((conn, status) -> {
                int maintenanceId = MaintenanceWindowUtility.addScheduledMaintenanceWindowDetails(maintenanceDetailBeans, conn);
                return MaintenanceWindowBean.builder().id(maintenanceId).build();
            });
        } catch (Exception e) {
            log.error("Error in processing adhoc maintenance window. Reason: ", e);
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }


}
