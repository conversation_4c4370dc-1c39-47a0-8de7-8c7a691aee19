package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.DBTestCache;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.ValueType;
import com.appnomic.appsone.controlcenter.dao.mysql.CategoryDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.KPIDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.MasterDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommonVersionCompIdMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.KpiBean;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.KpiException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.AddKpiRequest;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.service.KeyCloakAuthService;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.model.JWTData;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
@Slf4j
public class Kpi {

    private static final ObjectMapper OBJECT_MAPPER = CommonUtils.getObjectMapperWithHtmlEncoder();
    private static final String INVALID_VALUES_FOR_ONE_OR_MORE_ATTRIBUTES = "Invalid request. Reason: Invalid values for one or more attributes. " +
            "Refer more details, refer application log file.";
    private static final String INVALID_REQUEST_BODY = "Invalid request body. Reason: Request body is either NULL or empty. " +
            "Refer more details, refer application log file.";

    private static final String AGGREGATION_TYPE = "AggregationType";
    private static final String CLUSTER_OPERATION = "Cluster_Operation";
    private static final String ROLLUP_OPERATION = "RollUpOperation";
    private static final String CATEGORY_TAG = "Category";
    private static final String VIEW_TYPE = "KPI";

    private final KPIDataService kpiDataService = new KPIDataService();

    public String getUserId(String authorizationKey) {
        AtomicReference<String> atomicString = new AtomicReference<>();
        try {
            JWTData jwtData = KeyCloakAuthService.extractUserDetails(authorizationKey);
            atomicString.set(jwtData.getSub());
        } catch (ControlCenterException e) {
            log.error("Exception encountered while retrieving user ID from request header. Reason: {}", e.getSimpleMessage());
        }
        return atomicString.get();
    }

    public AddKpiRequest clientValidation(String requestBody) throws KpiException {
        AddKpiRequest addKpiRequest;
        try {
            addKpiRequest = OBJECT_MAPPER.readValue(requestBody, new TypeReference<AddKpiRequest>() {});

            if (!addKpiRequest.isValid()) {
                throw new KpiException(INVALID_VALUES_FOR_ONE_OR_MORE_ATTRIBUTES);
            }

            if(null == addKpiRequest.getKpiIdentifier()) {
                addKpiRequest.setKpiIdentifier(addKpiRequest.getKpiName().concat("-").concat(Long.toString(Instant.now().toEpochMilli())));
                log.info("kpiIdentifier not provided in the request. Auto-generated value is [{}].", addKpiRequest.getKpiIdentifier());
            }
        } catch (IOException e) {
            log.error("Exception encountered while retrieving KPI creation request. Reason: {}", e.getMessage());
            throw new KpiException(INVALID_REQUEST_BODY);
        }

        return addKpiRequest;
    }

    public void serverValidation(AddKpiRequest addKpiRequest, int accountId) throws KpiException {
        validateKpiNameAndIdentifierUniqueness(addKpiRequest, accountId);

        ViewTypes kpiTypes = MasterCache.getMstTypeForSubTypeName(VIEW_TYPE, addKpiRequest.getKpiType());

        validateGroupKpiInfo(addKpiRequest, kpiTypes);

        validateOperationTypes(addKpiRequest);

        validateAggregationTypes(addKpiRequest);

        ViewTypes dataTypes = getDataType(kpiTypes.getSubTypeName(), addKpiRequest.getDataType());

        if (null == dataTypes) {
            log.error("dataType validation failure. Reason: dataType [{}] is not available for kpiType [{}].", addKpiRequest.getDataType(), addKpiRequest.getKpiType());
            throw new KpiException("dataType of the KPI is invalid");
        }

        CommonVersionCompIdMapping commonVersionCompIdMapping = MasterDataService.getCommonVersionId(addKpiRequest.getComponentName(),
                addKpiRequest.getComponentVersion());

        if (null == commonVersionCompIdMapping) {
            log.error("commonVersionId & componentId validation failure. Reason: commonVersionId & componentId are not available for " +
                    "componentName [{}] and componentVersion [{}].", addKpiRequest.getComponentName(), addKpiRequest.getComponentVersion());

            throw new KpiException("Combination of commonVersionId and componentId of the KPI is invalid");
        }

        MasterComponentTypeBean componentTypeBean = MasterCache
                .getMasterComponentTypeUsingName(addKpiRequest.getComponentType(), String.valueOf(accountId));

        if (null == componentTypeBean) {
            log.error("ComponentTypeId validation failure. " +
                    "Reason: ComponentTypeId is not available for ComponentType [{}].", addKpiRequest.getComponentType());

            throw new KpiException("ComponentTypeId of the KPI is invalid");
        }

        TagDetailsBean tagDetailsBean = MasterCache.getTagDetails(CATEGORY_TAG);

        if (null == tagDetailsBean) {
            log.error("CategoryName validation failure. Reason: TagDetails " +
                    "for provided categoryName is not available.");

            throw new KpiException("TagDetails for Category is not available");
        }

        CategoryDetailBean categoryDetailBean = new CategoryDataService().getCategory(addKpiRequest.getCategoryName());

        if (null == categoryDetailBean) {
            log.error("CategoryName validation failure. Reason: Category for " +
                    "corresponding categoryName provided, does not exist.");

            throw new KpiException("categoryName of the KPI is invalid");
        }
    }

    private void validateKpiNameAndIdentifierUniqueness(AddKpiRequest addKpiRequest, int accountId) throws KpiException {
        if (null != addKpiRequest.getKpiIdentifier()) {
            int existingKpiCount = kpiDataService.checkForKpiUsingIdentifier(addKpiRequest.getKpiIdentifier(), null);

            if (existingKpiCount > 0) {
                log.error("Adding KPI failed. Reason: aliasName provided already exists.");
                throw new KpiException("Kpi with provided alias name already exists");
            } else if (existingKpiCount == -1) {
                log.error("Adding KPI failed. Reason: Exception encountered when checking for existence of the KPI.");
                throw new KpiException("Exception encountered while retrieving KPI details.");
            }
        }

        int existingKpiCount = kpiDataService.checkForKpiUsingName(addKpiRequest.getKpiName(), accountId, null);

        if (existingKpiCount > 0) {
            log.error("Adding KPI failed. Reason: kpiName provided already exists.");
            throw new KpiException("Kpi with provided name already exists");
        } else if (existingKpiCount == -1) {
            log.error("Adding KPI failed. Reason: Exception encountered when checking for existence of the KPI.");
            throw new KpiException("Exception encountered while retrieving KPI details.");
        }
    }

    private void validateOperationTypes(AddKpiRequest addKpiRequest) throws KpiException {
        ViewTypes clusterOpsTypes = MasterCache
                .getMstTypeForSubTypeName(CLUSTER_OPERATION, addKpiRequest.getClusterOperation());

        if (null == clusterOpsTypes) {
            log.error("clusterOperation validation failure. Reason: clusterOperation " +
                    "is not one of Sum, Average or None.");
            throw new KpiException("clusterOperation of the KPI is invalid");
        }

        ViewTypes rollUpOpsTypes = MasterCache
                .getMstTypeForSubTypeName(ROLLUP_OPERATION, addKpiRequest.getRollupOperation());

        if (null == rollUpOpsTypes) {
            log.error("rollupOperation validation failure. " +
                    "Reason: rollupOperation is not one of Sum, Average, Last, Max or None.");
            throw new KpiException("rollupOperation of the KPI is invalid");
        }
    }

    private void validateAggregationTypes(AddKpiRequest addKpiRequest) throws KpiException {
        ViewTypes clusterAggTypes = MasterCache
                .getMstTypeForSubTypeName(AGGREGATION_TYPE, addKpiRequest.getClusterAggregation());

        if (null == clusterAggTypes) {
            log.error("clusterAggregation validation failure. Reason: clusterAggregation " +
                    "is not one of SingleValue, MultiValue or None.");
            throw new KpiException("clusterAggregation of the KPI is invalid");
        }

        ViewTypes instAggTypes = MasterCache
                .getMstTypeForSubTypeName(AGGREGATION_TYPE, addKpiRequest.getInstanceAggregation());

        if (null == instAggTypes) {
            log.error("instanceAggregation validation failure. Reason: instanceAggregation " +
                    "is not one of SingleValue, MultiValue or None.");
            throw new KpiException("instanceAggregation of the KPI is invalid");
        }
    }

    private void validateGroupKpiInfo(AddKpiRequest addKpiRequest, ViewTypes kpiTypes) throws KpiException {
        if (null == kpiTypes) {
            log.error("KpiType validation failure. Reason: kpiType should be one of Availability, Core, FileWatch or ConfigWatch.");
            throw new KpiException("kpiType of the KPI is invalid");
        } else {
            if(!"NONE".equalsIgnoreCase(addKpiRequest.getGroupKpiIdentifier())) {
                List<MasterKpiGroupBean> groupKpi = kpiDataService.checkForGroupKpiUsingIdentifier(addKpiRequest.getGroupKpiIdentifier(), null);

                if (1 != groupKpi.size()) {
                    log.error("groupKpiName validation failure. Reason: groupKpiName provided is not available.");
                    throw new KpiException("KPI with groupKpiName is not available");
                } else {
                    if (kpiTypes.getSubTypeId() != groupKpi.get(0).getKpiTypeId()) {
                        log.error("KpiType validation failure. Reason: kpiType provided is not matching the kpiType of the provided GroupKpiName");
                        throw new KpiException("kpiType of given KPI and corresponding group KPI does not match");
                    }
                }
            }
        }
    }

    private ViewTypes getDataType(String kpiTypeName, String addRequestDataType) {
        if (kpiTypeName.equalsIgnoreCase(Constants.CORE_KPI_TYPE)) {
            return MasterCache.getMstTypeForSubTypeName(Constants.CORE_DATA_TYPE, addRequestDataType);
        } else if (kpiTypeName.equalsIgnoreCase(Constants.AVAIL_KPI_TYPE)) {
            return MasterCache.getMstTypeForSubTypeName(Constants.AVAILABILITY_DATA_TYPE, addRequestDataType);
        } else if (kpiTypeName.equalsIgnoreCase(Constants.FILE_WATCH_KPI_TYPE)) {
            return MasterCache.getMstTypeForSubTypeName(Constants.FILE_WATCH_DATA_TYPE, addRequestDataType);
        } else if (kpiTypeName.equalsIgnoreCase(Constants.CONFIG_WATCH_KPI_TYPE)) {
            return MasterCache.getMstTypeForSubTypeName(Constants.CONFIG_WATCH_DATA_TYPE, addRequestDataType);
        }
        return null;
    }

    public IdPojo processAddKpiRequest(AddKpiRequest addKpiRequest, String userId, int accountId) throws KpiException {
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        IdPojo id;
        try {
            id = dbi.inTransaction((conn, status) -> insertData(addKpiRequest, userId, accountId, conn));
        } catch (Exception e) {
            if (Throwables.getRootCause(e) instanceof KpiException) {
                throw (KpiException) Throwables.getRootCause(e);
            } else {
                throw e;
            }
        }
        return id;
    }

    private IdPojo insertData(AddKpiRequest addKpiRequest, String userId, int accountId, Handle handle) throws KpiException {
        IdPojo idPojo;
        try {
            idPojo = addKpi(addKpiRequest, userId, accountId, handle);
            DBTestCache.addToCache("mst_kpi_details", idPojo.getId());
        } catch (KpiException e) {
            log.error("Exception occurred when adding KPI. Reason: {}", e.getMessage());
            throw new KpiException("Error while adding KPI details");
        }
        int kpiId = idPojo.getId();

        try {
            int tagId = addTagMapping(userId, addKpiRequest, kpiId, accountId, handle);
            DBTestCache.addToCache("tag_mapping", tagId);
        } catch (KpiException e) {
            log.error("Exception occurred when adding KPI. Reason: {}", e.getMessage());
            throw new KpiException("Error while mapping KPI to category");
        }

        MasterComponentTypeBean componentTypeBean = MasterCache
                .getMasterComponentTypeUsingName(addKpiRequest.getComponentType(), String.valueOf(accountId));

        if (null == componentTypeBean) {
            log.error("ComponentTypeId validation failure. " +
                    "Reason: ComponentTypeId is not available for ComponentType [{}].", addKpiRequest.getComponentType());

            throw new KpiException("ComponentTypeId of the KPI is invalid");
        }

        int componentTypeId = componentTypeBean.getId();
        CommonVersionCompIdMapping commonVersionCompIdMapping = MasterDataService.getCommonVersionId(addKpiRequest.getComponentName(),
                addKpiRequest.getComponentVersion());

        try {
            int id = CompVersionKpiMapping.addCompVerKpiMappingBean(userId, addKpiRequest, commonVersionCompIdMapping,
                    componentTypeId, kpiId, handle);
            DBTestCache.addToCache("mst_component_version_kpi_mapping", id);
        } catch (KpiException e) {
            log.error("Exception occurred when mapping component version to KPI. Reason: {}", e.getMessage());
            throw new KpiException("Error while mapping KPI to component version");
        }

        return idPojo;
    }

    private IdPojo addKpi(AddKpiRequest addKpiRequest, String userId, int accountId, Handle handle) throws KpiException {
        ViewTypes kpiTypes = MasterCache.getMstTypeForSubTypeName(VIEW_TYPE, addKpiRequest.getKpiType());
        if (null == kpiTypes) {
            throw new KpiException("KpiType of the KPI is invalid");
        }

        ViewTypes dataTypes = getDataType(kpiTypes.getSubTypeName(), addKpiRequest.getDataType());
        if (null == dataTypes) {
            throw new KpiException("dataType of the KPI is invalid");
        }

        List<MasterKpiGroupBean> groupKpiList = kpiDataService.checkForGroupKpiUsingIdentifier(addKpiRequest.getGroupKpiIdentifier(), handle);

        int groupKpiId = 0;
        if (!groupKpiList.isEmpty()) {
            groupKpiId = groupKpiList.get(0).getId();
        }

        int clusterAggType = MasterCache.getMstTypeForSubTypeName(AGGREGATION_TYPE, addKpiRequest.getClusterAggregation()).getSubTypeId();
        int instAggType = MasterCache.getMstTypeForSubTypeName(AGGREGATION_TYPE, addKpiRequest.getInstanceAggregation()).getSubTypeId();

        KpiBean kpiBean = KpiBean.builder()
                .status(1)
                .isCustom(addKpiRequest.getCustom())
                .isComputed(0)
                .kpiTypeId(kpiTypes.getSubTypeId())
                .accountId(accountId)
                .name(addKpiRequest.getKpiName())
                .identifier(addKpiRequest.getKpiIdentifier())
                .description(addKpiRequest.getDescription())
                .groupKpiId(groupKpiId)
                .userId(userId)
                .dataType(dataTypes.getSubTypeName())
                .valueType(ValueType.SNAPSHOT.toString())
                .measureUnits(addKpiRequest.getMeasureUnits())
                .clusterOperation(addKpiRequest.getClusterOperation())
                .rollupOperation(addKpiRequest.getRollupOperation())
                .instanceAggregation(instAggType)
                .clusterAggregation(clusterAggType)
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .cronExpression(addKpiRequest.getCronExpression())
                .resetDeltaValue(addKpiRequest.getResetDeltaValue())
                .deltaPerSec(addKpiRequest.getDeltaPerSec())
                .build();

        int kpiId = kpiDataService.createKpi(kpiBean, handle);

        if (kpiId == -1) {
            throw new KpiException("Error while adding KPI details.");
        }

        log.info("KPI successfully added");

        return IdPojo.builder()
                .id(kpiId)
                .identifier(addKpiRequest.getKpiIdentifier())
                .name(addKpiRequest.getKpiName())
                .build();
    }

    private int addTagMapping(String userId, AddKpiRequest addKpiRequest, int kpiId, int accountId, Handle handle) throws KpiException {
        TagDetailsBean tagDetailsBean = MasterCache.getTagDetails(CATEGORY_TAG);
        CategoryDetailBean categoryDetailBean = new CategoryDataService().getCategory(addKpiRequest.getCategoryName());

        if (null != tagDetailsBean && null != categoryDetailBean) {
            return new TagMapping().addTagMappings(tagDetailsBean.getId(), categoryDetailBean.getId(),
                    categoryDetailBean.getIdentifier(), userId, accountId, kpiId, handle);
        } else {
            log.error("Either tagDetails or categoryDetails is NULL for provided " +
                    "categoryName: [{}]", addKpiRequest.getCategoryName());
            throw new KpiException("Either tagDetails or categoryDetails is not available");
        }
    }
}
