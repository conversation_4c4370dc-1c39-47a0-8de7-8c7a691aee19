package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CommandDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandTriggerBean;
import com.appnomic.appsone.controlcenter.dao.opensearch.AgentForensicRepo;
import com.appnomic.appsone.controlcenter.dao.redis.MasterDataRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.dao.redis.UsersRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.TriggeredForensics;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.entities.BasicAgentBean;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.User;
import com.heal.configuration.pojos.opensearch.AgentCommandsData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class TriggeredForensicsListBL implements BusinessLogic<String, UtilityBean<String>, List<TriggeredForensics>> {
    private static final Logger log = LoggerFactory.getLogger(TriggeredForensicsListBL.class);

    @Override
    public UtilityBean<String> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String accountIdString = requestObject.getParams().get(UIMessages.ACCOUNT_IDENTIFIER);

        if (StringUtils.isEmpty(accountIdString)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String serviceIdentifier = requestObject.getParams().get(Constants.SERVICE_IDENTIFIER);

        if (serviceIdentifier == null || serviceIdentifier.trim().isEmpty()) {
            log.error(UIMessages.SERVICE_EMPTY_ERROR_MESSAGE, serviceIdentifier);
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        }

        return UtilityBean.<String>builder()
                .accountIdentifier(accountIdString)
                .authToken(authKey)
                .pojoObject(serviceIdentifier)
                .build();
    }

    @Override
    public UtilityBean<String> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getAccountIdentifier();
        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        String serviceIdentifier = utilityBean.getPojoObject();

        ServiceRepo serviceRepo = new ServiceRepo();
        BasicEntity serviceConfigurationById = serviceRepo.getServiceConfigurationByIdentifier(accountIdentifier, serviceIdentifier);

        if (serviceConfigurationById == null) {
            log.error("Obtained NULL while fetching service details from Redis for the service Id {} of account {}", serviceIdentifier, accountIdentifier);
            throw new ServerException("Obtained NULL while fetching service details from Redis for the service Id " + serviceIdentifier);
        }
        return utilityBean;
    }

    @Override
    public List<TriggeredForensics> process(UtilityBean<String> bean) throws DataProcessingException {
        log.debug("Fetching list of triggered forensics actions");

        UsersRepo usersRepo = new UsersRepo();
        MasterDataRepo masterDataRepo = new MasterDataRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        String serviceIdentifier = bean.getPojoObject();
        String accountIdentifier = bean.getAccountIdentifier();

        List<BasicAgentBean> agents = serviceRepo.getAgentsByServiceIdentifier(accountIdentifier, serviceIdentifier);

        if (agents.isEmpty()) {
            log.error("Obtained empty results from Redis when queried for service level agents for the service {} of account {}", serviceIdentifier, accountIdentifier);
            throw new DataProcessingException("Obtained empty results from Redis when queried for service level agents");
        }

        Map<Integer, BasicAgentBean> jimAgents = agents.stream()
                .filter(f -> f.getStatus() == 1)
                .filter(f -> f.getType().equals(Constants.JIM_AGENT_SUB_TYPE))
                .collect(Collectors.toMap(BasicEntity::getId, Function.identity()));

        List<com.heal.configuration.pojos.ViewTypes> types = masterDataRepo.getTypes();
        if(types.isEmpty()) {
            log.error("Obtained empty result from Redis when queried for view types");
            throw new DataProcessingException("Obtained empty result from Redis when queried for view types");
        }

        Map<String, com.heal.configuration.pojos.ViewTypes> viewTypesMap = types.stream()
                .filter(f -> f.getSubTypeName().equals(Constants.JIM_AGENT_SUB_TYPE) || f.getTypeName().equals(Constants.FORENSIC_COMMAND_TYPE))
                .collect(Collectors.toMap(com.heal.configuration.pojos.ViewTypes::getTypeName, Function.identity()));

        int jimSubTypeId = viewTypesMap.get(Constants.AGENT_TYPE).getSubTypeId();
        int forensicTypeId = viewTypesMap.get(Constants.FORENSIC_COMMAND_TYPE).getTypeId();

        List<CommandDetailsBean> forensicCommandDetailsByAgentType = CommandDataService.getCommandDetailsByAgentType(jimSubTypeId, forensicTypeId, null);

        if (forensicCommandDetailsByAgentType.isEmpty()) {
            log.error("Obtained empty results from Percona when queried for forensic action commands of JIM agent.");
            throw new DataProcessingException("Obtained empty results from Percona when queried for forensic action commands of JIM agent.");
        }

        Map<Integer, String> commmandIdsNameMap = forensicCommandDetailsByAgentType.parallelStream()
                .collect(Collectors.toMap(CommandDetailsBean::getId, CommandDetailsBean::getCommandName));

        log.debug("Command Ids that are specific to JIM agent are {}", commmandIdsNameMap.keySet());

        BindInDataService bindInDataService = new BindInDataService();
        List<CommandTriggerBean> latestTriggeredCommands = bindInDataService.getTriggeredJimForensicCommands(new ArrayList<>(commmandIdsNameMap.keySet()), new ArrayList<>(jimAgents.keySet()), null);

        if(latestTriggeredCommands.isEmpty()) {
            log.warn("Obtained empty results from data source when queried for latest triggered forensic commands for the agents {}", jimAgents.keySet());
            log.info("No forensic actions were triggered for any of the agents {}", jimAgents.keySet());
            return Collections.emptyList();
        }

        Map<Integer, CommandTriggerBean> agentTriggeredCommandsMap = latestTriggeredCommands.parallelStream()
                .collect(Collectors.toMap(CommandTriggerBean::getAgentId, Function.identity()));

        AgentForensicRepo agentForensicRepo = new AgentForensicRepo();
        List<AgentCommandsData> agentTriggeredForensics = agentForensicRepo.getAgentTriggeredForensics(bean.getAccountIdentifier(), agentTriggeredCommandsMap);

        List<TriggeredForensics> outputList = new ArrayList<>();

        agentTriggeredForensics.forEach(f-> {
            String currentStatus = f.getMetadata().get("status");
            Integer agentId = Integer.parseInt(f.getMetadata().get("Agent_id"));

            CommandTriggerBean commandTriggerBean = agentTriggeredCommandsMap.get(agentId);

            String commandName = commmandIdsNameMap.get(commandTriggerBean.getCommandId());
            Timestamp triggerTime = agentTriggeredCommandsMap.get(agentId).getTriggerTime();
            String userDetailsId = agentTriggeredCommandsMap.get(agentId).getUserDetailsId();

            User user = usersRepo.getUser(userDetailsId);
            String userName;

            if (user == null) {
                log.error("Obtained NULL from Redis when queried for user details of the user {}", userDetailsId);
                userName = userDetailsId;
            } else {
                userName = user.getUserName();
            }

            outputList.add(TriggeredForensics.builder()
                    .forensicAction(commandName)
                    .agentName(jimAgents.get(agentId).getName())
                    .lastTriggeredTime(triggerTime.toString())
                    .triggeredBy(userName)
                    .status(currentStatus)
                    .build());
        });
        return outputList;
    }
}
