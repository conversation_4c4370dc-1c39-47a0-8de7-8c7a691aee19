package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.MasterDataService;
import com.appnomic.appsone.controlcenter.dao.redis.AccountRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ComponentRepo;
import com.appnomic.appsone.controlcenter.dao.redis.TransactionRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.GetTransactionThresholdPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.TransactionStaticThresholds;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.pojos.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 09/06/22
 */
@Slf4j
public class GetTransactionThresholdBL implements BusinessLogic<GetTransactionThresholdPojo, UtilityBean<GetTransactionThresholdPojo>, List<TransactionStaticThresholds>> {
    @Override
    public UtilityBean<GetTransactionThresholdPojo> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        GetTransactionThresholdPojo getTransactionThresholdPojo = new GetTransactionThresholdPojo(requestObject);

        if (!getTransactionThresholdPojo.isValidParameters()) {
            log.error("Client validation failed for GET Transaction Thresholds request.");
            throw new ClientException("Client validation Failed.");
        }

        return UtilityBean.<GetTransactionThresholdPojo>builder()
                .pojoObject(getTransactionThresholdPojo)
                .accountIdentifier(getTransactionThresholdPojo.getAccountIdentifier())
                .authToken(authKey)
                .build();
    }

    @Override
    public UtilityBean<GetTransactionThresholdPojo> serverValidation(UtilityBean<GetTransactionThresholdPojo> utilityBean) throws ServerException {

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        Account account = new AccountRepo().getAccountWithAccountIdentifier(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        Transaction transactionDetails = new TransactionRepo().getTransactionById(account.getIdentifier(), utilityBean.getPojoObject().getTransactionId());
        if (transactionDetails == null) {
            log.error("Transaction id is invalid");
            throw new ServerException("Transaction id is invalid");
        }
        utilityBean.getPojoObject().setTransactionIdentifier(transactionDetails.getIdentifier());

        return utilityBean;
    }

    @Override
    public List<TransactionStaticThresholds> process(UtilityBean<GetTransactionThresholdPojo> bean) throws DataProcessingException {

        List<ComponentKpiEntity> kpiEntityList = new ComponentRepo().getComponentKpiDetails(bean.getAccountIdentifier(), Constants.TRANSACTION_IDENTIFIER_DEFAULT);
        List<TxnKPIViolationConfig> kpiViolationConfigList = new TransactionRepo().getTransactionViolationConfigDetails(bean.getAccountIdentifier(), bean.getPojoObject().getTransactionIdentifier());
        Map<Integer, List<KpiViolationConfig>> kpiViolationConfigMap = kpiViolationConfigList.parallelStream()
                .map(TxnKPIViolationConfig::getKpiViolationConfig)
                .flatMap(Collection::parallelStream)
                .collect(Collectors.groupingBy(KpiViolationConfig::getKpiId));

        Map<Integer, String> viewTypesSubTypeIdMap = MasterDataService.getAllTypes().parallelStream()
                .filter(c -> c.getTypeName().equalsIgnoreCase(Constants.TRANSACTION_RESPONSE_TYPE)
                        || c.getTypeName().equalsIgnoreCase(Constants.OPERATIONS_TYPE_NAME)
                        || c.getTypeName().equalsIgnoreCase(Constants.SIGNAL_SEVERITY_TYPE_LITERAL))
                .collect(Collectors.toMap(com.appnomic.appsone.controlcenter.beans.ViewTypes::getSubTypeId, ViewTypes::getSubTypeName));

        return kpiEntityList.parallelStream().map(kpi -> {
            if (!kpiViolationConfigMap.containsKey(kpi.getId())) {
                return TransactionStaticThresholds.builder()
                        .kpiId(String.valueOf(kpi.getId()))
                        .kpiName(kpi.getName())
                        .categoryId(kpi.getCategoryDetails().getId())
                        .categoryName(kpi.getCategoryDetails().getName())
                        .kpiDataType(kpi.getDataType())
                        .kpiUnit(kpi.getUnit())
                        .kpiAttribute(Constants.ALL)
                        .responseTimeType(bean.getPojoObject().getResponseTimeType())
                        .userThresholds(new HashMap<>())
                        .systemThresholds(new HashMap<>())
                        .build();
            }

            Map<String, KpiViolationConfig> violationMap = kpiViolationConfigMap.get(kpi.getId()).parallelStream()
                    .collect(Collectors.toMap(KpiViolationConfig::getDefinedBy, Function.identity()));
            Map<String, Double> thresholds = new HashMap<>();

            if (violationMap.containsKey("USER")) {
                thresholds.put("MAX", (double) violationMap.get("USER").getMaxThreshold());
                thresholds.put("MIN", (double) violationMap.get("USER").getMinThreshold());

                return TransactionStaticThresholds.builder()
                        .kpiId(String.valueOf(kpi.getId()))
                        .kpiName(kpi.getName())
                        .categoryId(kpi.getCategoryDetails().getId())
                        .categoryName(kpi.getCategoryDetails().getName())
                        .kpiDataType(kpi.getDataType())
                        .kpiUnit(kpi.getUnit())
                        .kpiAttribute(Constants.ALL)
                        .userDefinedOperationType(violationMap.get("USER").getOperation())
                        .generateAnomaly(violationMap.get("USER").getGenerateAnomaly() == 1)
                        .userDefinedSOR(true)
                        .userThresholds(thresholds)
                        .severe(viewTypesSubTypeIdMap.get(violationMap.get("USER").getSeverity()).equalsIgnoreCase("Severe"))
                        .excludeMaintenance(violationMap.get("USER").getExcludeMaintenance() == 1)
                        .responseTimeType(bean.getPojoObject().getResponseTimeType())
                        .persistence(violationMap.get("USER").getPersistence() == -1 ? null : violationMap.get("USER").getPersistence())
                        .suppression(violationMap.get("USER").getSuppression() == -1 ? null : violationMap.get("USER").getSuppression())
                        .build();
            } else if (violationMap.containsKey("SYSTEM")) {
                thresholds.put("MAX", (double) violationMap.get("SYSTEM").getMaxThreshold());
                thresholds.put("MIN", (double) violationMap.get("SYSTEM").getMinThreshold());

                return TransactionStaticThresholds.builder()
                        .kpiId(String.valueOf(kpi.getId()))
                        .kpiName(kpi.getName())
                        .categoryId(kpi.getCategoryDetails().getId())
                        .categoryName(kpi.getCategoryDetails().getName())
                        .kpiDataType(kpi.getDataType())
                        .kpiUnit(kpi.getUnit())
                        .kpiAttribute(Constants.ALL)
                        .systemOperationType(violationMap.get("SYSTEM").getOperation())
                        .generateAnomaly(violationMap.get("SYSTEM").getGenerateAnomaly() == 1)
                        .systemThresholds(thresholds)
                        .userThresholds(new HashMap<>())
                        .severe(viewTypesSubTypeIdMap.get(violationMap.get("SYSTEM").getSeverity()).equalsIgnoreCase("Severe"))
                        .excludeMaintenance(violationMap.get("SYSTEM").getExcludeMaintenance() == 1)
                        .responseTimeType(bean.getPojoObject().getResponseTimeType())
                        .persistence(violationMap.get("SYSTEM").getPersistence() == -1 ? null : violationMap.get("SYSTEM").getPersistence())
                        .suppression(violationMap.get("SYSTEM").getSuppression() == -1 ? null : violationMap.get("SYSTEM").getSuppression())
                        .build();
            } else {
                return TransactionStaticThresholds.builder()
                        .kpiId(String.valueOf(kpi.getId()))
                        .kpiName(kpi.getName())
                        .categoryId(kpi.getCategoryDetails().getId())
                        .categoryName(kpi.getCategoryDetails().getName())
                        .kpiDataType(kpi.getDataType())
                        .kpiUnit(kpi.getUnit())
                        .kpiAttribute(Constants.ALL)
                        .responseTimeType(bean.getPojoObject().getResponseTimeType())
                        .userThresholds(new HashMap<>())
                        .build();
            }
        }).distinct().filter(Objects::nonNull).collect(Collectors.toList());

    }
}
