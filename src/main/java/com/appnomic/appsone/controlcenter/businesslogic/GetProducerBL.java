package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ProducerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.GetProducerPojo;
import com.appnomic.appsone.controlcenter.pojo.ProducerDetailsPojo;
import com.appnomic.appsone.controlcenter.pojo.ProducerKPIMappingDetailsPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class GetProducerBL implements BusinessLogic<Integer, Integer, List<GetProducerPojo>> {

    public UtilityBean<Integer> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            log.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }

        String accountIdentifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(accountIdentifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        return UtilityBean.<Integer>builder()
                .accountIdentifier(accountIdentifier)
                .authToken(authToken)
                .build();
    }

    @Override
    public Integer serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error(UIMessages.INVALID_ACCOUNT_MESSAGE);
            throw new ServerException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }

        return account.getId();
    }

    @Override
    public List<GetProducerPojo> process(Integer accountId) throws DataProcessingException {
        List<ProducerDetailsPojo> producerDetailsList = ProducerDataService.getProducerDetailsWithAccId(accountId, null);

        if(producerDetailsList.isEmpty()) {
            log.info("Producers unavailable for account [{}]", accountId);
            return Collections.emptyList();
        }

        List<ProducerKPIMappingDetailsPojo> producerKpiMappingData = ProducerDataService.getProducerKPIMappingDetails(accountId, null);
        Map<Integer, Long> producerKpiMapping = producerKpiMappingData.parallelStream()
                .collect(Collectors.groupingBy(ProducerKPIMappingDetailsPojo::getProducerId, Collectors.counting()));

        return producerDetailsList.parallelStream()
                .map(c -> GetProducerPojo.builder()
                        .id(c.getId())
                        .name(c.getName())
                        .description(c.getDescription())
                        .producerType(c.getProducerTypeName())
                        .isCustom(c.getIsCustom())
                        .status(c.getStatus())
                        .isGroupKPI(c.getIsKpiGroup())
                        .kpiMapped(producerKpiMapping.getOrDefault(c.getId(), 0L) > 0)
                        .createdOn(DateTimeUtil.getGMTToEpochTime(c.getCreatedTime()))
                        .lastModifiedBy(c.getUserName())
                        .lastModifiedOn(DateTimeUtil.getGMTToEpochTime(c.getUpdatedTime()))
                        .build()).collect(Collectors.toList());
    }
}
