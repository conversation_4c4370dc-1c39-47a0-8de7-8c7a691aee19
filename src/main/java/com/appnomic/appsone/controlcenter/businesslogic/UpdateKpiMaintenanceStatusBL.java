package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.KPIDataService;
import com.appnomic.appsone.controlcenter.beans.KpiMaintenanceStatusBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.KpiBean;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.KpiViolationConfig;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class UpdateKpiMaintenanceStatusBL  implements BusinessLogic<InstanceKpiMaintenanceStatusDetails, List<KpiMaintenanceStatusBean>, String> {
    private static final KPIDataService KPI_DATA_SERVICE = new KPIDataService();
    InstanceRepo instanceRepo = new InstanceRepo();

    @Override
    public UtilityBean<InstanceKpiMaintenanceStatusDetails> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(requestObject.getBody())) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Account identifier is invalid");
            throw new ClientException("Account identifier is invalid");
        }

        String requestBody = requestObject.getBody();

        InstanceKpiMaintenanceStatusDetails instanceKpiMaintenanceStatusDetails;
        try {
            instanceKpiMaintenanceStatusDetails = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestBody,
                    new TypeReference<InstanceKpiMaintenanceStatusDetails>() {
                    });
        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        if(instanceKpiMaintenanceStatusDetails.getKpiMaintenanceStatusDetails().parallelStream()
                .anyMatch(e -> !e.validate())) {
            log.error("invalid input data");
            throw new ClientException("invalid input data");
        }

        return UtilityBean.<InstanceKpiMaintenanceStatusDetails>builder()
                .authToken(authToken)
                .accountIdentifier(identifier)
                .pojoObject(instanceKpiMaintenanceStatusDetails)
                .build();
    }

    @Override
    public List<KpiMaintenanceStatusBean> serverValidation(UtilityBean<InstanceKpiMaintenanceStatusDetails> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be " +
                    "invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        List<KpiMaintenanceStatusBean> attrConfigBeans = new ArrayList<>();
        int accountId = account.getId();
        InstanceKpiMaintenanceStatusDetails maintenanceStatusDetails = utilityBean.getPojoObject();
        List<Integer> instanceIds = maintenanceStatusDetails.getInstances();
        List<KpiMaintenanceStatusDetails> configDetails = maintenanceStatusDetails.getKpiMaintenanceStatusDetails();

        for(int instanceId : instanceIds) {
            String compInstanceIdentifier;
            try {
                compInstanceIdentifier = new CompInstanceDataService().getCompInstanceIdentifierFromId(instanceId, accountId, null);
            } catch (ControlCenterException e) {
                throw new ServerException(e.getMessage());
            }

            if (compInstanceIdentifier == null || compInstanceIdentifier.isEmpty()) {
                log.error("Component instance identifier unavailable for instanceId [{}]", instanceId);
                throw new ServerException("Invalid instanceId provided");
            }

            List<KpiMaintenanceStatusBean> instanceToKpiAttrMapping;
            List<KpiMaintenanceStatusBean> kpiMappingExists;

            try {
                instanceToKpiAttrMapping = KPI_DATA_SERVICE.fetchGroupKpiCompInstMapping(instanceId, null);
                kpiMappingExists = KPI_DATA_SERVICE.fetchKpiCompInstMapping(instanceId, null);
            } catch (ControlCenterException e) {
                throw new ServerException(e.getMessage());
            }

            List<KpiMaintenanceStatusBean> existingConfigBeans;
            try {
                existingConfigBeans = KPI_DATA_SERVICE.fetchCompInstanceKpiMaintenanceStatus(instanceId, accountId);
            } catch (ControlCenterException e) {
                throw new ServerException(e.getMessage());
            }

            ActionsEnum actionsEnum;
            for(KpiMaintenanceStatusDetails instKpiConfig : configDetails) {
                int kpiId = instKpiConfig.getKpiId();
                int groupKpiId = instKpiConfig.getGroupKpiId();

                KpiBean kpiBean = new KPIDataService().fetchKpiUsingKpiId(kpiId, accountId, null);
                if (kpiBean == null) {
                    log.error("Kpi with ID [{}] is unavailable", kpiId);
                    throw new ServerException(String.format("KPI with ID [%d] is unavailable", kpiId));
                }

                if (groupKpiId > 0 && kpiBean.getGroupKpiId() != groupKpiId) {
                    log.error("Group Kpi with ID [{}] is not mapped to KPI with ID [{}]", groupKpiId, kpiId);
                    throw new ServerException(String.format("Group KPI with ID [%d] is not mapped to KPI with ID [%d]", groupKpiId, kpiId));
                }

                if(!existingConfigBeans.isEmpty()) {
                    if(existingConfigBeans.parallelStream()
                            .anyMatch(c -> c.getKpiGroupId() == instKpiConfig.getGroupKpiId()
                                    && c.getKpiId() == instKpiConfig.getKpiId())) {
                        actionsEnum = ActionsEnum.MODIFY;
                    }
                    else {
                        actionsEnum = ActionsEnum.ADD;
                    }
                }
                else {
                    actionsEnum = ActionsEnum.ADD;
                }

                KpiMaintenanceStatusBean bean = KpiMaintenanceStatusBean.builder()
                        .kpiId(instKpiConfig.getKpiId())
                        .kpiGroupId(instKpiConfig.getGroupKpiId())
                        //.attributeValue(instKpiConfig.getAttributeValue())
                        .isMaintenanceExcluded(instKpiConfig.getIsMaintenanceExcluded())
                        .compInstanceId(instanceId)
                        .accountId(accountId)
                        .userDetailsId(userId)
                        .actionForUpdate(actionsEnum)
                        .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                        .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                        .accountIdentifier(utilityBean.getAccountIdentifier())
                        .build();

                if (bean.getKpiGroupId() > 0) {
                    log.debug("Attribute thresholds are part of group KPI [{}]", bean.getKpiGroupId());

                    if (instanceToKpiAttrMapping.parallelStream()
                            .noneMatch(c -> c.getKpiId() == bean.getKpiId()
                                    && c.getKpiGroupId() == bean.getKpiGroupId()
                                    && c.getCompInstanceId() == bean.getCompInstanceId())) {
                        log.error("Group KPI [{}] is not mapped to compInstanceId [{}], accountId [{}]", bean.getKpiGroupId(),
                                instanceId, accountId);
                        throw new ServerException(String.format("Group KPI [%s] is not mapped to compInstanceId [%s], accountId [%s]", bean.getKpiGroupId(),
                                instanceId, accountId));
                    }
                } else {
                    if (kpiMappingExists.parallelStream()
                            .noneMatch(c -> c.getCompInstanceId() == bean.getCompInstanceId()
                                    && c.getKpiId() == bean.getKpiId())) {
                        log.error("Non-group KPI [{}] provided are not mapped to compInstanceId [{}], accountId [{}]", bean.getKpiId(), instanceId, accountId);
                        throw new ServerException(String.format("Non-group KPI [%s] provided are not mapped to compInstanceId [%s], accountId [%s]",
                                bean.getKpiId(), instanceId, accountId));
                    }
                }

                attrConfigBeans.add(bean);
            }
        }

        Set<KpiMaintenanceStatusBean> tempSet = attrConfigBeans.parallelStream().collect(Collectors.toSet());
        if(tempSet.size() != attrConfigBeans.size()) {
            log.error("Duplicate entries in the input");
            throw new ServerException("Duplicate entries in the input");
        }

        return attrConfigBeans;
    }

    @Override
    public String process(List<KpiMaintenanceStatusBean> beans) throws DataProcessingException {
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        try {
            return dbi.inTransaction((conn, status) ->
            {
                Map<Boolean, List<KpiMaintenanceStatusBean>> configsByActionList = beans.parallelStream()
                        .collect(Collectors.partitioningBy(c -> ActionsEnum.ADD.equals(c.getActionForUpdate())));
                List<KpiMaintenanceStatusBean> configsToAdd = configsByActionList.get(true);
                List<KpiMaintenanceStatusBean> configsToUpdate = configsByActionList.get(false);
                if(!configsToAdd.isEmpty()) {
                    insertData(configsToAdd, conn);
                    try {
                        //update metric maintenance details in redis cache.
                        updateMetricMaintenanceDetails(configsToAdd);
                    }catch (Exception e){
                        log.error("Exception while adding the Metric Maintenance Details in Redis : ", e);
                    }
                }
                if(!configsToUpdate.isEmpty()) {
                    updateData(configsToUpdate, conn);
                    try {
                        //update metric maintenance details in redis cache.
                        updateMetricMaintenanceDetails(configsToUpdate);
                    }catch (Exception e){
                        log.error("Exception while updating the Metric Maintenance Details in Redis : ", e);
                    }
                }
                return "Metric maintenance status updated successfully";
            });
        } catch (Exception e) {
            log.error("Unable to update KPI maintenance status. Details: ", e);

            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
            } else {
                throw e;
            }
        }

    }

    private void insertData(List<KpiMaintenanceStatusBean> configsToAdd, Handle handle) throws DataProcessingException {
        int[] ids = KPI_DATA_SERVICE.addInstanceKpiMaintenanceStatus(configsToAdd, handle);

        if (ids == null || ids.length == 0) {
            log.error("Error while adding KPI maintenance status configuration");
            throw new DataProcessingException("Error while adding KPI maintenance status configuration");
        }
    }

    private void updateData(List<KpiMaintenanceStatusBean> configsToUpdate, Handle handle) throws DataProcessingException {
        int[] ids = KPI_DATA_SERVICE.updateInstanceKpiMaintenanceStatus(configsToUpdate, handle);

        if (ids == null || ids.length == 0) {
            log.error("Error while updating KPI maintenance status configuration");
            throw new DataProcessingException("Error while updating KPI maintenance status configuration");
        }
    }
    public void updateMetricMaintenanceDetails(List<KpiMaintenanceStatusBean> configsToUpdate)
    {
        configsToUpdate.parallelStream()
                .forEach(maintenanceDetail -> {
                    CompInstClusterDetails instClusterDetail = instanceRepo.getInstances(maintenanceDetail.getAccountIdentifier()).parallelStream().filter(f -> f.getId() == maintenanceDetail.getCompInstanceId()).findAny().orElse(null);
                    if(instClusterDetail == null) {
                        log.error("Could not find the instance details from redis cache. AccountId:{}, instanceId:{}", maintenanceDetail.getAccountIdentifier(), maintenanceDetail.getCompInstanceId());
                        return ;
                    }
                    List<CompInstKpiEntity> instanceWiseKpis = instanceRepo.getInstanceWiseKpis(maintenanceDetail.getAccountIdentifier(), instClusterDetail.getIdentifier());

                    CompInstKpiEntity kpiEntity = instanceWiseKpis.stream().filter(k -> k.getId() == maintenanceDetail.getKpiId()).findAny().orElse(null);
                    if(kpiEntity == null) {
                        log.error("The kpi details not found for the kpi [{}] and instance [{}]", maintenanceDetail.getKpiId(), maintenanceDetail.getCompInstanceId());
                        return;
                    }

                    if (kpiEntity.getKpiViolationConfig() == null || kpiEntity.getKpiViolationConfig().isEmpty()
                    || !kpiEntity.getKpiViolationConfig().containsKey(maintenanceDetail.getAttributeValue())) {

                        log.debug("KPI Violation config is null/empty or with attribute value: [{}] for kpiId: [{}] " +
                                        "not found in redis updating it", maintenanceDetail.getAttributeValue(), maintenanceDetail.getKpiId());


                        KpiViolationConfig kpiViolationConfig = KpiViolationConfig.builder()
                                .excludeMaintenance(maintenanceDetail.getIsMaintenanceExcluded())
                                .persistence(maintenanceDetail.getPersistence())
                                .suppression(maintenanceDetail.getSuppression())
                                .compInstanceId(maintenanceDetail.getCompInstanceId())
                                .kpiId(maintenanceDetail.getKpiId())
                                .attributeValue(maintenanceDetail.getAttributeValue())
                                .build();

                        if (kpiEntity.getKpiViolationConfig() == null) {
                            Map<String, com.heal.configuration.pojos.KpiViolationConfig> violationConfigMap = new HashMap<>();
                            violationConfigMap.put(kpiViolationConfig.getAttributeValue(), kpiViolationConfig);
                            kpiEntity.setKpiViolationConfig(violationConfigMap);
                        } else {
                            kpiEntity.getKpiViolationConfig().put(kpiViolationConfig.getAttributeValue(), kpiViolationConfig);
                        }

                    } else if(kpiEntity.getKpiViolationConfig().containsKey(maintenanceDetail.getAttributeValue())) {
                        log.debug("Updating kpi violation config in redis for attribute value: [{}] and kpiId: [{}]",
                                maintenanceDetail.getAttributeValue(), maintenanceDetail.getKpiId());

                        kpiEntity.getKpiViolationConfig().get(maintenanceDetail.getAttributeValue()).setExcludeMaintenance(maintenanceDetail.getIsMaintenanceExcluded());
                    }

                    kpiEntity.setIsMaintenanceExcluded(maintenanceDetail.getIsMaintenanceExcluded());
                    List<CompInstKpiEntity> updatedInstanceWiseKpis = instanceWiseKpis.stream().filter(k -> k.getId() != maintenanceDetail.getKpiId()).collect(Collectors.toList());
                    updatedInstanceWiseKpis.add(kpiEntity);

                    instanceRepo.updateKpiDetailsForKpiId(maintenanceDetail.getAccountIdentifier(), instClusterDetail.getIdentifier(), kpiEntity);
                    instanceRepo.updateKpiDetailsForKpiIdentifier(maintenanceDetail.getAccountIdentifier(), instClusterDetail.getIdentifier(), kpiEntity);
                    instanceRepo.updateKpiDetails(maintenanceDetail.getAccountIdentifier(), instClusterDetail.getIdentifier(), updatedInstanceWiseKpis);
                });
    }
}
