package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.beans.MaintenanceScheduledBean;
import com.appnomic.appsone.common.exception.AppsOneException;
import com.appnomic.appsone.common.util.MaintenanceUtils;
import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.MaintenanceDetailsBean;
import com.appnomic.appsone.controlcenter.beans.MaintenanceRecurringDetailsBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.MaintenanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompInstanceMaintenanceMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.MaintenanceDetails;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.ComptInstancePojo;
import com.appnomic.appsone.controlcenter.pojo.MaintenanceDetailArgumentBean;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.MaintenanceWindowUtility;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class GetAdhocMaintenanceWindowBL implements BusinessLogic<Integer, MaintenanceDetails, List<MaintenanceDetailsBean>> {

    private static final MaintenanceDataService MAINTENANCE_DATA_SERVICE = new MaintenanceDataService();

    @Override
    public UtilityBean<Integer> clientValidation(RequestObject request) throws ClientException {

        String accountIdentifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        String serviceIdStr = request.getParams().get(Constants.SERVICE_ID);

        if (StringUtils.isEmpty(request.getHeaders().get(Constants.AUTHORIZATION))) {
            log.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }

        if (StringUtils.isEmpty(request.getParams().get(Constants.ACCOUNT_IDENTIFIER))) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        if (StringUtils.isEmpty(serviceIdStr)) {
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        }

        int serviceId = 0;
        try {
            serviceId = Integer.parseInt(serviceIdStr);
        } catch (Exception e) {
            log.error("Invalid service id [{}]", serviceId);
            throw new ClientException("Invalid service id");
        }

        return UtilityBean.<Integer>builder()
                .accountIdentifier(accountIdentifier)
                .pojoObject(serviceId)
                .build();
    }

    @Override
    public MaintenanceDetails serverValidation(UtilityBean<Integer> maintenanceBean) throws ServerException {
        AccountBean account = ValidationUtils.validAndGetAccount(maintenanceBean.getAccountIdentifier());
        if (account == null) {
            log.error("Account with identifier [{}] unavailable", maintenanceBean.getAccountIdentifier());
            throw new ServerException("Account with identifier [{}] unavailable");
        }

        int accountId = account.getId();
        int serviceId = maintenanceBean.getPojoObject();

        ControllerBean controller = new ControllerDataService().getControllerById(serviceId, accountId, null);
        if (controller == null) {
            log.error("Service with id [{}] is unavailable for account id [{}]", maintenanceBean.getServiceId(), accountId);
            throw new ServerException(String.format("Service with id [%d] is unavailable for account id [%d]", serviceId, accountId));
        }

        MaintenanceDetails maintenanceDetails = new MaintenanceDetails();
        maintenanceDetails.setAccountId(account.getId());
        maintenanceDetails.setServiceId(serviceId);

        return maintenanceDetails;
    }

    @Override
    public List<MaintenanceDetailsBean> process(MaintenanceDetails details) throws DataProcessingException {

        List<MaintenanceDetailsBean> maintenanceDetailsBeanList = new ArrayList<>();
        Timestamp date;
        try {
            date = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
        } catch (ParseException e) {
            log.error("Error while fetching date from current timestamp. Details: ", e);
            throw new DataProcessingException("Error while fetching date from current timestamp");
        }

        CompInstanceBL compInstanceBL = new CompInstanceBL();
        List<ComptInstancePojo> componentInstanceBeanList;
        try {
            componentInstanceBeanList = compInstanceBL.getAllInstances(details.getAccountId(), details.getServiceId());
        } catch (ControlCenterException e) {
            log.error("Error while fetching component/host instances for service [{}] mapped to account [{}]", details.getServiceId(), details.getAccountId());
            throw new DataProcessingException(String.format("Error while fetching component/host instances for service [%d] mapped to account [%d]", details.getServiceId(), details.getAccountId()));
        }

        for (ComptInstancePojo componentInstanceBean : componentInstanceBeanList) {
            MaintenanceScheduledBean maintenanceScheduledBean;
            try {
                maintenanceScheduledBean = getMaintenanceScheduledByCompInstanceId(componentInstanceBean.getInstanceId(), date, date);
            } catch (AppsOneException e) {
                log.error("Error while fetching scheduled maintenance window for component instance [{}]", componentInstanceBean.getInstanceId());
                throw new DataProcessingException(String.format("Error while fetching scheduled maintenance window for component instance [%d]", componentInstanceBean.getInstanceId()));
            }

            if (maintenanceScheduledBean != null && maintenanceScheduledBean.getOnGoing() != null) {
                maintenanceDetailsBeanList.add(MaintenanceDetailsBean.builder()
                        .id(maintenanceScheduledBean.getOnGoing().getId())
                        .type(Constants.MST_SUB_TYPE_SCHEDULED)
                        .startTime(new Timestamp(maintenanceScheduledBean.getOnGoing().getStartTime()))
                        .endTime(new Timestamp(maintenanceScheduledBean.getOnGoing().getEndTime()))
                        .name(Objects.nonNull(componentInstanceBean.getInstanceName()) ? componentInstanceBean.getInstanceName() : componentInstanceBean.getHostName())
                        .ongoing(true)
                        .build());
            }
            if (maintenanceScheduledBean != null && maintenanceScheduledBean.getUpComing() != null) {
                maintenanceScheduledBean.getUpComing().parallelStream().forEach(maintenance -> maintenanceDetailsBeanList.add(MaintenanceDetailsBean.builder()
                        .id(maintenance.getId())
                        .type(Constants.MST_SUB_TYPE_SCHEDULED)
                        .startTime(new Timestamp(maintenance.getStartTime()))
                        .endTime(new Timestamp(maintenance.getEndTime()))
                        .name(Objects.nonNull(componentInstanceBean.getInstanceName()) ? componentInstanceBean.getInstanceName() : componentInstanceBean.getHostName())
                        .upcoming(true)
                        .build()));
            }
        }

        if (maintenanceDetailsBeanList.isEmpty()) {
            return maintenanceDetailsBeanList;
        }

        List<MaintenanceDetailsBean> maintenanceDetailsBeans = new ArrayList<>();
        Map<Integer, List<MaintenanceDetailsBean>> maintenanceDet =
                maintenanceDetailsBeanList
                        .stream()
                        .collect(Collectors.groupingBy(MaintenanceDetailsBean::getId));

        for (Map.Entry<Integer, List<MaintenanceDetailsBean>> entry : maintenanceDet.entrySet()) {
            StringBuilder name = new StringBuilder();
            for (MaintenanceDetailsBean maintenanceDetails : entry.getValue())
                name.append(maintenanceDetails.getName()).append(",");
            name.deleteCharAt(name.lastIndexOf(","));
            MaintenanceDetailsBean maintenanceDetailsBean = entry.getValue().get(0);
            MaintenanceDetailsBean updatedMaintenanceBean = MaintenanceDetailsBean.builder()
                    .id(maintenanceDetailsBean.getId())
                    .type(Constants.MST_SUB_TYPE_SCHEDULED)
                    .startTime(maintenanceDetailsBean.getStartTime())
                    .endTime(maintenanceDetailsBean.getEndTime())
                    .name(name.toString())
                    .ongoing(maintenanceDetailsBean.isOngoing())
                    .upcoming(maintenanceDetailsBean.isUpcoming())
                    .build();
            maintenanceDetailsBeans.add(updatedMaintenanceBean);
        }

        return maintenanceDetailsBeans;
    }

    public List<MaintenanceDetailArgumentBean> getMaintenanceConfig(int instanceId, String instanceName) throws AppsOneException {
        try {
            List<MaintenanceDetailArgumentBean> maintenanceDetailArgumentBeanList = new ArrayList<>();
            Timestamp date = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());

            MaintenanceScheduledBean maintenanceScheduledBean = getMaintenanceScheduledByCompInstanceId(instanceId, date, date);
            if (maintenanceScheduledBean != null && maintenanceScheduledBean.getOnGoing() != null)
                maintenanceDetailArgumentBeanList.add(MaintenanceDetailArgumentBean.builder()
                        .id(maintenanceScheduledBean.getOnGoing().getId())
                        .typeName(Constants.MST_SUB_TYPE_SCHEDULED)
                        .startTime(new Timestamp(maintenanceScheduledBean.getOnGoing().getStartTime()))
                        .endTime(new Timestamp(maintenanceScheduledBean.getOnGoing().getEndTime()))
                        .name(instanceName)
                        .ongoing(true)
                        .build());

            return maintenanceDetailArgumentBeanList;
        } catch (Exception e) {
            String error = "Error while fetching maintenance Config for adhoc maintenance";
            log.error(error, e);
            throw new AppsOneException(error);
        }
    }

    public static MaintenanceScheduledBean getMaintenanceScheduledByCompInstanceId(int compInstanceId, Timestamp startTime, Timestamp endTime) throws AppsOneException {
        List<Integer> maintenanceList = MAINTENANCE_DATA_SERVICE.getMaintenanceWindowsByCompInstanceId(compInstanceId)
                .parallelStream()
                .map(CompInstanceMaintenanceMapping::getMaintenanceId)
                .collect(Collectors.toList());

        if (maintenanceList.isEmpty()) {
            return null;
        }

        log.debug("Checking Maintenance Scheduled for comp instance id : {} startTime : {} endTime : {}", compInstanceId, startTime, endTime);
        MaintenanceRecurringDetailsBean maintenanceDetails = MaintenanceWindowUtility.getConfiguredMaintenanceDetails(maintenanceList, startTime, endTime);
        if (maintenanceDetails == null)
            return null;

        MaintenanceScheduledBean maintenanceScheduledBean = MaintenanceUtils.getMaintenanceScheduled(maintenanceDetails.getMaintenanceWindowBeanList(),
                maintenanceDetails.getRecurringDetailsMap(), startTime, endTime);
        if (!maintenanceScheduledBean.getUpComing().isEmpty()){
            maintenanceScheduledBean.setUpComing(maintenanceScheduledBean.getUpComing().stream().filter(x -> x.getEndTime() > x.getStartTime()).collect(Collectors.toList()));
        }
        return maintenanceScheduledBean;
    }
}



