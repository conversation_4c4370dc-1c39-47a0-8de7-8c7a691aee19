package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.cache.keys.CommVersionKPIs;
import com.appnomic.appsone.controlcenter.cache.keys.MstKpi;
import com.appnomic.appsone.controlcenter.cache.keys.ProducerKpis;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ComponentDao;
import com.appnomic.appsone.controlcenter.dao.redis.AgentRepo;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.BasicAgentEntity;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.CompInstClusterDetails;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
public class UpdateInstanceBL implements BusinessLogic<List<UpdateInstancePojo>, List<UpdateInstancePojo>, Object> {

    private int accId;
    private String userId;
    private String accountIdentifier;

    @Override
    public UtilityBean<List<UpdateInstancePojo>> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(requestObject.getBody())) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid account identifier");
        }

        List<UpdateInstancePojo> parsedRequestBody;
        try {
            ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();
            parsedRequestBody = objectMapper.readValue(requestObject.getBody(),
                    new TypeReference<List<UpdateInstancePojo>>() {
                    });

            if (parsedRequestBody.isEmpty()) {
                log.error(UIMessages.INVALID_REQUEST_BODY);
                throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
            }

            parsedRequestBody = parsedRequestBody.parallelStream().distinct().collect(Collectors.toList());

            for (UpdateInstancePojo data : parsedRequestBody) {
                if (!data.isValid()) {
                    log.error("Input Validation failure for Update Instance Details.");
                    throw new ClientException("Input Validation failure for Update Instance Details.");
                }
            }
        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        return UtilityBean.<List<UpdateInstancePojo>>builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .pojoObject(parsedRequestBody)
                .build();
    }

    @Override
    public List<UpdateInstancePojo> serverValidation(UtilityBean<List<UpdateInstancePojo>> utilityBean) throws ServerException {
        accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        accId = account.getId();

        userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        for (UpdateInstancePojo instance : utilityBean.getPojoObject()) {
            requestBodyServerValidation(instance);
        }

        return utilityBean.getPojoObject();
    }

    private void requestBodyServerValidation(UpdateInstancePojo instance) throws ServerException {

        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
        ControllerDataService controllerDataService = new ControllerDataService();

        /*
        Instance details check
         */
        ComponentInstanceBean instanceBean = compInstanceDataService.
                getComponentInstanceByIdAndIdentifierAndAccount(instance.getInstanceId(), instance.getInstanceIdentifier(), accId);
        if (instanceBean == null) {
            log.error("Instance with the id [{}] and identifier [{}] is unavailable for this account.", instance.getInstanceId(), instance.getInstanceIdentifier());
            throw new ServerException(String.format("Instance with id [%s] and identifier [%s] is unavailable for this account.",
                    instance.getInstanceId(), instance.getInstanceIdentifier()));
        }

        MasterComponentTypeBean componentTypeBean = MasterCache.getMasterComponentTypeUsingName(Constants.COMPONENT_TYPE_HOST, String.valueOf(accId));
        if (componentTypeBean == null) {
            String err = "Component with type '" + Constants.HOST + "' doesn't exist.";
            log.error(err);
            throw new ServerException(err);
        }

        instance.setHostInstance(componentTypeBean.getId() == instanceBean.getMstComponentTypeId());

        // add a check for host getting updated with env or ip address is already exist.
        // check only when you have a change request for environment id
        if (instance.isHostInstance() && instance.getEnvironment() != null) {
            try {
                int envId = new EnvironmentHelper().getEnvironmentId(instance.getEnvironment());

                int instanceDetailsByHostAddress = new CompInstanceDataService()
                        .getInstanceDetailsByHostAddress(accId, instanceBean.getHostAddress(), envId, null);

                if (instanceDetailsByHostAddress == -1) {
                    String errMsg = String.format("Exception while getting count of the instances with host_address %s and environment %s for account %s from Data source", instanceBean.getHostAddress(), envId, accId);
                    log.error(errMsg);
                    throw new ServerException(errMsg);
                }

                if (instanceDetailsByHostAddress >= 1) {
                    String err = "Host instance with the same environment and host address already exists.";
                    log.error(err);
                    throw new ServerException(err);
                }
                log.debug("no other host exist in env {} with host address {}",  envId, instanceBean.getHostAddress());

            } catch (DataProcessingException e) {
                String err = "Invalid environment provided";
                log.error(err);
                throw new ServerException(err);
            }
        }
        /*
        Application check
         */
        if (instance.getApplication() != null) {

            for (UpdateInstancePojo.ApplicationServiceMapping applicationData : instance.getApplication()) {

                ControllerBean appBean = controllerDataService.getApplicationsById(applicationData.getId(), accId, null);
                if (appBean == null || !appBean.getIdentifier().equals(applicationData.getIdentifier())) {
                    log.error("Application with the id [{}] and identifier [{}] is unavailable for this account.", applicationData.getId(),
                            applicationData.getIdentifier());
                    throw new ServerException(String.format("Application with id [%s] and identifier [%s] is unavailable for this account.",
                            applicationData.getId(), applicationData.getIdentifier()));
                }

                List<ControllerBean> serviceBeanList = new ArrayList<>();
                try {
                    serviceBeanList = controllerDataService.getServicesForAccount(accId, null);
                } catch (ControlCenterException e) {
                    log.error("Exception occurred. Details: ", e);
                }

                List<ViewApplicationServiceMappingBean> viewApplicationServiceMappingBeans =
                        controllerDataService.getApplicationServiceMappingWithAppId(accId, applicationData.getId(), null);

                /*
                Service check
                 */
                for (UpdateInstancePojo.IdAction serviceData : applicationData.getService()) {

                    ControllerBean serviceBean = serviceBeanList.stream().filter(c -> c.getId() == serviceData.getId()
                                    && c.getIdentifier().equals(serviceData.getIdentifier()))
                            .findAny()
                            .orElse(null);
                    if (serviceBean == null) {
                        log.error("Service with the id [{}] and identifier [{}] is unavailable for this account.", serviceData.getId(),
                                serviceData.getIdentifier());
                        throw new ServerException(String.format("Service with the id [%s] and identifier [%s] is unavailable for this account.",
                                serviceData.getId(), serviceData.getIdentifier()));
                    }

                    ViewApplicationServiceMappingBean appService = viewApplicationServiceMappingBeans.stream()
                            .filter(c -> c.getApplicationId() == applicationData.getId()
                                    && c.getApplicationIdentifier().equals(applicationData.getIdentifier())
                                    && c.getServiceId() == serviceData.getId()
                                    && c.getServiceIdentifier().equals(serviceData.getIdentifier()))
                            .findAny()
                            .orElse(null);

                    if (appService == null) {
                        log.error("Service with the id [{}] and identifier [{}] is not mapped to Application with id [{}] and " +
                                        "identifier [{}].", serviceData.getId(), serviceData.getIdentifier(),
                                applicationData.getId(), applicationData.getIdentifier());
                        throw new ServerException(String.format("Service with id [%s] and identifier [%s] is not mapped to" +
                                        " Application with id [%s] and identifier [%s].",
                                serviceData.getId(), serviceData.getIdentifier(), applicationData.getId(), applicationData.getIdentifier()));
                    }


                    if (serviceData.getAction().equalsIgnoreCase("Add")) {
                        List<InstanceClusterServicePojo> instanceDetails = new CompInstanceDataService()
                                .getInstanceClusterServiceDetails(serviceData.getId(), accId, null);
                        /*
                        if host cluster present, then component id and common version id should be same
                         */
                        if (instance.isHostInstance()) {

                            if (instanceDetails != null && !instanceDetails.isEmpty()) {
                                for (InstanceClusterServicePojo singleHostDetails : instanceDetails) {
                                    if (singleHostDetails.getClusterComponentId() != instanceBean.getMstComponentId()
                                            && singleHostDetails.getClusterComponentTypeId() == componentTypeBean.getId()) {
                                        String err = "Host Instance's component is different from existing host " +
                                                "cluster's component for the service id " + serviceData.getId() + ".";
                                        log.error(err);
                                        throw new ServerException(err);
                                    }

                                    if (singleHostDetails.getClusterCommonVersionId() != instanceBean.getMstCommonVersionId()
                                            && singleHostDetails.getClusterComponentTypeId() == componentTypeBean.getId()) {
                                        String err = "Host Instance's component common version is different from existing host " +
                                                "cluster's component common version for the service id " + serviceData.getId() + ".";
                                        log.error(err);
                                        throw new ServerException(err);
                                    }
                                }
                            }
                        } else {
                            /*
                            host cluster should be present for comp instance map check
                             */
                            List<InstanceClusterServicePojo> hostClusterDetails = new CompInstanceDataService()
                                    .getClusterServiceDetails(serviceData.getId(), componentTypeBean.getId(), null);
                            if (hostClusterDetails == null || hostClusterDetails.isEmpty()) {
                                String err = "No Host Cluster found while mapping component instance to the service " + serviceData.getName();
                                log.error(err);
                                throw new ServerException(err);
                            }

                            if (instanceDetails != null && !instanceDetails.isEmpty()) {
                                for (InstanceClusterServicePojo singleCompInstanceDetails : instanceDetails) {
                                    if (singleCompInstanceDetails.getClusterComponentId() != instanceBean.getMstComponentId()
                                            && singleCompInstanceDetails.getClusterComponentTypeId() != componentTypeBean.getId()) {
                                        String err = "Component Instance's component is different from existing component " +
                                                "cluster's component for the service id " + serviceData.getId() + ".";
                                        log.error(err);
                                        throw new ServerException(err);
                                    }

                                    if (singleCompInstanceDetails.getClusterCommonVersionId() != instanceBean.getMstCommonVersionId()
                                            && singleCompInstanceDetails.getClusterComponentTypeId() != componentTypeBean.getId()) {
                                        String err = "Component Instance's component common version is different from existing Component " +
                                                "cluster's component common version for the service id " + serviceData.getId() + ".";
                                        log.error(err);
                                        throw new ServerException(err);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

    }

    @Override
    public Object process(List<UpdateInstancePojo> instancePojoList) throws DataProcessingException {

        AgentRepo agentRepo = new AgentRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        InstanceRepo instanceRepo = new InstanceRepo();
        String updateTime = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());


        List<Integer> agentIds = new ArrayList<>();
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();

            dbi.inTransaction((conn, status) -> {
                for (UpdateInstancePojo instance : instancePojoList) {
                    try {
                        int agentId = updateInstance(instance, updateTime, conn);
                        if (agentId != 0) {
                            agentIds.add(agentId);
                        }
                    } catch (Exception e) {
                        log.error("Error while updating Instance information. Reason: ", e);
                        throw new DataProcessingException("Error in updating instance information for instance with id '"
                                + instance.getInstanceId() + "' and identifier " + instance.getInstanceIdentifier());
                    }
                }
                return null;
            });

            Map<Integer, CompInstClusterDetails> compInstDetailsMap = instanceRepo.getInstances(accountIdentifier).stream()
                    .collect(Collectors.toMap(CompInstClusterDetails::getId, Function.identity()));

            for (UpdateInstancePojo updateInstancePojo : instancePojoList) {
                if (updateInstancePojo.getName() != null || updateInstancePojo.getEnvironment() != null) {
                    CompInstClusterDetails instClusterDetails = compInstDetailsMap.get(updateInstancePojo.getInstanceId());

                    if (instClusterDetails == null) {
                        log.error("Could not find instance detail for given instanceId [{}]", updateInstancePojo.getInstanceId());
                        return null;
                    }

                    if (instClusterDetails.getComponentTypeId() == 1) {
                        List<CompInstClusterDetails> instancesMappedToThisHostAddress = compInstDetailsMap.values()
                                .parallelStream()
                                .filter(e -> e.getHostAddress() != null)
                                .filter(f -> f.getHostAddress().equalsIgnoreCase(instClusterDetails.getHostAddress()))
                                .collect(Collectors.toList());

                        Map<Boolean, List<CompInstClusterDetails>> mapOfCompAndHostInstances = instancesMappedToThisHostAddress.parallelStream().collect(Collectors.partitioningBy(pt -> pt.getComponentTypeId() == 1));

                        /*Code block below adds functionality of updating host-name in service level instances redis-cache key */
                        mapOfCompAndHostInstances.get(true).forEach(mappedHostInstanceObj -> {
                            List<BasicEntity> services = instanceRepo.getServices(accountIdentifier, mappedHostInstanceObj.getIdentifier());
                            services.forEach(service -> {
                                List<BasicInstanceBean> updatedBasicInstanceBean = serviceRepo.getServiceInstances(accountIdentifier, service.getIdentifier())
                                        .stream()
                                        .peek(p -> {
                                            if(p.getIdentifier().equalsIgnoreCase(mappedHostInstanceObj.getIdentifier())) {
                                                p.setName(updateInstancePojo.getName() == null ? instClusterDetails.getName() : updateInstancePojo.getName());
                                                p.setUpdatedTime(updateTime);
                                            }
                                        })
                                        .collect(Collectors.toList());

                                serviceRepo.updateServiceInstances(accountIdentifier, service.getIdentifier(), updatedBasicInstanceBean);
                            });

                            /*Code block below adds functionality of updating host-name in agent level instances redis-cache key */
                            CompInstClusterDetails compInstClusterDetails = instanceRepo.getInstanceDetailByIdentifier(accountIdentifier,
                                            mappedHostInstanceObj.getIdentifier());
                            if(compInstClusterDetails != null && compInstClusterDetails.getAgentIds() != null) {
                                   compInstClusterDetails.getAgentIds()
                                        .forEach(agentIdentifier -> {
                                            List<BasicEntity> updatedBasicEntity = agentRepo.getAgentInstanceMappingDetails(accountIdentifier, agentIdentifier)
                                                    .stream()
                                                    .peek(p -> {
                                                        if (p.getIdentifier().equalsIgnoreCase(mappedHostInstanceObj.getIdentifier())) {
                                                            p.setName(updateInstancePojo.getName() == null ? instClusterDetails.getName() : updateInstancePojo.getName());
                                                            p.setUpdatedTime(updateTime);
                                                        }

                                                    })
                                                    .collect(Collectors.toList());
                                            agentRepo.updateAgentInstanceMappingDetails(accountIdentifier, agentIdentifier, updatedBasicEntity);
                                        });
                            }
                        });
                        /*Code block below updates host-name in instances and specific instance(instance-by-identifier) redis-cache keys*/
                        mapOfCompAndHostInstances.get(false).forEach(mappedCompInstanceObj -> {
                                    mappedCompInstanceObj.setHostName(updateInstancePojo.getName() == null ? instClusterDetails.getName() : updateInstancePojo.getName());
                                    mappedCompInstanceObj.setUpdatedTime(updateTime);
                                    compInstDetailsMap.put(mappedCompInstanceObj.getId(), mappedCompInstanceObj);
                                    instanceRepo.updateInstanceByIdentifier(accountIdentifier, mappedCompInstanceObj);

                                }
                        );
                        instanceRepo.updateInstances(accountIdentifier, new ArrayList<>(compInstDetailsMap.values()));
                    }

                    updateInstanceDetailInRedis(compInstDetailsMap, updateInstancePojo, instClusterDetails, updateTime);

                    updateServiceWiseInstanceInRedis(updateInstancePojo, updateTime);

                    if (!agentIds.isEmpty()) {
                        List<BasicAgentEntity> agents = agentRepo.getAgents(accountIdentifier);
                        for (Integer agentId : agentIds) {
                            BasicAgentEntity basicAgentEntity = agents.parallelStream().filter(f -> f.getId() == agentId).findAny().orElse(null);
                            if (basicAgentEntity == null) {
                                log.error("Could not find agent detail for agentId [{}] from agents list", agentId);
                                return null;
                            }
                            List<BasicEntity> agentInstanceMappingDetails = agentRepo.getAgentInstanceMappingDetails(accountIdentifier, basicAgentEntity.getIdentifier());
                            BasicEntity basicEntity = agentInstanceMappingDetails.parallelStream().filter(f -> f.getId() == instClusterDetails.getId()).findAny().orElse(null);
                            if (basicEntity != null) {
                                basicEntity.setName(updateInstancePojo.getName() == null ? basicEntity.getName() : updateInstancePojo.getName());
                                basicEntity.setUpdatedTime(updateTime);
                                agentRepo.updateAgentInstanceMappingDetails(accountIdentifier, basicEntity.getIdentifier(), agentInstanceMappingDetails);
                            }
                        }
                    }
                }

            }

        } catch (Exception e) {
            log.error("Error while updating Instance information. Reason: ", e);
            throw (DataProcessingException) Throwables.getRootCause(e);
        }
        return null;
    }

    private void updateInstanceDetailInRedis(Map<Integer, CompInstClusterDetails> compInstDetailsMap, UpdateInstancePojo updateInstancePojo, CompInstClusterDetails instClusterDetails, String updateTime) {
        AgentRepo agentRepo = new AgentRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        InstanceRepo instanceRepo = new InstanceRepo();
        instClusterDetails.setName(updateInstancePojo.getName() == null ? instClusterDetails.getName() : updateInstancePojo.getName());

        int environmentId;
        try {
            environmentId = new EnvironmentHelper().getEnvironmentId(updateInstancePojo.getEnvironment());
        } catch (Exception e) {
            log.error("Setting the value the environment id to NONE");
            environmentId = 383;
        }

        instClusterDetails.setIsDR(environmentId);
        instClusterDetails.setUpdatedTime(updateTime);
        compInstDetailsMap.put(updateInstancePojo.getInstanceId(), instClusterDetails);
        instanceRepo.updateInstanceByIdentifier(accountIdentifier, instClusterDetails);
        instanceRepo.updateInstances(accountIdentifier, new ArrayList<>(compInstDetailsMap.values()));

        for (String agentIdentifier : instClusterDetails.getAgentIds()) {
            List<BasicEntity> agentInstanceMappingDetails = agentRepo.getAgentInstanceMappingDetails(accountIdentifier, agentIdentifier);
            BasicEntity basicEntity = agentInstanceMappingDetails.parallelStream().filter(f -> f.getId() == updateInstancePojo.getInstanceId()).findAny().orElse(null);
            if (basicEntity != null) {
                basicEntity.setName(updateInstancePojo.getName());
                basicEntity.setUpdatedTime(updateTime);
                agentRepo.updateAgentInstanceMappingDetails(accountIdentifier, agentIdentifier, agentInstanceMappingDetails);
            }
        }

        List<BasicEntity> services = instanceRepo.getServices(accountIdentifier, instClusterDetails.getIdentifier());
        List<String> serviceIdentifiersMappedToInstance = services.parallelStream().map(BasicEntity::getIdentifier).collect(Collectors.toList());
        for (String serviceIdentifier : serviceIdentifiersMappedToInstance) {
            List<BasicInstanceBean> serviceInstances = serviceRepo.getServiceInstances(accountIdentifier, serviceIdentifier);
            BasicInstanceBean basicInstanceBean = serviceInstances.parallelStream().filter(f -> f.getId() == updateInstancePojo.getInstanceId()).findAny().orElse(null);
            if (basicInstanceBean != null) {
                basicInstanceBean.setName(updateInstancePojo.getName());
                basicInstanceBean.setUpdatedTime(updateTime);
                serviceRepo.updateServiceInstances(accountIdentifier, serviceIdentifier, serviceInstances);
            }
        }
    }

    private void updateServiceWiseInstanceInRedis(UpdateInstancePojo updateInstancePojo, String updateTime) throws ControlCenterException {
        ServiceRepo serviceRepo = new ServiceRepo();
        InstanceRepo instanceRepo = new InstanceRepo();
        BindInDataService bindInDataService = new BindInDataService();

        if (updateInstancePojo.getApplication() == null) {
            log.debug("Application is not updated for the given instanceId: [{}]", updateInstancePojo.getInstanceId());
            return;
        }
        Map<Integer, String> serviceDetailMapWithAddAction = updateInstancePojo.getApplication().get(0).getService().parallelStream().filter(f -> f.getAction().equalsIgnoreCase("add")).collect(Collectors.toMap(UpdateInstancePojo.IdAction::getId, UpdateInstancePojo.IdAction::getIdentifier));
        Map<Integer, String> serviceDetailMapWithRemoveAction = updateInstancePojo.getApplication().get(0).getService().parallelStream().filter(f -> f.getAction().equalsIgnoreCase("remove")).collect(Collectors.toMap(UpdateInstancePojo.IdAction::getId, UpdateInstancePojo.IdAction::getIdentifier));
        List<BasicEntity> services = instanceRepo.getServices(accountIdentifier, updateInstancePojo.getInstanceIdentifier());

        for (String serviceIdentifier : serviceDetailMapWithRemoveAction.values()) {
            List<BasicInstanceBean> serviceInstances = serviceRepo.getServiceInstances(accountIdentifier, serviceIdentifier);
            serviceInstances.removeIf(f -> f.getId() == updateInstancePojo.getInstanceId());
            serviceRepo.updateServiceInstances(accountIdentifier, serviceIdentifier, serviceInstances);
            services.removeIf(f -> f.getIdentifier().equalsIgnoreCase(serviceIdentifier));
        }
        instanceRepo.updateInstanceWiseServices(accountIdentifier, updateInstancePojo.getInstanceIdentifier(), services);

        if (serviceDetailMapWithAddAction.isEmpty()) {
            log.debug("No new service is mapped to the instance of instanceId [{}]", updateInstancePojo.getInstanceId());
            return;
        }

        ClusterInstancePojo clusterInstanceMapping = bindInDataService.getClusterInstanceMapping(updateInstancePojo.getInstanceId(), null);
        CompInstClusterDetails instanceDetailByIdentifier = instanceRepo.getInstanceDetailByIdentifier(accountIdentifier, updateInstancePojo.getInstanceIdentifier());
        BasicInstanceBean basicInstanceBeanObject = BasicInstanceBean.builder()
                .id(instanceDetailByIdentifier.getId())
                .status(instanceDetailByIdentifier.getStatus())
                .createdTime(instanceDetailByIdentifier.getCreatedTime())
                .updatedTime(updateTime)
                .name(instanceDetailByIdentifier.getName())
                .identifier(instanceDetailByIdentifier.getIdentifier())
                .lastModifiedBy(instanceDetailByIdentifier.getLastModifiedBy())
                .accountId(instanceDetailByIdentifier.getAccountId())
                .componentId(instanceDetailByIdentifier.getComponentId())
                .componentTypeId(instanceDetailByIdentifier.getComponentTypeId())
                .componentVersionId(instanceDetailByIdentifier.getComponentVersionId())
                .commonVersionId(instanceDetailByIdentifier.getCommonVersionId())
                .clusterId(clusterInstanceMapping.getClusterId())
                .clusterIdentifier(clusterInstanceMapping.getClusterIdentifier())
                .build();


        for (int serviceId : serviceDetailMapWithAddAction.keySet()) {
            List<BasicInstanceBean> serviceInstances = serviceRepo.getServiceInstances(accountIdentifier, serviceDetailMapWithAddAction.get(serviceId));
            basicInstanceBeanObject.setConcernedConfigId(serviceId);
            basicInstanceBeanObject.setConcernedConfigIdentifier(serviceDetailMapWithAddAction.get(serviceId));
            serviceInstances.add(basicInstanceBeanObject);
            serviceRepo.updateServiceInstances(accountIdentifier, serviceDetailMapWithAddAction.get(serviceId), serviceInstances);
            BasicEntity serviceConfigurationById = serviceRepo.getServiceConfigurationById(accountIdentifier, serviceId);
            services.add(serviceConfigurationById);
        }
        instanceRepo.updateInstanceWiseServices(accountIdentifier, updateInstancePojo.getInstanceIdentifier(), services);
    }

    private int updateInstance(UpdateInstancePojo instance, String updateTime, Handle handle) throws DataProcessingException {

        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();

        ComponentInstanceBean instanceBean = compInstanceDataService.
                getComponentInstanceByIdAndIdentifierAndAccount(instance.getInstanceId(), instance.getInstanceIdentifier(), accId);

        MasterComponentTypeBean componentTypeBean = MasterCache.getMasterComponentTypeUsingName(Constants.COMPONENT_TYPE_HOST, String.valueOf(accId));
        assert componentTypeBean != null;

        /*
        instance name edit
         */
        if (instance.getName() != null) {
            instanceDetailsUpdate(instance, updateTime, handle);
        }

        /*
        instance environment edit
         */
        if (instance.getEnvironment() != null) {
            instanceEnvironmentUpdate(instance, updateTime, handle);
        }

        /*
        get agent id for the instance
         */
        int agentId = 0;
        if (instance.isHostInstance()) {
            agentId = AgentDataService.getAgentId(instanceBean.getId(), null);
        }
        if (agentId == -1) {
            log.error("Exception while getting agent id from agent_comp_instance_mapping table.");
            throw new DataProcessingException("Exception while getting agent id from agent_comp_instance_mapping table.");
        }
        TagDetailsBean tagDetailsBean = MasterCache.getTagDetails(Constants.CONTROLLER_TAG);
        int tagId = 1;
        if (tagDetailsBean == null) {
            log.warn("Exception while fetching tag id for 'Controller' type. Setting tag id value to default i.e 1.");
        } else {
            tagId = tagDetailsBean.getId();
        }

        /*
        instance cluster edit
         */
        if (instance.getApplication() != null) {

            List<ViewClusterServicesBean> viewClusterServicesBeans = new ControllerDataService().getClusterServiceMapping(null);

            for (UpdateInstancePojo.ApplicationServiceMapping applicationData : instance.getApplication()) {

                for (UpdateInstancePojo.IdAction serviceData : applicationData.getService()) {

                    if (serviceData.getAction().equalsIgnoreCase("Add")) {

                        /*
                        In case earlier mapping was wrong and component instance had host_id 0
                         */
                        if (!instance.isHostInstance()) {
                            List<InstanceClusterServicePojo> hostDetails = new CompInstanceDataService()
                                    .getInstanceClusterServiceDetails(serviceData.getId(), accId, null);
                            hostDetails.stream().filter(c -> c.getHostAddress().equals(instanceBean.getHostAddress())
                                            && c.getClusterComponentTypeId() == componentTypeBean.getId()).findAny()
                                    .ifPresent(hostInstPojo -> instanceBean.setHostId(hostInstPojo.getInstanceId()));
                            ComponentDao componentInstanceDao = MySQLConnectionManager.getInstance().open(ComponentDao.class);
                            componentInstanceDao.updateInstanceHostIds(instanceBean.getHostId(), instanceBean.getId(), accId);
                        }

                        int clusterId = compInstanceDataService
                                .getClusterId(instanceBean.getMstComponentId(),
                                        instanceBean.getMstCommonVersionId(), instanceBean.getMstComponentTypeId(), serviceData.getId(),
                                        Objects.requireNonNull(MasterCache.getTagDetails(Constants.CONTROLLER_TAG)).getId(), accId, handle);

                         /*
                        get host id for add cluster
                         */
                        int clusterHostId = 0;

                        if (clusterId <= 0) {
                            if (!instance.isHostInstance()) {
                                try {
                                    List<Integer> hostIds = new BindInDataService()
                                            .getHostClusterId(Collections.singletonList(serviceData.getId()), componentTypeBean.getId(), null);
                                    if (hostIds.size() != 1) {
                                        log.warn("Multiple host cluster Ids available for the provided service. Resetting hostId to 0 for add cluster due to this conflict.");
                                    } else {
                                        clusterHostId = hostIds.get(0);
                                    }
                                } catch (ControlCenterException e) {
                                    log.error("Unable to fetch host cluster ID");
                                    throw new DataProcessingException(e.getMessage());
                                }
                            }
                            /*
                            add cluster
                             */
                            clusterId = addCluster(instanceBean, Collections.singletonList(serviceData), updateTime, clusterHostId, handle);
                        }
                        /*
                        add component cluster mapping
                         */
                        addComponentClusterMapping(instanceBean.getId(), updateTime, clusterId, handle);

                         /*
                        add agent-service data in tag mapping
                         */
                        if (agentId != 0) {
                            addAgentServiceTagMapping(tagId, agentId, String.valueOf(serviceData.getId()), serviceData.getIdentifier(), handle);
                        }

                    } else if (serviceData.getAction().equalsIgnoreCase("Remove")) {

                        ViewClusterServicesBean serviceCluster = viewClusterServicesBeans.stream()
                                .filter(c -> c.getServiceId() == serviceData.getId()
                                        && c.getServiceIdentifier().equals(serviceData.getIdentifier())
                                        && instance.isHostInstance() == (c.getHostClusterId() == 0))
                                .findAny().orElse(null);

                        if (serviceCluster == null) {
                            log.error("No cluster-service mapping found for instance [{}] and service [{}]",
                                    instanceBean.getName(), serviceData.getName());
                            throw new DataProcessingException("No cluster-service mapping found for instance" +
                                    " [" + instanceBean.getName() + "] and service [" + serviceData.getName() + "]");
                        }

                        int oldClusterId = serviceCluster.getClusterId();

                        //Find remaining services mapped to that serviceCluster
                        List<UpdateInstancePojo.IdAction> remainingServList = viewClusterServicesBeans.stream()
                                .filter(c -> c.getClusterId() == oldClusterId)
                                .filter(c -> c.getServiceId() != serviceData.getId())
                                .map(c -> {
                                    UpdateInstancePojo.IdAction idAction = new UpdateInstancePojo.IdAction();
                                    idAction.setId(c.getServiceId());
                                    idAction.setIdentifier(c.getServiceIdentifier());
                                    return idAction;
                                }).collect(Collectors.toList());

                        //Get the mapped instances to the old cluster
                        List<CompClusterMappingBean> compClusterMappingBeanList =
                                ComponentDataService.getClusterMapping(oldClusterId, accId);

                        if (!remainingServList.isEmpty()) {
                            //Find hostId
                            int clusterHostId = 0;
                            if (!instance.isHostInstance()) {
                                try {
                                    List<Integer> hostIds = new BindInDataService()
                                            .getHostClusterId(remainingServList.stream().map(UpdateInstancePojo.IdAction::getId).collect(Collectors.toList()),
                                                    componentTypeBean.getId(), null);
                                    if (hostIds.size() != 1) {
                                        log.warn("Multiple host cluster Ids available for the provided service. Resetting hostId to 0 for add cluster due to this conflict.");
                                    } else {
                                        clusterHostId = hostIds.get(0);
                                    }
                                } catch (ControlCenterException e) {
                                    log.error("Unable to fetch host cluster ID");
                                    throw new DataProcessingException(e.getMessage());
                                }
                            }

                            //Add new Cluster
                            int newClusterId = addCluster(instanceBean, remainingServList, updateTime, clusterHostId, handle);


                            //Map all the instances to the new cluster
                            for (CompClusterMappingBean compClusterMappingBean : compClusterMappingBeanList) {
                                addComponentClusterMapping(compClusterMappingBean.getCompInstanceId(), updateTime, newClusterId, handle);
                            }

                            //Remove the remaining service mapping to the old cluster
                            for (UpdateInstancePojo.IdAction service : remainingServList) {
                                compInstanceDataService.deleteTagMappingWithClusterIdandService(oldClusterId,
                                        tagId, Constants.COMP_INSTANCE_TABLE, service.getId(), service.getIdentifier(), handle);
                            }
                        } else {

                            // Check if any other instances are part of the cluster before removing the tag mapping.
                            // If the mapping beans size is equal to 1, we will remove the tag mapping.
                            // We check for size 1 because there should be a cluster mapping for the instance
                            // that triggered the update instance API.

                            List<CompClusterMappingBean> clusterMappingBeans = compClusterMappingBeanList
                                    .parallelStream()
                                    .filter(c -> c.getClusterId() == oldClusterId)
                                    .collect(Collectors.toList());

                            if (clusterMappingBeans.size() == 1) {
                                compInstanceDataService.deleteTagMappingWithClusterIdandService(oldClusterId,
                                        tagId, Constants.COMP_INSTANCE_TABLE, serviceData.getId(), serviceData.getIdentifier(), handle);
                                log.info("Component-cluster is successfully unmapped for clusterId : {} and serviceId : {}.", oldClusterId, serviceData.getId());
                            }
                        }


                        //delete component cluster mapping
                        try {
                            deleteComponentClusterMapping(instanceBean, oldClusterId, handle);
                        } catch (Exception e) {
                            log.error("Exception while deleting component-cluster mapping. Reason:-", e);
                            throw new DataProcessingException("Exception while deleting component-cluster mapping for instance " +
                                    instance.getInstanceIdentifier() + "and cluster " + oldClusterId);
                        }

                        /*
                        delete agent-service data in tag mapping
                         */
                        if (agentId != 0) {
                            deleteAgentServiceTagMapping(tagId, agentId, String.valueOf(serviceData.getId()), serviceData.getIdentifier(), handle);
                        }
                    }
                }
            }
        }
        return agentId;
    }

    private void instanceDetailsUpdate(UpdateInstancePojo instance, String updateTime, Handle handle) throws DataProcessingException {
        try {
            int result = new CompInstanceDataService().updateInstanceName(instance.getInstanceId(), instance.getInstanceIdentifier(),
                    instance.getName(), updateTime, userId, accId, handle);
            if (result == 0) {
                log.warn("No rows updated for instance name update request for [{}].", instance.getInstanceIdentifier());
            } else if (result == -1) {
                log.error("Error while updating instance name in 'comp_instance' table for [{}].", instance.getInstanceIdentifier());
                throw new DataProcessingException("Error while updating instance name for " + instance.getInstanceIdentifier());
            } else {
                log.trace(result + " number of rows updated successfully for instance name update request for [{}].", instance.getInstanceIdentifier());
            }
        } catch (Exception e) {
            log.error("Exception while updating Instance name. Reason:-", e);
            throw new DataProcessingException("Exception while updating Instance name for " + instance.getInstanceIdentifier());
        }
    }

    private void instanceEnvironmentUpdate(UpdateInstancePojo instance, String updateTime, Handle handle) throws DataProcessingException {
        try {
            int environmentId;
            try {
                environmentId = new EnvironmentHelper().getEnvironmentId(instance.getEnvironment());
            } catch (Exception e) {
                log.error("Setting the value the environment id to NONE");
                environmentId = 383;
            }

            int result = new CompInstanceDataService().updateInstanceEnvDetails(instance.getInstanceId(), instance.getInstanceIdentifier(),
                    environmentId , updateTime, userId, accId, handle);

            if (result == 0) {
                log.warn("No rows updated for instance Environment update request for [{}]", instance.getInstanceIdentifier());
            } else if (result == -1) {
                log.error("Error while updating instance Environment in 'comp_instance' table for [{}]", instance.getInstanceIdentifier());
                throw new DataProcessingException("Error while updating instance Environment for " + instance.getInstanceIdentifier());
            } else {
                log.trace(result + " number of rows updated successfully for instance Environment update request for [{}]",
                        instance.getInstanceIdentifier());
            }
        } catch (Exception e) {
            log.error("Exception while updating Instance Environment. Reason:-", e);
            throw new DataProcessingException("Exception while updating Instance Environment for " + instance.getInstanceIdentifier());
        }
    }

    private int addCluster(ComponentInstanceBean instanceBean, List<UpdateInstancePojo.IdAction> services, String updateTime, int hostId, Handle handle) throws DataProcessingException {
        ComponentInstanceBean clusterBean = ComponentInstanceBean.builder()
                .name(instanceBean.getName() + "_Cluster")
                .status(1)
                .hostId(hostId)
                .isDR(0)
                .isCluster(1)
                .mstComponentVersionId(instanceBean.getMstComponentVersionId())
                .createdTime(updateTime)
                .updatedTime(updateTime)
                .userDetailsId(userId)
                .accountId(accId)
                .mstComponentId(instanceBean.getMstComponentId())
                .mstComponentTypeId(instanceBean.getMstComponentTypeId())
                .discovery(0)
                .hostAddress(null)
                .identifier(UUID.randomUUID().toString())
                .mstCommonVersionId(instanceBean.getMstCommonVersionId())
                .parentId(0)
                .build();

        int clusterId = new CompInstanceDataService().addComponentInstance(clusterBean, handle);

        int tagId = Objects.requireNonNull(MasterCache.getTagDetails(Constants.CONTROLLER_TAG)).getId();

        for (UpdateInstancePojo.IdAction service : services) {
            int serviceId = service.getId();
            String serviceIdentifier = service.getIdentifier();

            int serviceAddSuccess = TagMappingBL.addTagMapping(tagId, clusterId, Constants.COMP_INSTANCE_TABLE,
                    String.valueOf(serviceId), serviceIdentifier, userId, accId, handle);
            if (serviceAddSuccess == -1) {
                log.error("Exception while adding tag mapping details between cluster id [{}] and service id [{}].",
                        clusterId, serviceId);
                throw new DataProcessingException(String.format("Exception while adding tag mapping details between cluster" +
                        " id [%s] and service id [%s].", clusterId, serviceId));
            } else {
                log.trace("Cluster id [{}] and Service id [{}] is mapped in tag mapping successfully.",
                        clusterId, serviceId);
            }
        }


        try {
            addKPIs(instanceBean, clusterId, updateTime, handle);
            addGroupKPIs(instanceBean, clusterId, updateTime, handle);
        } catch (Exception e) {
            throw new DataProcessingException("Exception while adding KPI/Group KPI details for instance id "
                    + instanceBean.getId() + " and cluster id " + clusterId);
        }

        return clusterId;
    }

    private void addKPIs(ComponentInstanceBean bean, int clusterId, String updateTime, Handle handle) throws ControlCenterException {
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
        List<CompInstanceKpiDetailsBean> kpiList = compInstanceDataService.getDefaultCompInstanceKPIsData(bean.getMstComponentId(),
                bean.getMstCommonVersionId(), bean.getMstComponentVersionId(), bean.getMstComponentTypeId(), handle);
        if (kpiList != null && !kpiList.isEmpty()) {
            for (CompInstanceKpiDetailsBean kpiBean : kpiList) {
                kpiBean.setCompInstanceId(clusterId);
                kpiBean.setCreatedTime(updateTime);
                kpiBean.setUpdatedTime(updateTime);
                kpiBean.setUserDetailsId(userId);
                int id = compInstanceDataService.addNonGroupComponentInstanceKPI(kpiBean, handle);
                if (id == -1) {
                    log.error("Unable to add KPI [{}] for component instance id [{}]", kpiBean.getMstKpiDetailsId(), clusterId);
                    throw new ControlCenterException(String.format("Unable to add KPI [%s] for component instance id [%s]",
                            kpiBean.getMstKpiDetailsId(), clusterId));
                }
                log.info("Added KPI [{}] for component instance id [{}]", kpiBean.getMstKpiDetailsId(), clusterId);
            }
        } else {
            log.info("No KPIs found for component instance id [{}]", clusterId);
        }

    }

    private void addGroupKPIs(ComponentInstanceBean bean, int clusterId, String updateTime, Handle handle) throws ControlCenterException {

        CommVersionKPIs commVersionKPIs = new CommVersionKPIs();
        commVersionKPIs.setAccountId(accId);
        commVersionKPIs.setMstCommonVersionId(bean.getMstCommonVersionId());
        List<ViewCommonVersionKPIsBean> viewCommonVersionKPIsBeansGroup = MasterCache.getGroupKPIUsingCommonVersionId(commVersionKPIs);
        if (viewCommonVersionKPIsBeansGroup == null) {
            String message = "No group kpi is available for given master component version -" + bean.getMstCommonVersionId() +
                    ", mstComponentType-" + bean.getMstComponentTypeId();
            log.warn(message);
            return;
        }

        final Set<Integer> clusterKpiIds = MasterDataService.getAllKpisList().stream().filter(
                        kpi -> !kpi.getClusterOperation().equalsIgnoreCase(Constants.NONE))
                .map(AllKpiList::getKpiId).collect(Collectors.toSet());

        viewCommonVersionKPIsBeansGroup = viewCommonVersionKPIsBeansGroup.stream().filter(
                group -> clusterKpiIds.contains(group.getKpiId())).collect(Collectors.toList());

        for (ViewCommonVersionKPIsBean viewCommonVersionKPIsBean : viewCommonVersionKPIsBeansGroup) {
            if (viewCommonVersionKPIsBean.getStatus() == 0) {
                continue;
            }

            addEachGroupKPI(viewCommonVersionKPIsBean, bean, clusterId, updateTime, handle);

        }
    }

    private void addEachGroupKPI(ViewCommonVersionKPIsBean viewCommonVersionKPIsBean, ComponentInstanceBean bean,
                                 int instanceId, String updateTime, Handle handle) throws ControlCenterException {

        int collectionInterval = viewCommonVersionKPIsBean.getDefaultCollectionInterval();
        int groupId = viewCommonVersionKPIsBean.getKpiGroupId();
        MasterKpiGroupBean groupBean = MasterCache.getGroupKpiDetailList(accId, groupId);
        if (groupBean == null) {
            String message = "No group kpis found in master kpi group details table for group id-" + groupId;
            log.warn(message);
            throw new ControlCenterException(message);
        }

        //only for discovered Group KPIs, add instance
        //Non-discovered ones, user has to manually add attributes entries in CC UI
        if (groupBean.getDiscovery() == 0) {
            return;
        }

        ProducerKpis producerKpis = new ProducerKpis();
        producerKpis.setMstKpiDetailsId(viewCommonVersionKPIsBean.getKpiId());
        producerKpis.setMstCompVersionId(bean.getMstComponentVersionId());
        producerKpis.setMstCompId(bean.getMstComponentId());
        producerKpis.setMstCompTypeId(bean.getMstComponentTypeId());
        producerKpis.setAccountId(accId);
        ViewProducerKPIsBean viewProducerKPIsBean = MasterCache.getViewProducerKPIsGroup(producerKpis);
        if (viewProducerKPIsBean == null) {
            String message = "No producers found for group kpi - " + viewCommonVersionKPIsBean.getKpiName();
            log.warn(message);
            return;
        }

        MstKpi mstKpi = new MstKpi();
        mstKpi.setAccountId(accId);
        mstKpi.setKpiId(viewProducerKPIsBean.getMstKpiDetailsId());
        MasterKPIDetailsBean kpiBean = MasterCache.getMasterKPIDetailsBean(mstKpi);
        if (kpiBean == null) {
            String message = "No group kpis found in master kpi details table for producer -" +
                    viewProducerKPIsBean.getProducerName();
            log.warn(message);
            throw new ControlCenterException(message);
        }

        CompInstanceKpiGroupDetailsBean compInstanceKpiGroupDetailsBean = new CompInstanceKpiGroupDetailsBean();
        compInstanceKpiGroupDetailsBean.setStatus(groupBean.getStatus());
        compInstanceKpiGroupDetailsBean.setCreatedTime(updateTime);
        compInstanceKpiGroupDetailsBean.setUpdatedTime(updateTime);
        compInstanceKpiGroupDetailsBean.setUserDetailsId(bean.getUserDetailsId());
        compInstanceKpiGroupDetailsBean.setCompInstanceId(instanceId);
        compInstanceKpiGroupDetailsBean.setMstProducerKpiMappingId(viewProducerKPIsBean.getMstProducerKpiMappingId());
        compInstanceKpiGroupDetailsBean.setCollectionInterval(collectionInterval);
        compInstanceKpiGroupDetailsBean.setMstKpiDetailsId(viewCommonVersionKPIsBean.getKpiId());
        compInstanceKpiGroupDetailsBean.setIsDiscovery(groupBean.getDiscovery());
        compInstanceKpiGroupDetailsBean.setKpiGroupName(groupBean.getIdentifier());
        compInstanceKpiGroupDetailsBean.setMstKpiGroupId(groupId);
        compInstanceKpiGroupDetailsBean.setMstProducerId(viewProducerKPIsBean.getProducerId());
        compInstanceKpiGroupDetailsBean.setAttributeValue(Constants.ALL);
        compInstanceKpiGroupDetailsBean.setAliasName(Constants.ALL);
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
        int id = compInstanceDataService.addGroupComponentInstanceKPI(
                compInstanceKpiGroupDetailsBean, handle);
        if (id == -1) {
            String message = "Unable to add group kpi for component instance id-" +
                    compInstanceKpiGroupDetailsBean.getCompInstanceId() + ", group kpi name-" +
                    kpiBean.getName();
            log.warn(message);
            throw new ControlCenterException(message);
        }
        log.info("Added group kpi for component instance id-" +
                compInstanceKpiGroupDetailsBean.getCompInstanceId() + ", group kpi name-" +
                kpiBean.getName());

    }

    private void addComponentClusterMapping(int instanceId, String updateTime, int clusterId, Handle handle) throws DataProcessingException {

        try {
            int result = addCompClusterMapping(updateTime, userId, accId, instanceId,
                    clusterId, handle);
            if (result == 0) {
                log.warn("No rows added for component-cluster mapping add request for instance id [{}] and cluster id [{}]",
                        instanceId, clusterId);
            } else if (result == -1) {
                log.error("Error while adding component-cluster mapping in 'component_cluster_mapping' table for instance id [{}] and cluster " +
                        " id [{}]", instanceId, clusterId);
                throw new DataProcessingException("Error while adding component-cluster mapping for instance id " + instanceId
                        + "and cluster id " + clusterId);
            } else {
                log.trace("Row with id [{}] added successfully for component-cluster mapping add request for instance id [{}] " +
                        "and cluster id [{}]", result, instanceId, clusterId);
            }
        } catch (Exception e) {
            throw new DataProcessingException("Exception while adding component-cluster mapping for instance id " + instanceId
                    + "and cluster id " + clusterId);
        }

    }

    private int addCompClusterMapping(String time, String userId, int accountId, int instanceId, int clusterId, Handle handle) throws ControlCenterException {

        try {
            CompClusterMappingBean compClusterMappingBean = new CompClusterMappingBean();
            compClusterMappingBean.setCreatedTime(time);
            compClusterMappingBean.setUpdatedTime(time);
            compClusterMappingBean.setUserDetailsId(userId);
            compClusterMappingBean.setAccountId(accountId);
            compClusterMappingBean.setCompInstanceId(instanceId);
            compClusterMappingBean.setClusterId(clusterId);
            return new CompInstanceDataService().addCompClusterMapping(compClusterMappingBean, handle);
        } catch (Exception e) {
            log.error("Exception while adding component-cluster mapping. Reason:-", e);
            throw new ControlCenterException(e.getMessage());
        }
    }

    private void deleteComponentClusterMapping(ComponentInstanceBean instanceBean, int clusterId, Handle handle) throws DataProcessingException {

        int result = new CompInstanceDataService().deleteComponentClusterMapping(instanceBean.getId(), clusterId, accId, handle);
        if (result == 0) {
            log.warn("No rows deleted for component-cluster mapping delete request for instance id [{}] and cluster id [{}]",
                    instanceBean.getId(), clusterId);
        } else if (result == -1) {
            log.error("Error while deleting component-cluster mapping in 'component_cluster_mapping' table for instance id [{}] and cluster " +
                    "id [{}]", instanceBean.getId(), clusterId);
            throw new DataProcessingException("Error while deleting component-cluster mapping for instance id " + instanceBean.getId()
                    + "and cluster id " + clusterId);
        } else {
            log.trace("[{}] number of Row(s) deleted successfully for component-cluster mapping delete request for instance id [{}] " +
                    "and cluster id [{}]", result, instanceBean.getId(), clusterId);
        }

    }

    private void addAgentServiceTagMapping(int tagId, int agentId, String serviceId, String serviceIdentifier, Handle handle) {
        TagMappingBL.addTagMapping(tagId, agentId, Constants.AGENT_TABLE, serviceId,
                serviceIdentifier, userId, accId, handle);
    }

    private void deleteAgentServiceTagMapping(int tagId, int agentId, String serviceId, String serviceIdentifier, Handle handle) throws DataProcessingException {
        int result = TagsDataService.deleteAgentServiceTagMapping(tagId, agentId, Constants.AGENT_TABLE, serviceId, serviceIdentifier, accId, handle);
        if (result == -1) {
            log.error("Exception while deleting agent-service mapping in tag-mapping.");
            throw new DataProcessingException("Exception while deleting agent-service mapping in tag-mapping.");
        } else if (result == 0) {
            log.warn("No rows deleted while deleting agent-service mapping in tag-mapping.");
        } else {
            log.trace("{} number of rows deleted successfully while deleting agent-service mapping in tag-mapping.", result);
        }
    }

}
