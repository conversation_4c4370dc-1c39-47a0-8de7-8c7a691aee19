package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.beans.MaintenanceScheduledBean;
import com.appnomic.appsone.common.beans.MaintenanceWindowBean;
import com.appnomic.appsone.common.beans.RecurringDetailsBean;
import com.appnomic.appsone.common.exception.AppsOneException;
import com.appnomic.appsone.common.util.MaintenanceUtils;
import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.MaintenanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.MaintenanceDetails;
import com.appnomic.appsone.controlcenter.dao.opensearch.MaintenanceWindowServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.ComptInstancePojo;
import com.appnomic.appsone.controlcenter.pojo.MaintenanceWindowPojo;
import com.appnomic.appsone.controlcenter.pojo.RecurringBean;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class UpdateMaintenanceWindowBL implements BusinessLogic<MaintenanceWindowPojo, MaintenanceWindowList, String> {

    private static final MaintenanceDataService MAINTENANCE_DATA_SERVICE = new MaintenanceDataService();

    @Override
    public UtilityBean<MaintenanceWindowPojo> clientValidation(RequestObject requestObject) throws ClientException {
        String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        String accountIdentifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        String serviceIdStr = requestObject.getParams().get(Constants.SERVICE_ID);
        String maintenanceIdStr = requestObject.getParams().get(Constants.MAINTENANCE_ID);

        if (StringUtils.isEmpty(authToken)) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty.");
            throw new ClientException("Invalid authorization token");
        }

        if (StringUtils.isEmpty(accountIdentifier)) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty.");
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        if (StringUtils.isEmpty(serviceIdStr)) {
            log.error("Invalid service ID. Reason: It is either NULL or empty.");
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        }

        int serviceId;
        try {
            serviceId = Integer.parseInt(serviceIdStr);
        } catch (Exception e) {
            throw new ClientException(MessageFormat.format(UIMessages.INVALID_VALUE, Constants.SERVICE_IDENTIFIER, serviceIdStr));
        }

        if (StringUtils.isEmpty(maintenanceIdStr)) {
            throw new ClientException(UIMessages.MAINTENANCE_ID_EMPTY_ERROR);
        }

        int maintenanceId;
        try {
            maintenanceId = Integer.parseInt(maintenanceIdStr);
        } catch (Exception e) {
            throw new ClientException(MessageFormat.format(UIMessages.INVALID_VALUE, Constants.MAINTENANCE_ID, maintenanceIdStr));
        }

        if (StringUtils.isEmpty(requestObject.getBody())) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();
        MaintenanceWindowPojo maintenanceWindowPojo;

        try {
            maintenanceWindowPojo = objectMapper.readValue(requestObject.getBody(), MaintenanceWindowPojo.class);
        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        maintenanceWindowPojo.setServiceId(serviceId);
        maintenanceWindowPojo.setId(maintenanceId);
        maintenanceWindowPojo.setUpdate(true);

        if (Constants.MST_SUB_TYPE_RECURRING.equalsIgnoreCase(maintenanceWindowPojo.getMaintenanceType())) {
            maintenanceWindowPojo.validateRecurring();
        } else if (Constants.MST_SUB_TYPE_SCHEDULED.equalsIgnoreCase(maintenanceWindowPojo.getMaintenanceType())) {
            maintenanceWindowPojo.validate();
        } else {
            maintenanceWindowPojo.validateAdhoc();
        }

        if (!maintenanceWindowPojo.getError().isEmpty()) {
            throw new ClientException(maintenanceWindowPojo.getError().toString());
        }

        return UtilityBean.<MaintenanceWindowPojo>builder()
                .accountIdentifier(accountIdentifier)
                .authToken(authToken)
                .pojoObject(maintenanceWindowPojo)
                .build();
    }

    @Override
    public MaintenanceWindowList serverValidation(UtilityBean<MaintenanceWindowPojo> utilityBean) throws ServerException {
        String authToken = utilityBean.getAuthToken();

        UserAccountBean userAccBean;
        try {
            userAccBean = ValidationUtils.commonServerValidations(authToken, utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            log.error(e.getMessage());
            throw new ServerException(e.getMessage());
        }

        String userId = userAccBean.getUserId();
        int accountId = userAccBean.getAccount().getId();
        MaintenanceWindowPojo maintenanceWindowPojo = utilityBean.getPojoObject();
        int serviceId = maintenanceWindowPojo.getServiceId();

        Timestamp date;
        try {
            date = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
        } catch (ParseException e) {
            log.error("Error while constructing date for current timestamp. Details: ", e);
            throw new ServerException("Error while constructing date for current timestamp");
        }

        if (maintenanceWindowPojo.getEndTime().before(date)) {
            log.error("Invalid endTime of maintenance window [{}]. Reason: EndTime [{}] is in the past.",
                    maintenanceWindowPojo.getId(), maintenanceWindowPojo.getEndTime());
            throw new ServerException(String.format("Invalid endTime of maintenance window [%d]. Reason: EndTime [%s] is in the past",
                    maintenanceWindowPojo.getId(), maintenanceWindowPojo.getEndTime()));
        }

        ControllerBean controller = new ControllerDataService().getControllerById(serviceId, accountId, null);
        if (controller == null) {
            log.error("Service with id [{}] is unavailable for account id [{}]", maintenanceWindowPojo.getServiceId(), accountId);
            throw new ServerException(String.format("Service with id [%d] is unavailable for account id [%d]", maintenanceWindowPojo.getServiceId(), accountId));
        }

        MaintenanceDetails maintenanceDetail = MAINTENANCE_DATA_SERVICE.getMaintenanceWindowDetails(maintenanceWindowPojo.getId());
        if (maintenanceDetail == null) {
            log.error("Maintenance window with id [{}] unavailable for service [{}]", maintenanceWindowPojo.getId(), serviceId);
            throw new ServerException(String.format("Maintenance window with id [%d] unavailable for service [%d]", maintenanceWindowPojo.getId(), serviceId));
        }

        ViewTypes maintenanceType = MasterCache.getMstSubTypeForSubTypeId(maintenanceDetail.getTypeId());

        MaintenanceWindowList maintenanceWindowList = new MaintenanceWindowList();
        List<MaintenanceDetails> maintenanceDetailsToBeModified = new ArrayList<>();

        if (Constants.MST_SUB_TYPE_SCHEDULED.equalsIgnoreCase(maintenanceWindowPojo.getMaintenanceType())) {
            List<MaintenanceDetails> existingMaintenanceWindowsForService = MAINTENANCE_DATA_SERVICE.getMaintenanceDetailsByServiceId(serviceId, accountId);
            if (existingMaintenanceWindowsForService.isEmpty()) {
                log.error("No maintenance windows available for service with id [{}]", serviceId);
                throw new ServerException(String.format("No maintenance windows available for service with id [%d]", serviceId));
            }

            MaintenanceWindowBean maintenanceWindowBean;
            try {
                maintenanceWindowBean = MaintenanceWindowUtility.getMaintenanceConflictByServiceId(maintenanceWindowPojo.getId(), serviceId,
                        maintenanceWindowPojo.getStartTime(), maintenanceWindowPojo.getEndTime());
            } catch (AppsOneException e) {
                log.error("Error while checking maintenance window conflicts for service ID [{}]", serviceId);
                throw new ServerException(String.format("Error while checking maintenance window conflicts for service ID [%d]", serviceId));
            }

            if (maintenanceWindowBean != null) {
                String error = "Maintenance window already exists from " + new Date(maintenanceWindowBean.getStartTime()) + " to "
                        + new Date(maintenanceWindowBean.getEndTime());
                throw new ServerException(error);
            }

            maintenanceDetailsToBeModified.add(MaintenanceDetails.builder()
                    .id(maintenanceWindowPojo.getId())
                    .accountId(accountId)
                    .serviceId(serviceId)
                    .accountIdentifier(userAccBean.getAccount().getIdentifier())
                    .maintenanceType(maintenanceType.getSubTypeName())
                    .serviceIdentifier(controller.getIdentifier())
                    .userDetails(userId)
                    .status(true)
                    .existingStartTime(maintenanceDetail.getStartTime())
                    .existingEndTime(maintenanceDetail.getEndTime())
                    .startTime(maintenanceWindowPojo.getStartTime())
                    .endTime(maintenanceWindowPojo.getEndTime())
                    .updatedTime(date)
                    .build());
        } else if (Constants.MST_SUB_TYPE_RECURRING.equalsIgnoreCase(maintenanceWindowPojo.getMaintenanceType())) {
            List<MaintenanceDetails> existingMaintenanceWindowsForService = MAINTENANCE_DATA_SERVICE.getMaintenanceDetailsByServiceId(serviceId, accountId);
            if (existingMaintenanceWindowsForService.isEmpty()) {
                log.error("No maintenance windows available for service with id [{}]", serviceId);
                throw new ServerException(String.format("No maintenance windows available for service with id [%d]", serviceId));
            }

            ViewTypes recurringType = MasterCache.getMstTypeForSubTypeName(Constants.MST_TYPE_RECURRING, maintenanceWindowPojo.getRecurring().getRecurringType());

            RecurringBean existingRecurringBean = MAINTENANCE_DATA_SERVICE.getRecurringDetails(maintenanceDetail.getId());

            if (existingRecurringBean == null) {
                log.error("There are no recurring maintenance windows available for maintenance Id [{}]", maintenanceDetail.getId());
                throw new ServerException(String.format("There are no recurring maintenance windows available for maintenance Id [%d]", maintenanceDetail.getId()));
            }

            ViewTypes existingRecurringType = MasterCache.getMstSubTypeForSubTypeId(existingRecurringBean.getRecurringTypeId());
            existingRecurringBean.setRecurringType(existingRecurringType.getSubTypeName());

            long duration;
            try {
                DateFormat formatter = new SimpleDateFormat("HH:mm");
                Date startHr = formatter.parse(maintenanceWindowPojo.getRecurring().getStartHour());
                Date endHr = formatter.parse(maintenanceWindowPojo.getRecurring().getEndHour());
                duration = endHr.getTime() - startHr.getTime();
            } catch (ParseException e) {
                log.error("Error while parsing start hour and end hour. Details: ", e);
                throw new ServerException("Error while parsing start hour and end hour.");
            }

            RecurringBean recurringDetailsBean = maintenanceWindowPojo.getRecurring();
            recurringDetailsBean.setUpdatedTime(date);
            recurringDetailsBean.setUserDetails(userId);
            recurringDetailsBean.setDuration(duration);
            recurringDetailsBean.setRecurringType(recurringType.getSubTypeName());
            recurringDetailsBean.setRecurringTypeId(recurringType.getSubTypeId());

            MaintenanceWindowUtility.verifyAndBuildRecurringMaintenanceWindow(maintenanceWindowPojo, serviceId, recurringDetailsBean, true);

            maintenanceDetailsToBeModified.add(MaintenanceDetails.builder()
                    .name(maintenanceWindowPojo.getName())
                    .id(maintenanceWindowPojo.getId())
                    .accountId(accountId)
                    .serviceId(serviceId)
                    .accountIdentifier(userAccBean.getAccount().getIdentifier())
                    .maintenanceType(maintenanceType.getSubTypeName())
                    .serviceIdentifier(controller.getIdentifier())
                    .userDetails(userId)
                    .recurring(recurringDetailsBean)
                    .existingRecurringDetails(existingRecurringBean)
                    .status(true)
                    .existingStartTime(maintenanceDetail.getStartTime())
                    .existingEndTime(maintenanceDetail.getEndTime())
                    .startTime(maintenanceWindowPojo.getStartTime())
                    .endTime(maintenanceWindowPojo.getEndTime())
                    .updatedTime(date)
                    .build());

        } else if (!Constants.MST_SUB_TYPE_RECURRING.equalsIgnoreCase(maintenanceWindowPojo.getMaintenanceType())
                && !Constants.MST_SUB_TYPE_SCHEDULED.equalsIgnoreCase(maintenanceWindowPojo.getMaintenanceType())) {
            maintenanceWindowList = fetchInstanceWiseMaintenanceList(userAccBean, maintenanceWindowPojo, maintenanceDetail, date, controller);
        }

        if (maintenanceWindowList.getMaintenanceDetailsToBeModified() == null) {
            maintenanceWindowList.setMaintenanceDetailsToBeModified(maintenanceDetailsToBeModified);
        } else {
            maintenanceWindowList.getMaintenanceDetailsToBeModified().addAll(maintenanceDetailsToBeModified);
        }

        return maintenanceWindowList;
    }

    private MaintenanceWindowList fetchInstanceWiseMaintenanceList(UserAccountBean userAccBean, MaintenanceWindowPojo maintenanceWindowPojo, MaintenanceDetails maintenanceDetail,
                                                                   Timestamp date, ControllerBean controller) throws ServerException {

        List<MaintenanceDetails> maintenanceDetailsToBeAdded = new ArrayList<>();
        List<MaintenanceDetails> maintenanceDetailsToBeDeleted = new ArrayList<>();
        List<MaintenanceDetails> maintenanceDetailsToBeModified = new ArrayList<>();

        int accountId = userAccBean.getAccount().getId();
        int serviceId = controller.getId();

        List<Integer> instancesAlreadyMarkedForMaintenance = MAINTENANCE_DATA_SERVICE.getInstancesByMaintenanceId(maintenanceWindowPojo.getId())
                .parallelStream().map(MaintenanceDetails::getInstanceId).collect(Collectors.toList());

        if (instancesAlreadyMarkedForMaintenance.isEmpty()) {
            log.error("Unable to retrieve the instances for maintenanceId [{}] ", maintenanceWindowPojo.getId());
            throw new ServerException(String.format("Unable to retrieve the instances for maintenanceId [%d]", maintenanceWindowPojo.getId()));
        }

        maintenanceWindowPojo.setInstancesToBeDeleted(instancesAlreadyMarkedForMaintenance.parallelStream()
                .filter(markedInst -> maintenanceWindowPojo.getInstances()
                        .parallelStream().noneMatch(markedInst::equals))
                .collect(Collectors.toList()));

        maintenanceWindowPojo.setInstancesToBeAdded(maintenanceWindowPojo.getInstances().parallelStream()
                .filter(addedInst -> instancesAlreadyMarkedForMaintenance
                        .parallelStream().noneMatch(addedInst::equals)).collect(Collectors.toList()));

        List<Integer> modifiedInstanceIds = maintenanceWindowPojo.getInstances().parallelStream()
                .filter(modifiedInst -> instancesAlreadyMarkedForMaintenance.parallelStream().anyMatch(modifiedInst::equals)).collect(Collectors.toList());

        CompInstanceBL compInstanceBL = new CompInstanceBL();
        List<ComptInstancePojo> allCompInstanceIds;
        try {
            allCompInstanceIds = compInstanceBL.getAllInstances(accountId, serviceId);
        } catch (ControlCenterException e) {
            log.error("Error while fetching instances for serviceId [{}] mapped to accountId [{}]", serviceId, accountId);
            throw new ServerException(String.format("Error while fetching instances for serviceId [%d] mapped to accountId [%d]", serviceId, accountId));
        }

        Map<Integer, ComptInstancePojo> compInstances = allCompInstanceIds.stream().collect(HashMap::new, (m,v) -> {
                    if(v.getInstanceName() != null){
                        m.put(v.getInstanceId(), v);
                    }else {
                        m.put(v.getHostId(), v);
                    }
                },
                HashMap::putAll);

        if (maintenanceWindowPojo.getInstancesToBeDeleted() != null && !maintenanceWindowPojo.getInstancesToBeDeleted().isEmpty()) {

            for (int id : maintenanceWindowPojo.getInstancesToBeDeleted()) {
                if (!compInstances.containsKey(id)) {
                    log.warn("Skipping this instance Id : [{}]. Reason: Instance not found in redis", id);
                    continue;
                }
                maintenanceDetailsToBeDeleted.add(MaintenanceDetails.builder()
                        .name(maintenanceDetail.getName())
                        .id(maintenanceWindowPojo.getId())
                        .instanceId(id)
                        .instanceName(compInstances.get(id).getInstanceName())
                        .instanceIdentifier(compInstances.get(id).getInstanceIdentifier())
                        .accountId(accountId)
                        .serviceId(serviceId)
                        .accountIdentifier(userAccBean.getAccount().getIdentifier())
                        .serviceIdentifier(controller.getIdentifier())
                        .userDetails(userAccBean.getUserId())
                        .status(false)
                        .startTime(maintenanceWindowPojo.getStartTime())
                        .endTime(maintenanceWindowPojo.getEndTime())
                        .updatedTime(date)
                        .build());
            }
        }

        if (maintenanceWindowPojo.getInstancesToBeAdded() != null && !maintenanceWindowPojo.getInstancesToBeAdded().isEmpty()) {
            ViewTypes maintenanceType = MasterCache.getMstTypeForSubTypeName(Constants.MST_TYPE_MAINTENANCE, Constants.MST_SUB_TYPE_SCHEDULED);

            for (int id : maintenanceWindowPojo.getInstancesToBeAdded()) {
                if (!compInstances.containsKey(id)) {
                    log.warn("Skipping this instance Id : [{}]. Reason: Instance not found in redis", id);
                    continue;
                }

                try {
                    MaintenanceWindowBean instanceMaintenanceWindowBean = MaintenanceWindowUtility.getMaintenanceConflictByCompInstanceId(id,
                            maintenanceWindowPojo.getId(), maintenanceWindowPojo.getStartTime(), maintenanceWindowPojo.getEndTime());

                    if (instanceMaintenanceWindowBean != null) {
                        log.error("Conflicting maintenance window available for instance ID [{}]", id);
                        throw new ServerException(String.format("Conflicting maintenance window available for instance ID [%d]", id));
                    }
                } catch (AppsOneException e) {
                    log.error("Error while checking for adhoc maintenance window conflicts for instance [{}] mapped to service [{}]", id, serviceId);
                    throw new ServerException(String.format("Error while checking for maintenance window conflicts for service [%d]", serviceId));
                }

                maintenanceDetailsToBeAdded.add(MaintenanceDetails.builder()
                        .name(maintenanceDetail.getName())
                        .id(maintenanceWindowPojo.getId())
                        .maintenanceType(maintenanceType.getSubTypeName())
                        .typeId(maintenanceType.getSubTypeId())
                        .instanceId(id)
                        .instanceName(compInstances.get(id).getInstanceName())
                        .instanceIdentifier(compInstances.get(id).getInstanceIdentifier())
                        .accountId(accountId)
                        .serviceId(serviceId)
                        .accountIdentifier(userAccBean.getAccount().getIdentifier())
                        .serviceIdentifier(controller.getIdentifier())
                        .userDetails(userAccBean.getUserId())
                        .status(true)
                        .startTime(maintenanceWindowPojo.getStartTime())
                        .endTime(maintenanceWindowPojo.getEndTime())
                        .createdTime(date)
                        .updatedTime(date)
                        .build());
            }
        }

        if (!modifiedInstanceIds.isEmpty()) {
            for (int id : modifiedInstanceIds) {
                if (!compInstances.containsKey(id)) {
                    log.warn("Skipping this instance Id : [{}]. Reason: Instance not found in redis", id);
                    continue;
                }

                try {
                    MaintenanceWindowBean instanceMaintenanceWindowBean = MaintenanceWindowUtility.getMaintenanceConflictByCompInstanceId(id,
                            maintenanceWindowPojo.getId(), maintenanceWindowPojo.getStartTime(), maintenanceWindowPojo.getEndTime());

                    if (instanceMaintenanceWindowBean != null) {
                        log.error("Conflicting maintenance window available for instance ID [{}]", id);
                        throw new ServerException(String.format("Conflicting maintenance window available for instance ID [%d]", id));
                    }
                } catch (AppsOneException e) {
                    log.error("Error while checking for adhoc maintenance window conflicts for instance [{}] mapped to service [{}]", id, serviceId);
                    throw new ServerException(String.format("Error while checking for maintenance window conflicts for service [%d]", serviceId));
                }

                maintenanceDetailsToBeModified.add(MaintenanceDetails.builder()
                        .name(maintenanceDetail.getName())
                        .id(maintenanceWindowPojo.getId())
                        .instanceId(id)
                        .instanceName(compInstances.get(id).getInstanceName())
                        .instanceIdentifier(compInstances.get(id).getInstanceIdentifier())
                        .accountId(accountId)
                        .serviceId(serviceId)
                        .accountIdentifier(userAccBean.getAccount().getIdentifier())
                        .serviceIdentifier(controller.getIdentifier())
                        .userDetails(userAccBean.getUserId())
                        .status(true)
                        .existingStartTime(maintenanceDetail.getStartTime())
                        .existingEndTime(maintenanceDetail.getEndTime())
                        .startTime(maintenanceWindowPojo.getStartTime())
                        .endTime(maintenanceWindowPojo.getEndTime())
                        .updatedTime(date)
                        .build());
            }
        }

        return MaintenanceWindowList.builder()
                .maintenanceDetailsToBeAdded(maintenanceDetailsToBeAdded)
                .maintenanceDetailsToBeDeleted(maintenanceDetailsToBeDeleted)
                .maintenanceDetailsToBeModified(maintenanceDetailsToBeModified)
                .build();
    }

    @Override
    public String process(MaintenanceWindowList bean) throws DataProcessingException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            return dbi.inTransaction((conn, status) -> {
                if (bean.getMaintenanceDetailsToBeDeleted() != null && !bean.getMaintenanceDetailsToBeDeleted().isEmpty()) {
                    for (MaintenanceDetails maintenanceDet : bean.getMaintenanceDetailsToBeDeleted()) {
                        MAINTENANCE_DATA_SERVICE.deleteCompInstanceMaintenanceMapping(maintenanceDet, conn);
                        MaintenanceWindowServiceRepo.updateInstanceOngoingMaintenanceOS(maintenanceDet.getAccountIdentifier(),
                                maintenanceDet.getInstanceIdentifier(), maintenanceDet.getStartTime(), maintenanceDet.getEndTime());
                    }
                }
                if (bean.getMaintenanceDetailsToBeAdded() != null && !bean.getMaintenanceDetailsToBeAdded().isEmpty()) {
                    MaintenanceWindowUtility.addScheduledMaintenanceWindowDetails(bean.getMaintenanceDetailsToBeAdded(), conn);
                }

                if (bean.getMaintenanceDetailsToBeModified() != null && !bean.getMaintenanceDetailsToBeModified().isEmpty()) {
                    for (MaintenanceDetails maintenanceDet : bean.getMaintenanceDetailsToBeModified()) {
                        MaintenanceWindowBean windowBean = new MaintenanceWindowBean();
                        windowBean.setId(maintenanceDet.getId());
                        windowBean.setName(maintenanceDet.getName());
                        windowBean.setType(maintenanceDet.getMaintenanceType());
                        windowBean.setStartTime(maintenanceDet.getExistingStartTime().getTime());
                        windowBean.setEndTime(maintenanceDet.getExistingEndTime().getTime());

                        List<MaintenanceWindowBean> maintenanceDetails = Collections.singletonList(windowBean);
                        Map<Integer, RecurringDetailsBean> recurringDetails = new HashMap<>();
                        RecurringBean recurringBean = maintenanceDet.getRecurring();
                        RecurringBean existingRecurringBean = maintenanceDet.getExistingRecurringDetails();

                        if (recurringBean != null) {
                            RecurringDetailsBean recurringDetailsBean = new RecurringDetailsBean();
                            recurringDetailsBean.setMaintenanceId(maintenanceDet.getId());
                            recurringDetailsBean.setRecurringType(existingRecurringBean.getRecurringType());
                            recurringDetailsBean.setRecurringData(existingRecurringBean.getRecurringData());
                            recurringDetailsBean.setStartHrMin(existingRecurringBean.getStartHour());
                            recurringDetailsBean.setEndHrMin(existingRecurringBean.getEndHour());

                            recurringDetails.put(maintenanceDet.getId(), recurringDetailsBean);
                        }
                        try {
                            if (maintenanceDet.getInstanceName() != null) {
                                updateInstanceLevelMaintenanceWindow(maintenanceDet);
                            } else {
                                MaintenanceScheduledBean scheduledBean = MaintenanceUtils.getMaintenanceScheduled(maintenanceDetails, recurringDetails, maintenanceDet.getUpdatedTime(), maintenanceDet.getUpdatedTime());
                                MaintenanceDetailsBean ongoingBean = MaintenanceWindowUtility.getOngoingMaintenance(maintenanceDet.getAccountIdentifier(), maintenanceDet.getServiceIdentifier(), scheduledBean);
                                updateServiceLevelMaintenanceWindow(conn, maintenanceDet, recurringBean, existingRecurringBean, ongoingBean);
                            }
                        } catch (AppsOneException | ControlCenterException e) {
                            log.error("Error while fetching ongoing maintenance window details", e);
                            throw new ServerException("Error while fetching ongoing maintenance window details");
                        }

                        MAINTENANCE_DATA_SERVICE.updateMaintenanceWindowDetails(maintenanceDet, conn);
                    }
                }
                return "Maintenance window successfully updated";
            });
        } catch (Exception e) {
            log.error("Error while updating maintenance window. Details: ", e);
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }

    private void updateInstanceLevelMaintenanceWindow(MaintenanceDetails maintenanceDet) throws ControlCenterException {
        MaintenanceWindowServiceRepo.updateInstanceOngoingMaintenanceOS(maintenanceDet.getAccountIdentifier(), maintenanceDet.getInstanceIdentifier(),
                maintenanceDet.getExistingStartTime(), maintenanceDet.getStartTime());
        new MaintenanceWindowServiceRepo().insertInstanceMaintenanceOS(maintenanceDet.getAccountIdentifier(), maintenanceDet.getInstanceIdentifier(),
                maintenanceDet.getStartTime(), maintenanceDet.getEndTime());
    }

    private void updateServiceLevelMaintenanceWindow(Handle conn, MaintenanceDetails maintenanceDet, RecurringBean recurringBean, RecurringBean existingRecurringBean, MaintenanceDetailsBean existingOngoingBean) throws ControlCenterException {

        Timestamp newEndTime = maintenanceDet.getEndTime();
        Timestamp newStartTime = maintenanceDet.getStartTime();
        Timestamp existingStartTime = maintenanceDet.getExistingStartTime();
        Timestamp existingEndTime = maintenanceDet.getExistingEndTime();

        if (recurringBean != null) {
            Calendar newEndTimeCal = Calendar.getInstance(TimeZone.getTimeZone("Asia/Calcutta"));
            newEndTimeCal.setTimeInMillis(maintenanceDet.getStartTime().getTime());
            String[] newEndHrMin = recurringBean.getEndHour().split(":");
            newEndTimeCal.add(Calendar.HOUR_OF_DAY, Integer.parseInt(newEndHrMin[0]));
            newEndTimeCal.add(Calendar.MINUTE, Integer.parseInt(newEndHrMin[1]));
            newEndTime = new Timestamp(newEndTimeCal.getTime().getTime());
        }

        if (existingRecurringBean != null) {
            Calendar existingStartTimeCal = Calendar.getInstance(TimeZone.getTimeZone("Asia/Calcutta"));
            existingStartTimeCal.setTimeInMillis(maintenanceDet.getExistingStartTime().getTime());
            String[] existingStartHrMin = existingRecurringBean.getStartHour().split(":");
            existingStartTimeCal.add(Calendar.HOUR_OF_DAY, Integer.parseInt(existingStartHrMin[0]));
            existingStartTimeCal.add(Calendar.MINUTE, Integer.parseInt(existingStartHrMin[1]));
            existingStartTime = new Timestamp(existingStartTimeCal.getTime().getTime());

            Calendar existingEndTimeCal = Calendar.getInstance(TimeZone.getTimeZone("Asia/Calcutta"));
            existingEndTimeCal.setTimeInMillis(maintenanceDet.getExistingStartTime().getTime());
            String[] existingEndHrMin = existingRecurringBean.getEndHour().split(":");
            existingEndTimeCal.add(Calendar.HOUR_OF_DAY, Integer.parseInt(existingEndHrMin[0]));
            existingEndTimeCal.add(Calendar.MINUTE, Integer.parseInt(existingEndHrMin[1]));
            existingEndTime = new Timestamp(existingEndTimeCal.getTime().getTime());
        }

        //If a MW is ongoing, startTime cannot be edited. Only endTime (if changed) will be edited.
        if (existingOngoingBean != null && !newEndTime.equals(existingEndTime)) {
                //Update existing cassandra entry with newEndTime as endTime
            MaintenanceWindowServiceRepo.updateServiceOngoingMaintenanceCassandra(maintenanceDet.getAccountIdentifier(), maintenanceDet.getServiceIdentifier(),
                        existingStartTime, newEndTime);
        }

        if(existingOngoingBean == null && Constants.MST_SUB_TYPE_SCHEDULED.equalsIgnoreCase(maintenanceDet.getMaintenanceType())) {
            MaintenanceWindowServiceRepo.deleteMaintenanceCassandra(maintenanceDet.getAccountIdentifier(), maintenanceDet.getServiceIdentifier(),
                    maintenanceDet.getExistingStartTime());
            MaintenanceWindowServiceRepo.updateServiceOngoingMaintenanceCassandra(maintenanceDet.getAccountIdentifier(), maintenanceDet.getServiceIdentifier(),
                    newStartTime, newEndTime);
        }

        if (maintenanceDet.getRecurring() != null) {
            MAINTENANCE_DATA_SERVICE.updateRecurringWindowDetails(recurringBean, maintenanceDet.getId(), conn);
        }
    }
}
