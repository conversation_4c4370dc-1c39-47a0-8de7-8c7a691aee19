package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.keys.AccountControllerKey;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ApplicationPercentilesDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ApplicationPercentilesBean;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.pojo.ApplicationPercentiles;
import com.appnomic.appsone.controlcenter.pojo.Percentile;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class GetAppPercentilesBL implements BusinessLogic<Integer, AccountControllerKey, ApplicationPercentiles> {

    private static final Logger LOGGER = LoggerFactory.getLogger(GetAppPercentilesBL.class);
    ApplicationPercentilesDataService applicationPercentilesDataService = new ApplicationPercentilesDataService();

    @Override
    public UtilityBean<Integer> clientValidation(RequestObject request) throws ClientException {

        if (request == null) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (authToken == null || authToken.trim().isEmpty()) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty.");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            LOGGER.error(UIMessages.ACCOUNT_NULL_OR_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_NULL_OR_EMPTY);
        }

        String appIdString = request.getParams().get(Constants.APPLICATION_ID);
        if (appIdString == null || appIdString.trim().isEmpty()) {
            String error = "Invalid path parameter 'applicationId'. Reason: It is either is NULL or empty.";
            LOGGER.error(error);
            throw new ClientException(error);
        }

        String error = "'applicationId' is not a positive integer.";

        try {
            if (Integer.parseInt(appIdString) < 1) {
                LOGGER.error(error);
                throw new ClientException(error);
            }
        } catch (NumberFormatException e) {
            LOGGER.error(error);
            throw new ClientException(error);
        }

        return UtilityBean.<Integer>builder()
                .authToken(authToken)
                .accountIdentifier(identifier)
                .applicationId(appIdString)
                .build();
    }

    @Override
    public AccountControllerKey serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {

        UserAccountBean userAccountBean;
        try {
            userAccountBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            throw new ServerException(e.getSimpleMessage());
        }
        int accountId = userAccountBean.getAccount().getId();
        String appId = utilityBean.getApplicationId();

        int typeId = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT, Constants.APPLICATION_CONTROLLER_TYPE).getSubTypeId();
        ControllerBean application = new ControllerDataService().getControllerByIdAndTypeId(Integer.parseInt(appId), accountId, typeId, null);

        if (application == null) {
            LOGGER.error("Application with ID [{}] is unavailable for account [{}]", appId, accountId);
            throw new ServerException("Application('applicationId') is not present for the specified account.");
        }

        return AccountControllerKey.builder().account(userAccountBean.getAccount()).controllerId(Integer.parseInt(appId)).build();
    }

    @Override
    public ApplicationPercentiles process(AccountControllerKey accountControllerKey) throws DataProcessingException {
        try {
            List<ApplicationPercentilesBean> appPercentiles = applicationPercentilesDataService
                    .getApplicationPercentiles(accountControllerKey.getAccount().getId(), accountControllerKey.getControllerId());

            String percentileKpisSuffix = ConfProperties.getString(Constants.PERCENTILE_KPIS_IDENTIFIER_SUFFIX, Constants.PERCENTILE_KPIS_IDENTIFIER_SUFFIX_DEFAULT);

            List<Percentile> percentiles = appPercentiles.parallelStream()
                    .map(p -> {
                        String[] percentileKpiIdentifierArr = p.getKpiIdentifier().trim().split("_");
                        if (percentileKpiIdentifierArr[percentileKpiIdentifierArr.length - 1].equals(percentileKpisSuffix)) {
                            return Percentile.builder()
                                    .id(p.getId())
                                    .name(p.getKpiIdentifier())
                                    .value(Integer.parseInt(percentileKpiIdentifierArr[percentileKpiIdentifierArr.length - 2]))
                                    .build();
                        } else {
                            LOGGER.warn("Percentiles kpi {} suffix is not matching with {}.", p.getKpiIdentifier(), percentileKpisSuffix);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            return ApplicationPercentiles.builder()
                    .percentile(percentiles)
                    .defaultPercentile(getDefaultPercentiles())
                    .build();

        } catch (ControlCenterException e) {
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }

    public static List<Percentile> getDefaultPercentiles() {
        String percentileKpisSuffix = ConfProperties.getString(Constants.PERCENTILE_KPIS_IDENTIFIER_SUFFIX, Constants.PERCENTILE_KPIS_IDENTIFIER_SUFFIX_DEFAULT);
        String defaultApplicationPercentilesString = ConfProperties.getString(Constants.APPLICATION_PERCENTILES_DEFAULT_CONFIGURATION, Constants.APPLICATION_PERCENTILES_DEFAULT_VALUES);
        String[] percentileKpisIdentifiers = defaultApplicationPercentilesString.split(",");

        return Arrays.stream(percentileKpisIdentifiers)
                .map(percentileKpiIdentifier -> {
                    String[] percentileKpiIdentifierArr = percentileKpiIdentifier.trim().split("_");
                    if (percentileKpiIdentifierArr[percentileKpiIdentifierArr.length - 1].equals(percentileKpisSuffix)) {
                        return Percentile.builder()
                                .id(0)
                                .name(percentileKpiIdentifier.trim())
                                .value(Integer.parseInt(percentileKpiIdentifierArr[percentileKpiIdentifierArr.length - 2]))
                                .build();
                    } else {
                        LOGGER.warn("Percentiles kpi {} suffix is not matching with {}.", percentileKpiIdentifier, percentileKpisSuffix);
                        return null;
                    }
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
