package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.KpiMaintenanceStatusBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.KPIDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.KpiViolationConfig;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class UpdateInstanceKpiSeverityAnomalyBL implements BusinessLogic<InstanceKpiThresholdSeverityAnomalyPojo, List<KpiMaintenanceStatusBean>, String> {
    private static final KPIDataService KPI_DATA_SERVICE = new KPIDataService();

    private List<String> actList = Arrays.asList("MARK_SEVERE", "UNMARK_SEVERE", "GENERATE_EVENTS", "DONT_GENERATE_EVENTS");
    private String userActionList;
    private String accountIdentifier;

    @Override
    public UtilityBean<InstanceKpiThresholdSeverityAnomalyPojo> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(requestObject.getBody())) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Account identifier is invalid");
            throw new ClientException("Account identifier is invalid");
        }

        String requestBody = requestObject.getBody();

        InstanceKpiThresholdSeverityAnomalyPojo instanceKpiThresholdSeverityAnomalyPojo;
        try {
            instanceKpiThresholdSeverityAnomalyPojo = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestBody,
                    new TypeReference<InstanceKpiThresholdSeverityAnomalyPojo>() {
                    });
        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }
        if(instanceKpiThresholdSeverityAnomalyPojo.getInstanceIds().isEmpty()){
            throw new ClientException("instanceIds can't be null");
        }
        if(instanceKpiThresholdSeverityAnomalyPojo.getKpiIds().isEmpty()){
            throw new ClientException("kpiIds can't be null");
        }
        if (StringUtils.isEmpty(instanceKpiThresholdSeverityAnomalyPojo.getAction())) {
            log.error("invalid action provided");
            throw new ClientException("invalid action provided");
        }
        if (!actList.contains(instanceKpiThresholdSeverityAnomalyPojo.getAction())) {
            throw new ClientException("Invalid action provided");
        }

        return UtilityBean.<InstanceKpiThresholdSeverityAnomalyPojo>builder()
                .authToken(authToken)
                .accountIdentifier(identifier)
                .pojoObject(instanceKpiThresholdSeverityAnomalyPojo)
                .build();
    }

    @Override
    public List<KpiMaintenanceStatusBean> serverValidation(UtilityBean<InstanceKpiThresholdSeverityAnomalyPojo> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be " +
                    "invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }
        accountIdentifier = account.getIdentifier();

        int accountId = account.getId();
        InstanceKpiThresholdSeverityAnomalyPojo instanceKpiThresholdSeverityAnomalyPojo = utilityBean.getPojoObject();
        List<Integer> instanceIds = instanceKpiThresholdSeverityAnomalyPojo.getInstanceIds();
        List<KpiMaintenanceStatusBean> kpiMappingExists = new ArrayList<>();
        List<KpiMaintenanceStatusBean> kpiGroupMappingExists = new ArrayList<>();
        List<Integer> kpiIds = instanceKpiThresholdSeverityAnomalyPojo.getKpiIds();

        for (int instanceId : instanceIds) {
            String compInstanceIdentifier;
            try {
                compInstanceIdentifier = new CompInstanceDataService().getCompInstanceIdentifierFromId(instanceId, accountId, null);
            } catch (ControlCenterException e) {
                throw new ServerException(e.getMessage());
            }

            if (compInstanceIdentifier == null) {
                log.error("Component instance identifier unavailable for instanceId [{}]", instanceId);
                throw new ServerException("Invalid instanceId provided");
            }

            if (kpiIds.size() == 0) {
                log.error("No kpi id provides [{}]", kpiIds);
                throw new ServerException("Please provide kpi ids");
            }

            try {
                kpiMappingExists = KPI_DATA_SERVICE.fetchKpiCompInstMapping(instanceId, null);
                kpiGroupMappingExists = KPI_DATA_SERVICE.fetchGroupKpiCompInstMapping(instanceId, null);
                kpiGroupMappingExists.addAll(kpiMappingExists);
            } catch (ControlCenterException e) {
                throw new ServerException(e.getMessage());
            }
        }
        for (int kpiId : kpiIds) {
            KpiMaintenanceStatusBean kpiMaintenanceStatusBean = kpiGroupMappingExists.parallelStream().filter(kpi -> kpi.getKpiId() == kpiId).findAny().orElse(null);
            if (kpiMaintenanceStatusBean == null) {
                log.error("Kpi group mapping not exist for the kpiId: {}", kpiId);
                throw new ServerException("Kpi instance mapping not exist.");
            }
        }
        List<KpiMaintenanceStatusBean> kpiMaintenanceStatusBeans = kpiGroupMappingExists.stream().filter(kpi -> kpiIds.contains(kpi.getKpiId()))
                .collect(Collectors.toList());


        userActionList = instanceKpiThresholdSeverityAnomalyPojo.getAction();
        return kpiMaintenanceStatusBeans;
    }

    @Override
    public String process(List<KpiMaintenanceStatusBean> beans) throws DataProcessingException {
        KPIDataService kpiDataService = new KPIDataService();
        try {
            if (KpiThresholdConfigEnum.MARK_SEVERE.toString().equals(userActionList)) {
                if (beans.size() > 0) {
                   kpiDataService.updateInstanceKpiSeverity(1, beans, null);

                   try {
                       updateInstanceKpiSeverityInRedis(1, beans);
                   }catch (Exception e){
                       log.error("Exception while updating the instance KPI Severity in Redis : ", e);
                   }
                }
            } else if (KpiThresholdConfigEnum.UNMARK_SEVERE.toString().equals(userActionList)) {
                if (beans.size() > 0) {
                    kpiDataService.updateInstanceKpiSeverity(0, beans, null);

                    try {
                        updateInstanceKpiSeverityInRedis(0, beans);
                    }catch (Exception e){
                        log.error("Exception while updating the instance KPI Severity in Redis : ", e);
                    }
                }
            } else if (KpiThresholdConfigEnum.GENERATE_EVENTS.toString().equals(userActionList)) {
                List<KpiMaintenanceStatusBean> nonGroup = beans.parallelStream().filter(x -> x.getKpiGroupId() == 0).collect(Collectors.toList());
                List<KpiMaintenanceStatusBean> group = beans.parallelStream().filter(x -> !(x.getKpiGroupId() == 0)).collect(Collectors.toList());

                if (nonGroup.size() > 0) {
                    kpiDataService.updateNonGroupInstanceKpiAnomaly(1, nonGroup, null);
                }
                if (group.size() > 0) {
                    kpiDataService.updateGroupInstanceKpiAnomaly(1, group, null);
                }

                List<InstanceKpiAttributeThresholdBean> instanceKpiAttributeThresholdBeans = beans.parallelStream()
                        .map(x -> InstanceKpiAttributeThresholdBean.builder()
                        .status(1)
                        .compInstanceId(x.getCompInstanceId())
                        .kpiGroupId(x.getKpiGroupId())
                        .kpiId(x.getKpiId())
                        .build()).collect(Collectors.toList());
                kpiDataService.updateStatusForCompInstanceKpiThresholds(instanceKpiAttributeThresholdBeans, null);

                try {
                    updateInstanceKpiAnomalyAndThresholdsStatusInRedis(1, 1, beans);
                }catch (Exception e){
                    log.error("Exception while updating the instance KPI Anomaly and Threshold Status in Redis : ", e);
                }

            } else if (KpiThresholdConfigEnum.DONT_GENERATE_EVENTS.toString().equals(userActionList)) {
                List<KpiMaintenanceStatusBean> nonGroup = beans.parallelStream().filter(x -> x.getKpiGroupId() == 0).collect(Collectors.toList());
                List<KpiMaintenanceStatusBean> group = beans.parallelStream().filter(x -> !(x.getKpiGroupId() == 0)).collect(Collectors.toList());

                if (nonGroup.size() > 0) {
                    kpiDataService.updateNonGroupInstanceKpiAnomaly(0, nonGroup, null);
                }
                if (group.size() > 0) {
                    kpiDataService.updateGroupInstanceKpiAnomaly(0, group, null);
                }

                List<InstanceKpiAttributeThresholdBean> instanceKpiAttributeThresholdBeans = beans.parallelStream()
                        .map(x -> InstanceKpiAttributeThresholdBean.builder()
                        .status(0)
                        .compInstanceId(x.getCompInstanceId())
                        .kpiGroupId(x.getKpiGroupId())
                        .kpiId(x.getKpiId())
                        .build()).collect(Collectors.toList());
                kpiDataService.updateStatusForCompInstanceKpiThresholds(instanceKpiAttributeThresholdBeans, null);

                try {
                    updateInstanceKpiAnomalyAndThresholdsStatusInRedis(0, 0, beans);
                }catch (Exception e){
                    log.error("Exception while updating the instance KPI Anomaly and Threshold Status in Redis : ", e);
                }
            }
            return "Metric config status updated successfully";

        } catch (Exception e) {
            log.error("Unable to update KPI maintenance status. Details: ", e);
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }

    private void insertData(List<KpiMaintenanceStatusBean> configsToAdd, Handle handle) throws DataProcessingException {
        int[] ids = KPI_DATA_SERVICE.addInstanceKpiMaintenanceStatus(configsToAdd, handle);

        if (ids == null || ids.length == 0) {
            log.error("Error while adding KPI maintenance status configuration");
            throw new DataProcessingException("Error while adding KPI maintenance status configuration");
        }
    }

    private void updateData(List<KpiMaintenanceStatusBean> configsToUpdate, Handle handle) throws DataProcessingException {
        int[] ids = KPI_DATA_SERVICE.updateInstanceKpiMaintenanceStatus(configsToUpdate, handle);

        if (ids == null || ids.length == 0) {
            log.error("Error while updating KPI maintenance status configuration");
            throw new DataProcessingException("Error while updating KPI maintenance status configuration");
        }
    }

    private void updateInstanceKpiSeverityInRedis(int severity, List<KpiMaintenanceStatusBean> beans) {
        InstanceRepo instanceRepo = new InstanceRepo();
        Map<Integer, com.heal.configuration.pojos.CompInstClusterDetails> instanceDetails = instanceRepo.getInstances(accountIdentifier).parallelStream().collect(Collectors.toMap(CompInstClusterDetails::getId, Function.identity()));
        for (KpiMaintenanceStatusBean kpiBean : beans) {
            CompInstClusterDetails instClusterDetail = instanceDetails.get(kpiBean.getCompInstanceId());
            if (instClusterDetail == null) {
                log.error("Instance details not found for the instanceId : {}", kpiBean.getCompInstanceId());
                return;
            }
            List<CompInstKpiEntity> instanceWiseKpis = instanceRepo.getInstanceWiseKpis(accountIdentifier, instClusterDetail.getIdentifier());
            CompInstKpiEntity kpiEntity = instanceWiseKpis.stream().filter(entity -> entity.getId() == kpiBean.getKpiId()).findAny().orElse(null);
            if (kpiEntity == null) {
                log.error("The kpi details not found for the kpi [{}] and instance [{}]", kpiBean.getKpiId(), kpiBean.getCompInstanceId());
                return;
            }
            if (kpiEntity.getKpiViolationConfig() == null || kpiEntity.getKpiViolationConfig().isEmpty()) {
                log.error("Thresholds details not found for the kpiId: {} and instanceId: {}", kpiBean.getKpiId(), kpiBean.getCompInstanceId());
                return;
            }
            for (Map.Entry<String, KpiViolationConfig> thresholdsEntry : kpiEntity.getKpiViolationConfig().entrySet()) {
                thresholdsEntry.getValue().setSeverity(severity);
            }
            instanceRepo.updateKpiDetailsForKpiId(accountIdentifier, instClusterDetail.getIdentifier(), kpiEntity);
            instanceRepo.updateKpiDetailsForKpiIdentifier(accountIdentifier, instClusterDetail.getIdentifier(), kpiEntity);
            instanceRepo.updateKpiDetails(accountIdentifier, instClusterDetail.getIdentifier(), instanceWiseKpis);
        }
    }

    private void updateInstanceKpiAnomalyAndThresholdsStatusInRedis(int notification, int status, List<KpiMaintenanceStatusBean> beans) {
        InstanceRepo instanceRepo = new InstanceRepo();
        Map<Integer, com.heal.configuration.pojos.CompInstClusterDetails> instanceDetails = instanceRepo.getInstances(accountIdentifier).parallelStream().collect(Collectors.toMap(CompInstClusterDetails::getId, Function.identity()));
        for (KpiMaintenanceStatusBean kpiBean : beans) {
            CompInstClusterDetails instClusterDetail = instanceDetails.get(kpiBean.getCompInstanceId());
            if (instClusterDetail == null) {
                log.error("Instance details not found for the instanceId : {}", kpiBean.getCompInstanceId());
                return;
            }
            List<CompInstKpiEntity> instanceWiseKpis = instanceRepo.getInstanceWiseKpis(accountIdentifier, instClusterDetail.getIdentifier());
            CompInstKpiEntity kpiEntity = instanceWiseKpis.stream().filter(entity -> entity.getId() == kpiBean.getKpiId()).findAny().orElse(null);
            if (kpiEntity == null) {
                log.error("The kpi details not found for the kpi [{}] and instance [{}]", kpiBean.getKpiId(), kpiBean.getCompInstanceId());
                return;
            }
            kpiEntity.setNotification(notification);
            if (kpiEntity.getKpiViolationConfig() == null || kpiEntity.getKpiViolationConfig().isEmpty()) {
                log.error("Thresholds details not found for the kpiId: {} and instanceId: {}", kpiBean.getKpiId(), kpiBean.getCompInstanceId());
                return;
            }
            for (Map.Entry<String, KpiViolationConfig> thresholdsEntry : kpiEntity.getKpiViolationConfig().entrySet()) {
                thresholdsEntry.getValue().setStatus(status);
            }
            instanceRepo.updateKpiDetailsForKpiId(accountIdentifier, instClusterDetail.getIdentifier(), kpiEntity);
            instanceRepo.updateKpiDetailsForKpiIdentifier(accountIdentifier, instClusterDetail.getIdentifier(), kpiEntity);
            instanceRepo.updateKpiDetails(accountIdentifier, instClusterDetail.getIdentifier(), instanceWiseKpis);
        }
    }
}
