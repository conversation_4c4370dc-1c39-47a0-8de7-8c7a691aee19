package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.AgentCompInstMappingBean;
import com.appnomic.appsone.controlcenter.beans.ComponentInstanceBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.redis.AgentRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.ActionsEnum;
import com.appnomic.appsone.controlcenter.pojo.AgentCompInstanceMaping;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.BasicAgentEntity;
import com.heal.configuration.pojos.BasicEntity;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.utils.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class UpdateAgentMappingForCompInstance implements BusinessLogic<List<AgentCompInstanceMaping>, List<AgentCompInstMappingBean>, String> {
    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateMetricGroupAttributes.class);
    private static final BindInDataService BIND_IN_DATA_SERVICE = new BindInDataService();
    private static final CompInstanceDataService COMP_INST_DATA_SERVICE = new CompInstanceDataService();
    AgentRepo agentRepo = new AgentRepo();

    @Override
    public UtilityBean<List<AgentCompInstanceMaping>> clientValidation(RequestObject request) throws ClientException {

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }

        if (StringUtils.isEmpty(request.getBody())) {
            LOGGER.error("Request body is empty.");
            throw new ClientException("Request body is empty.");
        }
        List<AgentCompInstanceMaping> agentCompInstanceMaping;
        try {
            agentCompInstanceMaping = new ObjectMapper().readValue(request.getBody(),
                    new TypeReference<List<AgentCompInstanceMaping>>() {
                    });
            for(AgentCompInstanceMaping data: agentCompInstanceMaping) {
                if (!data.validate()) {
                    LOGGER.error("Request validation failure.");
                    throw new ClientException("Request validation failure. Kindly check the logs.");
                }
            }
        } catch (IOException e) {
            LOGGER.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        return UtilityBean.<List<AgentCompInstanceMaping>>builder()
            .accountIdentifier(identifier)
            .pojoObject(agentCompInstanceMaping)
            .authToken(authToken)
            .build();
    }

    @Override
    public List<AgentCompInstMappingBean> serverValidation(UtilityBean<List<AgentCompInstanceMaping>> utilityBean) throws ServerException {
        UserAccountBean userAccBean;
        try {
            userAccBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            throw new ServerException(e.getMessage());
        }

        int accountId = userAccBean.getAccount().getId();
        List<AgentCompInstanceMaping> agentCompInstanceMapingList = utilityBean.getPojoObject();

        return agentCompInstanceValidation(accountId, agentCompInstanceMapingList, utilityBean.getAccountIdentifier());
    }

    @Override
    public String process(List<AgentCompInstMappingBean> agentCompInsteMancapingBeanList) throws DataProcessingException {
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        String accountIdentifier = agentCompInsteMancapingBeanList.get(0).getAccountIdentifier();
        try {
            return dbi.inTransaction((conn, status) -> {
                Map<Boolean, List<AgentCompInstMappingBean>> configWithAddAction = agentCompInsteMancapingBeanList.parallelStream()
                        .collect(Collectors.partitioningBy(c -> c.getActionForUpdate().equals(ActionsEnum.ADD)));

                List<AgentCompInstMappingBean> configToBeDeleted = configWithAddAction.get(false);
                if(!configToBeDeleted.isEmpty()) {
                    deleteAgentCompInstMappingData(conn, configToBeDeleted);
                    deleteAgentInstanceMapping(configToBeDeleted, accountIdentifier);
                }

                List<AgentCompInstMappingBean> configToBeAdded = configWithAddAction.get(true);
                if(!configToBeAdded.isEmpty()) {
                    insertAgentCompInstMappingData(conn, configToBeAdded);
                    addAgentInstanceMapping(configToBeAdded, accountIdentifier);

                }

                return "Agent(s) mapped with component instance(s) successfully";
            });
        } catch (Exception e) {
            LOGGER.error("Unable to update agent/component instance mapping. Details: ", e);

            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
            } else {
                throw e;
            }
        }
    }

    private List<AgentCompInstMappingBean> agentCompInstanceValidation(int accountId, List<AgentCompInstanceMaping> agentCompInstanceMaping, String accountIdentifier) throws ServerException {
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
        List<AgentCompInstMappingBean> agentCompInstMappingBeansList = new ArrayList<>();

        for(AgentCompInstanceMaping agentCompInstMap : agentCompInstanceMaping) {
            int instanceId = agentCompInstMap.getInstanceId();
            ComponentInstanceBean bean = compInstanceDataService.getComponentInstanceByIdAndAccount(instanceId, accountId);
            if (bean == null) {
                throw new ServerException("Invalid instance Id : " + instanceId);
            }

            List<Integer> agentIdsToAdd = agentCompInstMap.getAddedAgentIds();
            if(CollectionUtils.isNotEmpty(agentIdsToAdd)) {
                List<AgentCompInstMappingBean> existingAgentCompInstMappingBeans = BIND_IN_DATA_SERVICE.getAgentCompInstMapping(agentIdsToAdd);
                for (int agentId : agentIdsToAdd) {
                    boolean agentIdExists = AgentDataService.isAgentIdExists(agentId);
                    if(!agentIdExists) {
                        LOGGER.error("Agent to be added has invalid Id: {}", agentId);
                        throw new ServerException(String.format("Agent to be added has invalid Id:: [%s]", agentId));
                    }
                    AgentCompInstMappingBean agentCompInstMappingBean = AgentCompInstMappingBean.builder()
                            .compInstanceId(instanceId)
                            .agentId(agentId)
                            .actionForUpdate(ActionsEnum.ADD)
                            .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                            .compInstanceName(bean.getName())
                            .compInstanceIdentifier(bean.getIdentifier())
                            .status(bean.getStatus())
                            .userDetailsId(bean.getUserDetailsId())
                            .accountId(accountId)
                            .accountIdentifier(accountIdentifier)
                            .build();

                    validateIndividualAgentCompInstMapping(agentCompInstMappingBean, existingAgentCompInstMappingBeans);
                    agentCompInstMappingBeansList.add(agentCompInstMappingBean);
                }
            }

            List<Integer> agentIdsToDelete = agentCompInstMap.getDeletedAgentIds();
            if(CollectionUtils.isNotEmpty(agentIdsToDelete)) {
                List<AgentCompInstMappingBean> existingAgentCompInstMappingBeans = BIND_IN_DATA_SERVICE.getAgentCompInstMapping(agentIdsToDelete);
                for (int agentId : agentIdsToDelete) {
                    boolean agentIdExists = AgentDataService.isAgentIdExists(agentId);
                    if(!agentIdExists) {
                        LOGGER.error("Agent to be unmapped has invalid Id: {}", agentId);
                        throw new ServerException(String.format("Agent to be unmapped has invalid Id:: [%s]", agentId));
                    }
                    AgentCompInstMappingBean agentCompInstMappingBean = AgentCompInstMappingBean.builder()
                        .compInstanceId(instanceId)
                        .agentId(agentId)
                        .actionForUpdate(ActionsEnum.DELETE)
                        .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                        .accountId(accountId)
                        .accountIdentifier(accountIdentifier)
                        .build();

                    validateIndividualAgentCompInstMapping(agentCompInstMappingBean, existingAgentCompInstMappingBeans);
                    agentCompInstMappingBeansList.add(agentCompInstMappingBean);
                }
            }
        }

        return agentCompInstMappingBeansList;
    }

    private void validateIndividualAgentCompInstMapping(AgentCompInstMappingBean bean, List<AgentCompInstMappingBean> existingAgentCompInstMappingBeans) throws ServerException {
        if(ActionsEnum.ADD.equals(bean.getActionForUpdate())) {
            if (existingAgentCompInstMappingBeans.contains(bean)) {
                LOGGER.error("Agent Id [{}] is already mapped with instanceId [{}]. Details: {}",
                        bean.getAgentId(), bean.getCompInstanceId(), bean);
                throw new ServerException("Error in configuring agent/instance mapping details");
            }
        }
        else if(ActionsEnum.DELETE.equals(bean.getActionForUpdate())) {
            if (!existingAgentCompInstMappingBeans.contains(bean)) {
                LOGGER.error("Cannot unmap agent. Agent Id [{}] is not mapped with instanceId [{}]. Details: {}",
                        bean.getAgentId(), bean.getCompInstanceId(), bean);
                throw new ServerException("Error in configuring agent/instance mapping details");
            }
        }
    }

    private void insertAgentCompInstMappingData(Handle conn, List<AgentCompInstMappingBean> configToBeAdded) throws DataProcessingException {
        int[] ids = COMP_INST_DATA_SERVICE.addAgentMappingToInstance(configToBeAdded, conn);

        if (ids == null || ids.length == 0) {
            LOGGER.error("Error while adding agent mapping to instance");
            throw new DataProcessingException("Error while adding agent mapping to instance");
        }
    }

    private void deleteAgentCompInstMappingData(Handle conn, List<AgentCompInstMappingBean> configToBeDeleted) throws DataProcessingException {
        int[] ids = COMP_INST_DATA_SERVICE.deleteAgentMappingFromInstance(configToBeDeleted, conn);
        if (ids == null || ids.length == 0) {
            LOGGER.error("Error while deleting agent mapping from instance");
            throw new DataProcessingException("Error while deleting agent mapping from instance");
        }
    }
    private void addAgentInstanceMapping(List<AgentCompInstMappingBean> configToBeAdded, String accountIdentifier) {
        Map<Integer, BasicAgentEntity> agentDetails = agentRepo.getAgents(accountIdentifier).parallelStream().collect(Collectors.toMap(BasicAgentEntity::getId, Function.identity()));
        if (agentDetails.isEmpty()) {
            LOGGER.error("the agent details not found for the accountIdentifier: {} from redis cache.", accountIdentifier);
            return;
        }
        configToBeAdded.forEach(mappingDetails -> {
            if(agentDetails.get(mappingDetails.getAgentId()) == null) {
                LOGGER.error("Agent with ID [{}] unavailable for accountIdentifier [{}]", mappingDetails.getAgentId(), accountIdentifier);
                return;
            }

            List<BasicEntity> existingAgentInstances = agentRepo.getAgentInstanceMappingDetails(mappingDetails.getAccountIdentifier(), agentDetails.get(mappingDetails.getAgentId()).getIdentifier());

            if (existingAgentInstances.stream().anyMatch(o -> o.getId() == mappingDetails.getCompInstanceId())) {
                LOGGER.error("The instance details already exist for agentId:{}", mappingDetails.getAgentId());
                return;
            }
            BasicEntity basicEntity = BasicEntity.builder()
                    .id(mappingDetails.getCompInstanceId())
                    .status(mappingDetails.getStatus())
                    .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .name(mappingDetails.getCompInstanceName())
                    .identifier(mappingDetails.getCompInstanceIdentifier())
                    .lastModifiedBy(mappingDetails.getUserDetailsId())
                    .build();
            List<BasicEntity> newBasicEntityList = new ArrayList<>(existingAgentInstances);
            newBasicEntityList.add(basicEntity);
            agentRepo.updateAgentInstanceMappingDetails(mappingDetails.getAccountIdentifier(), agentDetails.get(mappingDetails.getAgentId()).getIdentifier(), newBasicEntityList);
        });
    }
    public void deleteAgentInstanceMapping(List<AgentCompInstMappingBean> configToBeDelete, String accountIdentifier){
        Map<Integer, BasicAgentEntity> agentDetails = agentRepo.getAgents(accountIdentifier).parallelStream().collect(Collectors.toMap(BasicAgentEntity::getId, Function.identity()));
        if(agentDetails.isEmpty()){
            LOGGER.error("the agent details not found for the accountIdentifier: {} from redis cache.", accountIdentifier);
            return;
        }
        configToBeDelete.parallelStream()
                .forEach(mappingDetail -> {
                    List<BasicEntity> existingAgentInstances = agentRepo.getAgentInstanceMappingDetails(mappingDetail.getAccountIdentifier(), agentDetails.get(mappingDetail.getAgentId()).getIdentifier());
                    if(existingAgentInstances.isEmpty() || existingAgentInstances.parallelStream().noneMatch(c -> mappingDetail.getCompInstanceId() == c.getId()) ) {
                        LOGGER.error("the agent details not found for the agentIdentifier: {} and accountIdentifier: {} from redis cache.", agentDetails.get(mappingDetail.getAgentId()).getIdentifier() ,accountIdentifier);
                        return;
                    }
                    existingAgentInstances.removeIf(obj -> obj.getId() == mappingDetail.getCompInstanceId());
                    agentRepo.updateAgentInstanceMappingDetails(mappingDetail.getAccountIdentifier(), agentDetails.get(mappingDetail.getAgentId()).getIdentifier(), existingAgentInstances);
                });
    }
}
