package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.dao.mysql.SupervisorDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.SupervisorBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.Supervisor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

public class GetSupervisorsBL implements BusinessLogic<String, Integer, List<Supervisor>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(GetSupervisorsBL.class);

    @Override
    public UtilityBean<String> clientValidation(RequestObject requestObject) throws ClientException {
                return UtilityBean.<String>builder()
                .build();
    }

    @Override
    public Integer serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        return 0;
    }

    @Override
    public List<Supervisor> process(Integer accountId) throws DataProcessingException {
        try {
            List<SupervisorBean> supervisorsDetailList = new SupervisorDataService().getAllSupervisorDetails(null);

            return supervisorsDetailList.parallelStream().map(supervisorDetails -> Supervisor.builder()
                    .id(supervisorDetails.getId())
                    .supervisorId(supervisorDetails.getIdentifier())
                    .name(supervisorDetails.getName())
                    .supervisorTypeName(MasterCache.getMstSubTypeForSubTypeId(supervisorDetails.getSupervisorType()).getSubTypeName())
                    .hostAddress(supervisorDetails.getHostAddress())
                    .hostBoxName(supervisorDetails.getHostBoxName())
                    .version(supervisorDetails.getVersion())
                    .status(supervisorDetails.isStatus())
                    .mode(supervisorDetails.getMode().toUpperCase())
                    .build()).collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting supervisor Details", e);
            throw new DataProcessingException(e.getMessage());
        }
    }
}
