package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.xpt.UpdateBean;
import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.TransactionDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.UpdateObjectReferenceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.TransactionAuditConfigurationBean;
import com.appnomic.appsone.controlcenter.dao.redis.TransactionRepo;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.TransactionAuditDetail;
import com.appnomic.appsone.controlcenter.pojo.TxnAndGroupBean;
import com.appnomic.appsone.controlcenter.pojo.UpdatedValue;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.Transaction;
import org.apache.commons.lang3.StringUtils;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.appnomic.appsone.controlcenter.util.StringUtils.isEmpty;

public class UpdateTransactionsBL implements BusinessLogic<List<UpdatedValue>, UtilityBean<List<UpdatedValue>>, String> {
    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateTransactionsBL.class);
    private static final int TRANSACTION_MIN_NAME = 3;
    private static final int TRANSACTION_MAX_NAME = 256;

    @Override
    public UtilityBean<List<UpdatedValue>> clientValidation(RequestObject requestObject) throws ClientException {
        LOGGER.debug("Inside Client validation");
        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        List<UpdatedValue> updatedValueList;

        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authKey)) {
            LOGGER.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }

        try {
            updatedValueList = CommonUtils.getObjectMapper().readValue(requestObject.getBody(), new TypeReference<List<UpdatedValue>>() {
            });
        } catch (IOException e) {
            LOGGER.error(UIMessages.JSON_INVALID + " Details: {}", e.getMessage());
            throw new ClientException(UIMessages.JSON_INVALID);
        }
        Map<String, String> error = new HashMap<>();
        for (UpdatedValue updatedValue : updatedValueList) {
            if (updatedValue.getId() == 0) {
                LOGGER.error(UIMessages.INVALID_TRANSACTION_ID);
                error.put("Transaction Id", UIMessages.INVALID_TRANSACTION_ID);
            }
            if (!StringUtils.isEmpty(updatedValue.getName()) && (updatedValue.getName().trim().length() < TRANSACTION_MIN_NAME || updatedValue.getName().length() > TRANSACTION_MAX_NAME)) {
                LOGGER.error(UIMessages.INVALID_TRANSACTION_NAME);
                error.put("Transaction Name", UIMessages.INVALID_TRANSACTION_NAME);
            }

            String errorMsg = "Invalid : It can only be 0 or 1.";

            if (updatedValue.getMonitorEnabled() < 0 || updatedValue.getMonitorEnabled() > 1) {
                LOGGER.error(errorMsg);
                error.put("Transaction - monitorEnabled ", errorMsg);
            }

            if (updatedValue.getAuditEnabled() < 0 || updatedValue.getAuditEnabled() > 1) {
                LOGGER.error(errorMsg);
                error.put("Transaction - auditEnabled ", errorMsg);
            }

            validateAuditDetails(updatedValue);
        }
        if (error.size() > 0) {
            throw new ClientException(error.toString());
        }
        return UtilityBean.<List<UpdatedValue>>builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .pojoObject(updatedValueList)
                .build();

    }

    private void validateAuditDetails(UpdatedValue updatedValue) throws ClientException {

        List<String> displayNames = new ArrayList<>();
        List<String> variables = new ArrayList<>();

        for (TransactionAuditDetail audit : updatedValue.getAddAuditDetails()) {
            if (audit.validateForAdd()) {
                LOGGER.error("Invalid inputs for addAuditDetails : displayName and variable should not contain any " +
                        "special characters - " + audit);
                throw new ClientException("Invalid inputs for addAuditDetails : displayName and variable should " +
                        "not contain any special characters - " + audit);
            }
            displayNames.add(audit.getDisplayName().trim());
            variables.add(audit.getVariable().trim());
        }
        for (TransactionAuditDetail audit : updatedValue.getModifyAuditDetails()) {
            if (audit.validateForUpdate()) {
                LOGGER.error("Invalid inputs for modifyAuditDetails : " + audit);
                throw new ClientException("Invalid inputs for modifyAuditDetails : " + audit);
            }
            if (!isEmpty(audit.getDisplayName())) {
                displayNames.add(audit.getDisplayName().trim());
            }
            if (!isEmpty(audit.getVariable())) {
                variables.add(audit.getVariable().trim());
            }
        }
        if (updatedValue.getDeleteAuditDetails().parallelStream().anyMatch(i -> i < 1)) {
            LOGGER.error("Invalid inputs for deleteAuditDetails : Ids should be greater than 0 - " + updatedValue
                    .getDeleteAuditDetails());
            throw new ClientException("Invalid inputs for deleteAuditDetails : Ids should be greater than 0 - " +
                    updatedValue.getDeleteAuditDetails());
        }

        if (displayNames.size() != new HashSet<>(displayNames).size() || variables.size() != new HashSet<>(variables).size()) {
            LOGGER.error("Duplicate displayNames and variables are present in the request.");
            throw new ClientException("Duplicate displayNames and variables are present in the request.");
        }
    }

    @Override
    public UtilityBean<List<UpdatedValue>> serverValidation(UtilityBean<List<UpdatedValue>> utilityBean) throws ServerException {
        LOGGER.debug("Inside Server validation");
        UserAccountBean userAccountBean;

        try {
            userAccountBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            String err = e.getSimpleMessage();
            LOGGER.error(err);
            throw new ServerException(err);
        }
        List<TxnAndGroupBean> txnAndGroupBeanList = TransactionDataService.getTxnAndGroupList(userAccountBean.getAccount().getId());
        if (txnAndGroupBeanList == null || txnAndGroupBeanList.isEmpty()) {
            LOGGER.error("No Transaction present in transaction table for account {}", userAccountBean.getAccount().getId());
            throw new ServerException("No Transaction present in transaction table.");
        }

        List<TransactionAuditConfigurationBean> auditDetailsForTransactions;
        try {
            List<Integer> txnIds = utilityBean.getPojoObject().parallelStream().map(UpdatedValue::getId).collect(Collectors.toList());
            auditDetailsForTransactions = new TransactionDataService().getTxnAuditConfigurationDetails(txnIds, null);
        } catch (ControlCenterException e) {
            throw new ServerException(e.getMessage());
        }
        for (UpdatedValue updatedValue : utilityBean.getPojoObject()) {
            int id = updatedValue.getId();
            if (txnAndGroupBeanList.stream().noneMatch(x -> x.getTxnId() == id)) {
                LOGGER.error("Transaction id {} is not present in transaction table.", id);
                throw new ServerException("Transaction id " + id + " is not present in transaction table.");
            }
            String txnName = updatedValue.getName();
            if (txnAndGroupBeanList.stream().filter(x -> !(x.getTxnId() == id)).anyMatch(x -> Objects.equals(x.getTxnName(), txnName))) {
                LOGGER.error("Duplicate transaction name in transaction table.", txnName);
                throw new ServerException("Transaction name " + txnName + " is present in transaction table.");
            }
            validateAuditConfigurations(updatedValue, auditDetailsForTransactions);
        }
        return UtilityBean.<List<UpdatedValue>>builder()
                .account(userAccountBean.getAccount())
                .authToken(utilityBean.getAuthToken())
                .userId(userAccountBean.getUserId())
                .pojoObject(utilityBean.getPojoObject())
                .build();
    }

    private void validateAuditConfigurations(UpdatedValue updatedValue, List<TransactionAuditConfigurationBean> auditDetails) throws ServerException {

        int txnId = updatedValue.getId();

        Set<Integer> existingTxnAuditConfigIds = auditDetails.parallelStream().filter(f-> f.getTransactionId() == txnId).map(TransactionAuditConfigurationBean::getId).collect(Collectors.toSet());
        List<Integer> rulesAuditConfigIds = updatedValue.getModifyAuditDetails() == null ? new ArrayList<>() :
                updatedValue.getModifyAuditDetails().parallelStream().map(TransactionAuditDetail::getId).collect(Collectors.toList());
        if (updatedValue.getDeleteAuditDetails() != null || updatedValue.getDeleteAuditDetails().size() != 0) {
            rulesAuditConfigIds.addAll(updatedValue.getDeleteAuditDetails());
        }
        List<Integer> invalidAuditConfigIds = new ArrayList<>();
        if (rulesAuditConfigIds.parallelStream().anyMatch(id -> {
            boolean check = !existingTxnAuditConfigIds.contains(id);
            if (check) {
                invalidAuditConfigIds.add(id);
            }
            return check;
        })) {
            String error = "Invalid Transaction Audit Configuration Id or Ids specified in 'modifyAuditDetails' or " +
                    "'deleteAuditDetails' : " + invalidAuditConfigIds + " for transactionId - " + updatedValue.getId();
            LOGGER.error(error);
            throw new ServerException(error);
        }

        Set<String> auditLookUpNames = auditDetails.parallelStream().filter(f-> f.getTransactionId() == txnId).map(TransactionAuditConfigurationBean::getLookupName).collect(Collectors.toSet());
        Set<String> auditDisplayNames = auditDetails.parallelStream().filter(f-> f.getTransactionId() == txnId).map(TransactionAuditConfigurationBean::getDisplayName).collect(Collectors.toSet());

        List<TransactionAuditDetail> transactionAuditDetails = updatedValue.getModifyAuditDetails() == null || updatedValue.getModifyAuditDetails().isEmpty()? new ArrayList<>() :
                updatedValue.getModifyAuditDetails();
        if (updatedValue.getAddAuditDetails() != null || !updatedValue.getAddAuditDetails().isEmpty()) {
            transactionAuditDetails.addAll(updatedValue.getAddAuditDetails());
        }

        List<TransactionAuditDetail> invalidConfig = new ArrayList<>();
        if (transactionAuditDetails.parallelStream().anyMatch(a -> {
            boolean check = (!StringUtils.isEmpty(a.getVariable())  && auditLookUpNames.contains(a.getVariable().toLowerCase()))
                    || (!StringUtils.isEmpty(a.getDisplayName()) && auditDisplayNames.contains(a.getDisplayName().toLowerCase()));
            if (check) {
                invalidConfig.add(a);
            }
            return check;
        })) {
            String error = "Invalid Transaction Audit Configuration details specified in 'modifyAuditDetails' or " +
                    "'addAuditDetails' (displayName or variable already exists) : " + invalidConfig;
            LOGGER.error(error);
            throw new ServerException(error);
        }
    }


    @Override
    public String process(UtilityBean<List<UpdatedValue>> bean) throws DataProcessingException {
        try {
            TransactionRepo transactionRepo = new TransactionRepo();
            String returnValue = MySQLConnectionManager.getInstance().getHandle().inTransaction((conn, status) ->
                    updateTransactions(bean, conn));

            bean.getPojoObject().forEach(updatedValue -> {
                Transaction transaction = transactionRepo.getTransactionById(bean.getAccount().getIdentifier(), updatedValue.getId());
                if(transaction == null) {
                    LOGGER.error("Could not find transaction in redis cache for accountId:{}, txnId:{}", bean.getAccount().getIdentifier(), updatedValue.getId());
                    return;
                }

                transaction.setAuditEnabled(updatedValue.getAuditEnabled());
                transaction.setMonitorEnabled(updatedValue.getMonitorEnabled());
                if(!StringUtils.isEmpty(updatedValue.getName())) {
                    transaction.setName(updatedValue.getName());
                }

                transactionRepo.updateTransactionDetailsById(bean.getAccount().getIdentifier(), transaction);
                transactionRepo.updateTransactionDetailsByIdentifier(bean.getAccount().getIdentifier(), transaction);
            });
            return returnValue;
        } catch (Exception e) {
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }

    }

    private String updateTransactions(UtilityBean<List<UpdatedValue>> bean, Handle handle) throws DataProcessingException, ControlCenterException {
        String timestamp = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());

        List<UpdateBean> updateBeanList = bean.getPojoObject().parallelStream()
                .map(updatedValue -> getUpdateBean(updatedValue, bean.getUserId(), timestamp))
                .collect(Collectors.toList());
        new UpdateObjectReferenceDataService().updateTransactions(updateBeanList, handle);

        LOGGER.info("Successfully updated transaction table");

        updateTransactionAuditConfigurations(bean.getPojoObject(), bean.getUserId(), bean.getAccount(), timestamp, handle);

        return Constants.SUCCESS;
    }

    private UpdateBean getUpdateBean(UpdatedValue updatedValue, String userId, String timestamp) {
        UpdateBean updateBean = new UpdateBean();
        updateBean.setId(updatedValue.getId());
        updateBean.setName(StringUtils.isEmpty(updatedValue.getName()) ? null : updatedValue.getName());
        updateBean.setMonitorEnabled(updatedValue.getMonitorEnabled());
        updateBean.setAuditEnabled(updatedValue.getAuditEnabled());
        updateBean.setUpdateTime(timestamp);
        updateBean.setUserDetailsId(userId);
        return updateBean;
    }

    private void updateTransactionAuditConfigurations(List<UpdatedValue> updatedValueList, String userId,
                                                      AccountBean account, String timestamp, Handle handle) throws ControlCenterException {

        List<TransactionAuditConfigurationBean> addTxnConfigurationList = new ArrayList<>();
        List<TransactionAuditConfigurationBean> modifyTxnConfigurationList = new ArrayList<>();
        List<Integer> deleteTxnConfigurationList = new ArrayList<>();


        TransactionDataService dataService = new TransactionDataService();
        List<Integer> txnIds = updatedValueList.parallelStream().map(UpdatedValue::getId).collect(Collectors.toList());
        List<TransactionAuditConfigurationBean> existingTxnAuditConfigurationDetails = dataService.getTxnAuditConfigurationDetails(txnIds, handle);

        for (UpdatedValue updatedValue : updatedValueList) {

            if (updatedValue.getAddAuditDetails() != null && !updatedValue.getAddAuditDetails().isEmpty()) {
                int auditCaptureTypeId = MasterCache.getMstSubTypeForSubTypeName("HttpTransactions").getSubTypeId();
                addTxnConfigurationList.addAll(updatedValue.getAddAuditDetails().parallelStream()
                        .map(a -> TransactionAuditConfigurationBean.builder()
                                .transactionId(updatedValue.getId())
                                .auditCaptureTypeId(auditCaptureTypeId)
                                .displayName(a.getDisplayName())
                                .lookupName(a.getVariable())
                                .userDetailsId(userId)
                                .createdTime(timestamp)
                                .updatedTime(timestamp)
                                .status(Constants.STATUS_ACTIVE)
                                .build())
                        .collect(Collectors.toList()));
            }

            if (updatedValue.getModifyAuditDetails() != null && !updatedValue.getModifyAuditDetails().isEmpty()) {
                modifyTxnConfigurationList.addAll(updatedValue.getModifyAuditDetails().parallelStream()
                        .map(a -> TransactionAuditConfigurationBean.builder()
                                .transactionId(updatedValue.getId())
                                .id(a.getId())
                                .displayName(StringUtils.isEmpty(a.getDisplayName()) ? null : a.getDisplayName())
                                .lookupName(StringUtils.isEmpty(a.getVariable()) ? null : a.getVariable())
                                .userDetailsId(userId)
                                .updatedTime(timestamp)
                                .build())
                        .collect(Collectors.toList()));
            }

            if (updatedValue.getDeleteAuditDetails() != null && !updatedValue.getDeleteAuditDetails().isEmpty()) {
                deleteTxnConfigurationList.addAll(updatedValue.getDeleteAuditDetails());
            }
        }


        TransactionRepo transactionRepo = new TransactionRepo();
        if (!addTxnConfigurationList.isEmpty() || !modifyTxnConfigurationList.isEmpty() || !deleteTxnConfigurationList.isEmpty()) {
            // Getting transaction detail for only one txnId as currently request payload will have only one txn ID
            Transaction transaction = transactionRepo.getTransactionById(account.getIdentifier(), updatedValueList.get(0).getId());
            if(transaction == null) {
                CCCache.INSTANCE.updateCCErrors(1);
                LOGGER.error("Transaction id does not exists in redis cache. TxnId:{}", updatedValueList.get(0).getId());
                return;
            }
            Map<String, String> existingAuditConfigurationsInCache = transaction.getAuditConfigurations();

            if (!addTxnConfigurationList.isEmpty()) {
                dataService.addTxnAuditConfigurationDetails(addTxnConfigurationList, handle);
                for (TransactionAuditConfigurationBean addTxnConfiguration : addTxnConfigurationList) {
                    existingAuditConfigurationsInCache.put(addTxnConfiguration.getLookupName(), addTxnConfiguration.getDisplayName());
                }
                LOGGER.info("Transaction audit configurations added successfully.");
            }

            if (!modifyTxnConfigurationList.isEmpty()) {
                dataService.updateTxnAuditConfigurationDetails(modifyTxnConfigurationList, handle);
                for (TransactionAuditConfigurationBean modifyAuditConfigurationBean : modifyTxnConfigurationList) {
                    TransactionAuditConfigurationBean existingAuditDetailToBeModified = existingTxnAuditConfigurationDetails.parallelStream().filter(f -> f.getId() == modifyAuditConfigurationBean.getId()).findAny().orElse(null);
                    if (existingAuditDetailToBeModified != null ) {
                        if(modifyAuditConfigurationBean.getDisplayName() != null && modifyAuditConfigurationBean.getLookupName() != null ){
                            existingAuditConfigurationsInCache.remove(existingAuditDetailToBeModified.getLookupName());
                            existingAuditConfigurationsInCache.put(modifyAuditConfigurationBean.getLookupName() , modifyAuditConfigurationBean.getDisplayName() );
                        }
                        else if (modifyAuditConfigurationBean.getLookupName() != null) {
                            existingAuditConfigurationsInCache.remove(existingAuditDetailToBeModified.getLookupName());
                            existingAuditConfigurationsInCache.put(modifyAuditConfigurationBean.getLookupName(),existingAuditDetailToBeModified.getDisplayName());
                        }
                        else if (modifyAuditConfigurationBean.getDisplayName() != null) {
                            existingAuditConfigurationsInCache.replace(existingAuditDetailToBeModified.getLookupName(), modifyAuditConfigurationBean.getDisplayName());
                        }
                    }
                }
                LOGGER.info("Transaction audit configurations updated successfully.");

            }

            if (!deleteTxnConfigurationList.isEmpty()) {
                for (Integer id : deleteTxnConfigurationList) {
                    dataService.deleteTxnAuditConfigurationDetailsById(id, handle);
                    TransactionAuditConfigurationBean existingToBeDeleted = existingTxnAuditConfigurationDetails.parallelStream().filter(f -> f.getId() == id).findFirst().orElse(null);
                    if(existingToBeDeleted == null) {
                        continue;
                    }
                    String existingDisplayNameToBeDeleted = existingAuditConfigurationsInCache.get(existingToBeDeleted.getLookupName());
                    if (existingDisplayNameToBeDeleted != null) {
                        existingAuditConfigurationsInCache.remove(existingToBeDeleted.getLookupName());
                    }
                }
            }
            transaction.setAuditConfigurations(existingAuditConfigurationsInCache);
            transactionRepo.updateTransactionDetailsById(account.getIdentifier(), transaction);
            transactionRepo.updateTransactionDetailsByIdentifier(account.getIdentifier(), transaction);
            LOGGER.info("Transaction audit configurations deleted successfully.");
        }
    }
}
