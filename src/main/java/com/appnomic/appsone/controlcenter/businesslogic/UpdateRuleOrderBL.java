package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.RulesBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.RulesDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.AddRulesPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.Rule;
import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class UpdateRuleOrderBL implements BusinessLogic<List<AddRulesPojo>, UtilityBean<List<AddRulesPojo>>, String> {

    List<Rule> rulesDetails = new ArrayList<>();
    private ServiceRepo serviceRepo = new ServiceRepo();

    @Override
    public UtilityBean<List<AddRulesPojo>> clientValidation(RequestObject requestObject) throws ClientException {

        List<AddRulesPojo> updatedValueList;

        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if(requestObject.getBody() == null) {
            log.error(UIMessages.REQUEST_BODY_NULL_EMPTY);
            throw new ClientException(UIMessages.REQUEST_BODY_NULL_EMPTY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String accountIdentifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (accountIdentifier == null || StringUtils.isEmpty(accountIdentifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String serviceIdStr = requestObject.getParams().get(Constants.SERVICE_ID.toLowerCase());
        if (serviceIdStr == null || StringUtils.isEmpty(serviceIdStr)) {
            log.error(UIMessages.EMPTY_SERVICE_IDENTIFIER);
            throw new ClientException(UIMessages.EMPTY_SERVICE_IDENTIFIER);
        }

        try {
            updatedValueList = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestObject.getBody(), new TypeReference<List<AddRulesPojo>>() {
            });
        } catch (JsonProcessingException e) {
            log.error(Constants.JSON_PARSE_ERROR + " : {}", e.getMessage());
            throw new ClientException(Constants.JSON_PARSE_ERROR + " : {}" + e.getMessage());
        }

        if(updatedValueList == null || updatedValueList.isEmpty()) {
            log.error("Invalid input. The rules list is either NULL or empty.");
            throw new ClientException("Invalid input");
        }

        return UtilityBean.<List<AddRulesPojo>>builder()
                .accountIdentifier(accountIdentifier)
                .serviceId(serviceIdStr)
                .pojoObject(updatedValueList)
                .build();
    }

    @Override
    public UtilityBean<List<AddRulesPojo>> serverValidation(UtilityBean<List<AddRulesPojo>> utilityBean) throws ServerException {

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());


        if (account == null) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID + "Provided");
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID + "Provided");
        }

        ControllerBean serviceDetails = new ControllerDataService().getControllerById(Integer.parseInt(utilityBean.getServiceId()), account.getId(), null);
        if (serviceDetails == null) {
            log.error(UIMessages.INVALID_SERVICE, utilityBean.getServiceId());
            throw new ServerException(UIMessages.INVALID_SERVICE);
        }
        String serviceIdentifier = serviceDetails.getIdentifier();

        rulesDetails = serviceRepo.getServiceRules(account.getIdentifier(), serviceDetails.getIdentifier());

        if (rulesDetails.isEmpty()) {
            log.error("No rules available for the provided service: {}", serviceIdentifier);
            throw new ServerException("No rules available for the provided service: " + serviceIdentifier);
        }

        utilityBean.setAccount(account);
        utilityBean.setServiceIdentifier(serviceDetails.getIdentifier());

        return utilityBean;
    }

    @Override
    public String process(UtilityBean<List<AddRulesPojo>> bean) throws DataProcessingException {

        List<AddRulesPojo> updatedValueList = bean.getPojoObject();

        List<RulesBean> rulesBeansList = new ArrayList<>();
        Timestamp date;
        String accIdentifier = bean.getAccount().getIdentifier();
        String serviceIdentifier = bean.getServiceIdentifier();

        try {
            date = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
        } catch (ParseException e) {
            throw new DataProcessingException(e, "Error while fetching current timestamp");
        }

        updatedValueList.forEach(order -> {
            RulesBean rulesBean = new RulesBean();
            rulesBean.setUpdatedTime(date);
            rulesBean.setId(order.getId());
            rulesBean.setOrder(order.getOrder());
            rulesBean.setAccountId(bean.getAccount().getId());
            rulesBeansList.add(rulesBean);

            rulesDetails.forEach(tempRule -> {
                if (tempRule.getId() == order.getId()) {
                    tempRule.setOrder(order.getOrder());
                }
            });
        });


        RulesDataService.updateRulesOrder(rulesBeansList);

        log.info("Order updated for the Rules in percona");

        serviceRepo.updateServiceRules(accIdentifier, serviceIdentifier, rulesDetails);

        log.info("Order updated for the Rules in redis");

        return "rules updated successfully";
    }


}
