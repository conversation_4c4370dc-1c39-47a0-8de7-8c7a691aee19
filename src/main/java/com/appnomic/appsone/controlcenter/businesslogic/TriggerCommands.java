package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.appnomic.appsone.controlcenter.beans.CommandArgumentsBean;
import com.appnomic.appsone.controlcenter.beans.ForensicCommandTriggerWrapperBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentStatusDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CommandDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.RulesDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.SupervisorDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.*;
import com.appnomic.appsone.controlcenter.dao.opensearch.AgentHealthStatusRepo;
import com.appnomic.appsone.controlcenter.dao.redis.AgentRepo;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.dao.redis.MasterDataRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.ForensicCmdTriggeredStatus;
import com.appnomic.appsone.controlcenter.pojo.ForensicCommandsPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.service.QueuePublisher;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import com.heal.configuration.entities.BasicAgentBean;
import com.heal.configuration.pojos.Agent;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.InstanceAttributes;
import com.heal.configuration.pojos.Supervisor;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class TriggerCommands implements BusinessLogic<List<ForensicCommandsPojo>, UtilityBean<List<ForensicCommandsPojo>>, List<ForensicCmdTriggeredStatus>> {
    private static final Logger log = LoggerFactory.getLogger(TriggerCommands.class);

    @Override
    public UtilityBean<List<ForensicCommandsPojo>> clientValidation(RequestObject requestObject) throws ClientException {
        List<ForensicCommandsPojo> triggerJimForensicCommands;

        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (requestObject.getBody() == null) {
            log.error(UIMessages.REQUEST_BODY_NULL_EMPTY);
            throw new ClientException(UIMessages.REQUEST_BODY_NULL_EMPTY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String accountIdString = requestObject.getParams().get(UIMessages.ACCOUNT_IDENTIFIER);

        if (StringUtils.isEmpty(accountIdString)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String serviceIdentifier = requestObject.getParams().get(Constants.SERVICE_IDENTIFIER);

        if (serviceIdentifier == null || serviceIdentifier.trim().isEmpty()) {
            log.error("Service identifier is null or empty.");
            throw new ClientException("Service identifier is null or empty.");
        }

        try {
            triggerJimForensicCommands = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestObject.getBody(), new TypeReference<List<ForensicCommandsPojo>>() {
            });
        } catch (JsonProcessingException e) {
            log.error(Constants.JSON_PARSE_ERROR + " : {}", e.getMessage());
            throw new ClientException(Constants.JSON_PARSE_ERROR + " : {}" + e.getMessage());
        }

        if (triggerJimForensicCommands == null || triggerJimForensicCommands.isEmpty()) {
            log.error("Invalid input. The input forensic commands list is either NULL or empty.");
            throw new ClientException("Invalid input");
        }

        return UtilityBean.<List<ForensicCommandsPojo>>builder()
                .accountIdentifier(accountIdString)
                .serviceId(serviceIdentifier)
                .authToken(authKey)
                .pojoObject(triggerJimForensicCommands)
                .build();
    }

    @Override
    public UtilityBean<List<ForensicCommandsPojo>> serverValidation(UtilityBean<List<ForensicCommandsPojo>> utilityBean) throws ServerException {
        try {
            String accountIdentifier = utilityBean.getAccountIdentifier();
            AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

            if (account == null) {
                log.error("Account identifier is invalid");
                throw new ServerException("Account identifier is invalid");
            }

            String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

            if (userId == null) {
                log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
                throw new ServerException("Error while extracting user details from authorization token");
            }

            utilityBean.setUserId(userId);

            List<ForensicCommandsPojo> inputCommands = utilityBean.getPojoObject();

            Map<Integer, List<CommandDetailArgumentBean>> commandIdVsArguments = CommandDataService.getAllCommandArguments()
                    .stream().collect(Collectors.groupingBy(CommandDetailArgumentBean::getCommandId));

            MasterDataRepo masterDataRepo = new MasterDataRepo();
            List<com.heal.configuration.pojos.ViewTypes> types = masterDataRepo.getTypes();

            if (types.isEmpty()) {
                log.error("Obtained empty result from Redis when queried for view types");
                throw new ServerException("Obtained empty result from Redis when queried for view types");
            }

            //Checking if the input requests are intended for Jim Agent or Supervisor Agent.
            if(inputCommands.get(0).getAgentType().equalsIgnoreCase("Supervisor")) {
                List<SupervisorBean> supervisorBeans = new SupervisorDataService().getAccountWiseSupervisorDetailsWithGlobalAccount(account.getId(), null);

                if(supervisorBeans.isEmpty()) {
                    log.error("Obtained empty results from data source when queried for supervisors of account {}", accountIdentifier);
                    throw new ServerException(String.format("Obtained empty results from Redis when queried for supervisors of account %s", accountIdentifier));
                }

                Map<String, SupervisorBean> supervisorIdentifierMap = supervisorBeans.stream().filter(SupervisorBean::isStatus)
                        .collect(Collectors.toMap(SupervisorBean::getIdentifier, Function.identity()));

                List<CommandDetailsBean> commandDetailsList = CommandDataService.getCommandDetails();

                if(commandDetailsList.isEmpty()) {
                    log.error("Obtained empty results from DS when queried for command details.");
                    throw new ServerException("Obtained empty results from DS when queried for command details.");
                }

                Map<String, Integer> commandDetails = commandDetailsList.stream()
                        .collect(Collectors.toMap(CommandDetailsBean::getIdentifier, CommandDetailsBean::getId));

                com.heal.configuration.pojos.ViewTypes scriptMetaDataType = types.stream().filter(f -> f.getSubTypeName().equalsIgnoreCase(Constants.SCRIPT_METADATA_ARGS))
                        .findAny().orElse(null);

                if(scriptMetaDataType == null) {
                    log.error("Could not find SCRIPT_Parameter_Type 'METADATA_ARGS' in view_types when queried from Redis");
                    throw new ServerException("Could not find SCRIPT_Parameter_Type 'METADATA_ARGS' in view_types when queried from Redis");
                }

                List<ForensicCommandsPojo> validatedInputCommands = inputCommands.parallelStream()
                        .map(request -> {
                            try {
                                //Validating input request supervisor against the supervisor details from Redis
                                if (!supervisorIdentifierMap.containsKey(request.getAgentIdentifier())) {
                                    log.error("Could not find provided supervisor identifier {} in the request in the Redis.", request.getAgentIdentifier());
                                    throw new ServerException(String.format("Could not find provided supervisor identifier %s in the request in the Redis.", request.getAgentIdentifier()));
                                }

                                //Validating input request command against the configured commands in the DB
                                if (!commandDetails.containsKey(request.getCommandIdentifier())) {
                                    log.error("Request command {} is not part of the commands configured in DataSource.", request.getCommandIdentifier());
                                    throw new ServerException(String.format("Request command %s is not part of the commands configured in DataSource.", request.getCommandIdentifier()));
                                }

                                //Validating input request command id against the configured commands in the DB
                                if(commandDetails.get(request.getCommandIdentifier()) != request.getCommandId()) {
                                    log.error("Request command id {} and identifier {} do not match as per data source.",request.getCommandId(), request.getCommandIdentifier());
                                    throw new ServerException(String.format("Request command id %s and identifier %s do not match as per data source.", request.getCommandId(), request.getCommandIdentifier()));
                                }

                                //Validating input request command arguments against the configured arguments in the DB
                                List<CommandArgumentsBean> requestCommandArguments = request.getCommandArguments();

                                if (requestCommandArguments == null || requestCommandArguments.isEmpty()) {
                                    log.debug("No commands arguments are provided in the request. Continuing with next iteration");
                                    return request;
                                }

                                Map<String, Integer> cmdDetailArgsKeyAndTypeMap = commandIdVsArguments.get(request.getCommandId()).stream()
                                        .collect(Collectors.toMap(CommandDetailArgumentBean::getArgumentKey, CommandDetailArgumentBean::getArgumentTypeId));

                                List<CommandArgumentsBean> unmatchedRequestArguments = requestCommandArguments.stream()
                                        .filter(f -> !cmdDetailArgsKeyAndTypeMap.containsKey(f.getKey()))
                                        .collect(Collectors.toList());

                                if (!unmatchedRequestArguments.isEmpty()) {
                                    log.error("Arguments {} provided in the input request are not mapped to the command {}", unmatchedRequestArguments, request.getCommandId());
                                    throw new ServerException(String.format("Arguments %s provided in the input request are not mapped to the command %s", unmatchedRequestArguments, request.getCommandId()));
                                }

                                //Check if the argument is supposed to be populated in argumentsMap or in metaData
                                int subTypeId = scriptMetaDataType.getSubTypeId();

                                Map<String, String> metaData = new HashMap<>();
                                Map<String, String> argumentsMap = new HashMap<>();

                                requestCommandArguments.forEach(arguments -> {
                                    if (cmdDetailArgsKeyAndTypeMap.containsKey(arguments.getKey())
                                            && cmdDetailArgsKeyAndTypeMap.get(arguments.getKey()) == subTypeId) {
                                        metaData.put(arguments.getKey(), arguments.getValue());
                                    } else {
                                        argumentsMap.put(arguments.getKey(), arguments.getValue());
                                    }
                                });
                                request.setArgumentsMap(argumentsMap);
                                request.setMetaData(metaData);
                                return request;
                            } catch (ServerException e) {
                                log.error("Request validation failed at server level, Examine logs for more details.", e);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                if(inputCommands.size() != validatedInputCommands.size()) {
                    log.error("Mismatch between input request commands and valid commands. Expected: {}, Actual: {}. Please examine logs for more information.",
                            inputCommands.size(), validatedInputCommands.size());
                    throw new ServerException(String.format("Mismatch between input commands and valid commands. Expected: %s, Actual: %s.", inputCommands.size(), validatedInputCommands.size()));
                }

                utilityBean.setPojoObject(validatedInputCommands);
                return utilityBean;
            }

            // Below code validates requests to ensure they are specifically for the JIM agent type.
            ServiceRepo serviceRepo = new ServiceRepo();
            InstanceRepo instanceRepo = new InstanceRepo();

            String serviceIdentifier = utilityBean.getServiceId();
            List<BasicAgentBean> agentsByServiceIdentifier = serviceRepo.getAgentsByServiceIdentifier(accountIdentifier, serviceIdentifier);
            if (agentsByServiceIdentifier.isEmpty()) {
                log.error("Obtained NULL from Redis when queried for service agents for the service {} of account {}", serviceIdentifier, accountIdentifier);
                throw new ServerException(String.format("Obtained Empty results from Redis when queried for service agents for the service %s of account %s", serviceIdentifier, accountIdentifier));
            }

            List<String> jimAgentIdentifiers = agentsByServiceIdentifier.parallelStream()
                    .filter(f -> f.getStatus() == 1)
                    .filter(f -> f.getType().equals(Constants.JIM_AGENT_SUB_TYPE))
                    .map(BasicEntity::getIdentifier).distinct()
                    .collect(Collectors.toList());

            if (jimAgentIdentifiers.isEmpty()) {
                log.error("None of the agents mapped to service {} of account {} are JIM agents.", serviceIdentifier, accountIdentifier);
                throw new ServerException(String.format("None of the agents mapped to service %s of account %s are JIM agents.", serviceIdentifier, accountIdentifier));
            }

            //Get command details and their respective arguments
            Map<String, com.heal.configuration.pojos.ViewTypes> viewTypesMap = types.stream()
                    .filter(f -> f.getSubTypeName().equals(Constants.JIM_AGENT_SUB_TYPE) || f.getTypeName().equals(Constants.FORENSIC_COMMAND_TYPE))
                    .collect(Collectors.toMap(com.heal.configuration.pojos.ViewTypes::getTypeName, Function.identity()));

            int jimSubTypeId = viewTypesMap.get(Constants.AGENT_TYPE).getSubTypeId();
            int forensicTypeId = viewTypesMap.get(Constants.FORENSIC_COMMAND_TYPE).getTypeId();

            List<CommandDetailsBean> forensicActionDetailsByAgentType = CommandDataService.getCommandDetailsByAgentType(jimSubTypeId, forensicTypeId, null);
            if (forensicActionDetailsByAgentType.isEmpty()) {
                log.error("Commands unavailable for JIM agent");
                throw new ServerException("Commands unavailable for JIM agent");
            }

            List<Integer> commandsByAgentType = forensicActionDetailsByAgentType
                    .parallelStream().map(CommandDetailsBean::getId)
                    .collect(Collectors.toList());

            //Validate the incoming forensic action
            List<ForensicCommandsPojo> validCommands = inputCommands.parallelStream()
                    .map(command ->
                    {
                        try {
                            Map<String, String> argumentsMap = new HashMap<>();
                            String compInstanceIdentifier = command.getCompInstanceIdentifier();
                            List<CommandArgumentsBean> requestCommandArguments = command.getCommandArguments();

                            if (!commandsByAgentType.contains(command.getCommandId())) {
                                log.error("Requested forensic action {} is not part of the actions configured for JIM agent", command.getCommandIdentifier());
                                throw new ServerException(String.format("Requested forensic action %s is not part of the actions configured for JIM agent", command.getCommandIdentifier()));
                            }

                            if (!jimAgentIdentifiers.contains(command.getAgentIdentifier())) {
                                log.error("Agent identifier provided in the request do not belong to the service. " +
                                        "Details:- Invalid agent identifier {}, Service {}", command.getAgentIdentifier(), serviceIdentifier);
                                throw new ServerException(String.format("Agent identifier %s is not mapped to the service %s.", command.getAgentIdentifier(), serviceIdentifier));
                            }

                            List<CommandDetailArgumentBean> cmdSpecificArgs = commandIdVsArguments.getOrDefault(command.getCommandId(), new ArrayList<>());

                            //If cmdArgs exist for this commandId then check if input command arguments size and cmdArgs size are same.
                            if (command.getCommandArguments().size() != cmdSpecificArgs.size()) {
                                log.error("Invalid number of command arguments provided in the request for the command {}. Please ensure all the command arguments are provided in the request.", command.getCommandId());
                                throw new ServerException(String.format("Invalid number of command arguments provided in the request for the command %s. Please ensure all the command arguments are provided in the request.", command.getCommandId()));
                            }

                            List<InstanceAttributes> attributes = instanceRepo.getAttributes(accountIdentifier, compInstanceIdentifier);

                            //Segregating arguments based on the placeholder value
                            Map<Boolean, List<CommandArgumentsBean>> placeholderBasedCommandArguments = requestCommandArguments.stream()
                                    .collect(Collectors.partitioningBy(bean -> {
                                        String placeHolder = bean.getIsPlaceHolder().toLowerCase();
                                        return placeHolder.equals("1") || placeHolder.equals("true");
                                    }));

                            //Set command arguments without placeholders in argument Map.
                            List<CommandArgumentsBean> cmdArgumentsWithoutPlaceholders = placeholderBasedCommandArguments.get(false);
                            if (cmdArgumentsWithoutPlaceholders != null && !cmdArgumentsWithoutPlaceholders.isEmpty()) {
                                cmdArgumentsWithoutPlaceholders.forEach(arguments -> argumentsMap.put(arguments.getKey(), arguments.getValue()));
                            }

                            //Set command arguments with placeholders in argument map by retrieving its value from instance attributes redis key.
                            List<CommandArgumentsBean> cmdArgumentsWithPlaceholders = placeholderBasedCommandArguments.get(true);
                            if (cmdArgumentsWithPlaceholders != null && !cmdArgumentsWithPlaceholders.isEmpty()) {
                                Map<String, String> instanceAttributesKeyValueMap = attributes.stream()
                                        .collect(Collectors.toMap(InstanceAttributes::getAttributeName, InstanceAttributes::getAttributeValue));

                                cmdArgumentsWithPlaceholders.forEach(argument -> {
                                    String value = instanceAttributesKeyValueMap.get(argument.getKey());
                                    if (value == null) {
                                        argumentsMap.put(argument.getKey(), argument.getValue());
                                    } else {
                                        argumentsMap.put(argument.getKey(), value);
                                    }
                                });
                            }
                            command.setArgumentsMap(argumentsMap);
                            return command;
                        } catch (ServerException e) {
                            log.error("Request validation failed at server level, Examine logs for more details.", e);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if(inputCommands.size() != validCommands.size()) {
                log.error("Mismatch between input commands and valid commands. Expected: {}, Actual: {}. Please examine logs for more information.",
                        inputCommands.size(), validCommands.size());
                throw new ServerException(String.format("Mismatch between input commands and valid commands. Expected: %s, Actual: %s.", inputCommands.size(), validCommands.size()));
            }

            utilityBean.setPojoObject(validCommands);
            return utilityBean;
        } catch (Exception e) {
            log.error("Error occurred while validating the input request to trigger forensic commands", e);
            throw new ServerException("Error occurred while validating the input request to trigger forensic commands");
        }
    }

    @Override
    public List<ForensicCmdTriggeredStatus> process(UtilityBean<List<ForensicCommandsPojo>> commands) throws DataProcessingException {
        try {
            String userId = commands.getUserId();
            AgentRepo agentRepo = new AgentRepo();
            String accountIdentifier = commands.getAccountIdentifier();
            List<ForensicCommandsPojo> triggerCommandList = commands.getPojoObject();
            List<ForensicCmdTriggeredStatus> forensicCommandTriggeredStatusBeanList;
            List<CommandRequestProtos.CommandRequest> commandRequests = new ArrayList<>();

            Map<String, Supervisor> supervisorsMap = agentRepo.getSupervisors(accountIdentifier)
                    .stream().collect(Collectors.toMap(Supervisor::getIdentifier, Function.identity()));

            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            try {
                forensicCommandTriggeredStatusBeanList = dbi.inTransaction((conn, status) -> {
                    List<ForensicCmdTriggeredStatus> beansList = new ArrayList<>();

                    for (ForensicCommandsPojo triggerCommand : triggerCommandList) {
                        ForensicCommandTriggerWrapperBean wrapperBean;
                        if (triggerCommand.getAgentType().equalsIgnoreCase(Constants.JIM_AGENT_SUB_TYPE)) {
                            Agent agentDetails = agentRepo.getAgentDetails(triggerCommand.getAgentIdentifier());
                            if(agentDetails == null) {
                                log.error("Agent details for identifier {} not found in the Redis.", triggerCommand.getAgentIdentifier());
                                throw new DataProcessingException(String.format("Agent details for identifier %s not found in the Redis.", triggerCommand.getAgentIdentifier()));
                            }
                            wrapperBean = processCommand(accountIdentifier, agentDetails, triggerCommand, userId, conn);
                        }
                        else {
                            Supervisor supervisor = supervisorsMap.get(triggerCommand.getAgentIdentifier());
                            if(supervisor == null) {
                                log.error("Supervisor details for identifier {} not found in the supervisor map, which is populated from Redis. Available keys: {}",
                                        triggerCommand.getAgentIdentifier(), supervisorsMap.keySet());
                                throw new DataProcessingException(String.format("Supervisor details for identifier %s not found in the supervisor map, which is populated from Redis. Available keys: %s",
                                        triggerCommand.getAgentIdentifier(), supervisorsMap.keySet()));
                            }
                            wrapperBean = processSupervisorCommand(accountIdentifier, supervisor, triggerCommand, userId, conn);
                        }
                        beansList.add(wrapperBean.getForensicCmdTriggeredStatus());
                        commandRequests.add(wrapperBean.getCommandRequest());
                    }
                    log.info("Number of command requests are pushing into RMQ is {}.", commandRequests.size());

                    for (CommandRequestProtos.CommandRequest commandRequest : commandRequests) {
                        QueuePublisher.sendAgentCommandMessage(commandRequest);
                    }
                    return beansList;
                });
            } catch (Exception e) {
                log.error("Error while triggering command. Reason: ", e);
                if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                    throw (DataProcessingException) Throwables.getRootCause(e);
                } else {
                    throw e;
                }
            }

            return forensicCommandTriggeredStatusBeanList;
        } catch (DataProcessingException e) {
            log.error("CommandException encountered while inserting command details. Details: ", e);
            throw new DataProcessingException("CommandException encountered while adding command");
        }
    }

    // The function 'processCommand' processes a list of trigger commands. The list contains commands associated with a specific agent.
    // Each command entry in the list corresponds to a unique instance identifier, while the rest of the agent details
    // remain consistent across all entries. This means that for an agent mapped to multiple instances, the list will
    // include multiple command objects with identical agent information but distinct instance identifiers.
    private ForensicCommandTriggerWrapperBean processCommand(String accountIdentifier, Agent agentDetails, ForensicCommandsPojo agentCommandPojo,
                                                             String userId, Handle conn) throws DataProcessingException {

        int physicalAgentId = agentDetails.getPhysicalAgentId();
        Map<String, String> argumentsMap = agentCommandPojo.getArgumentsMap();
        String physicalAgentIdentifier = agentDetails.getPhysicalAgentIdentifier();
        String compInstanceIdentifier = agentCommandPojo.getCompInstanceIdentifier();

        AgentHealthStatusRepo healthStatusRepo = new AgentHealthStatusRepo();

        log.info("Processing command Identifier [{}] triggered for agent [{}]", agentCommandPojo.getCommandIdentifier(), physicalAgentId);

        CommandTriggerBean commandTriggerBean = new CommandTriggerBean();
        commandTriggerBean.setCommandStatus(1);
        commandTriggerBean.setUserDetailsId(userId);
        commandTriggerBean.setAgentId(agentDetails.getId());
        commandTriggerBean.setPhysicalAgentIdentifier(physicalAgentId);
        commandTriggerBean.setCommandId(agentCommandPojo.getCommandId());
        commandTriggerBean.setCommandJobId(String.valueOf(UUID.randomUUID()));
        commandTriggerBean.setTriggerTime(new Timestamp(new Date().getTime()));

        int agentTypeId = agentDetails.getTypeId();
        String agentType = agentDetails.getTypeName();

        CommandDetailsBean commandDetailsBean = CommandDataService.getCommandDetail(agentCommandPojo.getCommandId(), conn);
        if (commandDetailsBean == null) {
            log.error("Default/Selected command not found for provided agentType [{}] to process the request.", agentType);
            throw new DataProcessingException("Default/Selected command not found to process the request.");
        }

        argumentsMap.put("Command", commandDetailsBean.getName());

        Map<String, String> metaData = new HashMap<>();
        metaData.put("accountId", accountIdentifier);
        metaData.put("instanceIdentifier", compInstanceIdentifier); //Setting the instance identifier
        metaData.put("agentType", agentCommandPojo.getAgentType()); //This flag is utilized in the supervisor controller to independently process Jim forensic actions.
        metaData.put("commandId", commandDetailsBean.getIdentifier());
        metaData.put("status", Constants.FORENSIC_ACTION_TRIGGER_STATUS); //Setting the status of jim forensic action to 'in-progress'

        try {
            AgentStatusDataService.updateJobId(physicalAgentIdentifier, commandTriggerBean.getCommandJobId(), userId, conn);
        } catch (ControlCenterException e) {
            throw new DataProcessingException("Exception encountered while updating commandJobID for agent");
        }

        ViewTypes commandTypeTypes = MasterCache.getMstTypeById(commandDetailsBean.getCommandTypeId());
        if (commandTypeTypes == null) {
            log.error("CommandType [{}] is invalid", commandDetailsBean.getCommandTypeId());
            throw new DataProcessingException("commandType is invalid");
        }

        String commandType = commandTypeTypes.getTypeName();
        String commandOutputType = RulesDataService.getNameFromMSTSubType(commandDetailsBean.getOutputTypeId());

        CommandRequestProtos.Command command =
                CommandRequestProtos.Command.newBuilder()
                        .setRetryNumber(3)
                        .setSupervisorCtrlTTL(300)
                        .setCommandType(commandType)
                        .putAllArguments(argumentsMap)
                        .setCommandExecType("Execute")
                        .setCommand(commandDetailsBean.getCommandName())
                        .setCommandJobId(commandTriggerBean.getCommandJobId())
                        .setCommandTimeout(commandDetailsBean.getTimeOutInSecs())
                        .setCommandOutputType((null == commandOutputType) ? "" : commandOutputType)
                        .build();

        //Adding list of instances in place of supervisorIdentifiers
        CommandRequestProtos.CommandRequest commandDetails = CommandRequestProtos.CommandRequest.newBuilder().addAllSupervisorIdentifiers(Collections.singletonList(compInstanceIdentifier))
                .addCommands(command)
                .setAgentType(agentType)
                .putAllMetadata(metaData)
                .setTriggerSource("ControlCenter")
                .setTriggerTime(new Date().getTime())
                .setViolationTime(new Date().getTime())
                .setAgentIdentifier(physicalAgentIdentifier)
                .setUserDetailsID(commandDetailsBean.getUserDetails())
                .build();

        AgentStatusDataService.addAgentCommandTrigger(commandTriggerBean, conn);

        try {
            healthStatusRepo.insertCommandDetails(accountIdentifier, physicalAgentIdentifier, String.valueOf(commandTriggerBean.getCommandId()),
                    commandTriggerBean.getCommandJobId(), Constants.FORENSIC_ACTION_TRIGGER_STATUS, System.currentTimeMillis(), metaData);
        } catch (ControlCenterException e) {
            log.error("Error while pushing data into Opensearch index {}", Constants.INDEX_PREFIX_HEAL_AGENT_COMMANDS, e);
            throw new DataProcessingException(String.format("Error while pushing data into Opensearch index %s", Constants.INDEX_PREFIX_HEAL_AGENT_COMMANDS));
        }

        ForensicCmdTriggeredStatus bean = new ForensicCmdTriggeredStatus();
        bean.setAgentTypeId(agentTypeId);
        bean.setInstanceIdentifier(Collections.singletonList(compInstanceIdentifier));
        bean.setAgentIdentifier(agentDetails.getIdentifier());
        bean.setCommandJobId(commandTriggerBean.getCommandJobId());
        bean.setAgentId(commandTriggerBean.getPhysicalAgentIdentifier());
        bean.setPhysicalAgentIdentifier(agentDetails.getPhysicalAgentIdentifier());

        log.info("Command details updated for agent [{}]", physicalAgentIdentifier);

        return new ForensicCommandTriggerWrapperBean(bean, commandDetails);
    }

    // The function 'processSupervisorCommand' processes a list of trigger commands to supervisor.
    private ForensicCommandTriggerWrapperBean processSupervisorCommand(String accountIdentifier, Supervisor supervisorDetails, ForensicCommandsPojo agentCommandPojo,
                                                             String userId, Handle conn) throws DataProcessingException {

        Map<String, String> inputMetaData = agentCommandPojo.getMetaData() == null ? new HashMap<>() : agentCommandPojo.getMetaData();
        Map<String, String> argumentsMap = agentCommandPojo.getArgumentsMap() == null ? new HashMap<>() : agentCommandPojo.getArgumentsMap();

        log.info("Processing command Identifier [{}] triggered for supervisor agent [{}]", agentCommandPojo.getCommandIdentifier(), supervisorDetails.getIdentifier());

        CommandTriggerBean commandTriggerBean = new CommandTriggerBean();
        commandTriggerBean.setCommandStatus(1);
        commandTriggerBean.setUserDetailsId(userId);
        commandTriggerBean.setAgentId(supervisorDetails.getId());
        commandTriggerBean.setCommandId(agentCommandPojo.getCommandId());
        commandTriggerBean.setCommandJobId(String.valueOf(UUID.randomUUID()));
        commandTriggerBean.setTriggerTime(new Timestamp(new Date().getTime()));

        int agentTypeId = supervisorDetails.getSupervisorTypeId();

        CommandDetailsBean commandDetailsBean = CommandDataService.getCommandDetail(agentCommandPojo.getCommandId(), conn);
        if (commandDetailsBean == null) {
            log.error("Default/Selected command not found for provided agentType [{}] to process the request.", agentCommandPojo.getAgentType());
            throw new DataProcessingException("Default/Selected command not found to process the request.");
        }

        inputMetaData.put(Constants.METADATA_ACCOUNT_ID, accountIdentifier);
        inputMetaData.put(Constants.METADATA_AGENT_TYPE, agentCommandPojo.getAgentType());
        inputMetaData.put(Constants.METADATA_COMMAND_ID, commandDetailsBean.getIdentifier());
        inputMetaData.put(Constants.METADATA_STATUS, Constants.FORENSIC_ACTION_TRIGGER_STATUS);

        ViewTypes commandTypeTypes = MasterCache.getMstTypeById(commandDetailsBean.getCommandTypeId());
        if (commandTypeTypes == null) {
            log.error("CommandType [{}] is invalid", commandDetailsBean.getCommandTypeId());
            throw new DataProcessingException("commandType is invalid");
        }

        String commandType = commandTypeTypes.getTypeName();

        ViewTypes mstSubTypeForSubTypeId = MasterCache.getMstSubTypeForSubTypeId(commandDetailsBean.getOutputTypeId());
        if(mstSubTypeForSubTypeId == null) {
            log.error("OutputTypeId [{}] is invalid", commandDetailsBean.getOutputTypeId());
            throw new DataProcessingException("OutputTypeId is invalid");
        }

        String commandOutputType = mstSubTypeForSubTypeId.getSubTypeName();

        CommandRequestProtos.Command command =
                CommandRequestProtos.Command.newBuilder()
                        .setRetryNumber(3)
                        .setSupervisorCtrlTTL(300)
                        .setCommandType(commandType)
                        .putAllArguments(argumentsMap)
                        .setCommandExecType("Execute")
                        .setCommand(commandDetailsBean.getCommandName())
                        .setCommandJobId(commandTriggerBean.getCommandJobId())
                        .setCommandTimeout(commandDetailsBean.getTimeOutInSecs())
                        .setCommandOutputType((null == commandOutputType) ? "" : commandOutputType)
                        .build();

        CommandRequestProtos.CommandRequest commandDetails = CommandRequestProtos.CommandRequest.newBuilder().addAllSupervisorIdentifiers(Collections.singletonList(supervisorDetails.getIdentifier()))
                .addCommands(command)
                .setAgentType(agentCommandPojo.getAgentType()) //using AgentType from the input request which is 'Supervisor'
                .putAllMetadata(inputMetaData)
                .setTriggerSource("ControlCenter")
                .setTriggerTime(new Date().getTime())
                .setViolationTime(new Date().getTime())
                .setAgentIdentifier(supervisorDetails.getIdentifier())
                .setUserDetailsID(commandDetailsBean.getUserDetails())
                .build();

        ForensicCmdTriggeredStatus bean = new ForensicCmdTriggeredStatus();
        bean.setAgentTypeId(agentTypeId);
        bean.setAgentIdentifier(supervisorDetails.getIdentifier());
        bean.setCommandJobId(commandTriggerBean.getCommandJobId());
        bean.setAgentId(commandTriggerBean.getPhysicalAgentIdentifier());

        log.info("Command details updated for agent [{}]", supervisorDetails.getIdentifier());

        return new ForensicCommandTriggerWrapperBean(bean, commandDetails);
    }
}


