package com.appnomic.appsone.controlcenter.businesslogic.connectors;

import com.appnomic.appsone.controlcenter.common.ConnectorConstants;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.connectors.ConnectorDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.connectors.KubernetesDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.ApplicationToKpiMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.DomainToHealKpiMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.HealKpi;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.PrometheusHealKpis;
import com.appnomic.appsone.controlcenter.exceptions.FileUploadException;
import com.appnomic.appsone.controlcenter.pojo.connectors.*;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class KubernetesConnectorBL {
    public List<KubernetesConfiguration> getKubernetesConfiguration(File fileName) {
        List<KubernetesConfiguration> kubernetesConfigurations = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro;
                if (sheet.getRow(i) != null) ro = sheet.getRow(i);
                else continue;
                if (ro.getCell(ConnectorConstants.KUB_CONF_ID_IDX) == null) break;
//                    ro.getCell(ConnectorConstants.APPD_APPLICATION_KPI_IDS_IDX).setCellType(Cell.CELL_TYPE_STRING);
                KubernetesConfiguration kubernetesConfiguration = KubernetesConfiguration.builder()
                        .id((int) ro.getCell(ConnectorConstants.KUB_CONF_ID_IDX).getNumericCellValue())
                        .fileName(ro.getCell(ConnectorConstants.KUB_CONF_FILE_NAME_IDX).getStringCellValue())
                        .kubernetesConfig(ro.getCell(ConnectorConstants.KUB_CONF_CONFIG_IDX).getStringCellValue())
                        .build();

                log.info("this is no of row in system {}", ro.getPhysicalNumberOfCells());
                kubernetesConfigurations.add(kubernetesConfiguration);

            }
        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Kubernetes Configuration Data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading the Kubernetes Configuration Data from excel file");
        }
        return kubernetesConfigurations;
    }

    public List<KubernetesHealAccessToken> getKubernetesHealAccessToken(File fileName) {
        List<KubernetesHealAccessToken> healAccessTokens = new ArrayList<>();
        healAccessTokens.add(KubernetesHealAccessToken.builder()
                .id(1)
                .hostPort(ConfProperties.getString(Constants.KEYCLOAK_IP) + ":" + ConfProperties.getString(Constants.KEYCLOAK_PORT))
                .userName(ConfProperties.getString(Constants.KEYCLOAK_USER))
                .password(CommonUtils.getDecryptedData(ConfProperties.getString(Constants.KEYCLOAK_PWD)))
                .clientId(Constants.KEYCLOAK_CLIENT_ID_DEFAULT)
                .grantType(Constants.KEYCLOAK_GRANT_TYPE_DEFAULT)
                .build());
        return healAccessTokens;
    }

    public List<KubernetesHealInstanceCreationPayload> getKubernetesHealInstanceCreationPayload(File fileName) {
        List<KubernetesHealInstanceCreationPayload> kubernetesHealInstanceCreationPayloads = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(1);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro;
                if (sheet.getRow(i) != null) ro = sheet.getRow(i);
                else continue;
                if (ro.getCell(ConnectorConstants.KUB_HICP_ID_IDX) == null) break;
//                    ro.getCell(ConnectorConstants.APPD_APPLICATION_KPI_IDS_IDX).setCellType(Cell.CELL_TYPE_STRING);
                KubernetesHealInstanceCreationPayload kubernetesHealInstanceCreationPayload = KubernetesHealInstanceCreationPayload.builder()
                        .id((int) ro.getCell(ConnectorConstants.KUB_HICP_ID_IDX).getNumericCellValue())
                        .image(ro.getCell(ConnectorConstants.KUB_HICP_IMAGE_IDX).getStringCellValue())
                        .namespace(ro.getCell(ConnectorConstants.KUB_HICP_NAMESPACE_IDX).getStringCellValue())
                        .hostInstanceCompName(ro.getCell(ConnectorConstants.KUB_HICP_HI_COMP_NAME_IDX).getStringCellValue())
                        .hostInstanceCompVersion(String.valueOf(ro.getCell(ConnectorConstants.KUB_HICP_HI_COMP_VER_IDX).getNumericCellValue()))
                        .hostInstanceServiceIdentifier(ro.getCell(ConnectorConstants.KUB_HICP_HI_SERVICE_ID_IDX).getStringCellValue())
                        .componentInstanceComponentName(ro.getCell(ConnectorConstants.KUB_HICP_CI_COMP_NAME_IDX).getStringCellValue())
                        .componentInstanceComponentVersion(String.valueOf(ro.getCell(ConnectorConstants.KUB_HICP_CI_COMP_VER_IDX).getNumericCellValue()))
                        .componentInstanceServiceIdentifier(ro.getCell(ConnectorConstants.KUB_HICP_CI_SERVICE_ID_IDX).getStringCellValue())
                        .build();

                log.info("this is no of row in system {}", ro.getPhysicalNumberOfCells());
                kubernetesHealInstanceCreationPayloads.add(kubernetesHealInstanceCreationPayload);

            }
        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Kubernetes Heal Access Token Data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading the Kubernetes Heal Access Token Data from excel file");
        }
        return kubernetesHealInstanceCreationPayloads;
    }

    public List<PrometheusKpiResponsePath> getPrometheusKpiResponsePath(File fileName) {
        List<PrometheusKpiResponsePath> prometheusKpiResponsePaths = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro;
                if (sheet.getRow(i) != null) ro = sheet.getRow(i);
                else continue;
                if (ro.getCell(ConnectorConstants.KUB_PROM_KPI_RESPONSE_PATH_ID_IDX) == null) break;
//                    ro.getCell(ConnectorConstants.APPD_APPLICATION_KPI_IDS_IDX).setCellType(Cell.CELL_TYPE_STRING);
                PrometheusKpiResponsePath prometheusKpiResponsePath = PrometheusKpiResponsePath.builder()
                        .id((int) ro.getCell(ConnectorConstants.KUB_PROM_KPI_RESPONSE_PATH_ID_IDX).getNumericCellValue())
                        .hostOrComponent(ro.getCell(ConnectorConstants.KUB_PROM_KPI_RESPONSE_PATH_HOSTORCOMP_IDX).getStringCellValue())
                        .serviceName(ro.getCell(ConnectorConstants.KUB_PROM_KPI_RESPONSE_PATH_SERVICE_NAME_IDX).getStringCellValue())
                        .kpiName(ro.getCell(ConnectorConstants.KUB_PROM_KPI_RESPONSE_PATH_KPI_NAME_IDX).getStringCellValue())
                        .podName(ro.getCell(ConnectorConstants.KUB_PROM_KPI_RESPONSE_PATH_POD_NAME_IDX).getStringCellValue())
                        .podNamespace(ro.getCell(ConnectorConstants.KUB_PROM_KPI_RESPONSE_PATH_POD_NAMESPACE_IDX).getStringCellValue())
                        .build();

                log.info("this is no of row in system {}", ro.getPhysicalNumberOfCells());
                prometheusKpiResponsePaths.add(prometheusKpiResponsePath);
            }

        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Prometheus Kpi Response Path Data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading the Prometheus Kpi Response Path Data from excel file");
        }
        return prometheusKpiResponsePaths;
    }

    public List<PrometheusApplication> getPrometheusApplication(File fileName) {
        List<PrometheusApplication> prometheusApplications = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro;
                if (sheet.getRow(i) != null) ro = sheet.getRow(i);
                else continue;
                if (ro.getCell(ConnectorConstants.KUB_PROM_APPLICATION_ID_IDX) == null) break;
//                    ro.getCell(ConnectorConstants.APPD_APPLICATION_KPI_IDS_IDX).setCellType(Cell.CELL_TYPE_STRING);
                PrometheusApplication prometheusApplication = PrometheusApplication.builder()
                        .id((int) ro.getCell(ConnectorConstants.KUB_PROM_APPLICATION_ID_IDX).getNumericCellValue())
                        .hostPort(ro.getCell(ConnectorConstants.KUB_PROM_APPLICATION_HOST_PORT_IDX).getStringCellValue())
                        .userName(ro.getCell(ConnectorConstants.KUB_PROM_APPLICATION_USERNAME_IDX).getStringCellValue())
                        .password(ro.getCell(ConnectorConstants.KUB_PROM_APPLICATION_PASSWORD_IDX).getStringCellValue())
                        .build();

                log.info("this is no of row in system {}", ro.getPhysicalNumberOfCells());
                prometheusApplications.add(prometheusApplication);

            }
        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Prometheus Applications Data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading the Prometheus Application Data from excel file");
        }
        return prometheusApplications;
    }

    public List<PrometheusKpis> getPrometheusKpis(File fileName) {
        List<PrometheusKpis> prometheusKpis = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro;
                if (sheet.getRow(i) != null) ro = sheet.getRow(i);
                else continue;
                if (ro.getCell(ConnectorConstants.KUB_PROM_KPI_ID_IDX) == null) break;
                PrometheusKpis prometheusKpi = PrometheusKpis.builder()
                        .id((int) ro.getCell(ConnectorConstants.KUB_PROM_KPI_ID_IDX).getNumericCellValue())
                        .metric(ro.getCell(ConnectorConstants.KUB_PROM_KPI_METRIC_IDX).getStringCellValue())
                        .hostOrComponent(ro.getCell(ConnectorConstants.KUB_PROM_KPI_HOSTORCOMP_IDX).getStringCellValue())
                        .healKpiId((int) ro.getCell(ConnectorConstants.KUB_PROM_KPI_HEAL_KPI_ID_IDX).getNumericCellValue())
                        .healKpiName(ro.getCell(ConnectorConstants.KUB_PROM_KPI_HEAL_KPI_NAME_IDX).getStringCellValue())
                        .healKpiIdentifier(ro.getCell(ConnectorConstants.KUB_PROM_KPI_HEAL_KPI_IDENTIFIER_IDX).getStringCellValue())
                        .isGroupKpi((int) ro.getCell(ConnectorConstants.KUB_PROM_KPI_IS_GROUP_KPI_IDX).getNumericCellValue())
                        .groupName(ro.getCell(ConnectorConstants.KUB_PROM_KPI_GROUP_NAME_IDX).getStringCellValue())
                        .applicationIds(String.valueOf(ro.getCell(ConnectorConstants.KUB_PROM_KPI_APP_IDS_IDX).getNumericCellValue()))
                        .build();

                log.info("this is no of row in system {}", ro.getPhysicalNumberOfCells());
                prometheusKpis.add(prometheusKpi);

            }
        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Prometheus Kpis Data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading the Prometheus Kpis Data from excel file");
        }
        return prometheusKpis;
    }

    public List<ElasticSearchResponsePath> getElasticSearchResponsePath(File fileName) {
        List<ElasticSearchResponsePath> elasticSearchResponsePaths = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro;
                if (sheet.getRow(i) != null) ro = sheet.getRow(i);
                else continue;
                if (ro.getCell(ConnectorConstants.KUB_ES_RESPONSE_PATH_ID_IDX) == null) break;
//                    ro.getCell(ConnectorConstants.APPD_APPLICATION_KPI_IDS_IDX).setCellType(Cell.CELL_TYPE_STRING);
                ElasticSearchResponsePath elasticSearchResponsePath = ElasticSearchResponsePath.builder()
                        .id((int) ro.getCell(ConnectorConstants.KUB_ES_RESPONSE_PATH_ID_IDX).getNumericCellValue())
                        .timestamp(ro.getCell(ConnectorConstants.KUB_ES_RESPONSE_PATH_TIMESTAMP_IDX).getStringCellValue())
                        .uri(ro.getCell(ConnectorConstants.KUB_ES_RESPONSE_PATH_URI_IDX).getStringCellValue())
                        .statusCode(ro.getCell(ConnectorConstants.KUB_ES_RESPONSE_PATH_STATUS_CODE_IDX).getStringCellValue())
                        .method(ro.getCell(ConnectorConstants.KUB_ES_RESPONSE_PATH_METHOD_IDX).getStringCellValue())
                        .responseTime(ro.getCell(ConnectorConstants.KUB_ES_RESPONSE_PATH_RESPONSE_TIME_IDX).getStringCellValue())
                        .podName(ro.getCell(ConnectorConstants.KUB_ES_RESPONSE_PATH_POD_NAME_IDX).getStringCellValue())
                        .namespaceName(ro.getCell(ConnectorConstants.KUB_ES_RESPONSE_PATH_NAMESPACE_NAME_IDX).getStringCellValue())
                        .build();

                log.info("this is no of row in system {}", ro.getPhysicalNumberOfCells());
                elasticSearchResponsePaths.add(elasticSearchResponsePath);

            }
        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Elasticsearch Response Path Data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading the Elasticsearch Response Path Data from excel file");
        }
        return elasticSearchResponsePaths;
    }

    public List<A1LogscanEndpoint> getA1LogscanEndpoint(File fileName) {
        List<A1LogscanEndpoint> a1LogscanEndpoints = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro;
                if (sheet.getRow(i) != null) ro = sheet.getRow(i);
                else continue;
                if (ro.getCell(ConnectorConstants.KUB_A1_LOGSCAN_ENDPT_ID_IDX) == null) break;
                //ro.getCell(ConnectorConstants.APPD_APPLICATION_KPI_IDS_IDX).setCellType(Cell.CELL_TYPE_STRING);
                A1LogscanEndpoint a1LogscanEndpoint = A1LogscanEndpoint.builder()
                        .id((int) ro.getCell(ConnectorConstants.KUB_A1_LOGSCAN_ENDPT_ID_IDX).getNumericCellValue())
                        .hostIp(ro.getCell(ConnectorConstants.KUB_A1_LOGSCAN_ENDPT_HOST_IP_IDX).getStringCellValue())
                        .port(String.valueOf(ro.getCell(ConnectorConstants.KUB_A1_LOGSCAN_ENDPT_PORT_IDX).getNumericCellValue()))
                        .type(ro.getCell(ConnectorConstants.KUB_A1_LOGSCAN_ENDPT_TYPE_IDX).getStringCellValue())
                        .build();

                log.info("this is no of row in system {}", ro.getPhysicalNumberOfCells());
                a1LogscanEndpoints.add(a1LogscanEndpoint);
            }

        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the A1Logscan Endpoint Data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading the A1Logscan Endpoint Data from excel file");
        }
        return a1LogscanEndpoints;
    }

    public void addKubernetesConfiguration(String schema, List<KubernetesConfiguration> kubernetesConfigurations) {
        KubernetesDataService kubernetesDataService = new KubernetesDataService();
        kubernetesDataService.addKubernetesConfiguration(schema, kubernetesConfigurations);
    }

    public void addPrometheusQueryFilter(String schema, List<PrometheusQueryFilter> prometheusQueryFilters) {
        KubernetesDataService kubernetesDataService = new KubernetesDataService();
        kubernetesDataService.addPrometheusQueryFilter(schema, prometheusQueryFilters);
    }

    public void addPrometheusKpiResponsePath(String schema, List<PrometheusKpiResponsePath> prometheusKpiResponsePaths) {
        KubernetesDataService kubernetesDataService = new KubernetesDataService();
        kubernetesDataService.addPrometheusKpiResponsePath(schema, prometheusKpiResponsePaths);
    }

    public void addPrometheusApplication(String schema, List<PrometheusApplication> prometheusApplications) {
        KubernetesDataService kubernetesDataService = new KubernetesDataService();
        kubernetesDataService.addPrometheusApplication(schema, prometheusApplications);
    }

    public void addPrometheusHealKpis(String schemaName, List<PrometheusKpis> prometheusKpis) {
        try {
            KubernetesDataService kubernetesDataService = new KubernetesDataService();
            ConnectorDataService connectorDataService = new ConnectorDataService();
            List<PrometheusHealKpis> prometheusKpisFromDB = kubernetesDataService.getPrometheusKpis();
            List<String> metricFile = prometheusKpis.stream().map(x->x.getMetric()).collect(Collectors.toList());
            List<String> metricDB = prometheusKpisFromDB.stream().map(x->x.getMetric()).collect(Collectors.toList());

            List<PrometheusHealKpis> prometheusKpisupdate = prometheusKpisFromDB.stream().filter(x -> !metricFile.contains(x.getMetric())).collect(Collectors.toList());
            List<PrometheusKpis> newPrometheusKpis = prometheusKpis.stream().filter(x -> !metricDB.contains(x.getMetric())).collect(Collectors.toList());

            List<PrometheusHealKpis> prometheusHealKpis = newPrometheusKpis.stream()
                    .map(this::getPrometheusKpiDetails)
                    .collect(Collectors.toList());
            int[] srcKpiIds = kubernetesDataService.addPrometheusKpis(schemaName, prometheusHealKpis);

            if (srcKpiIds.length > 0) {
                List<HealKpi> healKpiList = newPrometheusKpis.stream()
                        .map(this::getHealKpiDetails)
                        .collect(Collectors.toList());
                int[] conHealKpiIds = connectorDataService.addHealKpi(schemaName, healKpiList);

                List<DomainToHealKpiMapping> domainToHealKpiMappings = new ArrayList<>();
                if (srcKpiIds.length == conHealKpiIds.length && conHealKpiIds.length == newPrometheusKpis.size()) {
                    for (PrometheusKpis prometheusKpi : newPrometheusKpis) {
                        domainToHealKpiMappings.add(DomainToHealKpiMapping.builder()
                                .domainName("Prometheus")
                                .healIdentifier(prometheusKpi.getHealKpiIdentifier())
                                .sourceId(srcKpiIds[prometheusKpi.getId() - 1])
                                .build());
                    }
                }
                connectorDataService.addDomainToHealKpiMapping(schemaName, domainToHealKpiMappings);

                Map<Integer, List<Integer>> kpiApplicationMap = new HashMap<>();
                for (PrometheusKpis prometheusKpi : newPrometheusKpis) {
                    kpiApplicationMap.put(srcKpiIds[prometheusKpi.getId() - 1], Arrays.stream(prometheusKpi.getApplicationIds().split(","))
                            .map(Integer::parseInt)
                            .collect(Collectors.toList()));
                }


                List<ApplicationToKpiMapping> applicationToKpiMappings = getApplicationToKpiMapping(kpiApplicationMap);
                kubernetesDataService.addKubernetesApplicationKpiMapping(schemaName, applicationToKpiMappings);
            }

                for (PrometheusHealKpis dbKpi : prometheusKpisupdate) {
                    kubernetesDataService.updatePrometheusKpis(dbKpi);
                    Optional<PrometheusKpis> kpiDetail = prometheusKpis.parallelStream()
                            .filter(x -> x.getMetric().equals(dbKpi.getMetric())).findAny();
                    Optional<DomainToHealKpiMapping> domainToHeal = connectorDataService.getDomainToHealKpiMapping(schemaName).parallelStream()
                            .filter(x -> x.getSourceId() == dbKpi.getId()).findAny();
                    if (kpiDetail.isPresent() && domainToHeal.isPresent()) {
                        kubernetesDataService.updateDomainToHealKpiMapping(kpiDetail.get().getHealKpiIdentifier(), domainToHeal.get().getSourceId());
                        kubernetesDataService.updateHealKpi(kpiDetail.get().getHealKpiName(), kpiDetail.get().getHealKpiIdentifier(), domainToHeal.get().getHealIdentifier());
                    }
                }

        }catch (Exception e)
        {
            log.error("Exception while updating the following tables :  Prometheus_kpis, DomainToHealMapping and Heal_kpis : {}", e);
        }
    }

    public void addA1LogscanEndpt(String schema, List<A1LogscanEndpoint> a1LogscanEndpoints) {
        KubernetesDataService kubernetesDataService = new KubernetesDataService();
        kubernetesDataService.addA1LogscanEndpt(schema, a1LogscanEndpoints);
    }

    public void addElasticSearchResponsePath(String schema, List<ElasticSearchResponsePath> elasticSearchResponsePaths) {
        KubernetesDataService kubernetesDataService = new KubernetesDataService();
        kubernetesDataService.addElasticSearchResponsePath(schema, elasticSearchResponsePaths);
    }

    public void addKubernetesHealAccessToken(String schema, List<KubernetesHealAccessToken> kubernetesHealAccessTokens) {
        KubernetesDataService kubernetesDataService = new KubernetesDataService();
        kubernetesDataService.addKubernetesHealAccessToken(schema, kubernetesHealAccessTokens);
    }

    public void addKubernetesHealInstanceCreationPayloads(String schema, List<KubernetesHealInstanceCreationPayload> kubernetesHealInstanceCreationPayloads) {
        KubernetesDataService kubernetesDataService = new KubernetesDataService();
        kubernetesDataService.addKubernetesHealInstanceCreationPayload(schema, kubernetesHealInstanceCreationPayloads);
    }

    public PrometheusHealKpis getPrometheusKpiDetails(PrometheusKpis prometheusKpis) {
        return PrometheusHealKpis.builder()
                .id(prometheusKpis.getId())
                .metric(prometheusKpis.getMetric())
                .hostOrComp(prometheusKpis.getHostOrComponent())
                .build();
    }

    public HealKpi getHealKpiDetails(PrometheusKpis prometheusKpis) {
        return HealKpi.builder()
                .id(prometheusKpis.getId())
                .kpiId(prometheusKpis.getHealKpiId())
                .kpiName(prometheusKpis.getHealKpiName())
                .kpiIdentifier(String.valueOf(prometheusKpis.getHealKpiIdentifier()))
                .isGroupKpi(prometheusKpis.getIsGroupKpi())
                .groupName(prometheusKpis.getGroupName())
                .build();
    }

    public List<ApplicationToKpiMapping> getApplicationToKpiMapping(Map<Integer, List<Integer>> details) {
        List<ApplicationToKpiMapping> applicationToKpiMappings = new ArrayList<>();
        for (Map.Entry<Integer, List<Integer>> entry : details.entrySet()) {
            int srcKpiId = entry.getKey();
            for (Integer a : entry.getValue()) {
                applicationToKpiMappings.add(
                        ApplicationToKpiMapping.builder()
                                .kpi_id(srcKpiId)
                                .application_id(a)
                                .build()
                );
            }
        }
        return applicationToKpiMappings;
    }
}
