package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ParentApplicationDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.ParentApplicationRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.ParentApplicationPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.heal.configuration.entities.ParentApplicationBean;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.ParentApplication;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class AddParentApplicationBL implements BusinessLogic<List<ParentApplicationPojo>, List<ParentApplicationBean>, String> {
    String accountIdentifier;

    public UtilityBean<List<ParentApplicationPojo>> clientValidation(RequestObject requestObject) throws ClientException {
        List<ParentApplicationPojo> parentApplication;

        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(requestObject.getBody())) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        accountIdentifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (accountIdentifier == null || accountIdentifier.trim().isEmpty()) {
            log.error("Account identifier is invalid");
            throw new ClientException("Account identifier is invalid");
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        ObjectMapper obj_mapper = CommonUtils.getObjectMapperWithHtmlEncoder();

        try {
            parentApplication = obj_mapper.readValue(requestObject.getBody(), new TypeReference<List<ParentApplicationPojo>>() {
            });
        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID + "err:{}", e.getMessage());
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        Set<ParentApplicationPojo> parentAppSet = new HashSet<>(parentApplication);

        if (parentAppSet.size() < parentApplication.size()) {
            log.error(UIMessages.DUPLICATE_PARENT_APPLICATION_SERVICES);
            throw new ClientException(UIMessages.DUPLICATE_PARENT_APPLICATION_SERVICES);
        }

        parentAppSet = parentApplication.parallelStream().filter(ParentApplicationPojo::validate).collect(Collectors.toSet());

        if (parentAppSet.size() < parentApplication.size()) {
            log.error(UIMessages.DUPLICATE_PARENT_APPLICATION_SERVICES);
            throw new ClientException(UIMessages.INVALID_PARENT_APPLICATION_IDENTIFIER);
        }

        return UtilityBean.<List<ParentApplicationPojo>>builder()
                .authToken(authKey)
                .accountIdentifier(accountIdentifier)
                .pojoObject(parentApplication)
                .build();
    }


    public List<ParentApplicationBean> serverValidation(UtilityBean<List<ParentApplicationPojo>> utilityBean) throws ServerException {
        ParentApplicationRepo parentApplicationRepo = new ParentApplicationRepo();

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }
        int accountId = account.getId();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        List<String> parentApplications = parentApplicationRepo.getAllParentApplications(utilityBean.getAccountIdentifier())
                .parallelStream()
                .map(BasicEntity::getName)
                .collect(Collectors.toList());

        List<String> parentApplicationNameSet = utilityBean.getPojoObject()
                .stream()
                .map(ParentApplicationPojo::getParentApplication)
                .collect(Collectors.toList());

        List<ParentApplicationBean> parentApplicationBeanList = parentApplicationNameSet.parallelStream()
                .filter(p -> {
                    if (parentApplications.contains(p)) {
                        log.info("Parent Application {} already exits. Parent application with the same name will not be created.", p);
                        return false;
                    }
                    return true;
                }).map(name -> ParentApplicationBean.builder()
                        .name(name)
                        .identifier(UUID.randomUUID().toString())
                        .accountId(accountId)
                        .userId(userId)
                        .createdTime(DateTimeUtil.getCurrentTimestampInGMT())
                        .updatedTime(DateTimeUtil.getCurrentTimestampInGMT())
                        .status(1)
                        .build()).collect(Collectors.toList());

        log.info("{} parent applications will be created", parentApplicationBeanList.size());

        return parentApplicationBeanList;
    }

    public String process(List<ParentApplicationBean> parentApplicationBeanList) throws DataProcessingException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            List<ParentApplicationBean> beans = dbi.inTransaction((conn, status) -> addParentApplications(parentApplicationBeanList, conn));
            addParentApplicationInRedis(beans, null, accountIdentifier);
            return "Parent application(s) added successfully";
        } catch (Exception e) {
            if (Throwables.getRootCause(e) instanceof ControlCenterException) {
                throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
            } else {
                throw e;
            }
        }
    }

    public List<ParentApplicationBean> addParentApplications(List<ParentApplicationBean> parentApplicationBeans, Handle conn) throws ControlCenterException {
        ParentApplicationDataService parentApplicationDataService = new ParentApplicationDataService();

        parentApplicationDataService.addParentApplications(parentApplicationBeans, conn);

        return parentApplicationDataService.getParentApplicationsByIdentifiers(parentApplicationBeans, conn);
    }

    public void addParentApplicationInRedis(List<ParentApplicationBean> parentApplicationBeanList, String applicationIdentifier, String accountIdentifier) {
        ParentApplicationRepo parentApplicationRepo = new ParentApplicationRepo();

        List<ParentApplication> newParentApps = parentApplicationBeanList.parallelStream()
                .map(parentApplicationBean -> ParentApplication.builder()
                        .id(parentApplicationBean.getId())
                        .name(parentApplicationBean.getName())
                        .identifier(parentApplicationBean.getIdentifier())
                        .accountId(parentApplicationBean.getAccountId())
                        .createdTime(parentApplicationBean.getCreatedTime().toString())
                        .updatedTime(parentApplicationBean.getUpdatedTime().toString())
                        .status(1)
                        .applicationIdentifiers(applicationIdentifier == null ? null : Collections.singletonList(applicationIdentifier))
                        .build()).filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<ParentApplication> parentApplications = parentApplicationRepo.getAllParentApplications(accountIdentifier);

        parentApplications.addAll(newParentApps);

        parentApplicationRepo.updateParentApplication(parentApplications, accountIdentifier);

        newParentApps.forEach(newParentApp ->  {
            parentApplicationRepo.updateParentApplicationByIdentifier(newParentApp, accountIdentifier);
        });
    }
}