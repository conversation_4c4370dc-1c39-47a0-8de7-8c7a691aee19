package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.exception.AppsOneException;
import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.MaintenanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.MaintenanceDetails;
import com.appnomic.appsone.controlcenter.dao.opensearch.MaintenanceWindowServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.MaintenanceWindowPojo;
import com.appnomic.appsone.controlcenter.pojo.RecurringBean;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.MaintenanceWindowUtility;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;

import java.sql.Timestamp;
import java.text.MessageFormat;
import java.text.ParseException;
import java.util.List;

@Slf4j
public class DeleteMaintenanceWindowBL implements BusinessLogic<MaintenanceWindowPojo, MaintenanceDetails, Integer> {

    private static final MaintenanceDataService MAINTENANCE_DATA_SERVICE = new MaintenanceDataService();

    @Override
    public UtilityBean<MaintenanceWindowPojo> clientValidation(RequestObject requestObject) throws ClientException {
        String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        String accountIdentifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        String serviceIdStr = requestObject.getParams().get(Constants.SERVICE_ID);
        String maintenanceIdStr = requestObject.getParams().get(Constants.MAINTENANCE_ID);

        if (StringUtils.isEmpty(accountIdentifier)) {
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        if (StringUtils.isEmpty(serviceIdStr)) {
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        }

        int serviceId;
        try {
            serviceId = Integer.parseInt(serviceIdStr);
        } catch (Exception e) {
            throw new ClientException(MessageFormat.format(UIMessages.INVALID_VALUE, Constants.SERVICE_ID, serviceIdStr));
        }

        if (StringUtils.isEmpty(maintenanceIdStr)) {
            throw new ClientException(UIMessages.MAINTENANCE_ID_EMPTY_ERROR);
        }

        int maintenanceId;
        try {
            maintenanceId = Integer.parseInt(maintenanceIdStr);
        } catch (Exception e) {
            throw new ClientException(MessageFormat.format(UIMessages.INVALID_VALUE, Constants.MAINTENANCE_ID, maintenanceIdStr));
        }


        MaintenanceWindowPojo maintenanceWindowPojo = new MaintenanceWindowPojo();
        maintenanceWindowPojo.setId(maintenanceId);
        maintenanceWindowPojo.setServiceId(serviceId);

        return UtilityBean.<MaintenanceWindowPojo>builder()
                .accountIdentifier(accountIdentifier)
                .authToken(authToken)
                .pojoObject(maintenanceWindowPojo)
                .build();
    }

    @Override
    public MaintenanceDetails serverValidation(UtilityBean<MaintenanceWindowPojo> utilityBean) throws ServerException {
        Timestamp date;
        try {
            date = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
        } catch (ParseException e) {
            log.error("Error while constructing date for current timestamp. Details: ", e);
            throw new ServerException("Error while constructing date for current timestamp");
        }

        String authToken = utilityBean.getAuthToken();

        UserAccountBean userAccBean;
        try {
            userAccBean = ValidationUtils.commonServerValidations(authToken, utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            log.error(e.getMessage());
            throw new ServerException(e.getMessage());
        }

        AccountBean account = userAccBean.getAccount();
        int accountId = account.getId();

        MaintenanceWindowPojo maintenanceBean = utilityBean.getPojoObject();
        int serviceId = maintenanceBean.getServiceId();

        ControllerBean controller = new ControllerDataService().getControllerById(maintenanceBean.getServiceId(), accountId, null);

        if (controller == null) {
            log.error("Service with id [{}] is unavailable for account id [{}]", maintenanceBean.getServiceId(), accountId);
            throw new ServerException(String.format("Service with id [%d] is unavailable for account id [%d]", maintenanceBean.getServiceId(), accountId));
        }

        MaintenanceDetails maintenanceDetails;

        List<MaintenanceDetails> maintenanceDetailsList = MAINTENANCE_DATA_SERVICE.getMaintenanceDetailsByServiceId(serviceId, account.getId());
        if (maintenanceDetailsList.isEmpty()) {
            log.error("No maintenance windows available for service with id [{}]", serviceId);
            throw new ServerException(String.format("No maintenance windows available for service with id [%d]", serviceId));
        }

        if (maintenanceBean.getId() == 0) {
            utilityBean.setAccount(account);
            List<MaintenanceDetailsBean> maintenanceDetailsBeanList;
            try {
                maintenanceDetailsBeanList = MaintenanceWindowUtility.getMaintenanceDetails(utilityBean.getAccount().getIdentifier(),
                        controller.getIdentifier(), controller.getId());
            } catch (ParseException | AppsOneException | ControlCenterException e) {
                log.error("Error while fetching maintenance window details. Details: ", e);
                throw new ServerException(e.getMessage());
            }

            if(maintenanceDetailsBeanList.isEmpty()) {
                log.error("Ongoing maintenance window unavailable for service [{}]", controller.getIdentifier());
                throw new ServerException(String.format("Ongoing maintenance window unavailable for service [%s]", controller.getIdentifier()));
            }

            MaintenanceDetailsBean maintenanceDetailsBean = maintenanceDetailsBeanList.stream()
                    .filter(MaintenanceDetailsBean::isOngoing)
                    .findAny()
                    .orElse(null);

            if (maintenanceDetailsBean == null) {
                log.error("Ongoing maintenance window unavailable for service [{}]", controller.getIdentifier());
                throw new ServerException(String.format("Ongoing maintenance window unavailable for service [%s]", controller.getIdentifier()));
            }

            ViewTypes recurringType = MasterCache
                    .getMstTypeForSubTypeName(Constants.MST_TYPE_MAINTENANCE, Constants.MST_SUB_TYPE_RECURRING);

            maintenanceDetails = MaintenanceDetails.builder()
                    .id(maintenanceBean.getId())
                    .accountId(account.getId())
                    .serviceId(serviceId)
                    .accountIdentifier(account.getIdentifier())
                    .maintenanceType(recurringType.getSubTypeName())
                    .serviceIdentifier(controller.getIdentifier())
                    .userDetails(userAccBean.getUserId())
                    .status(true)
                    .startTime(maintenanceDetailsBean.getStartTime())
                    .endTime(date)
                    .updatedTime(date)
                    .build();

            return maintenanceDetails;
        }

        MaintenanceDetails maintenanceDetail = maintenanceDetailsList.stream()
                .filter(maintenanceListBean -> (maintenanceBean.getId() == maintenanceListBean.getId()))
                .findAny()
                .orElse(null);

        if (maintenanceDetail == null) {
            log.error("Maintenance window with id [{}] unavailable for service with identifier [{}]", maintenanceBean.getId(), serviceId);
            throw new ServerException(String.format("Maintenance window with id [%d] unavailable for service with id [%d]", maintenanceBean.getId(), serviceId));
        }

        ViewTypes maintenanceType = MasterCache.getMstSubTypeForSubTypeId(maintenanceDetail.getTypeId());

        RecurringBean recurringDetailsBean = null;
        if (maintenanceType.getSubTypeName().equals(Constants.MST_SUB_TYPE_RECURRING)) {
            recurringDetailsBean = MAINTENANCE_DATA_SERVICE.getRecurringDetails(maintenanceDetail.getId());
        }

        maintenanceDetails = MaintenanceDetails.builder()
                .id(maintenanceBean.getId())
                .accountId(account.getId())
                .serviceId(serviceId)
                .accountIdentifier(account.getIdentifier())
                .maintenanceType(maintenanceType.getSubTypeName())
                .serviceIdentifier(controller.getIdentifier())
                .userDetails(userAccBean.getUserId())
                .recurring(recurringDetailsBean)
                .status(true)
                .startTime(maintenanceDetail.getStartTime())
                .endTime(maintenanceDetail.getEndTime())
                .updatedTime(date)
                .build();

        return maintenanceDetails;
    }

    @Override
    public Integer process(MaintenanceDetails bean) throws DataProcessingException {
        if (bean.getMaintenanceType().equals(Constants.MST_SUB_TYPE_SCHEDULED))
            return deleteScheduled(bean);
        else
            return deleteRecurring(bean);
    }

    public static int deleteRecurring(MaintenanceDetails maintenanceDetails) throws DataProcessingException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            Timestamp date = DateTimeUtil.getCurrentTimestampInGMT();

            return dbi.inTransaction((conn, status) -> {
                if (maintenanceDetails.getId() == 0) {
                    MaintenanceWindowServiceRepo.updateServiceOngoingMaintenanceCassandra(maintenanceDetails.getAccountIdentifier(), maintenanceDetails.getServiceIdentifier(),
                            maintenanceDetails.getStartTime(), date);
                } else {
                    MAINTENANCE_DATA_SERVICE.deleteServiceMaintenanceMapping(maintenanceDetails, conn);
                    MAINTENANCE_DATA_SERVICE.deleteRecurringDetails(maintenanceDetails.getRecurring().getId(), conn);
                    MAINTENANCE_DATA_SERVICE.deleteMaintenanceWindow(maintenanceDetails, conn);
                }

                return maintenanceDetails.getId();
            });
        } catch (Exception e) {
            log.error("Exception while deleting recurring maintenance window details. Details: ", e);
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }

    public static int deleteScheduled(MaintenanceDetails maintenanceDetails) throws DataProcessingException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            Timestamp date = DateTimeUtil.getCurrentTimestampInGMT();

            return dbi.inTransaction((conn, status) -> {
                if ((maintenanceDetails.getStartTime()).before(date)) {
                    MaintenanceWindowServiceRepo.updateServiceOngoingMaintenanceCassandra(maintenanceDetails.getAccountIdentifier(), maintenanceDetails.getServiceIdentifier(),
                            maintenanceDetails.getStartTime(), date);
                } else {
                    MaintenanceWindowServiceRepo.deleteMaintenanceCassandra(maintenanceDetails.getAccountIdentifier(), maintenanceDetails.getServiceIdentifier(),
                            maintenanceDetails.getStartTime());
                }

                MAINTENANCE_DATA_SERVICE.deleteServiceMaintenanceMapping(maintenanceDetails, conn);
                MAINTENANCE_DATA_SERVICE.deleteMaintenanceWindow(maintenanceDetails, conn);

                return maintenanceDetails.getId();
            });
        } catch (Exception e) {
            log.error("Exception while deleting scheduled maintenance window details. Details: ", e);
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }
}
