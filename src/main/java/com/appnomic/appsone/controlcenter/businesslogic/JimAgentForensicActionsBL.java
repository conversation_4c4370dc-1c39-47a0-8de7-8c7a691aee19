package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CommandDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandDetailArgumentBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandDetailsBean;
import com.appnomic.appsone.controlcenter.dao.redis.MasterDataRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class JimAgentForensicActionsBL implements BusinessLogic<String, UtilityBean<String>, List<CommandDetailsBean>> {
    private static final Logger log = LoggerFactory.getLogger(JimAgentForensicActionsBL.class);

    @Override
    public UtilityBean<String> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String accountIdString = requestObject.getParams().get(UIMessages.ACCOUNT_IDENTIFIER);

        if (StringUtils.isEmpty(accountIdString)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String serviceIdentifier = requestObject.getParams().get(Constants.SERVICE_IDENTIFIER);

        if (serviceIdentifier == null || serviceIdentifier.trim().isEmpty()) {
            log.error("Service identifier is null or empty.");
            throw new ClientException("Service identifier is null or empty.");
        }

        return UtilityBean.<String>builder()
                .accountIdentifier(accountIdString)
                .authToken(authKey)
                .pojoObject(serviceIdentifier)
                .build();
    }

    @Override
    public UtilityBean<String> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getAccountIdentifier();
        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }
        return utilityBean;
    }

    @Override
    public List<CommandDetailsBean> process(UtilityBean<String> bean) throws DataProcessingException {
        log.debug("Fetching all the forensic action commands and there arguments mapped to JIM agent type");
        MasterDataRepo masterDataRepo = new MasterDataRepo();
        List<com.heal.configuration.pojos.ViewTypes> types = masterDataRepo.getTypes();

        if(types.isEmpty()) {
            log.error("Obtained empty result from Redis when queried for view types");
            throw new DataProcessingException("Obtained empty result from Redis when queried for view types");
        }

        Map<String, com.heal.configuration.pojos.ViewTypes> viewTypesMap = types.stream()
                .filter(f -> f.getSubTypeName().equals(Constants.JIM_AGENT_SUB_TYPE) || f.getTypeName().equals(Constants.FORENSIC_COMMAND_TYPE))
                .collect(Collectors.toMap(com.heal.configuration.pojos.ViewTypes::getTypeName, Function.identity()));

        int jimSubTypeId = viewTypesMap.get(Constants.AGENT_TYPE).getSubTypeId();
        int forensicTypeId = viewTypesMap.get(Constants.FORENSIC_COMMAND_TYPE).getTypeId();

        List<CommandDetailsBean> commandDetails = CommandDataService.getCommandDetailsByAgentType(jimSubTypeId, forensicTypeId, null);

        if(commandDetails.isEmpty()) {
            log.error("Obtained empty results from the database when queried for command details for agent type ID. {}", Constants.JIM_AGENT_TYPE_ID);
            throw new DataProcessingException("Obtained empty results from the database when queried for command details for agent type ID.");
        }

        List<CommandDetailArgumentBean> commandArguments = CommandDataService.getAllCommandArguments();

        if(commandArguments.isEmpty()) {
            log.info("Obtained empty results from data source when queried for all command arguments.");
        } else {
            Map<Integer, List<CommandDetailArgumentBean>> commandActionArgumentsMap = commandArguments.parallelStream()
                    .collect(Collectors.groupingBy(CommandDetailArgumentBean::getCommandId));


            commandDetails.forEach(commandDetail -> commandDetail.setCommandArguments(commandActionArgumentsMap.get(commandDetail.getId())));
        }
        return commandDetails;
    }
}
