package com.appnomic.appsone.controlcenter.businesslogic.connectors;

import com.appnomic.appsone.controlcenter.common.ConnectorConstants;
import com.appnomic.appsone.controlcenter.dao.mysql.connectors.AppDynamicsConnectorDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.connectors.ConnectorDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.*;
import com.appnomic.appsone.controlcenter.exceptions.FileUploadException;
import com.appnomic.appsone.controlcenter.pojo.connectors.AppDynamicsApplication;
import com.appnomic.appsone.controlcenter.pojo.connectors.AppDynamicsKpiDetails;
import com.appnomic.appsone.controlcenter.pojo.connectors.AzureHealKpi;
import com.appnomic.appsone.controlcenter.pojo.connectors.HealAgentInstance;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class AppDynamicsConnectorBL {

    public List<AppDynamicsApplication> getAppDynamicsApplications(File fileName) {
        List<AppDynamicsApplication> appDynamicApplications = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro;
                if (sheet.getRow(i) != null) ro = sheet.getRow(i);
                else continue;
                if (ro.getCell(ConnectorConstants.APPD_APPLICATION_ID_IDX) != null) {
                    AppDynamicsApplication appDynamicApplication = AppDynamicsApplication.builder()
                            .id((int) ro.getCell(ConnectorConstants.APPD_APPLICATION_ID_IDX).getNumericCellValue())
                            .applicationId(String.valueOf(ro.getCell(ConnectorConstants.APPD_APPLICATION_APPID_IDX).getNumericCellValue()))
                            .applicationName(ro.getCell(ConnectorConstants.APPD_APPLICATION_NAME_IDX).getStringCellValue())
                            .organisation(ro.getCell(ConnectorConstants.APPD_APPLICATION_ORG_IDX).getStringCellValue())
                            .password(ro.getCell(ConnectorConstants.APPD_APPLICATION_PWD_IDX).getStringCellValue())
                            .username(ro.getCell(ConnectorConstants.APPD_APPLICATION_USERNAME_IDX).getStringCellValue())
                            .appInstance(ro.getCell(ConnectorConstants.APPD_APPLICATION_APPINST_IDX).getStringCellValue())
                            .node(ro.getCell(ConnectorConstants.APPD_APPLICATION_NODE_IDX).getStringCellValue())
                            .logAgent(ro.getCell(ConnectorConstants.APPD_APPLICATION_LOG_AGENT_IDENTFIER_IDX).getStringCellValue())
                            .build();
                    log.info("this is no of row in system {}", ro.getPhysicalNumberOfCells());
                    appDynamicApplications.add(appDynamicApplication);
                }
            }
        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Azure Application Data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading the Azure Application Data from excel file");
        }
        return appDynamicApplications;
    }


    public List<AppDynamicsKpiDetails> getAppDynamicsKpiDetails(File fileName) {
        List<AppDynamicsKpiDetails> appDynamicsKpiDetails = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro;
                if (sheet.getRow(i) != null) ro = sheet.getRow(i);
                else continue;
                if (ro.getCell(ConnectorConstants.APPD_KPI_ID_IDX) == null) break;
                AppDynamicsKpiDetails appDynamicsKpiDetail = AppDynamicsKpiDetails.builder()
                        .id((int) ro.getCell(ConnectorConstants.APPD_KPI_ID_IDX).getNumericCellValue())
                        .kpiName(ro.getCell(ConnectorConstants.APPD_KPI_NAME_IDX).getStringCellValue())
                        .kpiType(ro.getCell(ConnectorConstants.APPD_KPI_TYPE_IDX).getStringCellValue())
                        .aggregationLevel(ro.getCell(ConnectorConstants.APPD_KPI_AGGREGATION_LEVEL_IDX).getStringCellValue())
                        .kpiAggregator(ro.getCell(ConnectorConstants.APPD_KPI_AGGREGATOR_IDX).getStringCellValue())
                        .healKpiId((int) ro.getCell(ConnectorConstants.APPD_HEAL_KPI_ID_IDX).getNumericCellValue())
                        .healKpiName(ro.getCell(ConnectorConstants.APPD_HEAL_KPI_NAME_IDX).getStringCellValue())
                        .healKpiIdentifier(ro.getCell(ConnectorConstants.APPD_HEAL_KPI_IDENTIFIER_IDX).getStringCellValue())
                        .isGroupKpi((int) ro.getCell(ConnectorConstants.APPD_HEAL_IS_GROUP_KPI_IDX).getNumericCellValue() == 1)
                        .healGroupName(ro.getCell(ConnectorConstants.APPD_HEAL_GROUP_NAME_IDX).getStringCellValue())
                        .build();
                log.info("this is no of row in system {}", ro.getPhysicalNumberOfCells());
                appDynamicsKpiDetails.add(appDynamicsKpiDetail);
            }
        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Azure Application Data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading the Azure Application Data from excel file");
        }
        return appDynamicsKpiDetails;
    }

    public List<HealAgentInstance> getSourceHealInstanceMappingFromFile(File fileName) {
        List<HealAgentInstance> healAgentInstances = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro = sheet.getRow(i);
                if (ro.getCell(ConnectorConstants.APPD_INST_NAME_IDX) == null)
                    break;
                HealAgentInstance agentInstance = HealAgentInstance.builder()
                        .sourceInstanceName(ro.getCell(ConnectorConstants.APPD_INST_NAME_IDX).getStringCellValue())
                        .agentIdentifier(ro.getCell(ConnectorConstants.APPD_HEAL_AGENT_ID_IDX).getStringCellValue())
                        .healInstanceName(ro.getCell(ConnectorConstants.APPD_HEAL_INST_NAME_IDX).getStringCellValue())
                        .build();
                healAgentInstances.add(agentInstance);
            }

        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try {}", ex.getMessage());
            throw new FileUploadException("File is not found at given location please check and try");
        } catch (IOException ex) {
            log.error("Exception when reading the Sap Instance data from excel file {}", ex.getMessage());
            throw new FileUploadException("Exception when reading the Sap Instance data from excel file");
        }
        return healAgentInstances;
    }

    public void addAppDynamicsHealKpis(List<AppDynamicsKpiDetails> appDynamicsKpiDetails) {

        String schemaName = "dataadapter_appdynamics";
        AppDynamicsConnectorDataService appDynamicsConnectorDataService = new AppDynamicsConnectorDataService();
        ConnectorDataService connectorDataService = new ConnectorDataService();
        appDynamicsKpiDetails.removeIf(x -> x.getKpiName().equals("") || x.getKpiName().equals(" ") || x.getKpiName() == null);
        List<HealKpi> healKpiList = appDynamicsKpiDetails.stream()
                .map(this::getHealKpiDetails)
                .collect(Collectors.toList());
        List<AppDynamicsKpi> appDynamicsKpis = appDynamicsKpiDetails.stream()
                .map(this::getAppDynamicsKpiDetails)
                .collect(Collectors.toList());
        List<String> kpis = appDynamicsConnectorDataService.getAppDKpis();
        List<String> kpisHeal = appDynamicsConnectorDataService.getHealKpis();
        List<AppDynamicsKpi> updateList = appDynamicsKpis.stream().filter(x -> kpis.contains(x.getKpiName())).collect(Collectors.toList());
        List<HealKpi> updateListHeal = healKpiList.stream().filter(x -> kpisHeal.contains(x.getKpiName())).collect(Collectors.toList());
        appDynamicsKpis.removeAll(updateList);
        healKpiList.removeAll(updateListHeal);
        appDynamicsKpiDetails.removeIf(x -> kpis.contains(x.getKpiName()));
        int[] ids = appDynamicsConnectorDataService.addAppDynamicsKpi(appDynamicsKpis);
        appDynamicsConnectorDataService.updateAppDynamicsKpi(updateList);
        connectorDataService.updateHealKpis(schemaName,updateListHeal);
        int i = 0;
        for (AppDynamicsKpiDetails healKpi : appDynamicsKpiDetails) {
            if (i < ids.length)
                healKpi.setId(ids[i]);
            i++;
        }
        List<DomainToHealKpiMapping> domainToHealKpiMappings = appDynamicsKpiDetails.stream()
                .map(this::getDomainToHealKpiMapping)
                .collect(Collectors.toList());

        connectorDataService.addHealKpi(schemaName, healKpiList);
        connectorDataService.addDomainToHealKpiMapping(schemaName, domainToHealKpiMappings);

    }

    public void addAppDynamicsApplication(List<AppDynamicsApplication> appDynamicsApplications) {

        Map<Integer, List<Integer>> applicationKpiMap = new HashMap<>();
        AppDynamicsConnectorDataService appDynamicsConnectorDataService = new AppDynamicsConnectorDataService();

        List<Integer> appDKpiList = appDynamicsConnectorDataService.getAppDynamicsKpiList();
        for (AppDynamicsApplication appDynamicsApplication : appDynamicsApplications) {
            applicationKpiMap.put(appDynamicsApplication.getId(), appDKpiList
                    .stream()
                    .map(Integer::intValue)
                    .collect(Collectors.toList()));
        }
        List<ApplicationToKpiMapping> applicationToKpiMappings = getApplicationToKpiMapping(applicationKpiMap);

        appDynamicsConnectorDataService.addAppDynamicsApplications(appDynamicsApplications);
        appDynamicsConnectorDataService.addAppDynamicsApplicationKpiMapping(applicationToKpiMappings);

    }

    public AppDynamicsKpi getAppDynamicsKpiDetails(AppDynamicsKpiDetails appDynamicsKpiDetails) {
        return AppDynamicsKpi.builder()
                .id(appDynamicsKpiDetails.getId())
                .kpiName(appDynamicsKpiDetails.getKpiName())
                .kpiType(appDynamicsKpiDetails.getKpiType())
                .kpiAggregator(appDynamicsKpiDetails.getKpiAggregator())
                .aggregationLevel(appDynamicsKpiDetails.getAggregationLevel())
                .build();
    }

    public HealKpi getHealKpiDetails(AppDynamicsKpiDetails appDynamicsKpiDetails) {
        return HealKpi.builder()
                .id(appDynamicsKpiDetails.getId())
                .kpiId(appDynamicsKpiDetails.getHealKpiId())
                .kpiName(appDynamicsKpiDetails.getHealKpiName())
                .kpiIdentifier(appDynamicsKpiDetails.getHealKpiIdentifier())
                .groupName(appDynamicsKpiDetails.getHealGroupName())
                .isGroupKpi(appDynamicsKpiDetails.isGroupKpi() ? 1 : 0)
                .build();
    }

    public DomainToHealKpiMapping getDomainToHealKpiMapping(AppDynamicsKpiDetails appDynamicsKpiDetails) {
        return DomainToHealKpiMapping.builder()
                .id(appDynamicsKpiDetails.getId())
                .domainName("appdynamics")
                .sourceId(appDynamicsKpiDetails.getId())
                .healIdentifier(appDynamicsKpiDetails.getHealKpiIdentifier())
                .build();
    }

    public List<ApplicationToKpiMapping> getApplicationToKpiMapping(Map<Integer, List<Integer>> details) {
        List<ApplicationToKpiMapping> applicationToKpiMappings = new ArrayList<>();
        for (Map.Entry<Integer, List<Integer>> entry : details.entrySet()) {
            int applicationId = entry.getKey();
            for (Integer a : entry.getValue()) {
                applicationToKpiMappings.add(
                        ApplicationToKpiMapping.builder()
                                .application_id(applicationId)
                                .kpi_id(a)
                                .build()
                );
            }
        }
        return applicationToKpiMappings;
    }

}
