package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.MasterDataRepo;
import com.appnomic.appsone.controlcenter.dao.redis.SchedulersRedisRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.entities.ScheduledJobDetails;
import com.heal.configuration.entities.SchedulerDetails;
import com.heal.configuration.pojos.ScheduleArguments;
import com.heal.configuration.pojos.ScheduledJob;
import com.heal.configuration.pojos.ViewTypes;
import java8.util.Objects;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class GetScheduledJobsBL implements BusinessLogic<Integer, List<ScheduledJobDetails>, List<ScheduledJob>> {

    SchedulersRedisRepo schedulersRedisRepo = new SchedulersRedisRepo();
    MasterDataRepo masterDataRepo = new MasterDataRepo();

    @Override
    public UtilityBean<Integer> clientValidation(RequestObject requestObject) throws ClientException {
        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Account identifier is invalid");
            throw new ClientException("Account identifier is invalid");
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);

        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String schedulerId = requestObject.getParams().get(":schedulerDetailsId");

        if (schedulerId == null || schedulerId.trim().isEmpty()) {
            log.error("Scheduler ID is invalid. Reason: It is either NULL or empty.");
            throw new ClientException("Scheduler ID is invalid");
        }

        int sid;
        try {
            sid = Integer.parseInt(schedulerId);
        } catch (NumberFormatException e) {
            log.error("Scheduler ID is invalid. Reason: It is not a valid integer. Details: ", e);
            throw new ClientException("Scheduler ID is invalid");
        }

        return UtilityBean.<Integer>builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .pojoObject(sid)
                .build();
    }

    @Override
    public List<ScheduledJobDetails> serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        String accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        List<SchedulerDetails> schedulerDetails = schedulersRedisRepo.getSchedulers(accountIdentifier);

        if (schedulerDetails.isEmpty()) {
            log.error("Scheduler details unavailable");
            throw new ServerException("Scheduler details unavailable");
        }

        int sid = utilityBean.getPojoObject();

        List<ScheduledJobDetails> jobDetails = schedulerDetails.parallelStream()
                .filter(s -> s.getId() == sid)
                .flatMap(s -> s.getScheduledJobDetails().stream()).collect(Collectors.toList());

        if(jobDetails.isEmpty()) {
            log.error("Scheduled job details unavailable");
            throw new ServerException("Scheduled job details unavailable");
        }
        return jobDetails;
    }

    @Override
    public List<ScheduledJob> process(List<ScheduledJobDetails> scheduledJobDetails) throws DataProcessingException {
        List<ViewTypes> viewTypes = masterDataRepo.getTypes();

        List<ScheduledJob> scheduledJobs = scheduledJobDetails.parallelStream()
                .map(s -> {
                    ViewTypes jobType = viewTypes.parallelStream().filter(v -> v.getSubTypeId() == s.getImplementationId()).findFirst().orElse(null);
                    if (jobType == null) {
                        log.error("JobType unavailable for ID [{}]", s.getImplementationId());
                        return null;
                    }

                    List<ScheduleArguments> arguments = s.getScheduledJobArguments().parallelStream()
                            .map(a -> ScheduleArguments.builder()
                                    .argumentName(a.getArgumentName())
                                    .argumentValue(a.getArgumentValue())
                                    .defaultValue(a.getDefaultValue())
                                    .placeholder(a.getPlaceholder())
                                    .modifiedBy(a.getUserDetailsId())
                                    .lastModifiedTime(a.getUpdatedTime())
                                    .build())
                            .collect(Collectors.toList());

                    return ScheduledJob.builder()
                            .id(s.getId())
                            .name(s.getName())
                            .implementationType(jobType.getTypeName())
                            .modifiedBy(s.getUserDetailsId())
                            .lastModifiedTime(s.getUpdatedTime())
                            .status(s.getStatus())
                            .arguments(arguments)
                            .build();
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());

        if(scheduledJobs.size() != scheduledJobDetails.size()) {
            log.error("Error in processing scheduled job list");
            throw new DataProcessingException("Error in processing scheduled job list");
        }

        return scheduledJobs;
    }
}
