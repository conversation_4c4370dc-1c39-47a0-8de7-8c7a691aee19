package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.KpiMaintenanceStatusBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.KPIDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import com.appnomic.appsone.controlcenter.dao.opensearch.InstanceKpiThresholdRepo;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.ActionsEnum;
import com.appnomic.appsone.controlcenter.pojo.InstanceKpiThresholdDetails;
import com.appnomic.appsone.controlcenter.pojo.InstanceLevelKpiAttributeThreshold;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.KpiAttributeLevelThresholdUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.KpiViolationConfig;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class UpdateInstanceLevelKpiThresholdBL implements BusinessLogic<InstanceKpiThresholdDetails, List<InstanceKpiAttributeThresholdBean>, String> {

    private static final KpiAttributeLevelThresholdUtil KPI_UTIL = new KpiAttributeLevelThresholdUtil();
    private static final KPIDataService KPI_DATA_SERVICE = new KPIDataService();
    private static final InstanceRepo INSTANCE_REPO = new InstanceRepo();

    private String accountIdentifier;

    @Override
    public UtilityBean<InstanceKpiThresholdDetails> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(requestObject.getBody())) {
            log.error("Invalid request body. Reason: Request body is either NULL or empty.");
            throw new ClientException("Invalid request body. Reason: Request body is either NULL or empty.");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Account identifier is invalid");
            throw new ClientException("Account identifier is invalid");
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);

        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        InstanceKpiThresholdDetails kpiAttributeThresholds = KPI_UTIL.getInstanceKpiThresholdDetails(requestObject, false);

        Set<InstanceLevelKpiAttributeThreshold> uniqueInputList = kpiAttributeThresholds.getThresholds()
                .parallelStream().collect(Collectors.toSet());

        if (uniqueInputList.size() != kpiAttributeThresholds.getThresholds().size()) {
            log.error("Duplicate threshold details for KPI attributes(s) provided in the request");
            throw new ClientException("Duplicate threshold details for KPI attributes(s) provided in the request");
        }

        return UtilityBean.<InstanceKpiThresholdDetails>builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .pojoObject(kpiAttributeThresholds)
                .build();
    }

    @Override
    public List<InstanceKpiAttributeThresholdBean> serverValidation(UtilityBean<InstanceKpiThresholdDetails> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        InstanceKpiThresholdDetails details = utilityBean.getPojoObject();
        int accountId = account.getId();

        Map<Integer, String> compInstIdToIdentifierMap = KPI_UTIL.verifyInstanceIdKpiAndGroupKpi(details, accountId);

        return KPI_UTIL.kpiAndCompInstanceValidation(details, compInstIdToIdentifierMap, userId, accountId, accountIdentifier, false);
    }

    @Override
    public String process(List<InstanceKpiAttributeThresholdBean> inputThresholdBeans) throws DataProcessingException {
        String result;
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        try {
            Map<Boolean, List<InstanceKpiAttributeThresholdBean>> partitionBasedOnAddAction = inputThresholdBeans.parallelStream()
                    .collect(Collectors.partitioningBy(c -> c.getActionForUpdate().equals(ActionsEnum.ADD)));

            List<InstanceKpiAttributeThresholdBean> thresholdsToBeUpdated = partitionBasedOnAddAction.get(false);
            Map<Boolean, List<InstanceKpiAttributeThresholdBean>> partitionBasedOnDeleteAction = thresholdsToBeUpdated.parallelStream()
                    .collect(Collectors.partitioningBy(c -> c.getActionForUpdate().equals(ActionsEnum.DELETE)));

            List<InstanceKpiAttributeThresholdBean> newThresholdsToBeAdded = partitionBasedOnAddAction.get(true);
            List<InstanceKpiAttributeThresholdBean> thresholdsToBeDeleted = partitionBasedOnDeleteAction.get(true);
            List<InstanceKpiAttributeThresholdBean> thresholdsToBeModified = partitionBasedOnDeleteAction.get(false);

            result = dbi.inTransaction((conn, status) -> {
                if (!newThresholdsToBeAdded.isEmpty()) {
                    insertData(conn, newThresholdsToBeAdded);
                }

                if (!thresholdsToBeModified.isEmpty()) {
                    updateAttributeThresholds(thresholdsToBeModified, conn);
                }

                if (!thresholdsToBeDeleted.isEmpty()) {
                    int[] ids = KPI_DATA_SERVICE.deleteInstanceKpiAttributeLevelThresholds(thresholdsToBeDeleted, conn);

                    if (ids == null || ids.length == 0) {
                        log.error("Error while deleting attribute level thresholds");
                        throw new DataProcessingException("Error while deleting attribute level thresholds");
                    }

                    try {
                        new InstanceKpiThresholdRepo().closeExistingThresholds(thresholdsToBeDeleted);
                    } catch (ControlCenterException e) {
                        log.error("Error while updating endTime for the thresholds to OpenSearch");
                        throw new DataProcessingException("Error while updating endTime for the thresholds to OpenSearch");
                    }
                }
                return "Thresholds for attributes updated successfully";
            });

            if (!newThresholdsToBeAdded.isEmpty()) {
                KPI_UTIL.addInstanceKpiAttributeLevelThresholdsInRedis(newThresholdsToBeAdded);
            }
            if (!thresholdsToBeModified.isEmpty()) {
                updateThresholdsForInstanceKpi(inputThresholdBeans);
            }
            if (!thresholdsToBeDeleted.isEmpty()) {
                KPI_UTIL.deleteThresholdsForInstanceKpiInRedis(thresholdsToBeDeleted);
            }
        } catch (Exception e) {
            log.error("Unable to update KPI thresholds. Details: ", e);

            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
            } else {
                throw e;
            }
        }

        return result;
    }

    private void insertData(Handle conn, List<InstanceKpiAttributeThresholdBean> newThresholdsToBeAdded) throws DataProcessingException {
        //Percona
        int[] ids = KPI_DATA_SERVICE.addInstanceKpiAttributeLevelThresholds(newThresholdsToBeAdded, conn);
        if (ids == null || ids.length == 0) {
            log.error("Error while adding attribute level thresholds");
            throw new DataProcessingException("Error while adding attribute level thresholds");
        }

        //OpenSearch
        try {
            new InstanceKpiThresholdRepo().createThreshold(newThresholdsToBeAdded);
        } catch (ControlCenterException e) {
            log.error("Error while adding the thresholds to OpenSearch");
            throw new DataProcessingException("Error while adding the thresholds to OpenSearch");
        }
    }

    private void updateAttributeThresholds(List<InstanceKpiAttributeThresholdBean> inputThresholdBeans, Handle handle) throws DataProcessingException {
        Map<Integer, List<InstanceKpiAttributeThresholdBean>> existingThresholdBeans = KPI_DATA_SERVICE.fetchCompInstanceKpiAttrThresholds(inputThresholdBeans.get(0).getCompInstanceId(), null)
                .parallelStream()
                .collect(Collectors.groupingBy(InstanceKpiAttributeThresholdBean::getKpiId));

        Map<Boolean, List<InstanceKpiAttributeThresholdBean>> severityChangePartitions = inputThresholdBeans.parallelStream()
                .collect(Collectors.partitioningBy(t -> checkForSeverityOrGenAnomalyChange(existingThresholdBeans, t)));
        List<InstanceKpiAttributeThresholdBean> severityChangeThresholds = severityChangePartitions.get(true);
        if (!severityChangeThresholds.isEmpty()) {
            int[] ids = KPI_DATA_SERVICE.updateInstanceKpiAttributeLevelThresholdSeverityAndGenAnomaly(severityChangeThresholds, handle);

            if (ids == null || ids.length == 0) {
                log.error("Error while updating instance level thresholds severity and Generate Anomaly");
                throw new DataProcessingException("Error while updating instance level thresholds severity and Generate Anomaly");
            }
        }

        List<InstanceKpiAttributeThresholdBean> multipleChangesThresholds = severityChangePartitions.get(false);

        if (!multipleChangesThresholds.isEmpty()) {
            try {
                KPIDataService kpiDataService = new KPIDataService();

                int[] ids = KPI_DATA_SERVICE.updateInstanceKpiAttributeLevelThresholds(multipleChangesThresholds, handle);
                if (ids == null || ids.length == 0) {
                    log.error("Error while updating attribute level thresholds");
                    throw new DataProcessingException("Error while updating attribute level thresholds");
                }

                List<KpiMaintenanceStatusBean> kpiNotificationBean = new ArrayList<>();
                multipleChangesThresholds.forEach(instanceKpiAttributeThresholdBean -> kpiNotificationBean.add(KpiMaintenanceStatusBean.builder()
                        .compInstanceId(instanceKpiAttributeThresholdBean.getCompInstanceId())
                        .kpiId(instanceKpiAttributeThresholdBean.getKpiId())
                        .kpiGroupId(instanceKpiAttributeThresholdBean.getKpiGroupId())
                        .status(instanceKpiAttributeThresholdBean.getStatus())
                        .build()));

                List<KpiMaintenanceStatusBean> nonGroup = kpiNotificationBean.parallelStream().filter(x -> x.getKpiGroupId() == 0).collect(Collectors.toList());
                if (nonGroup.size() > 0) {
                    for (KpiMaintenanceStatusBean bean : nonGroup) {
                        kpiDataService.updateNonGroupInstanceKpiAnomaly(bean.getStatus(), nonGroup, null);
                    }
                }

                List<KpiMaintenanceStatusBean> group = kpiNotificationBean.parallelStream().filter(x -> !(x.getKpiGroupId() == 0)).collect(Collectors.toList());
                if (group.size() > 0) {
                    for (KpiMaintenanceStatusBean bean : group) {
                        kpiDataService.updateGroupInstanceKpiAnomaly(bean.getStatus(), group, null);
                    }
                }
            } catch (ControlCenterException e) {
                log.error("Error while updating the thresholds to percona instanceId {} ", multipleChangesThresholds.get(0).getCompInstanceId(), e);
                throw new DataProcessingException("Error while updating the thresholds");
            }

            try {
                new InstanceKpiThresholdRepo().updateThresholds(accountIdentifier, multipleChangesThresholds);
            } catch (ControlCenterException e) {
                log.error("Error while updating the thresholds to OpenSearch");
                throw new DataProcessingException("Error while updating the thresholds to OpenSearch");
            }
        }
    }

    private boolean checkForSeverityOrGenAnomalyChange(Map<Integer, List<InstanceKpiAttributeThresholdBean>> kpiVsExistingThresholdBeans,
                                           InstanceKpiAttributeThresholdBean inputThresholdBean) {

        List<InstanceKpiAttributeThresholdBean> existingThresholds = kpiVsExistingThresholdBeans.get(inputThresholdBean.getKpiId());

        boolean retVal = existingThresholds.parallelStream()
                .anyMatch(existingThreshold -> existingThreshold.getOperationId() == inputThresholdBean.getOperationId()
                        && existingThreshold.getMaxThreshold().equals(inputThresholdBean.getMaxThreshold())
                        && existingThreshold.getMinThreshold().equals(inputThresholdBean.getMinThreshold())
                        && (existingThreshold.getStatus() != inputThresholdBean.getStatus()
                        || existingThreshold.getSeverity() != inputThresholdBean.getSeverity()));

        if (retVal) {
            log.info("Change in only severity. Hence OpenSearch data will not be updated");
        }

        return retVal;
    }

    private boolean checkForGenAnomalyChange(Map<Integer, List<InstanceKpiAttributeThresholdBean>> kpiVsExistingThresholdBeans,
                                             InstanceKpiAttributeThresholdBean inputThresholdBean) {

        List<InstanceKpiAttributeThresholdBean> existingThresholds = kpiVsExistingThresholdBeans.get(inputThresholdBean.getKpiId());

        boolean retVal = existingThresholds.parallelStream()
                .anyMatch(existingThreshold -> existingThreshold.getOperationId() == inputThresholdBean.getOperationId()
                        && existingThreshold.getMaxThreshold().equals(inputThresholdBean.getMaxThreshold())
                        && existingThreshold.getMinThreshold().equals(inputThresholdBean.getMinThreshold())
                        && existingThreshold.getStatus() != inputThresholdBean.getStatus());

        if (retVal) {
            log.info("Change in only Generate Anomaly flag. Hence OpenSearch data will not be updated");
        }

        return retVal;
    }

    public void updateThresholdsForInstanceKpi(List<InstanceKpiAttributeThresholdBean> inputThresholdBeans) {
        inputThresholdBeans.forEach(thresholdBean -> {
            List<CompInstKpiEntity> instanceWiseKpis = INSTANCE_REPO.getInstanceWiseKpis(thresholdBean.getAccountIdentifier(), thresholdBean.getCompInstanceIdentifier());

            CompInstKpiEntity kpiEntity = instanceWiseKpis.stream().filter(entity -> entity.getId() == thresholdBean.getKpiId() && thresholdBean.getKpiGroupId() == entity.getGroupId()).findAny().orElse(null);
            if (kpiEntity == null) {
                log.error("The kpi details not found for the kpi [{}] and instance [{}]", thresholdBean.getKpiId(), thresholdBean.getCompInstanceId());
                return;
            }

            if (kpiEntity.getKpiViolationConfig() == null || kpiEntity.getKpiViolationConfig().isEmpty()) {
                log.debug("The threshold details for the attribute value: [{}] not found.", thresholdBean.getAttributeValue());
            }

            kpiEntity.setNotification(thresholdBean.getStatus());

            KpiViolationConfig violationConfig = KPI_UTIL.buildKpiViolationConfig(thresholdBean);

            Map<String, KpiViolationConfig> violationConfigMap = new HashMap<>();
            violationConfigMap.put(violationConfig.getAttributeValue(), violationConfig);

            Map<String, KpiViolationConfig> existingKpiViolationConfig = new HashMap<>(kpiEntity.getKpiViolationConfig());
            if (existingKpiViolationConfig.containsKey(violationConfig.getAttributeValue())) {
                existingKpiViolationConfig.replace(violationConfig.getAttributeValue(), violationConfig);
                kpiEntity.setKpiViolationConfig(existingKpiViolationConfig);
            } else {
                kpiEntity.setKpiViolationConfig(violationConfigMap);
            }

            INSTANCE_REPO.updateKpiDetailsForKpiId(accountIdentifier, thresholdBean.getCompInstanceIdentifier(), kpiEntity);
            INSTANCE_REPO.updateKpiDetailsForKpiIdentifier(accountIdentifier, thresholdBean.getCompInstanceIdentifier(), kpiEntity);
            INSTANCE_REPO.updateKpiDetails(accountIdentifier, thresholdBean.getCompInstanceIdentifier(), instanceWiseKpis);
        });
    }
}
