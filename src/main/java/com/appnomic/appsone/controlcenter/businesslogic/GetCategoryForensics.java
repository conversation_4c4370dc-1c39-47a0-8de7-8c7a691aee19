package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ComponentInstanceBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ActionScriptDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CategoryForensicsDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompInstanceForensicCategoryBean;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.pojo.CategoryForensicDetails;
import com.appnomic.appsone.controlcenter.pojo.CategoryForensicsPOJO;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class GetCategoryForensics implements BusinessLogic<List<Integer>, CategoryForensicsPOJO, CategoryForensicsPOJO> {

    private static final Logger LOGGER = LoggerFactory.getLogger(GetCategoryForensics.class);
    CategoryForensicsDataService dataService = new CategoryForensicsDataService();

    @Override
    public UtilityBean<List<Integer>> clientValidation(RequestObject request) throws ClientException {
        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }

        if (request.getQueryParams().get("instanceIds").length == 0) {
            LOGGER.error("InstanceIds are not specified in query parameters.");
            throw new ClientException("InstanceIds are not specified in queryParams");
        }
        String[] instanceIdsString = request.getQueryParams().get("instanceIds");

        List<Integer> instanceIds = new ArrayList<>();
        if (instanceIdsString == null || instanceIdsString.length == 0) {
            LOGGER.error("InstanceIds is null or empty.");
            throw new ClientException("InstanceIds is null or empty.");
        } else {
            try {
                for (String s : instanceIdsString[0].split(",")) {
                    Integer parseInt = Integer.parseInt(s.trim());
                    instanceIds.add(parseInt);
                }
            } catch (NumberFormatException e) {
                LOGGER.error("InstanceIds should be positive integers.");
                throw new ClientException("InstanceIds should be positive integers.");
            }
            if (instanceIds.parallelStream().anyMatch(i -> i < 1)) {
                LOGGER.error("InstanceIds cannot be less than 1.");
                throw new ClientException("InstanceIds cannot be less than 1.");
            }
        }

        return UtilityBean.<List<Integer>>builder()
                .accountIdentifier(identifier)
                .pojoObject(instanceIds)
                .authToken(authToken)
                .build();
    }

    @Override
    public CategoryForensicsPOJO serverValidation(UtilityBean<List<Integer>> utilityBean) throws ServerException {

        UserAccountBean userAccBean;
        try {
            userAccBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            throw new ServerException(e.getMessage());
        }

        int accountId = userAccBean.getAccount().getId();
        List<ComponentInstanceBean> instances = new ArrayList<>();
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();

        Set<Integer> instanceIds = new HashSet<>(utilityBean.getPojoObject());
        for (int instanceId : instanceIds) {
            ComponentInstanceBean bean = compInstanceDataService.getComponentInstanceByIdAndAccount(instanceId, accountId);
            if (bean == null) {
                throw new ServerException("Invalid instance Id : " + instanceId);
            }
            instances.add(bean);
        }

        Set<Integer> componentIds = instances.parallelStream().map(ComponentInstanceBean::getMstComponentId)
                .collect(Collectors.toSet());
        if (componentIds.size() != 1) {
            LOGGER.error("InstanceIds specified are mapped to different components.");
            throw new ServerException("InstanceIds specified are mapped to different components.");
        }

        return CategoryForensicsPOJO.builder()
                .accountId(accountId)
                .componentId(new ArrayList<>(componentIds).get(0))
                .instanceIds(utilityBean.getPojoObject())
                .build();
    }

    @Override
    public CategoryForensicsPOJO process(CategoryForensicsPOJO bean) throws DataProcessingException {

        try {
            Set<CategoryForensicDetails> result = new HashSet<>(dataService.getCategoryForensicMappingsForComponentId
                    (bean.getComponentId(), bean.getAccountId(), null));
            int defaultSuppression = ConfProperties.getInt(Constants.INSTANCE_FORENSIC_TRIGGER_SUPPRESSION_INTERVAL,
                    Constants.INSTANCE_FORENSIC_TRIGGER_SUPPRESSION_INTERVAL_DEFAULT);

            List<Integer> instanceIds = bean.getInstanceIds();
            for (CategoryForensicDetails mapping : result) {
                List<CompInstanceForensicCategoryBean> list = new ArrayList<>();
                mapping.setCommandArguments(ActionScriptDataService.getActionArgumentsDetails(mapping.getCommandId(), null));

                for (Integer instanceId : instanceIds) {
                    CompInstanceForensicCategoryBean compInstanceForensicCategoryBean = dataService.getCompInstanceForensicCategoryDetailsForInstanceId
                            (instanceId, mapping.getCategoryId(), mapping.getForensicId(), null);
                    if (compInstanceForensicCategoryBean != null) {
                        list.add(compInstanceForensicCategoryBean);
                    }
                }

                if (list.isEmpty()) {
                    mapping.setTriggerForensicStatus(1);
                    mapping.setSuppressionInterval(defaultSuppression);
                    continue;
                }

                List<Integer> forensicTriggerStatus = list.parallelStream()
                        .map(CompInstanceForensicCategoryBean::getShouldTrigger).collect(Collectors.toList());
                if (forensicTriggerStatus.size() == instanceIds.size() && new HashSet<>(forensicTriggerStatus).size() == 1) {
                    mapping.setTriggerForensicStatus(forensicTriggerStatus.get(0));
                } else mapping.setTriggerForensicStatus(-1);

                List<Integer> suppressionIntervals = list.parallelStream()
                        .map(CompInstanceForensicCategoryBean::getSuppressionInterval).collect(Collectors.toList());
                if (suppressionIntervals.size() == instanceIds.size() && new HashSet<>(suppressionIntervals).size() == 1
                        && suppressionIntervals.get(0) != null) {
                    mapping.setSuppressionInterval(suppressionIntervals.get(0));
                } else mapping.setSuppressionInterval(-1);
            }

            bean.setDefaultSuppressionInterval(defaultSuppression);
            bean.setCategoryForensicDetails(new ArrayList<>(result));

            return bean;

        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
    }
}
