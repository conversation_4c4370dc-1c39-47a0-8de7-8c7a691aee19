package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ActionScriptDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.ForensicActionsPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;


public class GetForensicActionsBL implements BusinessLogic<Integer, Integer, List<ForensicActionsPojo>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(GetForensicActionsBL.class);

    @Override
    public UtilityBean<Integer> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }
        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            LOGGER.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }
        String accountIdentifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(accountIdentifier)) {
            LOGGER.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        return UtilityBean.<Integer>builder()
                .accountIdentifier(accountIdentifier)
                .authToken(authToken).build();

    }

    @Override
    public Integer serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }
        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        //Check account details
        if (account == null) {
            LOGGER.error(UIMessages.INVALID_ACCOUNT_MESSAGE);
            throw new ServerException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }

        return account.getId();
    }


    @Override
    public List<ForensicActionsPojo> process(Integer accountId) throws DataProcessingException {
        ActionScriptDataService actionScriptDataService = new ActionScriptDataService();
        try {
            List<ForensicActionsPojo> data = actionScriptDataService.getForensicActions(accountId);
            for (ForensicActionsPojo action : data) {
                action.setCategoryList(actionScriptDataService.getForensicsCategoryList(action.getId()));
                action.setParameters(actionScriptDataService.getForensicsParameters(action.getId()));
            }
            return data;
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }

    }
}
