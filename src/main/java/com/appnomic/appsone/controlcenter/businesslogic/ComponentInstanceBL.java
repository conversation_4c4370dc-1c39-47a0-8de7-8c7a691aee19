package com.appnomic.appsone.controlcenter.businesslogic;


import com.appnomic.appsone.common.exception.AppsOneException;
import com.appnomic.appsone.common.util.Commons;
import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ComponentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.TagsDataService;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.Attributes;
import com.appnomic.appsone.controlcenter.pojo.ComponentInstancePojo;
import com.appnomic.appsone.controlcenter.pojo.Controller;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import spark.Request;

import java.io.IOException;
import java.security.Security;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ComponentInstanceBL {

    private ComponentInstanceBL() {
    }

    private static final int controllerId = MasterCache.getTagDetails(Constants.CONTROLLER_TAG).getId();
    private static final String MONITOR_PORT ="MonitorPort";

    public static List<ComponentInstancePojo> addClientValidations(Request request) throws RequestException {

        List<ComponentInstancePojo> instances;
        ValidationUtils.commonClientValidations(request);

        if (StringUtils.isEmpty(request.body())) {
            log.error(UIMessages.REQUEST_NULL);
            throw new RequestException(UIMessages.REQUEST_NULL);
        }

        try {
            instances = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(request.body(),
                    new TypeReference<List<ComponentInstancePojo>>() {
                    });

        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID);
            throw new RequestException(UIMessages.JSON_INVALID);
        }

        addClientValidations(instances, null);

        return instances;
    }

    private static void addClientValidations(ComponentInstancePojo instance) throws RequestException {
        if (StringUtils.isEmpty(instance.getIdentifier()))
            instance.setIdentifier(UUID.randomUUID().toString());

        instance.validate();
        if (!instance.getError().isEmpty()) {
            log.error(instance.getError().toString());
            throw new RequestException(instance.getError().toString());
        }

        List<String> serviceIdentifiers = instance.getServiceIdentifiers();
        Set<String> serviceIdentifiersSet = new HashSet<>(serviceIdentifiers);
        if (serviceIdentifiersSet.size() < serviceIdentifiers.size()) {
            String err = "Duplicate Service Identifiers for Component instance: ' " + instance.getName() + " '";
            log.error(err);
            throw new RequestException(err);
        }

        List<Attributes> attributes = instance.getAttributes();
        if (attributes != null) {
            Set<Attributes> attributesSet = new HashSet<>(attributes);
            if (attributesSet.size() < attributes.size()) {
                String err = "Duplicate Attributes for Component instance: ' " + instance.getName() + " '";
                log.error(err);
                throw new RequestException(err);
            }
        }

    }

    public static void addClientValidations(List<ComponentInstancePojo> instances, String serviceIdentifier) throws RequestException {
        if (instances != null) {
            for (ComponentInstancePojo instance : instances) {
                if (serviceIdentifier != null) {
                    instance.getServiceIdentifiers().clear();
                    instance.getServiceIdentifiers().add(serviceIdentifier);
                }
                addClientValidations(instance);
            }

            Set<ComponentInstancePojo> instanceSet = new HashSet<>(instances);
            if (instanceSet.size() < instances.size()) {
                log.error(UIMessages.DUPLICATE_COMPONENT_INSTANCE);
                throw new RequestException(UIMessages.DUPLICATE_COMPONENT_INSTANCE);
            }
        }
    }

    public static List<ComponentInstanceBean> addServerValidations(List<ComponentInstancePojo> instances, String authToken, String accountIdentifier, List<Integer> agentBeanList, Handle handle) throws RequestException, ControlCenterException {

        UserAccountBean userAccBean = ValidationUtils.commonServerValidations(authToken, accountIdentifier);

        String userId = userAccBean.getUserId();
        int accId = userAccBean.getAccount().getId();

        List<Controller> serviceList = CommonUtils.getControllersByTypeBypassCache(Constants.SERVICES_CONTROLLER_TYPE, accId);

        return addServerValidations(instances, userId, accId, serviceList, agentBeanList, handle);
    }

    public static List<ComponentInstanceBean> addServerValidations(List<ComponentInstancePojo> instances, String userId, int accountId,
                                                                   List<Controller> serviceList, List<Integer> agentIdsList, Handle handle) throws RequestException {
        List<ComponentInstanceBean> beanList = new ArrayList<>();

        ComponentInstanceBean bean = null;
        if (instances != null) {
            for (ComponentInstancePojo instance : instances) {
                bean = ComponentInstanceUtil.validateAndGetComponentInstance(instance, userId, accountId, serviceList, agentIdsList, handle);
                beanList.add(bean);
            }
        }
        //Add more validations here for multiple instances coming in array
        if (bean != null) moreValidationsForEachFields(bean);
        return beanList;
    }

    public static List<IdPojo> process(List<ComponentInstanceBean> beanList, String accountIdentifier) throws ControlCenterException {
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        dbi.inTransaction((conn, status) -> {
            List<Integer> instanceIds = new ArrayList<>();
                    for (ComponentInstanceBean componentInstanceBean : beanList) {
                        instanceIds.add(ComponentInstanceBL.addComponentInstance(componentInstanceBean, conn));
                    }
                    return instanceIds;
                });

        return ComponentInstanceUtil.addInstancesToRedis(beanList, accountIdentifier);
    }

    private static int addComponentInstance(ComponentInstanceBean bean, Handle handle) throws ControlCenterException {
        int instanceId = ComponentInstanceUtil.addComponentInstance(bean, handle);
        if (instanceId != -1) {
            if (bean.getIsUpdate() == 0) {
                addConfigWatchKPIs(bean, instanceId, handle);
            } else {
                log.info("Updated comp instance for identifier:{}, name :{}", bean.getIdentifier(), bean.getName());
            }
        }
        return instanceId;
    }

    private static AttributesViewBean getValidAttribute(List<AttributesViewBean> attributesViewBeanList, String name) throws RequestException {
        if (attributesViewBeanList != null && !attributesViewBeanList.isEmpty()) {
            AttributesViewBean bean = attributesViewBeanList.stream().filter(a -> (a.getAttributeName().equals(name.trim()))).findAny().orElse(null);
            if (bean != null) {
                return bean;
            }
        }

        String err = "Attribute '" + name + "' " + Constants.DOES_NOT_EXIST;
        log.error(err);
        throw new RequestException(err);

    }

    private static List<CompInstanceAttributesBean> getComponentAttributesListBean(ComponentInstancePojo instance, int mstComponentId, int mstCommonVersionId, String userId) throws RequestException {
        List<CompInstanceAttributesBean> attributesBeanList = new ArrayList<>();
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
        List<AttributesViewBean> attributesViewBeanList = compInstanceDataService.getAttributeViewDataByComponentAndCommonVersion(mstComponentId, mstCommonVersionId, null);
        if (instance.getAttributes() != null) {
            String err;
            for (Attributes attribute : instance.getAttributes()) {
                if (attribute.getName().isEmpty()) {
                    err = "Attribute name is empty for component instance ' " + instance.getIdentifier() + " '";
                    log.error(err);
                    throw new RequestException(err);
                }
                //no need to check for empty value, as empty values will have an entry in db

                AttributesViewBean attributesViewBean = getValidAttribute(attributesViewBeanList, attribute.getName());

                if (attributesViewBean.getAttributeType().equalsIgnoreCase("password")
                        && !StringUtils.isEmpty(attribute.getValue())) {
                    String attributeValue = attribute.getValue();
                    try {
                        attributeValue = new AECSBouncyCastleUtil().decrypt(attributeValue);
                    } catch (Exception e) {
                        err = "Password is not properly encrypted.";
                        log.error(err + " Details: {}", e.getMessage(), e);
                        throw new RequestException(err);
                    }
                    try {
                        Security.removeProvider(Constants.BC_PROVIDER_NAME);
                        attributeValue = Commons.encrypt(attributeValue);
                        attribute.setValue(attributeValue);
                    } catch (AppsOneException e) {
                        log.error("Error while encrypting. Details: {}", e.getMessage(), e);
                        throw new RequestException("Error while encrypting");
                    }
                }

                CompInstanceAttributesBean instanceAttributesBean = CompInstanceAttributesBean.builder()
                        .attributeName(attributesViewBean.getAttributeName())
                        .attributeValue(attribute.getValue())
                        .mstComponentAttributeMappingId(attributesViewBean.getMstComponentAttributeMappingId())
                        .mstCommonAttributesId(attributesViewBean.getAttributeId())
                        .userDetailsId(userId)
                        .build();
                attributesBeanList.add(instanceAttributesBean);
            }
        }
        return attributesBeanList;
    }

    public static void moreValidationsForEachFields(ComponentInstanceBean bean) throws RequestException {
        List<ComponentInstanceBean> hostList = new ArrayList<>();
        List<ComponentInstanceBean> componentInstanceList = new ArrayList<>();
            if ( bean.getIsHost() == 1) {
                hostList.add( bean);
            } else {
                componentInstanceList.add( bean);
            }
        /*
        If more than 1 host instance has same host address, then issues
         */
        for (int i = 0; i < hostList.size() - 1; i++) {
            ComponentInstanceBean hostInstanceBean1 = hostList.get(i);
            List<Integer> list1 = Arrays.stream(hostInstanceBean1.getServiceIds()).boxed().sorted(Comparator.comparing(Integer::intValue)).collect(Collectors.toList());

            for (int j = i + 1; j < hostList.size(); j++) {
                ComponentInstanceBean hostInstanceBean2 = hostList.get(j);
                if (hostInstanceBean1.getHostAddress().equals(hostInstanceBean2.getHostAddress())) {
                    String err = "Multiple Host Instance with same host address found in request Body.";
                    log.error(err);
                    throw new RequestException(err);
                }

                List<Integer> list2 = Arrays.stream(hostInstanceBean2.getServiceIds()).boxed().sorted(Comparator.comparing(Integer::intValue)).collect(Collectors.toList());
                if (equateList(list1, list2)) {
                    if (hostInstanceBean1.getMstComponentId() != hostInstanceBean2.getMstComponentId()) {
                        String err = "Multiple Host Instance with different component name mapped to same service found in request Body.";
                        log.error(err);
                        throw new RequestException(err);
                    } else {
                        if (hostInstanceBean1.getMstCommonVersionId() != hostInstanceBean2.getMstCommonVersionId()) {
                            String err = "Multiple Host Instance with different common version mapped to same service found in request Body.";
                            log.error(err);
                            throw new RequestException(err);
                        }
                    }
                }
            }
        }

        for (int i = 0; i < componentInstanceList.size() - 1; i++) {
            ComponentInstanceBean componentInstanceBean1 = componentInstanceList.get(i);
            List<Integer> list1 = Arrays.stream(componentInstanceBean1.getServiceIds()).boxed().sorted(Comparator.comparing(Integer::intValue)).collect(Collectors.toList());

            String monitorPort1 = null;
            for (CompInstanceAttributesBean compInstanceAttributesBean : componentInstanceBean1.getAttributes()) {
                if (compInstanceAttributesBean.getAttributeName().equalsIgnoreCase(MONITOR_PORT)) {
                    monitorPort1 = compInstanceAttributesBean.getAttributeValue();
                }
            }

            for (int j = i + 1; j < componentInstanceList.size(); j++) {
                ComponentInstanceBean componentInstanceBean2 = componentInstanceList.get(j);

                String monitorPort2 = null;
                for (CompInstanceAttributesBean compInstanceAttributesBean : componentInstanceBean2.getAttributes()) {
                    if (compInstanceAttributesBean.getAttributeName().equalsIgnoreCase(MONITOR_PORT)) {
                        monitorPort2 = compInstanceAttributesBean.getAttributeValue();
                    }
                }

                 /*
                If more than 1 host instance has same host address:port, then issues
                 */
                if (monitorPort1 != null && monitorPort2 != null) {
                    if ((componentInstanceBean1.getHostAddress() + ":" + monitorPort1).equals(componentInstanceBean2.getHostAddress() + ":" + monitorPort2)) {
                        String err = "Multiple component Instance with same HostAddress:MonitorPort pair found in request Body.";
                        log.error(err);
                        throw new RequestException(err);
                    }
                }

                List<Integer> list2 = Arrays.stream(componentInstanceBean2.getServiceIds()).boxed().sorted(Comparator.comparing(Integer::intValue)).collect(Collectors.toList());
                if (equateList(list1, list2)) {
                    if (componentInstanceBean1.getMstComponentId() != componentInstanceBean2.getMstComponentId()) {
                        String err = "Multiple Component Instance with different component name mapped to same service found in request Body.";
                        log.error(err);
                        throw new RequestException(err);
                    } else {
                        if (componentInstanceBean1.getMstCommonVersionId() != componentInstanceBean2.getMstCommonVersionId()) {
                            String err = "Multiple Component Instance with different common version mapped to same service found in request Body.";
                            log.error(err);
                            throw new RequestException(err);
                        }
                    }
                }
            }
        }
    }

    private static boolean equateList(List<Integer> list1, List<Integer> list2) {

        List<Integer> list3 = new ArrayList<>(list2);
        list3.retainAll(list1);

        if (list3.size() == list1.size()) {
            return true;
        } else return list3.size() > 0;

    }

    public static void add(List<ComponentInstanceBean> beanList, int serviceId, Handle handle) throws ControlCenterException {
        try {
            for (ComponentInstanceBean bean : beanList) {
                bean.setServiceIds(new int[]{serviceId});
                ComponentInstanceBL.addComponentInstance(bean, handle);
            }
        } catch (Exception e) {
            throw new ControlCenterException(Throwables.getRootCause(e).getMessage());
        }
    }

    public static void addConfigWatchKPIs(ComponentInstanceBean bean, int instanceId, Handle handle) throws ControlCenterException {
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();

        List<CompInstanceKpiGroupDetailsBean> kpiList = ComponentDataService.getConfigWatchKpis(bean.getMstComponentId(), bean.getMstComponentVersionId());
        if (kpiList != null && !kpiList.isEmpty()) {
            for (CompInstanceKpiGroupDetailsBean kpiBean : kpiList) {
                kpiBean.setCompInstanceId(instanceId);
                kpiBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                kpiBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                kpiBean.setUserDetailsId(bean.getUserDetailsId());
                kpiBean.setStatus(1);
                kpiBean.setAliasName(kpiBean.getAttributeValue() == null ? Constants.ALL : kpiBean.getAttributeValue());
                int id = compInstanceDataService.addGroupComponentInstanceKPI(kpiBean, handle);
                if (id == -1) {
                    String err = "Unable to add Group KPIs -" + kpiBean.getMstKpiDetailsId() + " for component instance id-" + instanceId;
                    log.error(err);
                    throw new ControlCenterException(err);
                }
                log.info("Added config/file watch KPIs: {}, attribute: {}, for component instance id: {}", kpiBean.getMstKpiDetailsId(),
                        kpiBean.getAttributeValue(), instanceId);
            }
        } else {
            log.info("No config/file watch KPIs found for component instance id: {}, component id:{}, component version id: {}",
                    bean.getMstComponentId(), bean.getMstComponentVersionId(), instanceId);
        }
    }

    private static void addServiceDetails(int clusterId, ComponentInstanceBean bean, Handle handle) throws ControlCenterException {
        String err;
        if (bean.getIsUpdate() == 1) {
            int deleted = TagsDataService.deleteTagMapping(controllerId, clusterId, Constants.COMP_INSTANCE_TABLE, bean.getAccountId(), handle);
            if (deleted == -1) {
                err = "Failed to remove tag mapping info for comp instance name:" + bean.getName();
                log.error(err);
                throw new ControlCenterException(err);
            }
        }

        int[] svcIds = bean.getServiceIds();
        List<String> svcIdentifiers = bean.getServiceIdentifiers();
        for (int i = 0; i < svcIdentifiers.size(); i++) {
            int tagMappingId = TagMappingBL.addTagMapping(controllerId, clusterId, Constants.COMP_INSTANCE_TABLE,
                    String.valueOf(svcIds[i]), svcIdentifiers.get(i), bean.getUserDetailsId(), bean.getAccountId(), handle);
            if (tagMappingId != -1) {
                log.info("Tag mapping data is added successfully for comp instance name :{}, cluster_id:{}, service id:{}", bean.getName(), clusterId, svcIds[i]);
            } else {
                err = "Failed to add the Tag mapping data for comp instance name:" + bean.getName();
                log.error(err);
                throw new ControlCenterException(err);
            }
        }
    }

    private static int[] getServiceIds(List<Controller> serviceList, List<String> serviceIdentifiers) throws RequestException {
        int[] serviceIds = new int[serviceIdentifiers.size()];
        String err;

        if (serviceList != null && !serviceIdentifiers.isEmpty()) {
            int i = 0;
            for (String svcIdentifier : serviceIdentifiers) {
                Controller service = serviceList.stream().filter(c -> (c.getIdentifier().equals(svcIdentifier.trim())) && c.getStatus() == 1).findAny().orElse(null);
                if (service == null) {
                    err = "Service Identifier '" + svcIdentifier + "' " + Constants.DOES_NOT_EXIST;
                    log.error(err);
                    throw new RequestException(err);
                }
                serviceIds[i++] = Integer.parseInt(service.getAppId());
            }
        }
        return serviceIds;
    }

    public static ComponentInstancePojo updateClientValidations(Request request) throws RequestException {
        ComponentInstancePojo instance;

        ValidationUtils.commonClientValidations(request);

        String identifier = request.params(Constants.COMP_INSTANCE_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            log.error(UIMessages.EMPTY_COMP_INSTANCE_IDENTIFIER);
            throw new RequestException(UIMessages.EMPTY_COMP_INSTANCE_IDENTIFIER);
        }

        if (StringUtils.isEmpty(request.body())) {
            log.error(UIMessages.REQUEST_NULL);
            throw new RequestException(UIMessages.REQUEST_NULL);
        }

        try {
            instance = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(request.body(),
                    new TypeReference<ComponentInstancePojo>() {
                    });

        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID);
            throw new RequestException(UIMessages.JSON_INVALID);
        }

        instance.updateValidate();
        if (!instance.getError().isEmpty()) {
            log.error(instance.getError().toString());
            throw new RequestException(instance.getError().toString());
        }

        List<String> serviceIdentifiers = instance.getServiceIdentifiers();
        if (serviceIdentifiers != null) {
            Set<String> serviceIdentifiersSet = new HashSet<>(serviceIdentifiers);
            if (serviceIdentifiersSet.size() < serviceIdentifiers.size()) {
                String err = "Duplicate Service Identifiers for Component instance: ' " + instance.getName() + " '";
                log.error(err);
                throw new RequestException(err);
            }
        }

        List<Attributes> attributes = instance.getAttributes();
        if (attributes != null) {
            Set<Attributes> attributesSet = new HashSet<>(attributes);
            if (attributesSet.size() < attributes.size()) {
                String err = "Duplicate Attributes for Component instance";
                log.error(err);
                throw new RequestException(err);
            }
        }

        instance.setIdentifier(identifier);

        return instance;
    }

    public static ComponentInstanceBean updateServerValidations(ComponentInstancePojo instance, String authToken, String accountIdentifier) throws RequestException {

        UserAccountBean userAccBean = ValidationUtils.commonServerValidations(authToken, accountIdentifier);
        int accountId = userAccBean.getAccount().getId();
        String userId = userAccBean.getUserId();
        String err;

        ComponentInstanceBean instanceBean = new CompInstanceDataService().getActiveComponentInstanceByIdentifierAndName(instance.getIdentifier(), null, accountId, null);

        if (instanceBean == null) {
            err = "Component Instance Identifier '" + instance.getIdentifier() + "' " + Constants.DOES_NOT_EXIST;
            log.error(err);
            throw new RequestException(err);
        }

        if (instance.getAttributes() != null) {
            List<CompInstanceAttributesBean> attributesBeanList = getComponentAttributesListBean(instance, instanceBean.getMstComponentId(), instanceBean.getMstCommonVersionId(), userId);
            instanceBean.setAttributes(attributesBeanList);
        }

        if (instance.getServiceIdentifiers() != null) {
            MasterComponentTypeBean componentTypeBean = MasterCache.getMasterComponentTypeUsingName(Constants.COMPONENT_TYPE_HOST, String.valueOf(accountId));

            if (componentTypeBean == null) {
                err = "Component with type '" + Constants.HOST + "' doesn't exist.";
                log.error(err);
                throw new RequestException(err);
            }

            List<Controller> serviceList = CommonUtils.getControllersByTypeBypassCache(Constants.SERVICES_CONTROLLER_TYPE, accountId);
            int[] serviceIds = getServiceIds(serviceList, instance.getServiceIdentifiers());

            instanceBean.setServiceIds(serviceIds);
            instanceBean.setServiceIdentifiers(instance.getServiceIdentifiers());

            //for node instances, service list can be updated
            int appId = 0; //it will be set only for node instances
            int serviceTypeTagId = MasterCache.getTagDetails(Constants.SERVICE_TYPE_TAG).getId();
            int tagMappingId = TagsDataService.getTagMappingId(serviceTypeTagId, serviceIds[0], Constants.CONTROLLER, Constants.SERVICE_TYPE_DEFAULT, Constants.KUBERNETES, accountId, null);
            if (tagMappingId <= 0 || componentTypeBean.getId() != instanceBean.getMstComponentTypeId()) {
                err = "Only Instance with component type '" + Constants.HOST + "' and service identifier of type '" + Constants.KUBERNETES + "' can be updated with new service identifiers";
                log.error(err);
                throw new RequestException(err);
            }

            instanceBean.setAppId(appId);
        }

        if (instance.getSupervisorId() != 0 && instance.getSupervisorId() != instanceBean.getSupervisorId()) {
            instanceBean.setSupervisorId(instance.getSupervisorId());
        }

        instanceBean.setIsUpdate(1);

        return instanceBean;

    }

    public static int update(ComponentInstanceBean bean) throws ControlCenterException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            return dbi.inTransaction((conn, status) -> updateComponentInstance(bean, conn));
        } catch (Exception e) {
            throw new ControlCenterException(Throwables.getRootCause(e).getMessage());
        }
    }

    private static int updateComponentInstance(ComponentInstanceBean bean, Handle handle) throws ControlCenterException {
        int res = 1;

        //update attributes if provided
        ComponentInstanceUtil.addAttributes(bean, handle, true);
        //only node instances can be updated for service identifiers
        if (bean.getAppId() > 0) {
            int clusterId = new CompInstanceDataService().getComponentInstanceIdByComponentServiceCluster(bean.getMstComponentId(), bean.getMstComponentVersionId(), bean.getMstComponentTypeId(), bean.getAppId(), controllerId, bean.getAccountId(), handle);
            addServiceDetails(clusterId, bean, handle);
        }

        return res;
    }
}
