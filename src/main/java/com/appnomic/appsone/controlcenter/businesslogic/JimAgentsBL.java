package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.AgentInstanceMapping;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.entities.BasicAgentBean;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.InstanceAttributes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

public class JimAgentsBL implements BusinessLogic<Integer, UtilityBean<Integer>, List<AgentInstanceMapping>> {

    private static final Logger log = LoggerFactory.getLogger(JimAgentsBL.class);

    @Override
    public UtilityBean<Integer> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String accountIdString = requestObject.getParams().get(UIMessages.ACCOUNT_IDENTIFIER);

        if (StringUtils.isEmpty(accountIdString)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String serviceIdStr = requestObject.getParams().get(UIMessages.SERVICE_IDENTIFIER.toLowerCase());

        if (serviceIdStr == null || serviceIdStr.trim().isEmpty()) {
            log.error(UIMessages.SERVICE_EMPTY_ERROR_MESSAGE, serviceIdStr);
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        }

        int serviceId = 0;
        try {
            serviceId = Integer.parseInt(serviceIdStr);
        } catch (Exception e) {
            log.error("Invalid service id [{}]", serviceId);
            throw new ClientException("Invalid service id");
        }

        return UtilityBean.<Integer>builder()
                .accountIdentifier(accountIdString)
                .authToken(authKey)
                .pojoObject(serviceId)
                .build();
    }

    @Override
    public UtilityBean<Integer> serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getAccountIdentifier();
        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        int serviceId = utilityBean.getPojoObject();

        ServiceRepo serviceRepo = new ServiceRepo();
        BasicEntity serviceConfigurationById = serviceRepo.getServiceConfigurationById(accountIdentifier, serviceId);

        if(serviceConfigurationById == null) {
            log.error("Obtained NULL while fetching service details from Redis for the service Id {} of account {}", serviceId, accountIdentifier);
            throw new ServerException("Obtained NULL while fetching service details from Redis for the service Id " + serviceId);
        }

        utilityBean.setServiceId(serviceConfigurationById.getIdentifier());
        return utilityBean;
    }

    @Override
    public List<AgentInstanceMapping> process(UtilityBean<Integer> bean) throws DataProcessingException {
        /*Get all JIM agents mapped to respective service*/
        String accountIdentifier = bean.getAccountIdentifier();
        String serviceIdentifier = bean.getServiceId();

        ServiceRepo serviceRepo = new ServiceRepo();
        List<BasicAgentBean> agents = serviceRepo.getAgentsByServiceIdentifier(accountIdentifier, serviceIdentifier);

        if(agents.isEmpty()) {
            log.error("Obtained empty results from Redis when queried for service level agents for the service {} of account {}", serviceIdentifier, bean.getAccountIdentifier());
            throw new DataProcessingException("Obtained empty results from Redis when queried for service level agents");
        }

        List<BasicAgentBean> jimAgents = agents.stream()
                .filter(f -> f.getStatus() == 1)
                .filter(f -> f.getType().equals(Constants.JIM_AGENT_SUB_TYPE))
                .distinct().collect(Collectors.toList());

        if(jimAgents.isEmpty()) {
           log.info("No JIM agents found for the service {} of account {}", serviceIdentifier, accountIdentifier);
           return Collections.emptyList();
        }

        log.info("List of JIM agents mapped to the service {} are {}", serviceIdentifier, jimAgents);

        log.debug("Fetching instances mapped to this JIM agent");


        List<AgentInstanceMapping> agentInstanceMappings = new ArrayList<>();

        InstanceRepo instanceRepo = new InstanceRepo();
        for (BasicAgentBean agent : jimAgents) {
            List<BasicEntity> agentInstances = instanceRepo.getAgentInstances(accountIdentifier, agent.getIdentifier());

            if (agentInstances.isEmpty()) {
                log.error("Obtained empty result when queried for agent level instance from Redis. Details: Agent {}, Account {}", agent.getIdentifier(), accountIdentifier);
                continue;
            }

            List<String> instanceIdentifiers = agentInstances.stream()
                    .map(BasicEntity::getIdentifier).distinct()
                    .collect(Collectors.toList());

            Map<String, List<InstanceAttributes>> instanceAttributeMap = new HashMap<>();

            instanceIdentifiers.stream().peek(instance -> {
                List<InstanceAttributes> attributes = instanceRepo.getAttributes(accountIdentifier, instance);
                if (attributes.isEmpty()) {
                    log.warn("No attributes are mapped to this instance {}. Adding empty list attribute list to this instance", instance);
                    instanceAttributeMap.put(instance, new ArrayList<>());
                } else {
                    instanceAttributeMap.put(instance, attributes);
                }
            });

            agentInstanceMappings.add(AgentInstanceMapping.builder()
                    .agentId(agent.getId())
                    .agentName(agent.getName())
                    .agentIdentifier(agent.getIdentifier())
                    .agentType(agent.getType())
                    .physicalAgentIdentifier(agent.getPhysicalAgentIdentifier())
                    .compInstanceIdentifier(instanceIdentifiers)
                    .compInstanceAttributesMap(instanceAttributeMap)
                    .build());
        }
        return agentInstanceMappings;
    }
}
