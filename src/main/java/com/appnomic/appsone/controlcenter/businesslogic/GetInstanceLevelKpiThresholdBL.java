package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.KPIDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ThresholdDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.KpiBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.KpiIdVsAttribute;
import com.appnomic.appsone.controlcenter.dao.opensearch.KPIGroupAttributesRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.KpiAttrThresholdInfo;
import com.appnomic.appsone.controlcenter.pojo.KpiAttributeThresholdInfo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class GetInstanceLevelKpiThresholdBL implements BusinessLogic<InstancesKpisBean, InstancesKpisBean, KpiAttrThresholdInfo> {

    private int accountId;
    private String accountIdentifier;
    String userId;
    private final Map<Integer, String> compInstanceIdVsIdentifier = new HashMap<>();
    private static final KPIDataService KPI_DATA_SERVICE = new KPIDataService();
    private int COMP_INST_RANGE = ConfProperties.getInt(Constants.COMP_INST_RANGE, Constants.COMP_INST_RANGE_DEFAULT_VALUE);

    @Override
    public UtilityBean<InstancesKpisBean> clientValidation(RequestObject requestObject) throws ClientException {
        log.debug("GetInstanceLevelKpiThresholdBL:clientValidation: BEGIN");

        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Account identifier is invalid");
            throw new ClientException("Account identifier is invalid");
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);

        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        List<Integer> instanceIds = new ArrayList<>();
        int kpiId = 0;

        if (requestObject.getQueryParams() != null && !requestObject.getQueryParams().isEmpty()) {
            if (requestObject.getQueryParams().containsKey("instanceIds")) {
                instanceIds = Arrays.stream(requestObject.getQueryParams().get("instanceIds")[0].split(","))
                        .map(c -> Integer.parseInt(c.trim())).collect(Collectors.toList());
            }
            if (requestObject.getQueryParams().containsKey("kpiId")) {
                kpiId = Integer.parseInt(requestObject.getQueryParams().get("kpiId")[0]);
            }
        }

        if(instanceIds.isEmpty() || instanceIds.contains(0)) {
            log.error("Invalid instance ID. All the instance IDs should be a non-zero integer");
            throw new ClientException("Invalid instance ID. All the instance IDs should be a non-zero integer");
        }

        if(kpiId == 0) {
            log.error("Invalid KPI ID. It should be a non-zero integer");
            throw new ClientException("Invalid KPI ID. It should be a non-zero integer");
        }

        InstancesKpisBean bean = InstancesKpisBean.builder()
                .instanceIds(instanceIds)
                .kpiId(kpiId)
                .build();

        log.debug("GetInstanceLevelKpiThresholdBL:clientValidation: END with instancesKpiBean: {}", bean);

        return UtilityBean.<InstancesKpisBean>builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .pojoObject(bean)
                .build();
    }

    @Override
    public InstancesKpisBean serverValidation(UtilityBean<InstancesKpisBean> utilityBean) throws ServerException {
        log.debug("GetInstanceLevelKpiThresholdBL:serverValidation: BEGIN");

        userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        InstancesKpisBean instancesKpisBean = utilityBean.getPojoObject();
        List<Integer> instanceIds = instancesKpisBean.getInstanceIds();
        int kpiId = instancesKpisBean.getKpiId();
        accountId = account.getId();

        KpiBean kpiBean = KPI_DATA_SERVICE.fetchKpiUsingKpiId(kpiId, accountId, null);
        if (null == kpiBean) {
            log.error("KPI with ID [{}] is unavailable", kpiId);
            throw new ServerException(String.format("KPI with ID [%d] is unavailable", kpiId));
        }

        int discoveryFlag;
        try {
            discoveryFlag = new KPIDataService().getGroupKpiDiscovery(kpiBean.getGroupKpiId(), null);
        } catch (ControlCenterException e) {
            throw new ServerException(e.getMessage());
        }

        instancesKpisBean.setGroupKpiId(kpiBean.getGroupKpiId());
        instancesKpisBean.setDataType(kpiBean.getDataType());
        instancesKpisBean.setUnit(kpiBean.getMeasureUnits());
        instancesKpisBean.setDiscovery(discoveryFlag);

        for (int instanceId : instanceIds) {
            ComponentInstanceBean compInst = new CompInstanceDataService().getComponentInstanceByIdAndAccount(instanceId, accountId);

            if (compInst == null) {
                log.error("Component instance with ID [{}] with account ID [{}] is unavailable", instanceId, accountId);
                throw new ServerException("Invalid instanceId provided");
            }

            List<InstanceKpiAttributeThresholdBean> thresholdBeans;
            try {
                thresholdBeans = ThresholdDataService.getCompInstanceThresholdDetail(accountId, instanceId, kpiId);
            } catch (ControlCenterException e) {
                throw new ServerException(e.getMessage());
            }

            List<InstanceKpiAttributeThresholdBean> validOperationIdList = thresholdBeans.parallelStream().filter(t -> {
                ViewTypes operationTypeView = MasterCache.getMstSubTypeForSubTypeId(t.getOperationId());

                return operationTypeView != null;
            }).collect(Collectors.toList());

            if (validOperationIdList.size() != thresholdBeans.size()) {
                log.error("KPI(s) mapped to instanceId [{}] have invalid operationId", instanceId);
                throw new ServerException("KPI(s) have invalid operationId");
            }

            compInstanceIdVsIdentifier.put(instanceId, compInst.getIdentifier());
        }

        log.debug("GetInstanceLevelKpiThresholdBL:serverValidation: END with instancesKpiBean: {}", instancesKpisBean);

        return instancesKpisBean;
    }

    @Override
    public KpiAttrThresholdInfo process(InstancesKpisBean instancesKpisBean) throws DataProcessingException {
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        try {
            return dbi.inTransaction((conn, status) -> getKpiAttrThresholdInfo(instancesKpisBean, conn));
        } catch (Exception e) {
            log.error("Unable to fetch KPI thresholds. Details: ", e);

            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
            } else {
                throw e;
            }
        }
    }

    private KpiAttrThresholdInfo getKpiAttrThresholdInfo(InstancesKpisBean instancesKpisBean, Handle handle) throws DataProcessingException {
        log.debug("GetInstanceLevelKpiThresholdBL:getKpiAttrThresholdInfo: BEGIN");
        Long range = COMP_INST_RANGE * 60 * 1000L;
        List<CompInstanceKpiGroupDetailsBean> mappedKpis;
        Set<String> attributes = new HashSet<>();
        List<InstanceKpiAttributeThresholdBean> thresholdBeanList = new ArrayList<>();
        Map<KpiIdVsAttribute, List<InstanceKpiAttributeThresholdBean>> groupingBasedOnAttribute = new HashMap<>();
        Set<KpiIdVsAttribute> instanceThresholdNotPresent = new HashSet<>();

        List<Integer> instanceIds = instancesKpisBean.getInstanceIds();
        int kpiId = instancesKpisBean.getKpiId();
        int groupKpiId = instancesKpisBean.getGroupKpiId();

        for (int instanceId : instanceIds) {
            try {
                if (groupKpiId == 0) {
                    mappedKpis = KPI_DATA_SERVICE.getNonGroupKpiListForCompInstance(instanceId, kpiId);
                } else {
                    mappedKpis = KPI_DATA_SERVICE.getGroupKpiListForCompInstance(instanceId, groupKpiId);

                    if (1 == instancesKpisBean.getDiscovery()) {
                        try {
                            attributes.addAll(new KPIGroupAttributesRepo()
                                    .getGroupKpiAttributesWithDataCollected(accountIdentifier, compInstanceIdVsIdentifier.get(instanceId), range, new HashSet<Integer>() {{
                                        add(kpiId);
                                    }}));
                        }catch (ControlCenterException cce){
                            log.error("No kpi group attributes found for the instances - {} - ",compInstanceIdVsIdentifier.get(instanceId), cce);
                        }
                    } else {
                        boolean isAttrNotPresent = mappedKpis.parallelStream().noneMatch(e -> e.getMstKpiDetailsId() == kpiId && Constants.ALL.equalsIgnoreCase(e.getAttributeValue()));

                        if(isAttrNotPresent) {
                            attributes.add(Constants.ALL);

                            CompInstanceKpiGroupDetailsBean m = mappedKpis.get(0);
                            CompInstanceKpiGroupDetailsBean kpiBean = new CompInstanceKpiGroupDetailsBean();
                            kpiBean.setAttributeValue(Constants.ALL);
                            kpiBean.setStatus(1);
                            kpiBean.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                            kpiBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                            kpiBean.setUserDetailsId(userId);
                            kpiBean.setCompInstanceId(instanceId);
                            kpiBean.setMstProducerKpiMappingId(m.getMstProducerKpiMappingId());
                            kpiBean.setCollectionInterval(m.getCollectionInterval());
                            kpiBean.setMstKpiDetailsId(m.getMstKpiDetailsId());
                            kpiBean.setIsDiscovery(0);
                            kpiBean.setKpiGroupName(m.getKpiGroupName());
                            kpiBean.setMstKpiGroupId(m.getMstKpiGroupId());
                            kpiBean.setMstProducerId(m.getMstProducerId());
                            kpiBean.setNotification(m.getNotification());
                            kpiBean.setAttributeStatus(1);
                            kpiBean.setIsGroup(1);
                            kpiBean.setAliasName(Constants.ALL);
                            new CompInstanceDataService().addGroupComponentInstanceKPI(kpiBean, handle);
                        }
                    }
                }

                List<InstanceKpiAttributeThresholdBean> list = ThresholdDataService.getCompInstanceThresholdDetail(accountId, instanceId, kpiId);
                thresholdBeanList.addAll(list);

                mappedKpis.forEach(c -> {
                    attributes.add(c.getAttributeValue());
                    boolean attrNotPresent = list.parallelStream().noneMatch(i -> c.getAttributeValue().equals(i.getAttributeValue()));
                    if(attrNotPresent) {
                        instanceThresholdNotPresent.add(new KpiIdVsAttribute(kpiId, c.getAttributeValue()));
                    }
                });

            } catch (ControlCenterException e) {
                throw new DataProcessingException(e.getMessage());
            }

           groupingBasedOnAttribute.putAll(thresholdBeanList.parallelStream().collect(Collectors.groupingBy(i -> new KpiIdVsAttribute(i.getKpiId(), i.getAttributeValue()))));
        }

        Set<KpiAttributeThresholdInfo> output = thresholdBeanList.parallelStream().map(e -> {
            attributes.add(e.getAttributeValue());

            if (instanceIds.size() == 1) {
                ViewTypes operationTypeView = MasterCache.getMstSubTypeForSubTypeId(e.getOperationId());

                KpiAttributeThresholdInfo.Severity thresholdSeverity = KpiAttributeThresholdInfo.Severity.builder()
                        .common(1).value(e.getSeverity()).build();

                KpiAttributeThresholdInfo.Status generateEvents = KpiAttributeThresholdInfo.Status.builder()
                        .common(1).value(e.getStatus()).build();

                KpiAttributeThresholdInfo.ThresholdDetails thresholdOperation = KpiAttributeThresholdInfo.ThresholdDetails.builder()
                        .common(1).value(operationTypeView.getSubTypeName())
                        .maxThreshold(e.getMaxThreshold())
                        .minThreshold(e.getMinThreshold())
                        .build();

                return KpiAttributeThresholdInfo.builder()
                        .kpiId(e.getKpiId())
                        .groupKpiId(e.getKpiGroupId())
                        .attributeValue(e.getAttributeValue())
                        .dataType(instancesKpisBean.getDataType())
                        .unit(instancesKpisBean.getUnit())
                        .operation(thresholdOperation)
                        .status(generateEvents)
                        .severity(thresholdSeverity)
                        .build();
            }

            boolean commonThreshold = !instanceThresholdNotPresent.contains(new KpiIdVsAttribute(e.getKpiId(), e.getAttributeValue()));

            List<InstanceKpiAttributeThresholdBean> beans = groupingBasedOnAttribute.getOrDefault(new KpiIdVsAttribute(e.getKpiId(), e.getAttributeValue()), new ArrayList<>());
            InstanceKpiAttributeThresholdBean firstBean = beans.get(0);

            KpiAttributeThresholdInfo.Severity thresholdSeverity;
            KpiAttributeThresholdInfo.Status generateEvents;
            KpiAttributeThresholdInfo.ThresholdDetails thresholdOperation;

            boolean sameSeverity = thresholdBeanList.parallelStream().allMatch(x -> x.getSeverity() == firstBean.getSeverity());
            if (sameSeverity) {
                thresholdSeverity = KpiAttributeThresholdInfo.Severity.builder()
                        .common(1).value(e.getSeverity()).build();
            } else {
                thresholdSeverity = KpiAttributeThresholdInfo.Severity.builder()
                        .common(0).value(0).build();
            }

            boolean sameStatus = thresholdBeanList.parallelStream().allMatch(x -> x.getStatus() == firstBean.getStatus());
            if (sameStatus) {
                generateEvents = KpiAttributeThresholdInfo.Status.builder()
                        .common(1).value(e.getStatus()).build();
            } else {
                generateEvents = KpiAttributeThresholdInfo.Status.builder()
                        .common(0).value(0).build();
            }

            boolean sameThresholdOperation = beans.parallelStream()
                    .allMatch(x -> x.getOperationId() == firstBean.getOperationId()
                    && x.getMaxThreshold().equals(firstBean.getMaxThreshold())
                    && x.getMinThreshold().equals(firstBean.getMinThreshold()));

            if (sameThresholdOperation && commonThreshold) {
                ViewTypes operationTypeView = MasterCache.getMstSubTypeForSubTypeId(firstBean.getOperationId());

                thresholdOperation = KpiAttributeThresholdInfo.ThresholdDetails.builder()
                        .common(1).value(operationTypeView.getSubTypeName())
                        .maxThreshold(firstBean.getMaxThreshold())
                        .minThreshold(firstBean.getMinThreshold())
                        .build();
            } else {
                thresholdOperation = KpiAttributeThresholdInfo.ThresholdDetails.builder()
                        .common(0).value(null)
                        .maxThreshold(null)
                        .minThreshold(null)
                        .build();
            }

            return KpiAttributeThresholdInfo.builder()
                    .kpiId(e.getKpiId())
                    .groupKpiId(e.getKpiGroupId())
                    .attributeValue(e.getAttributeValue())
                    .dataType(instancesKpisBean.getDataType())
                    .unit(instancesKpisBean.getUnit())
                    .operation(thresholdOperation)
                    .status(generateEvents)
                    .severity(thresholdSeverity)
                    .build();
        }).collect(Collectors.toSet());

        KpiAttrThresholdInfo kpiAttrThresholdInfo = KpiAttrThresholdInfo.builder()
                .attributes(attributes.parallelStream().filter(Objects::nonNull).collect(Collectors.toSet()))
                .thresholds(output)
                .build();

        log.debug("GetInstanceLevelKpiThresholdBL:getKpiAttrThresholdInfo: END with attributes list [{}] and " +
                "thresholds list size [{}]", attributes.size(), output.size());

        return kpiAttrThresholdInfo;
    }
}
