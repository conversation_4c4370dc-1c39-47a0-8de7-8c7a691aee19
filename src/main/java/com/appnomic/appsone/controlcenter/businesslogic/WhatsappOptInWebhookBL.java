package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UserAttributesBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.UserDataService;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.OptInEnum;
import com.appnomic.appsone.controlcenter.pojo.OptInRequestPojo;
import com.appnomic.appsone.controlcenter.pojo.OptInWebhookRequestPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.skife.jdbi.v2.DBI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.text.MessageFormat;

public class WhatsappOptInWebhookBL implements BusinessLogic<OptInWebhookRequestPojo, UtilityBean<OptInRequestPojo>, String> {
    private static final Logger LOGGER = LoggerFactory.getLogger(WhatsappOptInWebhookBL.class);

    @Override
    public UtilityBean<OptInWebhookRequestPojo> clientValidation(RequestObject requestObject) throws ClientException {
        LOGGER.debug("Inside Client validation");

        OptInWebhookRequestPojo optInWebhookRequestPojo;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            optInWebhookRequestPojo = objectMapper.readValue(requestObject.getBody(), new TypeReference<OptInWebhookRequestPojo>() {});
        } catch (IOException e) {
            throw new ClientException(e, Constants.REQUEST_BODY_INVALID_ERROR_MESSAGE);
        }

        return UtilityBean.<OptInWebhookRequestPojo>builder()
                .pojoObject(optInWebhookRequestPojo)
                .build();
    }

    @Override
    public UtilityBean<OptInRequestPojo> serverValidation(UtilityBean<OptInWebhookRequestPojo> bean) throws ServerException {
        LOGGER.debug("Inside Server validation");

        UserAttributesBean userAttributesBean;
        try {
            UserDataService userDataService = new UserDataService();
            userAttributesBean = userDataService.getUserAttributesByContact(bean.getPojoObject().getMessages().get(0).getFrom());

            if (userAttributesBean == null) {
                LOGGER.error("Invalid User {}", bean.getPojoObject().getMessages().get(0).getFrom());
                throw new ServerException(MessageFormat.format("Invalid User {0}", bean.getPojoObject().getMessages().get(0).getFrom()));
            }
        } catch (ControlCenterException e) {
            LOGGER.error("Invalid User {}", bean.getPojoObject().getMessages().get(0).getFrom());
            throw new ServerException(MessageFormat.format("Invalid User {0}", bean.getPojoObject().getMessages().get(0).getFrom()));
        }

        String text = bean.getPojoObject().getMessages().get(0).getText().getBody();
        OptInRequestPojo pojo = new OptInRequestPojo();
        pojo.setOptInStatus(((text.equalsIgnoreCase("Yes")) ? OptInEnum.ACCEPTED.getStatusCode() : OptInEnum.REJECTED.getStatusCode()));
        pojo.setOptInLastRequestTimeStr(bean.getPojoObject().getMessages().get(0).getTimestamp());
        pojo.setUserAttributesBean(userAttributesBean);

        return UtilityBean.<OptInRequestPojo>builder()
                .pojoObject(pojo)
                .build();
    }

    @Override
    public String process(UtilityBean<OptInRequestPojo> bean) throws DataProcessingException {
        LOGGER.debug("Inside Process");

        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        return dbi.inTransaction((conn, status) -> {
            try {
                new UserDataService().updateUserOptIn(bean.getPojoObject(), conn);
                return OptInEnum.values()[bean.getPojoObject().getOptInStatus()].getStatus();
            } catch (Exception e) {
                throw new DataProcessingException(e, "Failed to update whatsapp opt-in.");
            }
        });
    }
}
