package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AuditBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserDetailsBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.AuditTrailPojo;
import com.appnomic.appsone.controlcenter.pojo.Controller;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

public class AuditTrailBL implements BusinessLogic<AuditTrailBean, AuditTrailBean, List<AuditTrailPojo>> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AuditTrailBL.class);
    private static final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    private static final Map<Integer, MasterBigFeatureBean> activityTypeMap = MasterCache.getBigFeatureList().stream().collect(Collectors.toMap(MasterBigFeatureBean::getId, e -> e));
    private static final Map<Integer, MasterPageActionBean> subActivityTypeMap = MasterCache.getPageActionList().stream().collect(Collectors.toMap(MasterPageActionBean::getId, e -> e));
    private final Map<String, UserDetailsBean> allUserMap = new UserDataService().getUsers().stream().collect(Collectors.toMap(UserDetailsBean::getId, e -> e));
    private Map<Integer, List<ViewApplicationServiceMappingBean>> svcMappedToApp = new HashMap<>();
    @Override
    public UtilityBean<AuditTrailBean> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            LOGGER.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String[] fromTime = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_FROM_TIME);
        if (fromTime == null || StringUtils.isEmpty(fromTime[0])) {
            LOGGER.error(Constants.INVALID_FROM_TIME);
            throw new ClientException(Constants.INVALID_FROM_TIME);
        }

        String[] toTime = requestObject.getQueryParams().get(Constants.REQUEST_PARAM_TO_TIME);
        if (toTime == null || StringUtils.isEmpty(toTime[0])) {
            LOGGER.error(Constants.INVALID_TO_TIME);
            throw new ClientException(Constants.INVALID_TO_TIME);
        }

        String[] serviceIds = requestObject.getQueryParams().get(Constants.AUDIT_PARAM_SERVICE_NAME);
        String[] applicationIds = requestObject.getQueryParams().get(Constants.AUDIT_PARAM_APPLICATION_NAME);
        String[] activityTypeIds = requestObject.getQueryParams().get(Constants.AUDIT_PARAM_ACTIVITY_TYPE);
        String[] userId = requestObject.getQueryParams().get(Constants.AUDIT_PARAM_USER_NAME);

        List<Integer> appIds = getIds(applicationIds);
        List<Integer> srvIds = getIds(serviceIds);
        List<Integer> activityIds = getActivityTypeIds(activityTypeIds);

        AuditTrailBean auditTrailBean = null;

        if (!StringUtils.isEmpty(toTime[0]) && !StringUtils.isEmpty(fromTime[0])) {
            auditTrailBean = AuditTrailBean.builder()
                    .appIds(appIds)
                    .serviceIds(srvIds)
                    .bigFeatureIds(activityIds).build();

            auditTrailBean.setFromTime(parseFromTime(fromTime[0]));

            auditTrailBean.setToTime(parseToTime(toTime[0]));

            if (userId != null && !StringUtils.isEmpty(userId[0])) {
                auditTrailBean.setUserId(userId[0]);
            }
        }
        return UtilityBean.<AuditTrailBean>builder()
                .accountIdentifier(identifier)
                .authToken(authToken)
                .pojoObject(auditTrailBean).build();
    }

    @Override
    public AuditTrailBean serverValidation(UtilityBean<AuditTrailBean> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }
        AccountBean accounts = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (accounts == null) {
            LOGGER.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }
        int accountId = accounts.getId();
        String accountIdentifier = accounts.getIdentifier();

        UserAccessDetails userAccessDetails = UserValidationUtil.getUserAccessDetails(userId, accountIdentifier);
        if (userAccessDetails == null) {
            LOGGER.error("User access details is invalid for user [{}] and account [{}]", userId, accountIdentifier);
            throw new ServerException(String.format("User access details is invalid for user %s and account %s", userId, accountIdentifier));
        }
        if (userAccessDetails.getApplicationIdentifiers() == null || userAccessDetails.getApplicationIdentifiers().isEmpty()) {
            LOGGER.error("Applications unavailable for user [{}] and account [{}]", userId, accountIdentifier);
            throw new ServerException("Applications unavailable for user");
        }

        MasterTimezoneBean timezoneBean;
        try {
            timezoneBean = AccountDataService.getAccountTimezoneDetails(accounts.getId(), null);
        } catch (ControlCenterException e) {
            throw new ServerException(e.getMessage());
        }
        if (timezoneBean == null) {
            LOGGER.error(UIMessages.INVALID_TIMEZONE);
            throw new ServerException(UIMessages.INVALID_TIMEZONE);
        }

        long offset = timezoneBean.getTimeOffset();
        String gmtTZ = String.format(Constants.TIME_ZONE_FORMAT,
                offset < Constants.DEFAULT_VALUE1 ? "-" : "+",
                Math.abs(offset) / Constants.DEFAULT_VALUE2,
                Math.abs(offset) / Constants.DEFAULT_VALUE3 % Constants.DEFAULT_VALUE4);

        AuditTrailBean auditTrailBean = utilityBean.getPojoObject();
        auditTrailBean.setAccountId(accountId);
        auditTrailBean.setTimeZone(gmtTZ);
        auditTrailBean.setDefaultTimeZone(Constants.DEFAULT_TIME_ZONE);

        for (Integer appId : utilityBean.getPojoObject().getAppIds()) {
            if (!userAccessDetails.getApplicationIds().contains(appId)) {
                throw new ServerException(UIMessages.ERROR_INVALID_APPLICATION_ID);
            }
        }

        for (Integer svcId : utilityBean.getPojoObject().getServiceIds()) {
            if (!userAccessDetails.getServiceIds().contains(svcId)) {
                throw new ServerException(UIMessages.ERROR_INVALID_SERVICE_ID);
            }
        }

        for (Integer featureId : utilityBean.getPojoObject().getBigFeatureIds()) {
            if (!activityTypeMap.containsKey(featureId)) {
                throw new ServerException(UIMessages.ERROR_INVALID_BIG_FEATURE_ID);
            }
        }

        if (utilityBean.getPojoObject().getServiceIds() == null || utilityBean.getPojoObject().getServiceIds().size() == 0) {
            utilityBean.getPojoObject().setServiceIds(userAccessDetails.getServiceIds());
        }
        return auditTrailBean;
    }

    @Override
    public List<AuditTrailPojo> process(AuditTrailBean bean) throws DataProcessingException {
        String whereClause = getWhereClause(bean);
        List<AuditTrailPojo> auditTrailData;
        try {
            List<AuditBean> auditBeanDb = new AuditTrailDataService().getAuditTrail(bean, whereClause);
            auditTrailData = getAuditDataList(bean, auditBeanDb);
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }

        auditTrailData.sort(Comparator.comparing(AuditTrailPojo::getAuditTime).reversed().thenComparing(AuditTrailPojo::getUpdatedBy));

        return auditTrailData;
    }

    private List<AuditTrailPojo> getAuditDataList(AuditTrailBean bean, List<AuditBean> auditBeanDb) throws ControlCenterException {
        Map<Integer, Controller> ControllerMap = MasterCache.getControllerList(bean.getAccountId())
                .stream()
                .collect(Collectors.toMap(e -> Integer.parseInt(e.getAppId()), e -> e));

        List<AuditTrailPojo> auditTrailData = new ArrayList<>();
        for (AuditBean auditBean : auditBeanDb) {
            AuditTrailPojo auditTrailPojo = new AuditTrailPojo();

            if (auditBean.getBigFeatureId() > 0) {
                MasterBigFeatureBean activityType = activityTypeMap.get(auditBean.getBigFeatureId());
                auditTrailPojo.setActivityType(activityType.getName());
            }
            if (auditBean.getPageActionId() > 0) {
                MasterPageActionBean subActivityType = subActivityTypeMap.get(auditBean.getPageActionId());
                auditTrailPojo.setSubActivityType(subActivityType.getName());
            }
            if (auditBean.getSvcId() > 0) {
                Controller service = ControllerMap.get(auditBean.getSvcId());
                if (service != null) {
                    auditTrailPojo.setServiceName(service.getName());
                }
            }

            try {
                Timestamp timestampGMT = DateTimeUtil.getTimestampInGMT(auditBean.getAuditTime());
                Long auditEpochTime = DateTimeUtil.getGMTToEpochTime(String.valueOf(timestampGMT));
                auditTrailPojo.setAuditTime(auditEpochTime);
            } catch (ParseException e) {
                LOGGER.info("Error parsing audit timestamp. Details: {}", e.getMessage(), e);
                throw new ControlCenterException("Error while parsing audit timestamp");
            }

            auditTrailPojo.setOperationType(auditBean.getOperationType());

            setData(auditTrailPojo, auditBean);

            auditTrailData.addAll(setApplicationNames(auditTrailPojo,auditBean));
        }

        return auditTrailData;
    }

    private List<AuditTrailPojo> setApplicationNames(AuditTrailPojo auditTrailPojo, AuditBean auditBean) {
        List<AuditTrailPojo> result = new ArrayList<>();
        List<ViewApplicationServiceMappingBean> viewApplicationServiceMappingBeans = svcMappedToApp.get(auditBean.getSvcId());
        if(viewApplicationServiceMappingBeans != null && !viewApplicationServiceMappingBeans.isEmpty()){
            for(ViewApplicationServiceMappingBean app: viewApplicationServiceMappingBeans){
                AuditTrailPojo pojo = new AuditTrailPojo(auditTrailPojo);
                pojo.setApplicationName(app.getApplicationName());
                result.add(pojo);
            }

        }else{
            result.add(auditTrailPojo);
        }
        return result;
    }

    private void setData(AuditTrailPojo auditTrailPojo, AuditBean auditBean) {
        if (auditBean.getUpdatedBy() != null) {
            UserDetailsBean userAttributesBean = allUserMap.get(auditBean.getUpdatedBy());
            if (userAttributesBean != null) {
                auditTrailPojo.setUpdatedBy(userAttributesBean.getUserName());
            } else {
                auditTrailPojo.setUpdatedBy(auditBean.getUpdatedBy());
            }
        }

        String auditData = auditBean.getAuditData();

        Map<String, Map<String, String>> valueForUpdate;

        if (auditData != null && auditBean.getOperationType() != null) {
            try {
                valueForUpdate = objectMapper.readValue(auditData,
                        new TypeReference<Map<String, Map<String, String>>>() {
                        });
                auditTrailPojo.setValue(valueForUpdate);
            } catch (IOException e) {
                LOGGER.info(UIMessages.ERROR_PARSING_AUDIT_VALUE + " : {}", e.getMessage());
            }
        }
    }

    private List<Integer> getIds(String[] ids) throws ClientException {
        List<Integer> appIds = new ArrayList<>();
        if (ids != null && ids.length > 0 && ids[0].length() > 0) {
            String[] idList = ids[0].split(",");
            for (String id : idList) {
                if (StringUtils.isNumber(id)) {
                    appIds.add(Integer.parseInt(id));
                } else {
                    LOGGER.error(UIMessages.ERROR_INVALID_APPLICATION_ID);
                    throw new ClientException(UIMessages.ERROR_INVALID_APPLICATION_ID);
                }
            }
        }
        return appIds;
    }

    private List<Integer> getActivityTypeIds(String[] activityIds) throws ClientException {
        List<Integer> activityTypeIds = new ArrayList<>();
        if (activityIds != null && activityIds.length > 0 && activityIds[0].length() > 0) {
            String[] activityIdList = activityIds[0].split(",");
            for (String id : activityIdList) {
                if (StringUtils.isNumber(id)) {
                    activityTypeIds.add(Integer.parseInt(id));
                } else {
                    LOGGER.error(UIMessages.ERROR_INVALID_BIG_FEATURE_ID);
                    throw new ClientException(UIMessages.ERROR_INVALID_BIG_FEATURE_ID);
                }
            }
        }
        return activityTypeIds;
    }

    private long parseFromTime(String fromTime) throws ClientException {
        String fTime;
        if (StringUtils.getLong(fromTime) != 0) {
            if (fromTime.length() > 10) {
                fTime = fromTime.substring(0, 10);
            } else {
                fTime = fromTime;
            }
        } else {
            LOGGER.error(Constants.INVALID_FROM_TIME);
            throw new ClientException(Constants.INVALID_FROM_TIME);
        }
        return Long.parseLong(fTime);
    }

    private long parseToTime(String toTime) throws ClientException {
        String tTime;
        if (StringUtils.getLong(toTime) != 0) {
            if (toTime.length() > 10) {
                tTime = toTime.substring(0, 10);
            } else {
                tTime = toTime;
            }
        } else {
            LOGGER.error(Constants.INVALID_TO_TIME);
            throw new ClientException(Constants.INVALID_TO_TIME);
        }
        return Long.parseLong(tTime);
    }

    private String getWhereClause(AuditTrailBean bean) {
        StringBuilder whereClause = new StringBuilder("account_id in (0,1, " + bean.getAccountId() + Constants.DB_CONDITION);
        if (!bean.getBigFeatureIds().isEmpty()) {
            whereClause.append(" mst_big_feature_id in (");
            for (Integer id : bean.getBigFeatureIds()) {
                whereClause.append(id).append(",");
            }
            whereClause.deleteCharAt(whereClause.length()-1);
            whereClause.append(") and ");
        }
        if (!bean.getServiceIds().isEmpty()) {
            whereClause.append(" service_id in (");
            for (Integer id : bean.getServiceIds()) {
                whereClause.append(id).append(",");
            }
            whereClause.append(" 0) and ");
        }
        if (bean.getUserId() != null) {
            whereClause.append(" audit_user = '").append(bean.getUserId()).append("' and ");
        }
        return whereClause.toString();
    }
}