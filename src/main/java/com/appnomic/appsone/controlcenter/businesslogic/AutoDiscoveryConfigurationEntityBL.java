package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.beans.discovery.Entity;
import com.appnomic.appsone.common.beans.discovery.EntityDetail;
import com.appnomic.appsone.controlcenter.beans.ComponentFilesDetailsBean;
import com.appnomic.appsone.controlcenter.beans.MasterComponentBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.ViewKpiGroupsBean;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.AutoDiscoveryDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.KPIDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.KpiDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.KPIDataDao;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class AutoDiscoveryConfigurationEntityBL implements BusinessLogic<UtilityBean, Object, List<Entity>> {

    private ViewKpiGroupsBean keyValueKpiGroupsBean = MasterCache.getViewKpiGroupsByName(Constants.KPI_NAME_KEY_VALUE);

    private ViewKpiGroupsBean fileWatchKpiGroupsBean = MasterCache.getViewKpiGroupsByName(Constants.KPI_NAME_FILE_WATCH);

    @Override
    public UtilityBean clientValidation(RequestObject requestObject) throws ClientException {
        return null;
    }

    @Override
    public Object serverValidation(UtilityBean utilityBean) throws ServerException {
        return null;
    }

    @Override
    public List<Entity> process(Object bean) throws DataProcessingException {

        List<Entity> configurationEntityList = new ArrayList<>();
        Map<Integer, Set<Integer>> componentIdVersionIdMap = new HashMap<>();

        List<ComponentFilesDetailsBean> componentFilesDetailsBeans;
        AutoDiscoveryDataService autoDiscoveryDataService = new AutoDiscoveryDataService();
        componentFilesDetailsBeans = autoDiscoveryDataService.getConfigurationEntities();

        if (componentFilesDetailsBeans.isEmpty()) {
            String log = "Error while fetching configuration entities.";
            AutoDiscoveryConfigurationEntityBL.log.error(log);
            throw new DataProcessingException(log);
        }

        Set<Integer> distinctComponentIds = getDistinctComponentIds(componentFilesDetailsBeans);

        for (Integer componentId : distinctComponentIds) {
            List<ComponentFilesDetailsBean> relatedComponentFilesDetailsBeans = getComponentFilesDetailsByComponentId(componentFilesDetailsBeans, componentId);
            Set<Integer> distinctVersionIds = getDistinctVersionIds(relatedComponentFilesDetailsBeans);

            componentIdVersionIdMap.put(componentId, distinctVersionIds);
        }

        for (Map.Entry<Integer, Set<Integer>> entry : componentIdVersionIdMap.entrySet()) {
            List<IdPojo> kpisForComponent = new KPIDataService().getKpisForComponent(entry.getKey(), null);

            for (Integer vid : entry.getValue()) {

                MasterComponentBean masterComponentBean = getMasterComponentBean(entry.getKey(), vid);

                Entity configurationEntity = new Entity();
                configurationEntity.setComponentName(masterComponentBean.getName());
                configurationEntity.setComponentType(masterComponentBean.getComponentTypeName());
                configurationEntity.setVersion(masterComponentBean.getComponentVersionName());

                List<EntityDetail> entities = getAllEntitiesByComponentIdVersionId(componentFilesDetailsBeans, entry.getKey(), vid, kpisForComponent);
                configurationEntity.setEntities(entities);
                configurationEntityList.add(configurationEntity);
            }
        }

        return configurationEntityList;
    }

    private Set<Integer> getDistinctVersionIds(List<ComponentFilesDetailsBean> componentFilesDetailsBeans) {
        Set<Integer> distinctVersionIds = new HashSet<>();

        componentFilesDetailsBeans
                .stream()
                .filter(r -> distinctVersionIds.add(r.getMstComponentVersionId()))
                .collect(Collectors.toList());

        return distinctVersionIds;
    }

    private Set<Integer> getDistinctComponentIds(List<ComponentFilesDetailsBean> componentFilesDetailsBeans) {
        Set<Integer> distinctComponentIds = new HashSet<>();

        componentFilesDetailsBeans
                .stream()
                .filter(c -> distinctComponentIds.add(c.getMstComponentId()))
                .collect(Collectors.toList());

        return distinctComponentIds;
    }

    private List<ComponentFilesDetailsBean> getComponentFilesDetailsByComponentId(List<ComponentFilesDetailsBean> componentFilesDetailsBeans, Integer componentId) {
        return componentFilesDetailsBeans
                .stream()
                .filter(c -> c.getMstComponentId() == componentId)
                .collect(Collectors.toList());
    }

    private MasterComponentBean getMasterComponentBean(int componentId, int componentVersionId) {

        List<MasterComponentBean> masterComponentBean = MasterCache.getMasterComponentsById(componentId, "1");

        return masterComponentBean
                .stream()
                .filter(m -> m.getId() == componentId)
                .filter(m -> m.getComponentVersionId() == componentVersionId)
                .findAny().orElse(null);
    }

    private List<EntityDetail> getAllEntitiesByComponentIdVersionId(List<ComponentFilesDetailsBean> componentFilesDetailsBeans, Integer componentId, Integer versionId, List<IdPojo> kpis) {

        List<EntityDetail> entities = new ArrayList<>();

        List<ComponentFilesDetailsBean> allRecordsForComponentIdVersionId = componentFilesDetailsBeans
                .stream()
                .filter(c -> c.getMstComponentId() == componentId)
                .filter(c -> c.getMstComponentVersionId() == versionId)
                .collect(Collectors.toList());

        for (ComponentFilesDetailsBean record : allRecordsForComponentIdVersionId) {
            String kpiType = StringUtils.EMPTY;
            IdPojo idPojo = kpis
                    .parallelStream()
                    .filter(k -> k.getId() == record.getMstKpiDetailsId())
                    .findAny()
                    .orElse(null);
            if (idPojo != null) {
                kpiType = idPojo.getIdentifier();
            }
            if (!StringUtils.isEmpty(kpiType)) {
                EntityDetail entityDetail = new EntityDetail();
                entityDetail.setEntityName(record.getFileName());
                entityDetail.setKpiType(kpiType);
                entityDetail.setRelativePath(record.getRelativePath());
                entities.add(entityDetail);
            }
        }
        return entities;
    }
}
