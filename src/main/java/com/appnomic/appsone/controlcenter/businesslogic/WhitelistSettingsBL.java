package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.WhitelistSettingsBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.WhitelistDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

@Slf4j
public class WhitelistSettingsBL implements BusinessLogic<WhitelistSettingsBean, WhitelistSettingsBean, WhitelistSettingsBean> {
    private static final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    @Override
    public UtilityBean<WhitelistSettingsBean> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }
        String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            log.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }
        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (StringUtils.isEmpty(identifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }
        WhitelistSettingsBean whitelistSettings = null;
        if (requestObject.getBody() != null) {
            try {
                whitelistSettings = objectMapper.readValue(requestObject.getBody(), new TypeReference<WhitelistSettingsBean>() {
                });
            } catch (IOException e) {
                log.error("IOException encountered while parsing request body. Details {}", e.getMessage());
                throw new ClientException(e.getMessage());
            }
        }
        return UtilityBean.<WhitelistSettingsBean>builder()
                .accountIdentifier(identifier)
                .authToken(authToken)
                .pojoObject(whitelistSettings).build();
    }

    @Override
    public WhitelistSettingsBean serverValidation(UtilityBean<WhitelistSettingsBean> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }
        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }
        utilityBean.setAccount(account);
        return utilityBean.getPojoObject();
    }

    @Override
    public WhitelistSettingsBean process(WhitelistSettingsBean bean) throws DataProcessingException {
        return null;
    }

    public String updateWhitelistSettings(WhitelistSettingsBean whitelistSettings) throws DataProcessingException {
        int key;
        try {
            key = new WhitelistDataService().updateWhitelistActive(whitelistSettings.isWhitelistActive());
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        return String.valueOf(key);
    }

    public static WhitelistSettingsBean getWhitelistSettings() {
        WhitelistSettingsBean whitelistSettings = new WhitelistSettingsBean();

        try {
            WhitelistDataService whitelistDataService = new WhitelistDataService();
            boolean isWhitelistActive = whitelistDataService.isWhitelistActive();
            whitelistSettings.setWhitelistActive(isWhitelistActive);
        } catch (ControlCenterException e) {
            log.error("Error occurred fetching whitelist settings", e);
        }
        return whitelistSettings;
    }
}