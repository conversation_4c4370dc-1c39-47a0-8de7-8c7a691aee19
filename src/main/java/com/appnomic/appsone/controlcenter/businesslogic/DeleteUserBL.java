package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UserAttributesBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.UserDataService;
import com.appnomic.appsone.controlcenter.dao.redis.UsersRepo;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.service.NotificationPreferencesDataService;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.UserUtility;
import com.appnomic.appsone.keycloak.KeycloakConnectionManager;
import com.google.common.base.Throwables;
import org.skife.jdbi.v2.DBI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

public class DeleteUserBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeleteUserBL.class);

    private static final int USER_MANAGER = 3;
    private static final int SUPER_ADMIN = 1;

    private static final String USER_NOT_EXIST = "User profile doesn't not exist.";
    private static final UserDataService userDataService = new UserDataService();

    public String clientValidation(Request request) throws RequestException {
        if (request == null) {
            LOGGER.error(UIMessages.REQUEST_NULL);
            throw new RequestException(UIMessages.REQUEST_NULL);
        }

        String userId = request.params(":userIdentifier");

        if (userId == null || userId.isEmpty()) {
            LOGGER.error("User Id is null or empty.");
            throw new RequestException("User Id is null or empty.");
        }

        return userId;
    }

    public void serverValidation(Request request, String userId) throws RequestException {
        //Server validation for User Manager
        UserAttributesBean userAttributesForUserManager;
        try {
            String userIdForUM = CommonUtils.getUserId(request);
            userAttributesForUserManager = userDataService.getUserAttributes(userIdForUM);

            if (userAttributesForUserManager == null || (userAttributesForUserManager.getRoleId() != USER_MANAGER && userAttributesForUserManager.getRoleId() != SUPER_ADMIN)) {
                LOGGER.error("This User either doesn't exist or unauthorized.");
                throw new RequestException("This User either doesn't exist or unauthorized.");
            }
        } catch (ControlCenterException e) {
            String err = "User Manager/SuperAdmin profile doesn't not exist.";
            LOGGER.error(err);
            throw new RequestException(err);
        }

        try {
            UserAttributesBean userAttributesForUser = userDataService.getUserAttributes(userId);

            if (userAttributesForUser == null) {
                LOGGER.error(USER_NOT_EXIST);
                throw new RequestException(USER_NOT_EXIST);
            } else if (userAttributesForUser.getRoleId() == SUPER_ADMIN) {
                LOGGER.error("Unauthorized to perform this operation. Reason: Super Admin can't be deleted from the system.");
                throw new RequestException("Unauthorized to perform this operation. Reason: Super Admin can't be deleted from the system.");
            }
        } catch (ControlCenterException e) {
            LOGGER.error(USER_NOT_EXIST);
            throw new RequestException(USER_NOT_EXIST);
        }
    }

    public void deleteUserDetails(String userId) throws ControlCenterException {
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        try {
            dbi.inTransaction((conn, status) -> {
                String setup = userDataService.getSetup();

                if (setup == null) {
                    LOGGER.error("Error while fetching integration mode.");
                    throw new ControlCenterException("Unable to get Setup Mode.");
                }

                if (Constants.SETUP_KEYCLOAK.equalsIgnoreCase(setup.trim())) {
                    KeycloakConnectionManager.deleteKeycloakUser(userId);
                }

                NotificationPreferencesDataService.removeNotificationDetailsForUser(userId, conn);
                NotificationPreferencesDataService.removeUserNotificationPreferencesForUser(userId, conn);
                NotificationPreferencesDataService.removeForensicNotificationPreferencesForUser(userId, conn);
                userDataService.deleteUserAttributesAndAccessDetails(userId, conn);
                return "NOTHING";
            });

            UsersRepo usersRepo = new UsersRepo();

            new UserUtility().removeUserDetailsFromAppLevelKeys(userId);
            usersRepo.deleteUserAccessDetails(userId);
            usersRepo.deleteUser(userId);

        } catch (Exception e) {
            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw new ControlCenterException(Throwables.getRootCause(e).getMessage());
            } else {
                throw e;
            }
        }
    }
}
