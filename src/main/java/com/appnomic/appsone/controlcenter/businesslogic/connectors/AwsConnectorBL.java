package com.appnomic.appsone.controlcenter.businesslogic.connectors;

import com.appnomic.appsone.controlcenter.common.ConnectorConstants;
import com.appnomic.appsone.controlcenter.dao.mysql.connectors.AwsConnectorDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.*;
import com.appnomic.appsone.controlcenter.exceptions.FileUploadException;
import com.appnomic.appsone.controlcenter.pojo.connectors.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class AwsConnectorBL {

    public List<AwsCredentialDetail> getAwsCredentialDetailsFromFile(File fileName) throws FileNotFoundException {
        List<AwsCredentialDetail> awsCredentialDetails = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro = sheet.getRow(i);
                if(null == ro.getCell(ConnectorConstants.AWS_CREDENTIAL_ID_INDEX)) break;
                AwsCredentialDetail credentialDetail = AwsCredentialDetail.builder()
                        .id((int) ro.getCell(ConnectorConstants.AWS_CREDENTIAL_ID_INDEX).getNumericCellValue())
                        .region(ro.getCell(ConnectorConstants.AWS_CREDENTIAL_REGION_INDEX).getStringCellValue())
                        .accessKeyId(ro.getCell(ConnectorConstants.AWS_CREDENTIAL_ACCESS_KEY_ID_INDEX).getStringCellValue())
                        .secretKeyAccess(ro.getCell(ConnectorConstants.AWS_CREDENTIAL_SECRET_KEY_ACCESS_INDEX).getStringCellValue())
                        .build();
                if (credentialDetail.getId() != 0) awsCredentialDetails.add(credentialDetail);
            }

        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try", ex);
            throw new FileNotFoundException("Exception when reading excel file");
        } catch (Exception ex) {
            log.error("Exception when reading the Aws credential Data from excel file", ex);
            throw new FileUploadException("Exception when reading the Aws credential Data from excel file");
        }
        return awsCredentialDetails;
    }

    public List<AwsInstanceDetail> getAwsInstanceDetailsFromFile(File fileName) {
        List<AwsInstanceDetail> awsInstanceDetails = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro = sheet.getRow(i);
                if(null == ro.getCell(ConnectorConstants.AWS_INSTANCE_ID_INDEX)) break;
                AwsInstanceDetail instanceDetails = AwsInstanceDetail.builder()
                        .id((int) ro.getCell(ConnectorConstants.AWS_INSTANCE_ID_INDEX).getNumericCellValue())
                        .instanceId(ro.getCell(ConnectorConstants.AWS_INSTANCE_INSTANCE_ID_INDEX).getStringCellValue())
                        .enableDetailMonitoring((int) ro.getCell(ConnectorConstants.AWS_INSTANCE_DETAILED_MONITORING_INDEX).getNumericCellValue())
                        .credentialId(String.valueOf(ro.getCell(ConnectorConstants.AWS_INSTANCE_CREDENTIAL_ID_INDEX).getNumericCellValue()))
                        .logKpiIds(String.valueOf(ro.getCell(ConnectorConstants.AWS_INSTANCE_LOG_KPIS_ID_INDEX).getNumericCellValue()))
                        .logsIds(String.valueOf(ro.getCell(ConnectorConstants.AWS_INSTANCE_LOGS_ID_INDEX).getNumericCellValue()))
                        .build();
                if (instanceDetails.getId() != 0) awsInstanceDetails.add(instanceDetails);
            }

        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try", ex);
            throw new FileUploadException("Exception when reading excel file");
        } catch (Exception ex) {
            log.error("Exception when reading the Aws credential Data from excel file", ex);
            throw new FileUploadException("Exception when reading the Aws Instance Data from excel file");
        }
        return awsInstanceDetails;
    }

    public List<AwsHealKpiDetail> getAwsHealKpiDetailsFromFile(File fileName) {
        List<AwsHealKpiDetail> healKpiDetails = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro = sheet.getRow(i);
                if(null == ro.getCell(ConnectorConstants.AWS_METRIC_ID_INDEX)) break;
                AwsHealKpiDetail healKpiDetail = AwsHealKpiDetail.builder()
                        .id((int) ro.getCell(ConnectorConstants.AWS_METRIC_ID_INDEX).getNumericCellValue())
                        .awsMetricName(ro.getCell(ConnectorConstants.AWS_METRIC_NAME_INDEX).getStringCellValue())
                        .awsMetricNamespace(ro.getCell(ConnectorConstants.AWS_METRIC_NAME_SPACE_INDEX).getStringCellValue())
                        .awsMetricStat(ro.getCell(ConnectorConstants.AWS_METRIC_STAT_INDEX).getStringCellValue())
                        .awsMetricUnit(ro.getCell(ConnectorConstants.AWS_METRIC_UNIT_INDEX).getStringCellValue())
                        .isDetailedMetric((int) ro.getCell(ConnectorConstants.AWS_METRIC_DETAILED_MONITORED_INDEX).getNumericCellValue())
                        .healId((int) ro.getCell(ConnectorConstants.AWS_METRIC_HEAL_ID_INDEX).getNumericCellValue())
                        .healName(ro.getCell(ConnectorConstants.AWS_METRIC_HEAL_NAME_INDEX).getStringCellValue())
                        .healIdentifier(ro.getCell(ConnectorConstants.AWS_METRIC_HEAL_IDENTIFIER_INDEX).getStringCellValue())
                        .isGroupKpi((int) ro.getCell(ConnectorConstants.AWS_METRIC_IS_GROUP_INDEX).getNumericCellValue())
                        .groupName(ro.getCell(ConnectorConstants.AWS_METRIC_GROUP_NAME_INDEX).getStringCellValue())
                        .build();
                if (healKpiDetail.getId() != 0) healKpiDetails.add(healKpiDetail);
            }

        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try", ex);
            throw new FileUploadException("Exception when reading excel file");
        } catch (Exception ex) {
            log.error("Exception when reading the Aws credential Data from excel file", ex);
            throw new FileUploadException("Exception when reading the Aws Heal Kpi Data from excel file");
        }
        healKpiDetails.removeIf(x -> x.getAwsMetricName().equals("") || x.getAwsMetricName().equals(" ") || x.getAwsMetricName() == null);
        return healKpiDetails;
    }

    public List<AwsLogsDetail> getAwsLogsDetailsFromFile(File fileName) {
        List<AwsLogsDetail> awsLogsDetails = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro = sheet.getRow(i);
                if(null == ro.getCell(ConnectorConstants.AWS_LOGS_ID_INDEX)) break;
                AwsLogsDetail logsDetails = AwsLogsDetail.builder()
                        .id((int) ro.getCell(ConnectorConstants.AWS_LOGS_ID_INDEX).getNumericCellValue())
                        .logGroupName(ro.getCell(ConnectorConstants.AWS_LOGS_GROUP_NAME_INDEX).getStringCellValue())
                        .logStreamName(ro.getCell(ConnectorConstants.AWS_LOGS_STREAM_NAME_INDEX).getStringCellValue())
                        .logPattern(ro.getCell(ConnectorConstants.AWS_LOGS_FILTER_PATTERN_INDEX).getStringCellValue())
                        .datePattern(ro.getCell(ConnectorConstants.AWS_LOGS_DATE_PATTERN_INDEX).getStringCellValue())
                        .credentialIds(String.valueOf(ro.getCell(ConnectorConstants.AWS_LOGS_CREDENTIAL_ID_INDEX).getNumericCellValue()))
                        .build();
                if (logsDetails.getId() != 0) awsLogsDetails.add(logsDetails);
            }

        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try", ex);
            throw new FileUploadException("Exception when reading excel file");
        } catch (Exception ex) {
            log.error("Exception when reading the Aws credential Data from excel file", ex);
            throw new FileUploadException("Exception when reading the Aws logs Data from excel file");
        }
        return awsLogsDetails;
    }

    public List<AwsLogKpiDetail> getAwsLogKpiDetailsFromFile(File fileName) {
        List<AwsLogKpiDetail> awsLogKpiDetails = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro = sheet.getRow(i);
                if(null == ro.getCell(ConnectorConstants.AWS_LOG_KPI_ID_INDEX)) break;
                AwsLogKpiDetail awsLogKpiDetail = AwsLogKpiDetail.builder()
                        .id((int) ro.getCell(ConnectorConstants.AWS_LOG_KPI_ID_INDEX).getNumericCellValue())
                        .logGroupName(ro.getCell(ConnectorConstants.AWS_LOG_KPI_GROUP_NAME_INDEX).getStringCellValue())
                        .logStreamName(ro.getCell(ConnectorConstants.AWS_LOG_KPI_STREAM_NAME_INDEX).getStringCellValue())
                        .filterPattern(ro.getCell(ConnectorConstants.AWS_LOG_KPI_FILTER_PATTERN_INDEX).getStringCellValue())
                        .credentialIds(String.valueOf(ro.getCell(ConnectorConstants.AWS_LOG_KPI_CREDENTIAL_ID_INDEX).getNumericCellValue()))
                        .build();
                if (awsLogKpiDetail.getId() != 0) awsLogKpiDetails.add(awsLogKpiDetail);
            }

        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try", ex);
            throw new FileUploadException("Exception when reading excel file");
        } catch (Exception ex) {
            log.error("Exception when reading the Aws credential Data from excel file", ex);
            throw new FileUploadException("Exception when reading the Aws log kpis Data from excel file");
        }
        return awsLogKpiDetails;
    }

    public List<AwsDimensionDetail> getAwsDimensionsDetailsFromFile(File fileName) {
        List<AwsDimensionDetail> awsDimensionDetails = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro = sheet.getRow(i);
                if(null == ro.getCell(ConnectorConstants.AWS_DIMENSION_ID_INDEX)) break;
                AwsDimensionDetail dimensionDetail = AwsDimensionDetail.builder()
                        .id((int) ro.getCell(ConnectorConstants.AWS_DIMENSION_ID_INDEX).getNumericCellValue())
                        .dimensionKey(ro.getCell(ConnectorConstants.AWS_DIMENSION_KEY_INDEX).getStringCellValue())
                        .dimensionValue(ro.getCell(ConnectorConstants.AWS_DIMENSION_VALUE_INDEX).getStringCellValue())
                        .instanceIds(String.valueOf(ro.getCell(ConnectorConstants.AWS_DIMENSION_INSTANCE_ID_INDEX).getNumericCellValue()))
                        .build();
                if (dimensionDetail.getId() != 0) awsDimensionDetails.add(dimensionDetail);
            }

        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try", ex);
            throw new FileUploadException("Exception when reading excel file");
        } catch (Exception ex) {
            log.error("Exception when reading the Aws credential Data from excel file", ex);
            throw new FileUploadException("Exception when reading the Aws dimensions Data from excel file");
        }
        return awsDimensionDetails;
    }

    public List<HealAgentInstance> getHealAgentInstanceFromFile(File fileName) {
        List<HealAgentInstance> healAgentInstances = new ArrayList<>();
        try (FileInputStream file = new FileInputStream(fileName)) {
            //Create Workbook instance holding reference to .xlsx file
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            //Get first/desired sheet from the workbook
            XSSFSheet sheet = workbook.getSheetAt(0);
            //I've Header and I'm ignoring header for that I've +1 in loop
            for (int i = sheet.getFirstRowNum() + 3; i <= sheet.getLastRowNum(); i++) {
                Row ro = sheet.getRow(i);
                if(null == ro.getCell(ConnectorConstants.AWS_AGENT_AWS_INSTANCE_NAME_INDEX)) break;
                HealAgentInstance healAgentInstance = HealAgentInstance.builder()
                        .sourceInstanceName(ro.getCell(ConnectorConstants.AWS_AGENT_AWS_INSTANCE_NAME_INDEX).getStringCellValue())
                        .agentIdentifier(ro.getCell(ConnectorConstants.AWS_AGENT_NAME_INDEX).getStringCellValue())
                        .healInstanceName(ro.getCell(ConnectorConstants.AWS_AGENT_HEAL_INSTANCE_NAME_INDEX).getStringCellValue())
                        .build();
                if (!Objects.equals(healAgentInstance.getHealInstanceName(), ""))
                    healAgentInstances.add(healAgentInstance);
            }

        } catch (FileNotFoundException ex) {
            log.error("File is not found at given location please check and try", ex);
            throw new FileUploadException("Exception when reading excel file");
        } catch (Exception ex) {
            log.error("Exception when reading the Aws credential Data from excel file", ex);
            throw new FileUploadException("Exception when reading the Aws dimenstions Data from excel file");
        }
        return healAgentInstances;
    }

    public List<AwsCredentialDetailBean> getCredentialBean(List<AwsCredentialDetail> awsCredentialDetails) {
        List<AwsCredentialDetailBean> awsCredentialDetailBeans = new ArrayList<>();
        awsCredentialDetails.forEach(a ->
                awsCredentialDetailBeans.add(AwsCredentialDetailBean
                        .builder().id(a.getId())
                        .region(a.getRegion())
                        .accessKeyId(a.getAccessKeyId())
                        .secretKeyAccess(a.getSecretKeyAccess())
                        .build())
        );
        return awsCredentialDetailBeans;
    }

    public List<AwsInstanceBean> getInstanceBean(List<AwsInstanceDetail> awsInstanceDetails) {
        List<AwsInstanceBean> beans = new ArrayList<>();
        awsInstanceDetails.forEach(a ->
                beans.add(AwsInstanceBean.builder()
                        .id(a.getId())
                        .instanceId(a.getInstanceId())
                        .enableDetailMonitoring(a.getEnableDetailMonitoring())
                        .build()));
        return beans;
    }

    public List<AwsMetricBean> getMetrics(List<AwsHealKpiDetail> awsHealKpiDetails) {
        List<AwsMetricBean> metricBeans = new ArrayList<>();
        awsHealKpiDetails.forEach(a -> metricBeans.add(AwsMetricBean.builder()
                .id(a.getId())
                .awsMetricName(a.getAwsMetricName())
                .awsMetricNamespace(a.getAwsMetricNamespace())
                .awsMetricStat(a.getAwsMetricStat())
                .awsMetricUnit(a.getAwsMetricUnit())
                .isDetailedMatric(a.getIsDetailedMetric())
                .build()));
        return metricBeans;
    }

    public List<HealKpi> getHealKpiList(List<AwsHealKpiDetail> awsHealKpiDetails) {
        List<HealKpi> healKpiList = new ArrayList<>();
        awsHealKpiDetails.forEach(a -> healKpiList.add(HealKpi.builder()
                .kpiId(a.getHealId())
                .kpiName(a.getHealName())
                .kpiIdentifier(a.getHealIdentifier())
                .isGroupKpi(a.getIsGroupKpi())
                .groupName(a.getGroupName())
                .build()));
        return healKpiList;
    }

    public List<DomainToHealKpiMapping> getDomainToHealKpiList(List<AwsHealKpiDetail> awsHealKpiDetails) {
        List<DomainToHealKpiMapping> domainToHealKpiMappings = new ArrayList<>();
        awsHealKpiDetails.forEach(a -> domainToHealKpiMappings.add(DomainToHealKpiMapping.builder()
                .domainName("aws")
                .sourceId(a.getId())
                .healIdentifier(a.getHealIdentifier())
                .build()));
        return domainToHealKpiMappings;
    }

    public List<AwsLogsBean> getLogs(List<AwsLogsDetail> logsDetails) {
        List<AwsLogsBean> logsBeans = new ArrayList<>();
        logsDetails.forEach(a -> logsBeans.add(AwsLogsBean.builder()
                .id(a.getId())
                .logGroupName(a.getLogGroupName())
                .logStreamName(a.getLogStreamName())
                .logPattern(a.getLogPattern())
                .datePattern(a.getDatePattern())
                .build()));
        return logsBeans;
    }

    public List<AwsLogKpiBean> getLogKpis(List<AwsLogKpiDetail> logKpiDetails) {
        List<AwsLogKpiBean> logKpiBeans = new ArrayList<>();
        logKpiDetails.forEach(a -> logKpiBeans.add(AwsLogKpiBean.builder()
                .id(a.getId())
                .logGroupName(a.getLogGroupName())
                .logStreamName(a.getLogStreamName())
                .filterPattern(a.getFilterPattern())
                .build()));
        return logKpiBeans;
    }

    public List<AwsDimensionBean> getDimensions(List<AwsDimensionDetail> awsDimensionDetails) {
        List<AwsDimensionBean> dimensionBeans = new ArrayList<>();
        awsDimensionDetails.forEach(a -> dimensionBeans.add(AwsDimensionBean.builder()
                .id(a.getId())
                .dimensionKey(a.getDimensionKey())
                .dimensionValue(a.getDimensionValue())
                .build()));
        return dimensionBeans;
    }

    public List<AwsCredentialMetricMapping> getCredentialMetricMapping(int[] ciIds) {
        List<AwsCredentialMetricMapping> awsCredentialMetricMappings = new ArrayList<>();
        AwsConnectorDataService dataService = new AwsConnectorDataService();
        List<Integer> metricList = dataService.getAwsMetricsList();
        Arrays.stream(ciIds).boxed().collect(Collectors.toList()).forEach(
                ciId -> metricList.forEach(
                        metricId -> awsCredentialMetricMappings.add(AwsCredentialMetricMapping.builder()
                                .credentialId(ciId)
                                .metricId(metricId)
                                .build())));
        return awsCredentialMetricMappings;
    }

    public List<AwsCredentialInstanceMapping> getCredentialInstanceMapping(List<AwsInstanceDetail> instanceDetails) {
        List<AwsCredentialInstanceMapping> awsCredentialInstanceMappings = new ArrayList<>();
        instanceDetails.forEach(a -> {
            List<Integer> credential = Arrays.stream(a.getLogsIds().split(","))
                    .map(Integer::valueOf).collect(Collectors.toList());
            credential.forEach(b -> awsCredentialInstanceMappings.add(AwsCredentialInstanceMapping.builder()
                    .credentialId(b)
                    .instanceId(a.getId())
                    .build()));
        });
        return awsCredentialInstanceMappings;
    }

    public List<AwsLogsInstanceMapping> getLogsInstanceMapping(List<AwsInstanceDetail> instanceDetails) {
        List<AwsLogsInstanceMapping> awsLogsInstanceMappings = new ArrayList<>();
        instanceDetails.forEach(a -> {
            List<Integer> logsIds = Arrays.stream(a.getLogsIds().split(","))
                    .map(Integer::valueOf).collect(Collectors.toList());
            logsIds.forEach(b -> awsLogsInstanceMappings.add(AwsLogsInstanceMapping.builder()
                    .logsId(b)
                    .instanceId(a.getId())
                    .build()));
        });
        return awsLogsInstanceMappings;
    }

    public List<AwsLogKpiInstanceMapping> getLogKpiInstanceMapping(List<AwsInstanceDetail> instanceDetails) {
        List<AwsLogKpiInstanceMapping> awsLogKpiInstanceMappings = new ArrayList<>();
        instanceDetails.forEach(a -> {
            List<Integer> logKpiIds = Arrays.stream(a.getLogKpiIds().split(","))
                    .map(Integer::valueOf).collect(Collectors.toList());
            logKpiIds.forEach(b -> awsLogKpiInstanceMappings.add(AwsLogKpiInstanceMapping.builder()
                    .logKpiId(b)
                    .instanceId(a.getId())
                    .build()));
        });
        return awsLogKpiInstanceMappings;
    }

    public List<AwsCredentialLogsMapping> getCredentialLogsMapping(List<AwsLogsDetail> awsLogsDetails) {
        List<AwsCredentialLogsMapping> awsCredentialLogsMappings = new ArrayList<>();
        awsLogsDetails.forEach(a -> {
            List<Integer> credentialIds = Arrays.stream(a.getCredentialIds().split(","))
                    .map(Integer::valueOf).collect(Collectors.toList());
            credentialIds.forEach(b -> awsCredentialLogsMappings.add(AwsCredentialLogsMapping.builder()
                    .logsId(a.getId())
                    .credentialId(b)
                    .build()));
        });
        return awsCredentialLogsMappings;
    }

    public List<AwsCredentialLogKpiMapping> getCredentialLogKpiMapping(List<AwsLogKpiDetail> awsLogKpiDetails) {
        List<AwsCredentialLogKpiMapping> awsCredentialLogKpiMappings = new ArrayList<>();
        awsLogKpiDetails.forEach(a -> {
            List<Integer> credentialIds = Arrays.stream(a.getCredentialIds().split(","))
                    .map(Integer::valueOf).collect(Collectors.toList());
            credentialIds.forEach(b -> awsCredentialLogKpiMappings.add(AwsCredentialLogKpiMapping.builder()
                    .logKpiId(a.getId())
                    .credentialId(b)
                    .build()));
        });
        return awsCredentialLogKpiMappings;
    }

    public List<AwsMetricDimensionMapping> getMetricDimensionMapping(List<AwsDimensionDetail> awsDimensionDetails) {
        List<AwsMetricDimensionMapping> metricDimensionMappings = new ArrayList<>();
        AwsConnectorDataService connectorDataService = new AwsConnectorDataService();
        awsDimensionDetails.forEach(a -> {
            List<Integer> instanceId = Arrays.stream(a.getInstanceIds().split(","))
                    .map(Integer::valueOf).collect(Collectors.toList());
            List<Integer> metricIds = connectorDataService.getAwsMetricsList();
            instanceId.forEach(b -> metricIds.forEach(c -> metricDimensionMappings.add(AwsMetricDimensionMapping.builder()
                    .dimensionId(a.getId())
                    .instanceId(b)
                    .metricId(c)
                    .build())));
        });
        return metricDimensionMappings;
    }
}
