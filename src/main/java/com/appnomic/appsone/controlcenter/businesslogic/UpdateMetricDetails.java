package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.MetricDetailsDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompInstanceKPIDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.InstanceDetailsForKPI;
import com.appnomic.appsone.controlcenter.dao.redis.ComponentRepo;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.CompInstClusterDetails;
import com.appnomic.appsone.controlcenter.pojo.Controller;
import com.appnomic.appsone.controlcenter.pojo.MetricDetails;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.BasicKpiEntity;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.Component;
import com.heal.configuration.pojos.ComponentKpiEntity;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class UpdateMetricDetails implements BusinessLogic<List<MetricDetails>, List<CompInstanceKPIDetailsBean>, String> {

    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateMetricDetails.class);
    MetricDetailsDataService dataService = new MetricDetailsDataService();
    InstanceRepo instanceRepo = new InstanceRepo();
    ComponentRepo componentRepo = new ComponentRepo();
    CompInstanceDataService compInstanceDataService = new CompInstanceDataService();

    @Override
    public UtilityBean<List<MetricDetails>> clientValidation(RequestObject request) throws ClientException {

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            LOGGER.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }

        String serviceIdString = request.getParams().get(Constants.SERVICE_ID);
        if (StringUtils.isEmpty(serviceIdString)) {
            LOGGER.error("serviceId is null or empty. {}", serviceIdString);
            throw new ClientException("serviceId is null or empty.");
        }
        try {
            Integer.parseInt(serviceIdString.trim());
        } catch (NumberFormatException e) {
            LOGGER.error("Service Id [{}] is not an integer. ", serviceIdString);
            throw new ClientException("Service Id is not an integer.");
        }

        if (StringUtils.isEmpty(request.getBody())) {
            LOGGER.error("Request body is null or empty.");
            throw new ClientException("Request body is null or empty.");
        }
        List<MetricDetails> metricDetails;
        try {
            metricDetails = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(request.getBody(),
                    new TypeReference<List<MetricDetails>>() {
                    });
        } catch (IOException e) {
            LOGGER.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        if (metricDetails == null || metricDetails.isEmpty()) {
            LOGGER.error("Metric details list is null or empty.");
            throw new ClientException("Metric details list is null or empty.");
        }

        if (metricDetails.parallelStream().anyMatch(metric -> !metric.validate())) {
            LOGGER.error("Invalid request : Kindly check the logs.");
            throw new ClientException("Invalid request : Kindly check the logs.");
        }

        return UtilityBean.<List<MetricDetails>>builder()
                .accountIdentifier(identifier)
                .serviceId(serviceIdString.trim())
                .pojoObject(metricDetails)
                .authToken(authToken)
                .build();
    }

    @Override
    public List<CompInstanceKPIDetailsBean> serverValidation(UtilityBean<List<MetricDetails>> utilityBean) throws ServerException {

        UserAccountBean userAccBean;
        try {
            userAccBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            throw new ServerException(e.getMessage());
        }

        int accountId = userAccBean.getAccount().getId();
        int serviceId = Integer.parseInt(utilityBean.getServiceId());
        Controller serviceDetails = ValidationUtils.getServiceDetails(accountId, serviceId);
        if (Objects.isNull(serviceDetails)) {
            LOGGER.error("Invalid service id [{}]", serviceId);
            throw new ServerException("Invalid service Id. Reason: Service Id specified is unavailable for the account.");
        }

        List<MetricDetails> metricDetails = utilityBean.getPojoObject();
        Set<Integer> instanceIds = new HashSet<>();
        metricDetails.forEach(metric -> {
            if (metric.getMappedInstances() != null) {
                instanceIds.addAll(metric.getMappedInstances());
            }
            if (metric.getUnmappedInstances() != null) {
                instanceIds.addAll(metric.getUnmappedInstances());
            }
        });

        List<CompInstClusterDetails> compInstClusterDetails;
        try {
            compInstClusterDetails = dataService.getCompInstanceDetailsForService(serviceId, accountId, null)
                    .parallelStream().filter(c -> instanceIds.contains(c.getInstanceId()))
                    .collect(Collectors.toList());

            if (instanceIds.size() != compInstClusterDetails.size()) {
                LOGGER.error("Invalid instanceIds provided for the service.");
                throw new ServerException("Invalid instanceIds provided for the service.");
            }
            Set<Integer> componentIds = compInstClusterDetails.parallelStream().map(CompInstClusterDetails::getCompId)
                    .collect(Collectors.toSet());
            if (componentIds.size() != 1) {
                LOGGER.error("InstanceIds specified are mapped to different components.");
                throw new ServerException("InstanceIds specified are mapped to different components.");
            }

            return validateMetricDetails(metricDetails, new ArrayList<>(componentIds).get(0), userAccBean);

        } catch (ControlCenterException e) {
            throw new ServerException(e.getMessage());
        }
    }

    private List<CompInstanceKPIDetailsBean> validateMetricDetails(List<MetricDetails> metricDetails, int componentId, UserAccountBean userAccBean)
            throws ServerException, ControlCenterException {
        int accountId = userAccBean.getAccount().getId();
        String accountIdentifier = userAccBean.getAccount().getIdentifier();
        String user = userAccBean.getUserId();

        List<CompInstanceKPIDetailsBean> compInstanceKPIDetailsBeans = new ArrayList<>();
        String timestamp = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());

        for (MetricDetails metric : metricDetails) {
            int kpiId = metric.getMetricId();
            List<InstanceDetailsForKPI> instanceDetails;
            if (StringUtils.isEmpty(metric.getMetricGroup())) {
                instanceDetails = dataService.getInstancesForComponentKPI(componentId, kpiId, accountId, null);
            } else {
                instanceDetails = dataService.getInstancesForComponentGroupKPI(componentId, kpiId, accountId, null);
            }

            //Updating the component instance KPI mapping details
            for (InstanceDetailsForKPI instance : instanceDetails) {
                int instanceId = instance.getInstanceId();
                if (metric.getMappedInstances().contains(instanceId) ||
                        metric.getUnmappedInstances().contains(instanceId)) {

                    if (metric.getCollectionInterval() > 0) {
                        instance.setCollectionInterval(metric.getCollectionInterval());
                    }
                    if (metric.getProducerId() > 0) {
                        instance.setProducerId(metric.getProducerId());
                        int mstProducerKPIMappingId = 0;
                        if (metric.getProducerId() > 0) {
                            mstProducerKPIMappingId = dataService.getMstProducerKPIMappingIdForCompInstance(metric.getProducerId(), componentId, instanceId, kpiId, accountId, null);
                            if (mstProducerKPIMappingId < 1) {
                                LOGGER.error("Producer [{}] is not mapped to the KPI [{}] or the component version of " +
                                                "the instance hence the component instance [{}] cannot be mapped to this producer.",
                                        metric.getProducerId(), kpiId, instanceId);
                                throw new ServerException("Producer is not mapped to the KPI hence the component instances cannot be " +
                                        "mapped to this producer.");
                            }
                        }
                        instance.setMstProducerKPIMappingId(mstProducerKPIMappingId);
                    }

                    if (metric.getMappedInstances().contains(instance.getInstanceId())) {
                        instance.setStatus(1);
                    } else if (metric.getUnmappedInstances().contains(instance.getInstanceId())) {
                        instance.setStatus(0);
                    }

                    compInstanceKPIDetailsBeans.add(getCompInstanceKPIDetailsBeanForUpdate(instance, kpiId, user, timestamp, accountIdentifier, componentId));
                }
            }
            compInstanceKPIDetailsBeans.addAll(getBeansToMapComponentInstancesToKPI(metric, instanceDetails, componentId, accountId, user, timestamp, accountIdentifier));
        }
        return compInstanceKPIDetailsBeans;
    }

    private CompInstanceKPIDetailsBean getCompInstanceKPIDetailsBeanForUpdate(InstanceDetailsForKPI instanceDetails,
                                                                              int kpiId, String user, String timestamp, String accountIdentifier, int componentId) {
        return CompInstanceKPIDetailsBean.builder()
                .id(instanceDetails.getInstanceKPIMappingId())
                .kpiId(kpiId)
                .instanceId(instanceDetails.getInstanceId())
                .producerId(instanceDetails.getProducerId())
                .mstProducerKPIMappingId(instanceDetails.getMstProducerKPIMappingId())
                .status(instanceDetails.getStatus())
                .collectionInterval(instanceDetails.getCollectionInterval())
                .isGroup(instanceDetails.getIsGroup())
                .userId(user)
                .updatedTime(timestamp)
                .accountIdentifier(accountIdentifier)
                .componentId(componentId)
                .groupKpiId(instanceDetails.getGroupKpiId())
                .build();
    }

    private List<CompInstanceKPIDetailsBean> getBeansToMapComponentInstancesToKPI(MetricDetails metric, List<InstanceDetailsForKPI>
            instanceDetails, int componentId, int accountId, String user, String timestamp, String accountIdentifier) throws ControlCenterException, ServerException {

        List<CompInstanceKPIDetailsBean> beans = new ArrayList<>();
        if (metric.getMappedInstances() != null) {
            Set<Integer> instancesMappedToKPI = instanceDetails.parallelStream().map(InstanceDetailsForKPI::getInstanceId)
                    .collect(Collectors.toSet());
            List<Integer> instancesToBeMapped = metric.getMappedInstances().parallelStream()
                    .filter(i -> !instancesMappedToKPI.contains(i)).collect(Collectors.toList());

            int kpiId = metric.getMetricId();
            if (!instancesToBeMapped.isEmpty()) {
                for (int instanceId : instancesToBeMapped) {
                    CompInstanceKPIDetailsBean compInstanceKPIDetailsBean = dataService.getDefaultCompInstanceKPIMappingDetails(componentId,
                            instanceId, kpiId, accountId, null);

                    int producerId;
                    int mstProducerKPIMappingId;
                    if (metric.getProducerId() > 0) {
                        producerId = metric.getProducerId();
                        mstProducerKPIMappingId = dataService.getMstProducerKPIMappingIdForCompInstance(producerId, componentId,
                                instanceId, kpiId, accountId, null);
                        if (mstProducerKPIMappingId < 1) {
                            LOGGER.error("Producer [{}] is not mapped to the KPI [{}] or the component version of " +
                                            "the instance hence the component instance [{}] cannot be mapped to this producer.",
                                    metric.getProducerId(), kpiId, instanceId);
                            throw new ServerException("Producer is not mapped to the KPI hence the component instances cannot be " +
                                    "mapped to this producer.");
                        }
                        compInstanceKPIDetailsBean.setProducerId(producerId);
                        compInstanceKPIDetailsBean.setMstProducerKPIMappingId(mstProducerKPIMappingId);
                        compInstanceKPIDetailsBean.setComponentId(componentId);
                    }

                    if (metric.getCollectionInterval() > 0) {
                        compInstanceKPIDetailsBean.setCollectionInterval(metric.getCollectionInterval());
                    }
                    compInstanceKPIDetailsBean.setAttribute(Constants.ALL);
                    compInstanceKPIDetailsBean.setUserId(user);
                    compInstanceKPIDetailsBean.setCreatedTime(timestamp);
                    compInstanceKPIDetailsBean.setUpdatedTime(timestamp);
                    compInstanceKPIDetailsBean.setStatus(Constants.STATUS_ACTIVE);
                    compInstanceKPIDetailsBean.setAttributeStatus(Constants.STATUS_ACTIVE);
                    compInstanceKPIDetailsBean.setComponentId(componentId);
                    compInstanceKPIDetailsBean.setAccountIdentifier(accountIdentifier);
                    compInstanceKPIDetailsBean.setAccountId(accountId);

                    beans.add(compInstanceKPIDetailsBean);
                }
            }
        }
        return beans;
    }


    @Override
    public String process(List<CompInstanceKPIDetailsBean> beans) throws DataProcessingException {
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        try {
            return dbi.inTransaction((conn, status) -> updateMetricDetails(beans, conn));
        } catch (Exception e) {
            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw (DataProcessingException) Throwables.getRootCause(e);
            } else {
                throw e;
            }
        }
    }

    private String updateMetricDetails(List<CompInstanceKPIDetailsBean> beans, Handle handle) throws ControlCenterException {

        List<CompInstanceKPIDetailsBean> updateInstanceDetailsForGroupKPIs = new ArrayList<>();
        List<CompInstanceKPIDetailsBean> updateInstanceDetailsForNonGroupKPIs = new ArrayList<>();
        List<CompInstanceKPIDetailsBean> addInstanceDetailsForGroupKPIs = new ArrayList<>();
        List<CompInstanceKPIDetailsBean> addInstanceDetailsForNonGroupKPIs = new ArrayList<>();

        beans.forEach(bean -> {
            if (bean.getId() > 0 && bean.getGroupKpiId() == 0) {
                updateInstanceDetailsForNonGroupKPIs.add(bean);
            } else if (bean.getId() > 0 && bean.getGroupKpiId() != 0) {
                updateInstanceDetailsForGroupKPIs.add(bean);
            } else if (bean.getId() == 0 && bean.getGroupKpiId() == 0) {
                addInstanceDetailsForNonGroupKPIs.add(bean);
            } else if (bean.getId() == 0 && bean.getGroupKpiId() != 0) {
                addInstanceDetailsForGroupKPIs.add(bean);
            }
        });

        if (!updateInstanceDetailsForNonGroupKPIs.isEmpty()) {
            dataService.updateInstanceProducerMappingDetailsForNonGroupKPI(updateInstanceDetailsForNonGroupKPIs, handle);
        }
        if (!updateInstanceDetailsForGroupKPIs.isEmpty()) {
            dataService.updateInstanceProducerMappingDetailsForGroupKPI(updateInstanceDetailsForGroupKPIs, handle);
        }
        if (!addInstanceDetailsForNonGroupKPIs.isEmpty()) {
            dataService.addInstanceProducerMappingDetailsForNonGroupKPI(addInstanceDetailsForNonGroupKPIs, handle);
        }
        if (!addInstanceDetailsForGroupKPIs.isEmpty()) {
            dataService.addInstanceProducerMappingDetailsForGroupKPI(addInstanceDetailsForGroupKPIs, handle);
        }
        //updating and adding a group and non group kpis.
        String accountIdentifier = beans.get(0).getAccountIdentifier();
        Map<Integer, com.heal.configuration.pojos.CompInstClusterDetails> instClusterDetailMap = instanceRepo.getInstances(accountIdentifier)
                .stream()
                .collect(Collectors.toMap(com.heal.configuration.pojos.CompInstClusterDetails::getId, Function.identity()));
        if(instClusterDetailMap.isEmpty()){
            LOGGER.error("Instance details not found for the given accountIdentifier: {}",accountIdentifier);
            return "";
        }
        updateGroupAndNonGroupMetricDetails(beans, instClusterDetailMap, accountIdentifier , handle);

        return "Component Instance - KPI - Producer mapping and collection interval updated successfully";
    }
    public void updateGroupAndNonGroupMetricDetails(List<CompInstanceKPIDetailsBean> beans, Map<Integer, com.heal.configuration.pojos.CompInstClusterDetails> instClusterDetailMap, String accountIdentifier, Handle handle){
        List<Component> componentDetails = componentRepo.getComponentDetails(accountIdentifier);
        if(componentDetails.isEmpty()){
            LOGGER.error("Component details not found for the given accountIdentifier: {}",accountIdentifier);
            return ;
        }
        beans.forEach(metricDetail -> {
             com.heal.configuration.pojos.CompInstClusterDetails instClusterDetail = instClusterDetailMap.get(metricDetail.getInstanceId());
            if(instClusterDetail == null){
                LOGGER.error("The instance details not found for the given accountIdentifier: {} and instanceId: {}", accountIdentifier, metricDetail.getInstanceId());
                return ;
            }
            List<CompInstKpiEntity> instanceWiseKpis = instanceRepo.getInstanceWiseKpis(accountIdentifier, instClusterDetail.getIdentifier());
            if(instanceWiseKpis.isEmpty()){
                instanceWiseKpis = new ArrayList<>();
            }
            CompInstKpiEntity kpiEntity = instanceWiseKpis.stream().filter(k -> k.getId() == metricDetail.getKpiId() && k.getGroupId() == metricDetail.getGroupKpiId()).findAny().orElse(null);
            if(kpiEntity == null)
            {
                Component componentDetail = componentDetails.
                        parallelStream()
                        .filter(f -> f.getId() == metricDetail.getComponentId())
                        .findAny()
                        .orElse(null);
                if(componentDetail == null) {
                    CCCache.INSTANCE.updateCCErrors(1);
                    LOGGER.error("Could not find the component details for redis cache. AccountId:{}", accountIdentifier);
                    return;
                }
                List<ComponentKpiEntity> componentKpiDetails = componentRepo.getComponentKpiDetails(accountIdentifier, componentDetail.getName());
                BasicKpiEntity componentKpi = componentKpiDetails.parallelStream()
                        .filter(f -> f.getId() == metricDetail.getKpiId() && f.getGroupId() == metricDetail.getGroupKpiId())
                        .findAny()
                        .orElse(null);
                if(componentKpi == null) {
                    CCCache.INSTANCE.updateCCErrors(1);
                    LOGGER.error("The kpi details not found for the given Component:{} from redis cache", componentDetail.getName());
                    return;
                }
                int comInsKpiId ;
                if(metricDetail.getGroupKpiId() >= 1){
                    try {
                        comInsKpiId = compInstanceDataService.getCompInsGroupKpiIdUsingKpiId(componentKpi.getId(), componentKpi.getGroupId(), instClusterDetail.getId(), handle);
                    } catch (ControlCenterException e) {
                        LOGGER.error("Could not found the comp_instance_kpi_group_details details for kpiId:{}", metricDetail.getKpiId());
                        return;
                    }
                }else{
                    try {
                        comInsKpiId = compInstanceDataService.getCompInsGroupKpiIdUsingKpiId(componentKpi.getId(), instClusterDetail.getId(), handle);
                    } catch (ControlCenterException e) {
                        LOGGER.error("Could not found the comp_instance_kpi_details id for kpiId:{}", metricDetail.getKpiId());
                        return;
                    }
                }
                kpiEntity = getCompInstKpiEntity(metricDetail, componentKpi, comInsKpiId);
                instanceWiseKpis.add(kpiEntity);
            }
            kpiEntity.setCollectionInterval(metricDetail.getCollectionInterval());
            kpiEntity.setStatus(metricDetail.getStatus());
            kpiEntity.setNotification(metricDetail.getNotification());
            kpiEntity.setDefaultProducerId(metricDetail.getProducerId());
            kpiEntity.setProducerKpiMappingId(metricDetail.getMstProducerKPIMappingId());

            instanceRepo.updateKpiDetailsForKpiId(accountIdentifier, instClusterDetail.getIdentifier(), kpiEntity);
            instanceRepo.updateKpiDetailsForKpiIdentifier(accountIdentifier, instClusterDetail.getIdentifier(), kpiEntity);
            instanceRepo.updateKpiDetails(accountIdentifier, instClusterDetail.getIdentifier(), instanceWiseKpis);
        });
    }

    private CompInstKpiEntity getCompInstKpiEntity(CompInstanceKPIDetailsBean metricDetail, BasicKpiEntity componentKpi, int comInsKpiId) {
        CompInstKpiEntity kpiEntity;
        kpiEntity = CompInstKpiEntity.builder()
               .id(componentKpi.getId())
               .name(componentKpi.getName())
               .categoryDetails(componentKpi.getCategoryDetails())
               .identifier(componentKpi.getIdentifier())
               .custom(componentKpi.getCustom())
               .unit(componentKpi.getUnit())
               .aggOperation(componentKpi.getAggOperation())
               .type(componentKpi.getType())
               .rollupOperation(componentKpi.getRollupOperation())
               .clusterAggType(componentKpi.getClusterAggType())
               .instanceAggType(componentKpi.getInstanceAggType())
               .valueType(componentKpi.getValueType())
               .dataType(componentKpi.getDataType())
               .isInfo(componentKpi.getIsInfo())
               .resetDeltaValue(componentKpi.getResetDeltaValue())
               .deltaPerSec(componentKpi.getDeltaPerSec())
               .cronExpression(componentKpi.getCronExpression())
               .categoryDetails(componentKpi.getCategoryDetails())
               .defaultProducerId(metricDetail.getMstProducerKPIMappingId())
               .collectionInterval(metricDetail.getCollectionInterval())
               .status(metricDetail.getStatus())
               .notification(metricDetail.getNotification())
               .groupId(componentKpi.getGroupId())
               .groupName(componentKpi.getGroupName())
               .groupIdentifier(componentKpi.getGroupIdentifier())
               .groupStatus(componentKpi.getGroupStatus())
               .isGroup(componentKpi.getIsGroup())
               .discovery(componentKpi.getDiscovery())
               .isMaintenanceExcluded(0)
                .compInstKpiId(comInsKpiId)
               .build();
        return kpiEntity;
    }
}


