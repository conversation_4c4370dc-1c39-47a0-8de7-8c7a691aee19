package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ComponentInstanceBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.CompInstanceEnvDetailsPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class ComponentInstanceEnvUpdateBL implements BusinessLogic<List<CompInstanceEnvDetailsPojo>, List<CompInstanceEnvDetailsPojo>, Object> {
    private int accId;

    @Override
    public UtilityBean<List<CompInstanceEnvDetailsPojo>> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(requestObject.getBody())) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid account identifier");
        }
        List<CompInstanceEnvDetailsPojo> parsedRequestBody;
        try {
            ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();
            parsedRequestBody = objectMapper.readValue(requestObject.getBody(),
                    new TypeReference<List<CompInstanceEnvDetailsPojo>>() {
                    });
            parsedRequestBody = parsedRequestBody.parallelStream().distinct().collect(Collectors.toList());
            for (CompInstanceEnvDetailsPojo data : parsedRequestBody) {
                if (!data.isValid()) {
                    log.error("Input Validation failure for CompInstanceEnvDetails");
                    throw new ClientException("Input Validation failure for CompInstanceEnvDetails");
                }
            }
        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        return UtilityBean.<List<CompInstanceEnvDetailsPojo>>builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .pojoObject(parsedRequestBody)
                .build();
    }

    @Override
    public List<CompInstanceEnvDetailsPojo> serverValidation(UtilityBean<List<CompInstanceEnvDetailsPojo>> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        accId = account.getId();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        for (CompInstanceEnvDetailsPojo compInstanceEnvDetailsPojo : utilityBean.getPojoObject()) {
            String instanceIdentifier = compInstanceEnvDetailsPojo.getIdentifier();

            ComponentInstanceBean componentInstanceBean = new CompInstanceDataService().
                    getComponentInstanceByIdentifier(instanceIdentifier, account.getId(), null);

            if (componentInstanceBean == null) {
                log.error("Component Instance Identifier [{}] is unavailable", instanceIdentifier);
                throw new ServerException(String.format("Component Instance Identifier [%s] is unavailable", instanceIdentifier));
            }

            int envId = compInstanceEnvDetailsPojo.getEnv();

            if (envId < 1 || envId > 5) {
                log.warn("Environment id [{}] is incorrect. Resetting the value to 0 (NA)", envId);
                compInstanceEnvDetailsPojo.setEnv(0);
            }
        }
        return utilityBean.getPojoObject();
    }

    @Override
    public Object process(List<CompInstanceEnvDetailsPojo> compInstanceEnvDetailsPojo) throws DataProcessingException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();

            dbi.inTransaction((conn, status) -> {
                for (CompInstanceEnvDetailsPojo data : compInstanceEnvDetailsPojo) {
                    try {
                        new CompInstanceDataService().updateEnvDetails(data, accId, conn);
                    } catch (Exception e) {
                        log.error("Error while updating environment information for component instances. Reason: ", e);
                        throw new DataProcessingException("Error in updating environment id '" + data.getEnv() + "' for '" + data.getIdentifier() + "' instance");
                    }
                }
                return null;
            });
        } catch (Exception e) {
            log.error("Error while updating environment information for component instances. Reason: ", e);
            throw (DataProcessingException) Throwables.getRootCause(e);
        }
        return null;
    }
}