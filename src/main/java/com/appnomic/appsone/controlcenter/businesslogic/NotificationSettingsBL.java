package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.NotificationSettingsBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.NotificationSettingsDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.NotificationSettings;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.pojo.NotificationSettingsPojo;
import com.appnomic.appsone.controlcenter.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.*;

public class NotificationSettingsBL {

    private static final ObjectMapper obj_mapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    private static final Logger logger = LoggerFactory.getLogger(NotificationSettingsBL.class);
    private static final int MIN_OPEN_FOR_LONG = ConfProperties.getInt(Constants.MIN_OPEN_FOR_LONG, Constants.MIN_OPEN_FOR_LONG_DEFAULT);
    private static final int MIN_OPEN_FOR_TOO_LONG = ConfProperties.getInt(Constants.MIN_OPEN_FOR_TOO_LONG, Constants.MIN_OPEN_FOR_TOO_LONG_DEFAULT);
    private static final int MAX_OPEN_FOR_LONG = ConfProperties.getInt(Constants.MAX_OPEN_FOR_LONG, Constants.MAX_OPEN_FOR_LONG_DEFAULT);
    private static final int MAX_OPEN_FOR_TOO_LONG = ConfProperties.getInt(Constants.MAX_OPEN_FOR_TOO_LONG, Constants.MAX_OPEN_FOR_TOO_LONG_DEFAULT);
    static int longId = MasterCache.getMstTypeForSubTypeName(Constants.NOTIFICATION_TYPE_LITERAL, Constants.LONG).getSubTypeId();

    static int tooLongId = MasterCache.getMstTypeForSubTypeName(Constants.NOTIFICATION_TYPE_LITERAL, Constants.TOO_LONG).getSubTypeId();

    static String longName = MasterCache.getMstTypeForSubTypeName(Constants.NOTIFICATION_TYPE_LITERAL, Constants.LONG).getSubTypeName();

    static String tooLongName = MasterCache.getMstTypeForSubTypeName(Constants.NOTIFICATION_TYPE_LITERAL, Constants.TOO_LONG).getSubTypeName();


    public static List<NotificationSettingsPojo> addClientValidations(Request request) throws RequestException {

        List<NotificationSettingsPojo> settings;
        ValidationUtils.commonClientValidations(request);

        if (StringUtils.isEmpty(request.body())) {
            logger.error(UIMessages.REQUEST_NULL);
            throw new RequestException(UIMessages.REQUEST_NULL);
        }

        try {
            settings = obj_mapper.readValue(request.body(),
                    new TypeReference<List<NotificationSettingsPojo>>() {
                    });

        } catch (IOException e) {
            logger.error(UIMessages.JSON_INVALID + "err:{}", e.getMessage());
            throw new RequestException(UIMessages.JSON_INVALID);
        }

        if (settings.isEmpty()) {
            logger.error(UIMessages.REQUEST_NULL);
            throw new RequestException(UIMessages.REQUEST_NULL);
        }

        Set<NotificationSettingsPojo> settingsSet = new HashSet<>(settings);
        if (settingsSet.size() < settings.size()) {
            logger.error(UIMessages.DUPLICATE_NOTIFICATION_SETTING);
            throw new RequestException(UIMessages.DUPLICATE_NOTIFICATION_SETTING);
        }

        for (NotificationSettingsPojo setting : settings) {
            setting.validate();
            if (!setting.getError().isEmpty()) {
                String err = setting.getError().toString();
                logger.error(err);
                throw new RequestException(err);
            }
        }
        validateData(settings);
        return settings;
    }

    public static List<NotificationSettings> addServerValidations(List<NotificationSettingsPojo> notificationSettings, UserAccountBean userAccBean) throws RequestException {

        int accountId = userAccBean.getAccount().getId();
        String userId = userAccBean.getUserId();
        List<NotificationSettings> settings = new ArrayList<>();
        Timestamp timestamp = null;

        try {
            timestamp = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
        } catch (ParseException e) {
            logger.error("Exception occurred. Details: ", e);
        }
        List<NotificationSettingsBean> settingsDB = NotificationSettingsDataService.getNotificationSetting(accountId);
        if(settingsDB.isEmpty()){
            logger.error("Error: Notification Settings not found for the account");
            throw new RequestException("Notification Settings not found for the account");
        }
        int openForLong = notificationSettings.stream().filter(notificationSetting -> notificationSetting.getTypeName().equals(longName))
                .filter(notificationSetting -> (notificationSetting.getTypeId() == longId)).map(NotificationSettingsPojo::getDurationInMin).map(Float::intValue).findAny().orElse(MIN_OPEN_FOR_LONG);
        int openForTooLong = notificationSettings.stream().filter(notificationSetting -> notificationSetting.getTypeName().equals(tooLongName))
                .filter(notificationSetting -> (notificationSetting.getTypeId() == tooLongId)).map(NotificationSettingsPojo::getDurationInMin).map(Float::intValue).findAny().orElse(MIN_OPEN_FOR_TOO_LONG);
        if (openForLong < MIN_OPEN_FOR_LONG) {
            openForLong = MIN_OPEN_FOR_LONG;
        } else if (openForLong > MAX_OPEN_FOR_LONG) {
            openForLong = MAX_OPEN_FOR_LONG;
        }
        if (openForTooLong < MIN_OPEN_FOR_TOO_LONG) {
            openForTooLong = MIN_OPEN_FOR_TOO_LONG;
        } else if (openForTooLong > MAX_OPEN_FOR_TOO_LONG) {
            openForTooLong = MAX_OPEN_FOR_TOO_LONG;
        }
        int remainder = (openForTooLong % openForLong);
        if (remainder != 0) {
            openForTooLong = ((openForTooLong / openForLong) * openForLong);
        }
        if (openForLong == openForTooLong) {
            openForTooLong = 2 * openForLong;
        }

        for (NotificationSettingsPojo setting : notificationSettings) {
            NotificationSettings entity = new NotificationSettings();
            entity.setAccountId(accountId);
            entity.setUpdatedTime(String.valueOf(timestamp));
            if((setting.getTypeName().equals(longName) && (setting.getTypeId() == longId))) {
                entity.setDurationInMin(openForLong);
            }
            else if((setting.getTypeName().equals(tooLongName) && (setting.getTypeId() == tooLongId))){
                entity.setDurationInMin(openForTooLong);
            }
            entity.setTypeId(setting.getTypeId());
            entity.setUserId(userId);
            settings.add(entity);
        }
        return settings;
    }

    public static void validateData(List<NotificationSettingsPojo> notificationSettings) throws RequestException {
        Map<String, String> error = new HashMap<>();

        for (NotificationSettingsPojo settings : notificationSettings) {
            if (!((settings.getTypeName().equals(longName)) || (settings.getTypeName().equals(tooLongName)))) {
                logger.error(UIMessages.INVALID_TYPE_NAME);
                error.put("Type name", UIMessages.INVALID_TYPE_NAME);
            }
            if (!((settings.getTypeName().equals(longName) && (settings.getTypeId() == longId)) || (settings.getTypeName().equals(tooLongName) && (settings.getTypeId() == tooLongId)))) {
                logger.error(UIMessages.INVALID_COMBINATION);
                error.put("Type name/id combination", UIMessages.INVALID_COMBINATION);
            }
            if ((settings.getDurationInMin() <= 0)) {
                logger.error(UIMessages.INVALID_DURATION);
                error.put("Duration In Minutes", UIMessages.INVALID_DURATION);
            }
            if (!error.isEmpty()) {
                String err = error.toString();
                logger.error(err);
                throw new RequestException(err);
            }
        }
    }


    public int[] addDefaultNotificationSettings(int accountId, String userId) {

        logger.trace("Method Invoked : NotificationSettingsBL/addNotificationSettings");
        int[] ids = {};
        try {
            List<NotificationSettings> defaultSettingsList = new ArrayList<>();
            NotificationSettings settingsLong = new NotificationSettings();
            NotificationSettings settingsTooLong = new NotificationSettings();
            Timestamp timestamp = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
            List<NotificationSettingsBean> settingsDB = NotificationSettingsDataService.getNotificationSetting(1);

            for (NotificationSettingsBean settings : settingsDB) {
                if (settings.getTypeId() == longId) {
                    settingsLong.setDurationInMin(settings.getDurationInMin());
                }
                if (settings.getTypeId() == tooLongId) {
                    settingsTooLong.setDurationInMin(settings.getDurationInMin());
                }
            }
            settingsLong.setUpdatedTime(timestamp.toString());
            settingsLong.setCreatedTime(timestamp.toString());
            settingsLong.setUserId(userId);
            settingsLong.setAccountId(accountId);
            settingsLong.setTypeId(longId);

            settingsTooLong.setUpdatedTime(timestamp.toString());
            settingsTooLong.setCreatedTime(timestamp.toString());
            settingsTooLong.setUserId(userId);
            settingsTooLong.setAccountId(accountId);
            settingsTooLong.setTypeId(tooLongId);

            defaultSettingsList.add(settingsLong);
            defaultSettingsList.add(settingsTooLong);

            ids = NotificationSettingsDataService.addNotificationSettings(defaultSettingsList);
            return ids;
        } catch (Exception e) {
            logger.error("Error occurred while adding Notification Settings.", e);
        }
        return ids;
    }

}