package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ParentApplicationDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.TagsDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.ApplicationRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ParentApplicationRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.Application;
import com.heal.configuration.pojos.ParentApplication;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.util.List;

@Slf4j
public class DeleteParentApplicationBL implements BusinessLogic<Object, ParentApplication, IdPojo> {

    ParentApplicationRepo parentApplicationRepo = new ParentApplicationRepo();
    String accountIdentifier;
    @Override
    public UtilityBean<Object> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid account identifier");
        }

        String parentApplicationIdentifier = requestObject.getParams().get(Constants.PARENT_APPLICATION_IDENTIFIER);
        if (parentApplicationIdentifier == null || parentApplicationIdentifier.trim().isEmpty()) {
            log.error("Invalid Parent Application identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid Parent Application identifier");
        }

        return UtilityBean.builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .pojoObject(parentApplicationIdentifier)
                .build();
    }

    @Override
    public ParentApplication serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
         accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        String parentApplicationIdentifier = utilityBean.getPojoObject().toString();

        ParentApplication parentApplication = parentApplicationRepo.getParentApplication(accountIdentifier, parentApplicationIdentifier);

        if(parentApplication == null) {
            log.error("Parent Application identifier does not exist");
            throw new ServerException("Parent Application identifier does not exist");
        }

        if(parentApplication.getApplicationIdentifiers() != null) {
            log.error("Applications are mapped to Parent Application {}. So, Parent Application {} cannot be deleted", parentApplicationIdentifier, parentApplicationIdentifier);
            throw new ServerException("Applications are mapped to Parent Application");
        }

        return parentApplication;
    }

    @Override
    public IdPojo process(ParentApplication parentApplication) throws DataProcessingException {
        ParentApplicationDataService parentApplicationDataService = new ParentApplicationDataService();
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            IdPojo idPojo;
            idPojo = dbi.inTransaction((conn, status) -> {
                IdPojo id;
                id = parentApplicationDataService.deleteParentApplication(conn, parentApplication);
                if(parentApplication.getApplicationIdentifiers() != null) {
                    removeParentApplicationTagMapping(parentApplication, conn);
                }
                return id;
            });
            deleteParentApplicationInRedis(parentApplication, accountIdentifier);
            return idPojo;
        }catch (Exception e) {
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }

    private void deleteParentApplicationInRedis(ParentApplication parentApplication, String accountIdentifier) {
        ApplicationRepo applicationRepo = new ApplicationRepo();
        List<ParentApplication> parentApplications = parentApplicationRepo.getAllParentApplications(accountIdentifier);
        List<String> applications = parentApplication.getApplicationIdentifiers();
        parentApplications.removeIf(app -> app.getIdentifier().equalsIgnoreCase(parentApplication.getIdentifier()));
        parentApplicationRepo.updateParentApplication(parentApplications, accountIdentifier);
        parentApplicationRepo.deleteParentApplicationByIdentifier(parentApplication.getIdentifier(), accountIdentifier);
        if (parentApplication.getApplicationIdentifiers() != null) {
            for (String app : applications) {
                Application application = applicationRepo.getApplicationForIdentifier(accountIdentifier, app);
                if (application != null) {
                    application.setParentApplication(null);
                    applicationRepo.updateApplication(accountIdentifier, application);
                }
            }
        }
    }

    public void removeParentApplicationTagMapping(ParentApplication parentApplication, Handle handle) throws ControlCenterException {
        try {
            TagsDataService.deleteTagMappingByObjectNTagId(parentApplication.getId(), Constants.PARENT_APPLICATION_TAG_ID, handle);
        } catch (Exception e) {
            log.error("Error removing tag for Parent Application Identifier : {}", parentApplication.getIdentifier());
            throw new ControlCenterException(e.getMessage());
        }
    }
}
