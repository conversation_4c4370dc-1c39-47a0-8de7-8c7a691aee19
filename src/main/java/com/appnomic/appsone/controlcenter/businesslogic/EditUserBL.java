package com.appnomic.appsone.controlcenter.businesslogic;


import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.UserAccessDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.UserDataService;
import com.appnomic.appsone.controlcenter.dao.redis.UsersRepo;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.UserInfo;
import com.appnomic.appsone.controlcenter.service.NotificationPreferencesDataService;
import com.appnomic.appsone.controlcenter.util.AECSBouncyCastleUtil;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.UserUtility;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.appnomic.appsone.keycloak.KeycloakConnectionManager;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.User;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class EditUserBL implements BusinessLogic<UserInfo, UserInfoBean, String> {

    private static final ObjectMapper OBJECT_MAPPER = CommonUtils.getObjectMapperWithHtmlEncoder();
    private static final int USER_MANAGER = 3;

    @Override
    public UtilityBean<UserInfo> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(request.getBody())) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String requestBody = request.getBody();
        String decryptedRequestBody;
        try {
            decryptedRequestBody = new AECSBouncyCastleUtil().decrypt(requestBody);
        } catch (InvalidCipherTextException e) {
            throw new ClientException(e, e.getMessage());
        }
        UserInfo userInfo;
        try {
            userInfo = OBJECT_MAPPER.readValue(decryptedRequestBody, new TypeReference<UserInfo>() {
            });
        } catch (IOException e) {
            log.error(Constants.JSON_PARSE_ERROR + " : {}", e.getMessage());
            throw new ClientException(Constants.JSON_PARSE_ERROR);
        }

        String identifier = request.getParams().get(Constants.USER_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            throw new ClientException("User Identifier is not present in the request.");
        }

        userInfo.setId(identifier);

        if (!userInfo.validate()) {
            log.error("Validation failure of details provided");
            throw new ClientException(UIMessages.INVALID_REQUEST);
        }

        return UtilityBean.<com.appnomic.appsone.controlcenter.pojo.UserInfo>builder()
                .authToken(authToken)
                .pojoObject(userInfo)
                .build();
    }

    @Override
    public UserInfoBean serverValidation(UtilityBean<UserInfo> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        UsersBL usersBL = new UsersBL();

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        UserInfo userRequest = utilityBean.getPojoObject();
        if (userRequest.getId() == null || new UserDataService().getUsers().parallelStream().noneMatch(u -> u.getId().equals(userRequest.getId()))) {
            throw new ServerException("Invalid userRequest identifier provided.");
        }

        UserInfo userInfo;
        try {
            userInfo = usersBL.getUserDetails(userRequest.getId());
        } catch (Exception e) {
            throw new ServerException("Unable to fetch user from db.");
        }
        userInfo.setUserDetailsId(userId);

        KeycloakUserBean backupKeycloak = KeycloakUserBean.builder()
                .enabled(String.valueOf(userInfo.getStatus() == 1))
                .email(userInfo.getEmailId())
                .firstName(userInfo.getFirstName())
                .lastName(userInfo.getLastName())
                .username(userInfo.getUserName()).build();

        try {
            UserDataService userDataService = new UserDataService();
            String setup = userDataService.getSetup();
            if (setup == null) {
                log.error("Error while fetching integration mode.");
                throw new ServerException("Unable to get Setup Mode.");
            }
            if (Constants.SETUP_AD_INTEGRATION.equalsIgnoreCase(setup)) {
                if (userRequest.getFirstName() != null || userRequest.getLastName() != null || userRequest.getEmailId() != null) {
                    throw new ServerException("User details (first name or last name or emailId) cannot be modified " +
                            "in Keycloak in AD Integrated setup.");
                }

                if (!userDataService.adEditStatusKeycloak() && userInfo.getStatus() != userRequest.getStatus()) {
                    throw new ServerException("User status cannot be modified in Keycloak in AD Integrated setup.");
                }
            } else if (Constants.SETUP_KEYCLOAK.equalsIgnoreCase(setup)) {

                if (userRequest.getFirstName() != null) {
                    userInfo.setEditInKeycloak(true);
                    userInfo.setFirstName(userRequest.getFirstName());
                }

                if (userRequest.getLastName() != null) {
                    userInfo.setEditInKeycloak(true);
                    userInfo.setLastName(userRequest.getLastName());
                }

                if (userRequest.getEmailId() != null) {
                    userInfo.setEditInKeycloak(true);
                    List<UserBean> userBeans = UserAccessDataService.getUserDetailsFromKeycloak();
                    usersBL.validateEmailId(userBeans, userRequest.getEmailId());
                    userInfo.setEmailId(userRequest.getEmailId());
                }
            }

            if (userRequest.getContactNumber() != null) {
                userInfo.setContactNumber(userRequest.getContactNumber());
            }
            if (userInfo.getStatus() != userRequest.getStatus()) {
                userInfo.setEditInKeycloak(true);
            }
            userInfo.setStatus(userRequest.getStatus());

            if (userRequest.getProfileChange() == 1) {
                usersBL.validateRoleAndProfile(userRequest.getRoleId(), userRequest.getProfileId());
                userInfo.setRoleId(userRequest.getRoleId());
                userInfo.setProfileId(userRequest.getProfileId());
                userInfo.setProfileChange(1);

                if (userRequest.getAccessDetails().parallelStream().anyMatch(u -> !u.getAction()
                        .equalsIgnoreCase("add") || (u.getApplications() != null && u.getApplications().parallelStream()
                        .anyMatch(a -> !a.getAction().trim().equalsIgnoreCase("add"))))) {
                    throw new ServerException("Only 'add' action is valid in accessDetails if user profile is being modified.");
                }
                userInfo.setAccessDetails(userRequest.getAccessDetails());
            } else if (userRequest.getAccessDetails() != null && !userRequest.getAccessDetails().isEmpty()) {
                UserInfo editAccessDetails = getEditAccessDetails(userInfo.getAccessDetails(), userRequest.getAccessDetails());
                userInfo.setAccessDetails(editAccessDetails.getAccessDetails());
                userInfo.setDeletedAccessDetails(editAccessDetails.getDeletedAccessDetails());
            } else if (userRequest.getAccessDetails() == null) {
                userInfo.setAccessDetails(null);
            }

            AccessDetailsBean accessDetailsBean = usersBL.validateAndGetAccessDetailsUser(userInfo.getAccessDetails());
            AccessDetailsBean deletedAccessDetailBean = usersBL.validateAndGetAccessDetailsUser(userInfo.getDeletedAccessDetails());


            UserInfoBean userInfoBean = usersBL.getUserInfoBean(userInfo, accessDetailsBean);
            userInfoBean.setKeycloakRollbackUserDetails(backupKeycloak);
            if (deletedAccessDetailBean != null) {
                userInfoBean.setDeletedAccessDetailBean(deletedAccessDetailBean);
            }
            return userInfoBean;
        } catch (ControlCenterException | RequestException c) {
            throw new ServerException(c.getMessage());
        }
    }

    private UserInfo getEditAccessDetails(List<UserInfo.AccessDetails> accessDetails,
                                                              List<UserInfo.AccessDetails> updateAccess)
            throws ServerException {

        List<UserInfo.AccessDetails> accessDetailsUpdated = null;
        List<UserInfo.AccessDetails> deletedAccessDetails = new ArrayList<>();


        for (UserInfo.AccessDetails access : updateAccess) {

            if (access.getAction().trim().equalsIgnoreCase("Add")) {
                boolean accountIsPresent = accessDetails.parallelStream().anyMatch(acd -> acd.getAccountId().equals(access.getAccountId()));
                if (accountIsPresent) {
                    log.warn("duplicate accountId provided in request skipping this accountIdentifier: [{}]", access.getAccountId());
                    continue;
                }
                accessDetails.add(access);

            } else if (access.getAction().trim().equalsIgnoreCase("Delete")) {

                Optional<UserInfo.AccessDetails> deleteAccess = accessDetails.parallelStream()
                        .filter(a -> a.getAccountId().equals(access.getAccountId())).findAny();
                if (deleteAccess.isPresent()) {
                    deletedAccessDetails.add(deleteAccess.get());
                    accessDetails.remove(deleteAccess.get());
                } else {
                    throw new ServerException("Account access is not available hence it cant be removed. accountId : "
                            + access);
                }

            } else if (access.getAction().trim().equalsIgnoreCase("Edit")) {

                int index;
                Optional<UserInfo.AccessDetails> editAccess = accessDetails.parallelStream()
                        .filter(a -> a.getAccountId().equals(access.getAccountId())).findAny();
                if (editAccess.isPresent()) {
                    index = accessDetails.indexOf(editAccess.get());
                } else {
                    throw new ServerException("Account access is not available hence it cant be edited. accountId : "
                            + access);
                }
                UserInfo editAppAccess = getEditAppAccess(accessDetails, index, access);
                deletedAccessDetails.addAll(editAppAccess.getDeletedAccessDetails());

                accessDetailsUpdated = editAppAccess.getAccessDetails();
                

            } else {
                throw new ServerException("Action Invalid : Only 'add', 'delete' and 'edit' are valid actions. " + access);
            }
            if (accessDetailsUpdated == null || accessDetailsUpdated.isEmpty()) {
                accessDetailsUpdated = accessDetails;
            }

        }
        UserInfo userInfo = new UserInfo();
        userInfo.setDeletedAccessDetails(deletedAccessDetails);
        userInfo.setAccessDetails(accessDetailsUpdated);
        return userInfo;
    }

    private UserInfo getEditAppAccess(List<UserInfo.AccessDetails> accessDetails, int index,
                                                          UserInfo.AccessDetails access)
            throws ServerException {

        List<UserInfo.AccessDetails> deletedAccessDetails = new ArrayList<>();
        for (UserInfo.AccessDetails.UserApp appAccess : access.getApplications()) {

            if (appAccess.getAction().trim().equalsIgnoreCase("Add")) {

                List<String> idsToAdd = appAccess.getIds().parallelStream()
                        .filter(id -> !accessDetails.get(index).getApplications().get(0).getIds().contains(id))
                        .collect(Collectors.toList());
                
                accessDetails.get(index).getApplications().get(0).getIds().addAll(idsToAdd);

            } else if (appAccess.getAction().trim().equalsIgnoreCase("Delete")) {

                UserInfo.AccessDetails deletedAccessDetail = new UserInfo.AccessDetails();

                List<UserInfo.AccessDetails.UserApp> deletedApplications = new ArrayList<>();

                UserInfo.AccessDetails.UserApp deletedApp = new UserInfo.AccessDetails.UserApp();
                deletedApp.setIds(new ArrayList<>(appAccess.getIds()));
                deletedApplications.add(deletedApp);
                deletedAccessDetail.setAccountId(access.getAccountId());

                deletedAccessDetail.setApplications(deletedApplications);

                accessDetails.get(index).getApplications().get(0).getIds().removeAll(appAccess.getIds());

                deletedAccessDetails.add(deletedAccessDetail);

            } else if (access.getAction().trim().isEmpty()) {
                throw new ServerException("Action Invalid : Only 'add' and 'delete' are valid actions for " +
                        "applications. " + appAccess);
            }
        }

        UserInfo userInfo = new UserInfo();
        userInfo.setAccessDetails(accessDetails);
        userInfo.setDeletedAccessDetails(deletedAccessDetails);

        return userInfo;
    }

    @Override
    public String process(UserInfoBean bean) throws DataProcessingException {
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        try {
            dbi.inTransaction((conn, status) -> {
                updateUser(bean, conn);
                return "NOTHING";
            });
            updateUsersInRedis(bean);
        } catch (Exception e) {
            throw new DataProcessingException(e.getCause().getCause().getMessage());
        }
        return null;
    }

    private void updateUser(UserInfoBean user, Handle conn) throws ControlCenterException {
        UsersBL usersBL = new UsersBL();
        UserDataService userDataService = new UserDataService();
        if (user.isEditInKeycloak()) {
            try {
                KeycloakConnectionManager.editKeycloakUser(OBJECT_MAPPER.writeValueAsString(usersBL.getKeycloakUserBean(user)), user.getId());
            } catch (IOException e) {
                log.error("Edit operation failed in keycloak.", e);
                throw new ControlCenterException("Edit operation failed in keycloak.");
            }
        }
        try {
            if (Boolean.TRUE.equals(user.getProfileChange()) && (user.getRoleId() == USER_MANAGER)) {
                usersBL.mapAdminRoleToUserManager(user.getId());
                NotificationPreferencesDataService.removeNotificationDetailsForUser(user.getId(), conn);
                NotificationPreferencesDataService.removeUserNotificationPreferencesForUser(user.getId(), conn);
                NotificationPreferencesDataService.removeForensicNotificationPreferencesForUser(user.getId(), conn);
            }

            userDataService.updateUserAttributes(usersBL.getUserAttributesBean(user), conn);
            if (user.getAccessDetails() != null) {
                userDataService.updateUserAccessDetails(usersBL.getUserAccessDetailsBean(user), conn);
                usersBL.updateNotificationPreferences(user, conn);
            }
        } catch (Exception e) {
            if (user.isEditInKeycloak()) {
                try {
                    KeycloakConnectionManager.editKeycloakUser(OBJECT_MAPPER.writeValueAsString(user.getKeycloakRollbackUserDetails()), user.getId());
                } catch (IOException exc) {
                    log.error("Error while rollback : User details are modified in Keycloak but not in schema.");
                }
            }
            throw new ControlCenterException(e.getMessage());
        }
    }

    private void updateUsersInRedis(UserInfoBean user) throws ControlCenterException {
        UserUtility userUtility = new UserUtility();

        updateUserDetails(user);

        if(user.getAccessDetails() != null) {
            userUtility.populateUserAccessDetails(user);
        }

        userUtility.populateUserDetailsInAppLevelKeys(user.getId());

        if (user.getDeletedAccessDetailBean() != null) {
            Map<String, AccessDetailsBean.Application> accountMapping = user.getDeletedAccessDetailBean().getAccountMapping();

            accountMapping.forEach((key, value) -> value.getApplications().forEach(appIdentifier -> {
                userUtility.removeApplicationWiseUser(key, appIdentifier, user.getId());
                userUtility.removeApplicationForensicUserMapping(key, appIdentifier, user.getId());
            }));
        }
    }

    private static void updateUserDetails(UserInfoBean user) {
        UsersRepo usersRepo = new UsersRepo();
        User existingUser = usersRepo.getUser(user.getId());

        if (existingUser == null) {
            log.error("User details does not exist in redis. Therefore, returning");
            return;
        }

        existingUser.setFirstName(user.getFirstName() != null ? user.getFirstName() : existingUser.getFirstName());
        existingUser.setLastName(user.getLastName() != null ? user.getLastName() : existingUser.getLastName());
        existingUser.setStatus(user.getStatus());
        existingUser.setContactNumber(user.getContactNumber());
        existingUser.setEmailId(user.getEmailId());
        existingUser.setUserDetailsId(user.getUserDetailsId());
        existingUser.setRoleId(user.getRoleId());
        existingUser.setProfileId(user.getProfileId());

        if (user.getRoleId() == USER_MANAGER) {
            existingUser.setEmailEnabled(0);
            existingUser.setSmsEnabled(0);
            existingUser.setForensicEnabled(0);
        }

        usersRepo.addUser(existingUser);
    }
}



