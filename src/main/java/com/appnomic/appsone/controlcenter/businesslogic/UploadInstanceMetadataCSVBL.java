package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.InstanceMetadataRequestBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.MasterDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.UserDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.InstanceMetadata;
import com.heal.configuration.pojos.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import spark.Request;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

@Slf4j
public class UploadInstanceMetadataCSVBL implements BusinessLogic<Set<InstanceMetadataRequestBean>, UtilityBean<List<InstanceMetadata>>, String> {
    private final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();
    private FileItem item = null;

    public static final String INSTANCE_METADATA_CSV_FILE_HEADERS = ConfProperties.getString(Constants.INSTANCE_METADATA_CSV_FILE_HEADERS,
            Constants.INSTANCE_METADATA_CSV_FILE_HEADERS_DEFAULT);
    @Override
    public UtilityBean<Set<InstanceMetadataRequestBean>> clientValidation(RequestObject request) throws ClientException {
        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String identifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Account identifier is null or empty.");
            throw new ClientException("Account identifier is null or empty.");
        }
        Set<InstanceMetadataRequestBean> instanceMetadataRequestBeans = getInstanceMetadataFromRequest(
                request, identifier);

        return UtilityBean.<Set<InstanceMetadataRequestBean>>builder()
                .authToken(authToken)
                .accountIdentifier(identifier)
                .pojoObject(instanceMetadataRequestBeans)
                .build();

    }

    public Set<InstanceMetadataRequestBean> validateCSVFile(FileItem item)
            throws ClientException {
        AtomicInteger rowCount = new AtomicInteger();
        try {

            String fileName = item.getName();
            item.write(new File(fileName));
            Pattern pattern = Pattern.compile(",");
            try (BufferedReader csvFile = new BufferedReader(new FileReader(fileName))) {

                String columnNames = CommonUtils.customReadLine(csvFile);
                if (columnNames == null || !columnNames.equals(INSTANCE_METADATA_CSV_FILE_HEADERS)) {
                    log.error("Error occurred while validating CSV file structure, CSV file should be in this structure: {}",
                            INSTANCE_METADATA_CSV_FILE_HEADERS);
                    throw new ClientException("CSV file is not in proper structure");
                }
                int status = columnNames.contains("status") ? 0 : 1;
                String[] header = pattern.split(columnNames);
                Set<InstanceMetadataRequestBean> listInstanceMetadataBean = csvFile.lines().map(row -> {
                    try {
                        rowCount.getAndIncrement();
                        String[] x = pattern.split(row, header.length);
                        if (x.length != header.length) {
                            log.error("Expected {} elements, but found {}, so skipping this row: [{}]", header.length, x.length, row);
                            return null;
                        }
                        return InstanceMetadataRequestBean.builder()
                                .instanceIdentifier(validateIdentifier(x[0], 0))
                                .environmentName(validateString(x[1]))
                                .relatedInstanceIdentifier(validateString(x[2]))
                                .memorySizeMB(convertToInteger(x[3], 3))
                                .diskSizeMB(convertToInteger(x[4], 4))
                                .cpuCores(convertToInteger(x[5], 5))
                                .status(validateStatus(x, status))
                                .build();
                    } catch (Exception e) {
                        log.error("Error occurred while validating the csv file, so skipping this row: {}, errorMessage: {}", row, e.getMessage());
                        return null;
                    }

                }).filter(Objects::nonNull).collect(Collectors.toSet());

                log.info("{} row/rows has been skipped due to duplicate or invalid data in csv file",
                        rowCount.get() - listInstanceMetadataBean.size());
                if (listInstanceMetadataBean.isEmpty()) {
                    log.error("The csv file contains no valid data" );
                    throw new ClientException("The csv file contains no valid data");
                }
                return listInstanceMetadataBean;
            }
        } catch (Exception e) {
            log.error("CSV File validation failed. Details: ", e );
            throw new ClientException("CSV File validation failed "+ e.getMessage());
        }


    }

    @Override
    public UtilityBean<List<InstanceMetadata>> serverValidation(UtilityBean<Set<InstanceMetadataRequestBean>> utilityBean) throws ServerException {
        String accountIdentifier  = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        User user = new UserDataService().getUserDetailsByUserIdentifier(userId, null);
        if (user == null) {
            log.error("User details for {} not found.", userId);
            throw new ServerException("User details for "+ userId +" not found.");
        }

        if (user.getStatus() != 1) {
            log.error("User {} is inactive.", userId);
            throw new ServerException("User "+ userId +  " is inactive.");
        }
        Set<InstanceMetadataRequestBean> instanceMetadataRequestBeans = utilityBean.getPojoObject();

        List<ViewTypes> viewTypes = MasterDataService.getAllTypes().parallelStream()
                .filter(v -> v.getTypeName().equals(Constants.ENVIRONMENT_TYPE_NAME))
                .collect(Collectors.toList());
        Map<String, ViewTypes> subTypeToViewType = viewTypes
                .parallelStream()
                .collect(toMap(ViewTypes::getSubTypeName, Function.identity(), (e, r) -> r));

        if (subTypeToViewType.isEmpty()) {
            log.error("Error occurred while getting types from DB. Reason: {} type name is not available in mst_sub_type",
                    Constants.ENVIRONMENT_TYPE_NAME);
            throw new ServerException(Constants.ENVIRONMENT_TYPE_NAME + " type name is not available in mst_sub_type");
        }

        List<InstanceMetadata> instanceMetadataList = validateInstanceMetadataInDB(instanceMetadataRequestBeans,
                subTypeToViewType, account.getId(), accountIdentifier, userId);

        if (instanceMetadataList.isEmpty()) {
            log.error("Error occurred while validating the instance metadata. Reason: Could invalid InstanceIdentifier request: {}",
                    instanceMetadataRequestBeans);
            throw new ServerException("Metadata validation failed");
        }

        return UtilityBean.<List<InstanceMetadata>>builder()
                .authToken(utilityBean.getAuthToken())
                .accountIdentifier(utilityBean.getAccountIdentifier())
                .pojoObject(instanceMetadataList)
                .build();
    }

    public List<InstanceMetadata> validateInstanceMetadataInDB(Set<InstanceMetadataRequestBean> instanceMetadataRequestBeans,
                                                               Map<String, ViewTypes> subTypeToViewType, int accountId,
                                                               String accountIdentifier, String userId) {

        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
        InstanceRepo instanceRepo = new InstanceRepo();

        List<InstanceMetadata> instMetadataByAccountId = instanceRepo
                .getInstanceMetadataByAccountIdentifier(accountIdentifier);

        Map<String, InstanceMetadata> instanceIdMap = instMetadataByAccountId.parallelStream()
                .collect(Collectors.toMap(InstanceMetadata::getInstanceIdentifier, Function.identity()));

        return instanceMetadataRequestBeans.parallelStream().map(instanceMetadataRequestBean -> {

            if (Objects.equals(instanceMetadataRequestBean.getInstanceIdentifier(), instanceMetadataRequestBean.getRelatedInstanceIdentifier())) {
                log.error("instanceIdentifier and relatedInstanceIdentifier is same , so skipping this row {}", instanceMetadataRequestBean);
                return null;
            }

            IdPojo instanceData = compInstanceDataService.
                    getCompInstanceNameAndIdentifierByAccountId(instanceMetadataRequestBean.getInstanceIdentifier(), accountId, null);

            if (instanceData == null) {
                log.error("instanceIdentifier: {} is not present in DB, so skipping this row {}",
                        instanceMetadataRequestBean.getInstanceIdentifier(), instanceMetadataRequestBean);
                return null;
            }

            if (instanceMetadataRequestBean.getRelatedInstanceIdentifier() != null) {
                IdPojo relatedInstanceData = compInstanceDataService.
                        getCompInstanceNameAndIdentifierByAccountId(instanceMetadataRequestBean.getRelatedInstanceIdentifier(), accountId, null);

                if (relatedInstanceData != null) {
                    instanceMetadataRequestBean.setRelatedInstanceId(relatedInstanceData.getId());
                }
            }

            InstanceMetadata instanceMetadataFromRedis = instanceIdMap.getOrDefault(instanceMetadataRequestBean.getInstanceIdentifier(), null);

            if (instanceMetadataFromRedis == null) {
                log.error("InstanceMetadata with instanceIdentifier: [{}] is not available in Redis, so skipping this row [{}]",
                        instanceMetadataRequestBean.getInstanceIdentifier(), instanceMetadataRequestBean);
                return null;
            }

            log.trace("Instance metadata for instanceIdentifier:{}, from redis: {}", instanceMetadataFromRedis.getInstanceIdentifier(), instanceMetadataFromRedis);

            if(instanceMetadataRequestBean.getStatus() == null) {
                instanceMetadataRequestBean.setStatus(instanceMetadataFromRedis.getStatus());
            }

            if (instanceMetadataRequestBean.getMemorySizeMB() == null) {
                instanceMetadataRequestBean.setMemorySizeMB(instanceMetadataFromRedis.getMemorySizeMB());
            }

            if (instanceMetadataRequestBean.getCpuCores() == null) {
                instanceMetadataRequestBean.setCpuCores(instanceMetadataFromRedis.getCpuCores());
            }

            if (instanceMetadataRequestBean.getDiskSizeMB() == null) {
                instanceMetadataRequestBean.setDiskSizeMB(instanceMetadataFromRedis.getDiskSizeMB());
            }

            if (instanceMetadataRequestBean.getCreatedTime() == null || instanceMetadataRequestBean.getCreatedTime().isEmpty()) {
                instanceMetadataRequestBean.setCreatedTime(instanceMetadataFromRedis.getCreatedTime());
            }

            if (instanceMetadataRequestBean.getRelatedInstanceId() == null) {
                instanceMetadataRequestBean.setRelatedInstanceId(instanceMetadataFromRedis.getRelatedInstanceId());
            }
            instanceMetadataRequestBean.setInstanceId(instanceData.getId());
            instanceMetadataRequestBean.setInstanceName(instanceData.getName());
            instanceMetadataRequestBean.setUserDetailsId(userId);
            instanceMetadataRequestBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
            instanceMetadataRequestBean.setEnvironmentId(instanceMetadataFromRedis.getEnvironmentId());

            if (instanceMetadataRequestBean.getEnvironmentName() == null) {
                instanceMetadataRequestBean.setEnvironmentName(instanceMetadataFromRedis.getEnvironmentName());
            } else {
                ViewTypes viewType = subTypeToViewType.getOrDefault(instanceMetadataRequestBean.getEnvironmentName(), null);
                if (viewType == null) {
                    log.error("environmentName: {} is not matching with mst_sub_type_name, so will not be updating  and environmentName for this row {}",
                            instanceMetadataRequestBean.getInstanceName(), instanceMetadataRequestBean);
                    return instanceMetadataRequestBean.mapToInstanceMetadata();
                }
                instanceMetadataRequestBean.setEnvironmentId(viewType.getSubTypeId());
                instanceMetadataRequestBean.setEnvironmentName(viewType.getSubTypeName());
            }

            return instanceMetadataRequestBean.mapToInstanceMetadata();

        }).filter(Objects::nonNull).collect(Collectors.toList());

    }
    @Override
    public String process(UtilityBean<List<InstanceMetadata>> utilityBean) throws DataProcessingException {

        try {
            InstanceRepo instanceRepo = new InstanceRepo();
            List<InstanceMetadata> instMetadataByAccountId = instanceRepo
                    .getInstanceMetadataByAccountIdentifier(utilityBean.getAccountIdentifier());

            List<InstanceMetadata> instanceMetadataList = utilityBean.getPojoObject();
            CompInstanceDataService compInstanceDataService = new CompInstanceDataService();

            int[] status = compInstanceDataService.updateInstanceMetadata(instanceMetadataList, null);

            if (Arrays.stream(status).anyMatch(i-> i == 0)) {
                log.warn("some instanceMetadata failed to update into Percona, so will not be updating into redis");
            } else {
                log.debug("Instance metadata updated in percona successfully.");
                log.debug("updating InstanceMetadata into redis");

                boolean redisUpdateStatus = updateInstanceMetadataIntoRedis(instanceMetadataList,
                        utilityBean.getAccountIdentifier(), instMetadataByAccountId);

                if (!redisUpdateStatus) {
                    log.warn("some instance metadata not updated in redis");
                }

                log.debug("updated InstanceMetadata into redis successfully");
            }

        } catch (Exception e) {
            log.error("Error occurred while uploading instanceMetadata csv to DB", e);
            throw new DataProcessingException(e.getMessage());
        }
        return "";
    }

    public Integer convertToInteger(String input, int index) throws ClientException {
        if (input == null || input.trim().isEmpty() || input.trim().equalsIgnoreCase("null")) {
            return null;
        }
        try {
            return Integer.valueOf(input);
        } catch (NumberFormatException e) {
           String []column = INSTANCE_METADATA_CSV_FILE_HEADERS.split(",");
            log.error("Value should be in proper Integer format for column: {}, provided value: {}", column[index], input);
            throw new ClientException("Value should be in proper Integer format for column: "+ column[index] + ", provided value: " + input);
        }
    }
    public String validateString(String input) {
        if (input == null || input.trim().isEmpty() || input.trim().equalsIgnoreCase("null")) {
            return null;
        }
        return input;
    }

    public String validateIdentifier(String input, int index) throws ClientException{
        if (input == null || input.trim().isEmpty() || input.trim().equalsIgnoreCase("null")) {
            String []column = INSTANCE_METADATA_CSV_FILE_HEADERS.split(",");
            log.error("Value should not be null or empty for column: {}, provided value: {}", column[index], input);
            throw new ClientException("Value should not be null or empty format for column: "+ column[index] + ", provided value: " + input);
        }
        return input;
    }

    public int validateStatus(String [] input, int status){
        if (status == 1) {
            return status;
        }
        if (input[6] == null || input[6].trim().isEmpty() || input[6].trim().equalsIgnoreCase("null")) {
            log.debug("Value of status not provided, So taking default value as 1 column: {}", input[6]);
            return 1;
        }

        try {
            int value = Integer.parseInt(input[6]);
            if (value == 0 || value == 1) {
                return value;
            }

        } catch (NumberFormatException e) {
            String []column = INSTANCE_METADATA_CSV_FILE_HEADERS.split(",");
            log.error("Value should be in proper Integer format for column: {}, provided value: {}", column[6], input[6]);
        }

        String []column = INSTANCE_METADATA_CSV_FILE_HEADERS.split(",");
        log.debug("Value of status is not 0 or 1 for column: {}, provided value: {}, so taking default value 1", column[6], input[6]);
        return 1;
    }

    public void setCSVFile(Request request) {
        DiskFileItemFactory factory = new DiskFileItemFactory();
        ServletFileUpload fileUpload = new ServletFileUpload(factory);
        List<FileItem> items;
        try {
            items = fileUpload.parseRequest(request.raw());
            item = items.stream()
                    .filter(e -> "file".equals(e.getFieldName()))
                    .findFirst().orElse(null);
        } catch (FileUploadException e) {
            log.error("Error occurred while getting the csv file");
        }
    }

    public boolean updateInstanceMetadataIntoRedis(List<InstanceMetadata> instanceMetadata, String accountIdentifier, List<InstanceMetadata> instMetadataByAccountId) {

        try {

            InstanceRepo instanceRepo =  new InstanceRepo();

            for (InstanceMetadata metadata : instanceMetadata) {
                boolean redisStatus = instanceRepo.updateInstanceMetadataInRedisByInstanceId(accountIdentifier, metadata);
                if (!redisStatus) {
                    log.warn("Update into redis failed for accountIdentifier: {}, instanceIdentifier: {}",
                            accountIdentifier, metadata.getInstanceIdentifier());
                    return false;
                }
            }

            List<InstanceMetadata> allInstanceMetadata = instMetadataByAccountId.parallelStream()
                    .filter(instance -> instanceMetadata.parallelStream()
                            .noneMatch(inst -> inst.getInstanceId() == instance.getInstanceId()))
                    .collect(Collectors.toList());

            allInstanceMetadata.addAll(instanceMetadata);

             return instanceRepo.updateInstanceMetadataInRedisByAccountIdentifier(accountIdentifier, allInstanceMetadata);
        } catch (Exception e) {
            log.error("Error occurred while updating instance metadata for accountIdentifier: {}", accountIdentifier, e);
            return false;
        }

    }

    public Set<InstanceMetadataRequestBean> getInstanceMetadataFromRequest(RequestObject request, String accountIdentifier)
            throws ClientException {
        if (item != null) {
            log.info("CSV file found, validating the file {}....", item.getName());
            return validateCSVFile(item);
        }
        log.info("File field not exist in request for given  accountIdentifier: {}, so getting data from request body", accountIdentifier);
        String requestBody = request.getBody();
        List<InstanceMetadataRequestBean> instanceMetadataRequestBeans;

        try {
            instanceMetadataRequestBeans = objectMapper.readValue(requestBody, new TypeReference<List<InstanceMetadataRequestBean>>() {
            });

        } catch (IOException e) {
            log.error("Exception while parsing input request body. Details: {}", e.getMessage());
            throw new ClientException("Error while parsing input");
        }

        Set<InstanceMetadataRequestBean> validData = instanceMetadataRequestBeans.parallelStream()
                .map(instanceMetadataRequestBean -> {
                    if (instanceMetadataRequestBean.getInstanceIdentifier() == null
                            || instanceMetadataRequestBean.getInstanceIdentifier().trim().isEmpty()) {
                        log.error("InstanceIdentifier is not provided in request body, so skipping this request: {}",
                                instanceMetadataRequestBean);
                        return null;

                    }
                    if (((instanceMetadataRequestBean.getMemorySizeMB() == null) && (instanceMetadataRequestBean.getCpuCores() == null)
                            && (instanceMetadataRequestBean.getDiskSizeMB() == null))) {
                        log.error("diskSizeMB, memorySizeMB, cpuCores one of these filed required in Request body, so skipping this request: {}",
                                instanceMetadataRequestBean);
                        return null;

                    }
                    return instanceMetadataRequestBean;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (validData.isEmpty()) {
            log.error("The request contains no valid data. request body: {}", requestBody);
            throw new ClientException("The request contains no valid data.");
        }

        return validData;

    }


}
