package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.ClientValidations;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.TransactionDataService;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.dao.redis.TransactionRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.RequestedTransactionTypeEntity;
import com.appnomic.appsone.controlcenter.pojo.TransactionDiscoveryStatus;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.entities.BasicTransactionBean;
import com.heal.configuration.pojos.BasicTransactionEntity;
import com.heal.configuration.pojos.Rule;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class GetTransactionDetailsBL implements BusinessLogic<String, UtilityBean<String>, List<RequestedTransactionTypeEntity>> {

    @Override
    public UtilityBean<String> clientValidation(RequestObject request) throws ClientException {
        ClientValidations.requestNullCheck(request);

        String accountIdentifier = ClientValidations.accountNullCheck(request);
        String authToken = ClientValidations.authTokenNullCheck(request);
        String serviceId = ClientValidations.serviceNullCheck(request);

        String[] txnRequestType = request.getQueryParams().get(Constants.TXN_REQUEST_TYPE);
        ClientValidations.requestParameterNullCheck(txnRequestType);

        return UtilityBean.<String>builder()
                .accountIdentifier(accountIdentifier)
                .serviceId(serviceId)
                .authToken(authToken)
                .pojoObject(txnRequestType[0])
                .build();
    }

    @Override
    public UtilityBean<String> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        UserAccountBean userAccountBean;
        try {
            userAccountBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
            utilityBean.setUserId(userAccountBean.getUserId());
        } catch (RequestException e) {
            log.error("Error while validating user and account details", e);
            throw new ServerException("Error while validating user and account details");
        }

        ControllerBean serviceDetails = new ControllerDataService().getControllerById(Integer.parseInt(utilityBean.getServiceId()), userAccountBean.getAccount().getId(), null);
        if (serviceDetails == null) {
            log.error("Service details unavailable for serviceID [{}]", utilityBean.getServiceId());
            throw new ServerException("Service details unavailable for serviceID [{}]" + utilityBean.getServiceId());
        }

        utilityBean.setServiceIdentifier(serviceDetails.getIdentifier());
        utilityBean.setAccount(userAccountBean.getAccount());

        return utilityBean;
    }

    @Override
    public List<RequestedTransactionTypeEntity> process(UtilityBean<String> utilityBean) throws DataProcessingException {
        // Get service wise rule details
        try {
            List<Rule> serviceWiseRules = new ServiceRepo().getServiceRules(utilityBean.getAccountIdentifier(), utilityBean.getServiceIdentifier());
            if (serviceWiseRules.isEmpty()) {
                log.error("Could not find serviceWiseRules for serviceIdentifier [{}]", utilityBean.getServiceIdentifier());
                throw new DataProcessingException("Could not find serviceWiseRules");
            }
            Map<Integer, String> ruleIdVsRuleNameMap = serviceWiseRules.parallelStream().collect(Collectors.toMap(Rule::getId, Rule::getName));

            // redis call to get required to be output
            List<BasicTransactionEntity> transactions = new TransactionRepo()
                    .getServiceWiseTransaction(utilityBean.getAccountIdentifier(),
                            utilityBean.getServiceIdentifier());
            int status = TransactionDiscoveryStatus.getValueForStatus(utilityBean.getPojoObject());
            List<BasicTransactionEntity> requestedTransactions = transactions.parallelStream()
                    .filter(transaction -> transaction.getStatus() == status)
                    .collect(Collectors.toList());

            if (utilityBean.getPojoObject().equalsIgnoreCase(TransactionDiscoveryStatus.DISCARDED.name())) {
                List<BasicTransactionBean> discardedTransactionsService = new TransactionDataService().getDiscardedTransactionsService(utilityBean.getAccount().getId(), Integer.parseInt(utilityBean.getServiceId()));
                requestedTransactions.addAll(discardedTransactionsService.stream().map(BasicTransactionBean::mapToTransactionEntity).collect(Collectors.toList()));
            }

            Map<String, Long> txnIdentifierVsRequestCountMap = new com.appnomic.appsone.controlcenter.dao.opensearch.TransactionRepo().getRequestCount(utilityBean);

            return requestedTransactions.parallelStream()
                    .map(transaction -> RequestedTransactionTypeEntity.builder()
                            .id(transaction.getId())
                            .status(transaction.getStatus())
                            .name(transaction.getName())
                            .identifier(transaction.getIdentifier())
                            .ruleId(transaction.getRuleId())
                            .createdTime(DateTimeUtil.getEpochTime(transaction.getCreatedTime()))
                            .lastModifiedOn(DateTimeUtil.getEpochTime(transaction.getUpdatedTime()))
                            .lastModifiedBy(transaction.getLastModifiedBy())
                            .rule(ruleIdVsRuleNameMap.get(transaction.getRuleId()))
                            .requestCount(txnIdentifierVsRequestCountMap.getOrDefault(transaction.getIdentifier(),0L))
                            .build())
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error while getting {} transaction for accountID :{}, serviceId: {}", utilityBean.getPojoObject(), utilityBean.getAccount().getId(),
                    utilityBean.getServiceId(), e);
            throw new DataProcessingException("Error while getting transaction");
        }

    }

}
