package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.common.ClientValidations;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.TransactionDataService;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.dao.redis.TransactionRepo;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.BasicTransactionEntity;
import com.heal.configuration.pojos.Service;
import com.heal.configuration.pojos.Transaction;
import com.heal.configuration.pojos.TransactionAutoAcceptance;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class AddRequestDiscoveryStatusBL implements BusinessLogic<TransactionAcceptancePojo, TransactionStatusBean, String> {
    private final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();
    private final TransactionDataService transactionDataService;
    private final TransactionRepo transactionRepo;
    private static final long txnExpTime = ConfProperties.getInt(Constants.SET_TXN_EXPIRY_TIME_SECONDS,
            Constants.SET_TXN_EXPIRY_TIME_SECONDS_DEFAULT);

    public AddRequestDiscoveryStatusBL() {
        super();
        transactionDataService = new TransactionDataService();
        transactionRepo = new TransactionRepo();
    }

    @Override
    public UtilityBean<TransactionAcceptancePojo> clientValidation(RequestObject request) throws ClientException {
        ClientValidations.requestNullCheck(request);

        String accountIdentifier = ClientValidations.accountNullCheck(request);
        String authToken = ClientValidations.authTokenNullCheck(request);
        String serviceId = ClientValidations.serviceNullCheck(request);

        String source = null;
        String[] sourceType = request.getQueryParams().get(Constants.SOURCE_REQUEST_TYPE);
        if (sourceType != null && sourceType.length > 0) {
            source = sourceType[0];
        }

        ClientValidations.requestBodyNullCheck(request.getBody());
        String acceptanceSettingsStr = request.getBody();
        List<TransactionStatusPojo> requestDiscoveryStatusPojos;
        try {
            requestDiscoveryStatusPojos = objectMapper.readValue(acceptanceSettingsStr, new TypeReference<List<TransactionStatusPojo>>() {
            });
        } catch (JsonProcessingException e) {
            log.error("Exception while parsing input request body. Details: {}", e.getMessage());
            CCCache.INSTANCE.updateCCErrors(1);
            throw new ClientException("Error while parsing input");
        }

        TransactionAcceptancePojo pojo = TransactionAcceptancePojo.builder()
                .source(source)
                .transactionStatusPojoList(requestDiscoveryStatusPojos)
                .build();

        return UtilityBean.<TransactionAcceptancePojo>builder()
                .accountIdentifier(accountIdentifier)
                .serviceId(serviceId)
                .authToken(authToken)
                .pojoObject(pojo)
                .build();
    }

    @Override
    public TransactionStatusBean serverValidation(UtilityBean<TransactionAcceptancePojo> utilityBean) throws ServerException {
        UserAccountBean userAccountBean;
        try {
            userAccountBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(),
                    utilityBean.getAccountIdentifier());
            utilityBean.setUserId(userAccountBean.getUserId());
        } catch (RequestException e) {
            log.error("Error while validating user and account details", e);
            CCCache.INSTANCE.updateCCErrors(1);
            throw new ServerException("Error while validating user and account details");
        }

        List<TransactionStatusEntity> transactionStatusEntityList = new ArrayList<>();

        List<TransactionStatusPojo> txnList = utilityBean.getPojoObject().getTransactionStatusPojoList();
        // Validate transaction status
        for (TransactionStatusPojo transactionStatusPojo : txnList) {

            int status = TransactionDiscoveryStatus.getValueForStatus(transactionStatusPojo.getAction());
            if (status == -1) {
                log.error("Transaction status : {} is invalid", transactionStatusPojo.getAction());
                CCCache.INSTANCE.updateCCErrors(1);
                throw new ServerException("Transaction status invalid");
            }

            int id = transactionStatusPojo.getId();

            TransactionStatusPojo txnIdFromTxnIdentifier;

            txnIdFromTxnIdentifier = transactionDataService.getTxnIdFromTxnIdentifier(transactionStatusPojo.getTxnIdentifier(), id);
            if (txnIdFromTxnIdentifier == null) {
                log.error("No transaction found for id: [{}] or identifier: [{}]", id, transactionStatusPojo.getTxnIdentifier());
                CCCache.INSTANCE.updateCCErrors(1);
                throw new ServerException("Exception occur while fetching transaction details");
            }

            transactionStatusEntityList.add(TransactionStatusEntity.builder()
                    .id(txnIdFromTxnIdentifier.getId())
                    .identifier(txnIdFromTxnIdentifier.getTxnIdentifier())
                    .status(status)
                    .build());
        }

        ControllerBean serviceDetails = new ControllerDataService().getControllerById(Integer.parseInt(utilityBean.getServiceId()), userAccountBean.getAccount().getId(), null);
        if (serviceDetails == null) {
            log.error("Service details unavailable for serviceID [{}]", utilityBean.getServiceId());
            CCCache.INSTANCE.updateCCErrors(1);
            throw new ServerException("Service details unavailable for serviceID [{}]" + utilityBean.getServiceId());
        }

        List<Integer> allTransactionId = transactionStatusEntityList.stream().map(TransactionStatusEntity::getId).collect(Collectors.toList());
        int matchingTxnIdCount = 0;
        if (!allTransactionId.isEmpty()) {
            matchingTxnIdCount = new BindInDataService().getMatchingTxnIdCount(allTransactionId);
        }
        if (matchingTxnIdCount != transactionStatusEntityList.size()) {
            log.error("Some or all of the transaction ids are not present in transactions table: [{}]", allTransactionId);
            CCCache.INSTANCE.updateCCErrors(1);
            throw new ServerException("Some or all of the transaction ids are not present in transactions table: " + allTransactionId);
        }

        return TransactionStatusBean.builder()
                .accountId(userAccountBean.getAccount().getId())
                .accountIdentifier(userAccountBean.getAccount().getIdentifier())
                .serviceId(Integer.parseInt(utilityBean.getServiceId()))
                .serviceIdentifier(serviceDetails.getIdentifier())
                .sourceType(utilityBean.getPojoObject().getSource())
                .lastUpdatedBy(userAccountBean.getUserId())
                .statusEntityList(transactionStatusEntityList)
                .build();
    }

    @Override
    public String process(TransactionStatusBean bean) throws DataProcessingException {
        try {

            if (bean.getSourceType() != null && bean.getSourceType().trim().equalsIgnoreCase(Constants.TXN_REQUEST_SOURCE_NAME)) {
                try {
                    // Delete Txn from percona
                    List<TransactionStatusEntity> discardTxns = bean.getStatusEntityList().parallelStream()
                            .filter(txn -> TransactionDiscoveryStatus.DISCARDED.getStatus() == txn.getStatus()).collect(Collectors.toList());

                    List<TransactionStatusEntity> acceptTxns = bean.getStatusEntityList().parallelStream()
                            .filter(txn -> TransactionDiscoveryStatus.ACCEPTED.getStatus() == txn.getStatus()).collect(Collectors.toList());

                    TransactionStatusBean acceptBean = TransactionStatusBean.builder()
                            .serviceId(bean.getServiceId())
                            .accountId(bean.getAccountId())
                            .sourceType(bean.getSourceType())
                            .accountIdentifier(bean.getAccountIdentifier())
                            .serviceIdentifier(bean.getServiceIdentifier())
                            .statusEntityList(acceptTxns)
                            .build();

                    String currentTime = String.valueOf(DateTimeUtil.getCurrentTimestampInGMT());
                    Service service = setCommitDuration(bean, discardTxns, acceptTxns, currentTime);
                    if (service == null) {
                        log.error("unable to get service by service identifier: {} and account identifier: {}", bean.getServiceIdentifier(), bean.getAccountIdentifier());
                        CCCache.INSTANCE.updateCCErrors(1);
                        throw new DataProcessingException("Exception occurred while getting service details");
                    }

                    BindInDataService bindInDataService = new BindInDataService();
                    MySQLConnectionManager.getInstance().getHandle().inTransaction((conn, status) -> {
                        transactionDataService.updateCommitDetails(currentTime, bean.getAccountId(), bean.getServiceId()
                                ,acceptTxns.size(), discardTxns.size(), bean.getLastUpdatedBy(), conn);

                        if (!discardTxns.isEmpty()) {
                            List<Integer> discardRequestIds = discardTxns.parallelStream()
                                    .map(TransactionStatusEntity::getId)
                                    .collect(Collectors.toList());

                            bindInDataService.deleteTransactionTagMappings(discardRequestIds, Constants.TXN_TABLE, bean.getAccountId(), conn);

                            List<Integer> txnGroupIds = bindInDataService.getTxnGroupIdsWithTxnId(discardRequestIds, conn);
                            if (!txnGroupIds.isEmpty()) {
                                int count = bindInDataService.getTxnGroupIdsMappedToMultipleTxns(discardRequestIds, txnGroupIds, conn);

                                if (count == 0) {
                                    bindInDataService.deleteTxnGroups(txnGroupIds, conn);
                                }
                            }

                            transactionDataService.deleteTransactionsAndDependencies(discardTxns, conn);
                        }

                        if (!acceptTxns.isEmpty()) {
                            transactionDataService.updateTransactionStatus(acceptBean.getStatusEntityList(), bean.getLastUpdatedBy());
                        }

                        return "Transaction updated successfully";
                    });

                    log.debug("Updating commit duration into redis for service identifier : [{}], account identifier: [{}]", bean.getServiceIdentifier(), bean.getAccountIdentifier());
                    new ServiceRepo().updateServiceConfigurationByServiceIdentifier(bean.getAccountIdentifier(), bean.getServiceIdentifier(), service);

                    if (!acceptTxns.isEmpty()) {
                        log.debug("updating transaction status in redis for {} count.", acceptTxns.size());
                        updateStatusInRedis(acceptBean);
                    }

                    if (!discardTxns.isEmpty()) {
                        log.debug("Deleting discarded transaction from Redis and OS for accountIdentifier: {}, serviceIdentifier: {}", bean.getServiceIdentifier(), bean.getServiceIdentifier());
                        deleteTxnFromRedis(bean);
                        new com.appnomic.appsone.controlcenter.dao.opensearch.TransactionRepo()
                                .deleteFromOS(bean.getAccountIdentifier(), bean.getServiceIdentifier(), discardTxns);
                    }

                } catch (Exception e) {
                    if (Throwables.getRootCause(e) instanceof ControlCenterException) {
                        throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
                    } else {
                        throw e;
                    }

                }

            } else {
                transactionDataService.updateTransactionStatus(bean.getStatusEntityList(), bean.getLastUpdatedBy());
                updateStatusInRedis(bean);
            }
        } catch (Exception e) {
            throw new DataProcessingException("Exception while updating transaction status in percona.");
        }
        return "Data Successfully inserted in percona and redis";
    }

    private void updateStatusInRedis(TransactionStatusBean bean) {
        // Update to redis
        Map<Integer, Integer> transactionIdStatusMap = bean.getStatusEntityList().parallelStream().collect(Collectors.toMap(TransactionStatusEntity::getId, TransactionStatusEntity::getStatus));

        // Update transaction status service wise transaction
        List<BasicTransactionEntity> basicTransactionEntityList = transactionRepo.getServiceWiseTransaction(bean.getAccountIdentifier(), bean.getServiceIdentifier());
        basicTransactionEntityList.forEach(transaction -> {
            if (transactionIdStatusMap.containsKey(transaction.getId())) {
                transaction.setStatus(transactionIdStatusMap.get(transaction.getId()));
            }
        });
        transactionRepo.updateServiceWiseTransactionDetails(bean.getAccountIdentifier(), bean.getServiceIdentifier(), basicTransactionEntityList);

        // Update transaction status account wise
        for (TransactionStatusEntity transactionStatusEntity : bean.getStatusEntityList()) {
            Transaction transaction = transactionRepo.getTransactionById(bean.getAccountIdentifier(), transactionStatusEntity.getId());
            transaction.setStatus(transactionStatusEntity.getStatus());
            transactionRepo.updateTransactionDetailsById(bean.getAccountIdentifier(), transaction);
            transactionRepo.updateTransactionDetailsByIdentifier(bean.getAccountIdentifier(), transaction);
        }
    }

    private void deleteTxnFromRedis(TransactionStatusBean bean) {
        // Delete Keys from Redis
        Map<String, Integer> transactionIdStatusMap = bean.getStatusEntityList().parallelStream()
                .filter(txn -> TransactionDiscoveryStatus.DISCARDED.getStatus() == txn.getStatus())
                .collect(Collectors.toMap(TransactionStatusEntity::getIdentifier, TransactionStatusEntity::getStatus));

        // Delete transaction service wise
        List<BasicTransactionEntity> basicTransactionEntityList = transactionRepo.getServiceWiseTransaction(bean.getAccountIdentifier(), bean.getServiceIdentifier());
        List<BasicTransactionEntity> updatedTransactionEntityList = new ArrayList<>();
        basicTransactionEntityList.forEach(transaction -> {
            if (!transactionIdStatusMap.containsKey(transaction.getIdentifier())) {
                updatedTransactionEntityList.add(transaction);
            }
        });

        transactionRepo.updateServiceWiseTransactionDetails(bean.getAccountIdentifier(), bean.getServiceIdentifier(), updatedTransactionEntityList);

        // Delete redis keys that contains TransactionIds in bean pojo object
        for (TransactionStatusEntity transactionStatusEntity : bean.getStatusEntityList()) {
            if (txnExpTime == 0) {
                transactionRepo.deleteTxnUsingHashCode(bean.getAccountIdentifier(), transactionStatusEntity.getIdentifier());
                transactionRepo.deleteTxnById(bean.getAccountIdentifier(), transactionStatusEntity.getId());
                transactionRepo.deleteTxnViolationConfig(bean.getAccountIdentifier(), transactionStatusEntity.getIdentifier());
            }
            else {
                Transaction transactionById = transactionRepo.getTransactionById(bean.getAccountIdentifier(), transactionStatusEntity.getId());
                transactionById.setStatus(3);
                transactionRepo.updateAndSetExpiryTxnUsingHashCode(bean.getAccountIdentifier(), transactionStatusEntity.getIdentifier()
                        , transactionById, txnExpTime);
                transactionRepo.updateAndSetExpiryTxnById(bean.getAccountIdentifier(), transactionStatusEntity.getId(),
                        transactionById, txnExpTime );

                transactionRepo.setExpiryTxnViolationConfig(bean.getAccountIdentifier(), transactionStatusEntity.getIdentifier(), txnExpTime);
            }

        }
    }
    public Service setCommitDuration(TransactionStatusBean bean, List<TransactionStatusEntity> discardTxns, List<TransactionStatusEntity> acceptTxns, String currentTime) {
        ServiceRepo serviceRepo = new ServiceRepo();

        try {
            Service service = serviceRepo.getServiceConfigurationByIdentifier(bean.getAccountIdentifier(), bean.getServiceIdentifier());
            if (service == null) {
                log.error("Error occurred while getting service for service identifier: {}", bean.getServiceIdentifier());
                CCCache.INSTANCE.updateCCErrors(1);
                return null;
            }

            TransactionAutoAcceptance txnAutoAcceptance = service.getTransactionAutoAcceptance();
            if (txnAutoAcceptance == null) {
                log.error("Error occurred while getting txnAutoAcceptance for service identifier: {}", bean.getServiceIdentifier());
                CCCache.INSTANCE.updateCCErrors(1);
                return null;
            }

            int lastAcceptedTxnCount = acceptTxns.size();
            int lastDiscardedTxnCount = discardTxns.size();
            txnAutoAcceptance.setLastCommitTime(currentTime);
            txnAutoAcceptance.setLastAcceptedTxnCount(lastAcceptedTxnCount);
            txnAutoAcceptance.setLastDiscardedTxnCount(lastDiscardedTxnCount);
            service.setTransactionAutoAcceptance(txnAutoAcceptance);

            return service;

        } catch (Exception e) {
            log.error("Error occurred while updating commitDetails for service id: {}",bean.getServiceId(), e);
            CCCache.INSTANCE.updateCCErrors(1);
            return null;
        }

    }
}
