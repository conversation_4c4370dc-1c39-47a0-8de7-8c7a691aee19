package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.KPIDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.InstKpiAttrPersistenceSuppressionBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.KpiBean;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.ActionsEnum;
import com.appnomic.appsone.controlcenter.pojo.InstKpiAttrPersistSuppressDetails;
import com.appnomic.appsone.controlcenter.pojo.InstanceKpiPersistenceSuppressionDetails;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.KpiAttributeLevelThresholdUtil;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.CompInstKpiEntity;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class UpdateKpiPersistenceSuppressionBL implements BusinessLogic<InstanceKpiPersistenceSuppressionDetails, List<InstKpiAttrPersistenceSuppressionBean>, String> {

    private static final KpiAttributeLevelThresholdUtil KPI_UTIL = new KpiAttributeLevelThresholdUtil();
    private static final KPIDataService KPI_DATA_SERVICE = new KPIDataService();
    InstanceRepo instanceRepo = new InstanceRepo();

    @Override
    public UtilityBean<InstanceKpiPersistenceSuppressionDetails> clientValidation(RequestObject requestObject) throws ClientException {
       CommonUtils.basicRequestValidation(requestObject);

        InstanceKpiPersistenceSuppressionDetails kpiAttributeThresholds = KPI_UTIL.getInstanceKpiPersistenceSuppressionDetails(requestObject, false);

        return UtilityBean.<InstanceKpiPersistenceSuppressionDetails>builder()
                .accountIdentifier(requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER))
                .authToken(requestObject.getHeaders().get(Constants.AUTHORIZATION))
                .pojoObject(kpiAttributeThresholds)
                .build();
    }

    @Override
    public List<InstKpiAttrPersistenceSuppressionBean> serverValidation(UtilityBean<InstanceKpiPersistenceSuppressionDetails> utilityBean) throws ServerException {
        List<InstKpiAttrPersistenceSuppressionBean> attrConfigBeans = new ArrayList<>();

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        String accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);
        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        int accountId = account.getId();
        InstanceKpiPersistenceSuppressionDetails details = utilityBean.getPojoObject();

        List<Integer> instanceIds = details.getInstances();
        List<InstKpiAttrPersistSuppressDetails> configDetails = details.getPersistenceSuppressionConfig();

        int kpiId = configDetails.get(0).getKpiId();
        int groupKpiId = configDetails.get(0).getGroupKpiId();

        KpiBean kpiBean = new KPIDataService().fetchKpiUsingKpiId(kpiId, accountId, null);
        if (kpiBean == null) {
            log.error("Kpi with ID [{}] is unavailable", kpiId);
            throw new ServerException(String.format("Kpi with ID [%d] is unavailable", kpiId));
        }

        if (groupKpiId > 0 && kpiBean.getGroupKpiId() != groupKpiId) {
            log.error("Group Kpi with ID [{}] is not mapped to Kpi with ID [{}]", groupKpiId, kpiId);
            throw new ServerException(String.format("Group Kpi with ID [%d] is not mapped to Kpi with ID [%d]", groupKpiId, kpiId));
        }

        for (int instanceId : instanceIds) {
            String compInstanceIdentifier;
            try {
                compInstanceIdentifier = new CompInstanceDataService().getCompInstanceIdentifierFromId(instanceId, accountId, null);
            } catch (ControlCenterException e) {
                throw new ServerException(e.getMessage());
            }

            if (compInstanceIdentifier == null) {
                log.error("Component instance identifier unavailable for instanceId [{}]", instanceId);
                throw new ServerException("Invalid instanceId provided");
            }

            List<InstKpiAttrPersistenceSuppressionBean> instanceToKpiAttrMapping;
            List<InstKpiAttrPersistenceSuppressionBean> kpiMappingExists;

            try {
                instanceToKpiAttrMapping = KPI_DATA_SERVICE.fetchGroupKpiCompInstanceMapping(instanceId, null);
                kpiMappingExists = KPI_DATA_SERVICE.fetchKpiCompInstanceMapping(instanceId, null);
            } catch (ControlCenterException e) {
                throw new ServerException(e.getMessage());
            }

            List<InstKpiAttrPersistenceSuppressionBean> existingConfigBeans;
            try {
                existingConfigBeans = KPI_DATA_SERVICE.fetchCompInstanceKpiPerSupValuesForCompInstance(instanceId, accountId);
            } catch (ControlCenterException e) {
                throw new ServerException(e.getMessage());
            }

            for (InstKpiAttrPersistSuppressDetails instKpiConfig : configDetails) {
                InstKpiAttrPersistenceSuppressionBean bean = InstKpiAttrPersistenceSuppressionBean.builder()
                        .persistence(instKpiConfig.getPersistence())
                        .suppression(instKpiConfig.getSuppression())
                        .attributeValue(instKpiConfig.getAttributeValue())
                        .kpiGroupId(instKpiConfig.getGroupKpiId())
                        .kpiId(instKpiConfig.getKpiId())
                        .compInstanceId(instanceId)
                        .accountId(accountId)
                        .userDetailsId(userId)
                        .actionForUpdate(instKpiConfig.getAction())
                        .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                        .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                        .accountIdentifier(accountIdentifier)
                        .build();

                if (bean.getKpiGroupId() > 0) {
                    log.debug("Attribute thresholds are part of group KPI [{}]", bean.getKpiGroupId());

                    int discoveryFlag;
                    try {
                        discoveryFlag = new KPIDataService().getGroupKpiDiscovery(bean.getKpiGroupId(), null);
                    } catch (ControlCenterException e) {
                        throw new ServerException(e.getMessage());
                    }

                    if (instanceToKpiAttrMapping.parallelStream().noneMatch(c -> c.getKpiId() == bean.getKpiId()
                            && c.getKpiGroupId() == bean.getKpiGroupId() && c.getCompInstanceId() == bean.getCompInstanceId())) {
                        log.error("Group KPI [{}] is not mapped to compInstanceId [{}], accountId [{}]", bean.getKpiGroupId(),
                                instanceId, accountId);
                        throw new ServerException(String.format("Group KPI [%s] is not mapped to compInstanceId [%s], accountId [%s]", bean.getKpiGroupId(),
                                instanceId, accountId));
                    }

                    if (discoveryFlag == 0) {
                        if (instanceToKpiAttrMapping.parallelStream().noneMatch(c -> c.getAttributeValue().equalsIgnoreCase(bean.getAttributeValue()))) {
                            log.error("Group KPI [{}] with attribute [{}] is not mapped to compInstanceId [{}], accountId [{}]", bean.getKpiGroupId(), bean.getAttributeValue(),
                                    instanceId, accountId);
                            throw new ServerException(String.format("Group KPI [%s] with attribute [%s] is not mapped to compInstanceId [%s], accountId [%s]", bean.getKpiGroupId(),
                                    bean.getAttributeValue(), instanceId, accountId));
                        }
                    }
                } else {
                    if (kpiMappingExists.parallelStream()
                            .noneMatch(c -> c.getCompInstanceId() == bean.getCompInstanceId() && c.getKpiId() == bean.getKpiId())) {
                        log.error("Non-group KPI [{}] provided are not mapped to compInstanceId [{}], accountId [{}]", bean.getKpiId(), instanceId, accountId);
                        throw new ServerException(String.format("Non-group KPI [%s] provided are not mapped to compInstanceId [%s], accountId [%s]",
                                bean.getKpiId(), instanceId, accountId));
                    }
                }

                if (instanceIds.size() == 1 && existingConfigBeans.isEmpty()) {
                    log.error("None of the Kpi configuration(s) thresholds exist");
                    throw new ServerException("None of the Kpi configuration(s) thresholds exist");
                }

                if (ActionsEnum.ADD.equals(bean.getActionForUpdate())) {
                    if (existingConfigBeans.contains(bean)) {
                        log.error("Persistence-Suppression configuration for KPI [{}] and attribute [{}] already exist for instanceId [{}]. Details: {}",
                                bean.getKpiId(), bean.getAttributeValue(), instanceId, bean);
                        throw new ServerException(String.format("Persistence-Suppression configuration for KPI [%d] and attribute [%s] already exist for instanceId [%d]",
                                bean.getKpiId(), bean.getAttributeValue(), instanceId));
                    }
                } else {
                    if (instanceIds.size() == 1 && !existingConfigBeans.contains(bean)) {
                        log.error("Persistence-Suppression configuration for KPI [{}] and attribute [{}] unavailable for instanceId [{}]. Details: {}",
                                bean.getKpiId(), bean.getAttributeValue(), instanceId, bean);
                        throw new ServerException(String.format("Persistence-Suppression configuration for KPI [%d] and attribute [%s] unavailable for instanceId [%d]",
                                bean.getKpiId(), bean.getAttributeValue(), instanceId));
                    }

                    if (instanceIds.size() > 1 && ActionsEnum.MODIFY.equals(bean.getActionForUpdate()) && !existingConfigBeans.contains(bean)) {
                        bean.setActionForUpdate(ActionsEnum.ADD);
                    }
                }

                attrConfigBeans.add(bean);
            }
        }

        Set<InstKpiAttrPersistenceSuppressionBean> tempSet = attrConfigBeans.parallelStream().collect(Collectors.toSet());
        if (tempSet.size() != attrConfigBeans.size()) {
            log.error("Duplicate entries in the input");
            throw new ServerException("Duplicate entries in the input");
        }

        return attrConfigBeans;
    }

    @Override
    public String process(List<InstKpiAttrPersistenceSuppressionBean> beans) throws DataProcessingException {
        KpiAttributeLevelThresholdUtil KPI_UTIL = new KpiAttributeLevelThresholdUtil();

        Map<Boolean, List<InstKpiAttrPersistenceSuppressionBean>> partitionBasedOnAddAction = beans.parallelStream()
                .collect(Collectors.partitioningBy(c -> ActionsEnum.ADD.equals(c.getActionForUpdate())));

        List<InstKpiAttrPersistenceSuppressionBean> newlyAddedConfigurations = partitionBasedOnAddAction.get(true);
        List<InstKpiAttrPersistenceSuppressionBean> configToBeUpdated = partitionBasedOnAddAction.get(false);

        Map<Boolean, List<InstKpiAttrPersistenceSuppressionBean>> partitionBasedOnDeleteAction = configToBeUpdated.parallelStream()
                .collect(Collectors.partitioningBy(c -> ActionsEnum.DELETE.equals(c.getActionForUpdate())));

        List<InstKpiAttrPersistenceSuppressionBean> configurationsToBeModified = partitionBasedOnDeleteAction.get(false);
        List<InstKpiAttrPersistenceSuppressionBean> configurationsToBeDeleted = partitionBasedOnDeleteAction.get(true);

        String retVal;
        try {
            retVal = MySQLConnectionManager.getInstance().getHandle().inTransaction((conn, status) -> {
                if (!newlyAddedConfigurations.isEmpty()) {
                    insertData(newlyAddedConfigurations, conn);
                }

                if (!configurationsToBeModified.isEmpty()) {
                    updateAttributeThresholds(configurationsToBeModified, conn);
                }

                if (!configurationsToBeDeleted.isEmpty()) {
                    deleteData(configurationsToBeDeleted, conn);
                }
                return "Persistence-Suppression configuration for attributes updated successfully";
            });
        } catch (Exception e) {
            log.error("Unable to update KPI configurations. Details: ", e);

            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
            } else {
                throw e;
            }
        }

        //Update in redis cache
        try {
            if (!newlyAddedConfigurations.isEmpty()) {
                KPI_UTIL.updatePersistenceSuppressionForKpiDetails(newlyAddedConfigurations);
            }
            if (!configurationsToBeModified.isEmpty()) {
                KPI_UTIL.updatePersistenceSuppressionForKpiDetails(configurationsToBeModified);
            }
            if (!configurationsToBeDeleted.isEmpty()) {
                KPI_UTIL.deletePersistenceSuppressionForKpiDetails(configurationsToBeDeleted);
            }
        } catch (Exception e) {
            log.error("Exception while adding attribute level persistence-suppression configuration in Redis : ", e);
        }

        return retVal;
    }

    private void updateAttributeThresholds(List<InstKpiAttrPersistenceSuppressionBean> existingEntries, Handle handle)
            throws DataProcessingException {
        int[] ids = KPI_DATA_SERVICE.updateInstanceKpiAttributePersistenceSuppression(existingEntries, handle);

        if (ids == null || ids.length == 0) {
            log.error("Error while updating attribute level persistence-suppression configuration");
            throw new DataProcessingException("Error while updating attribute level persistence-suppression configuration");
        }
    }

    private void insertData(List<InstKpiAttrPersistenceSuppressionBean> newEntries, Handle handle) throws DataProcessingException {
        int[] ids = KPI_DATA_SERVICE.addInstanceKpiAttributeLevelPersistSuppress(newEntries, handle);

        if (ids == null || ids.length == 0) {
            log.error("Error while adding attribute level persistence-suppression configuration");
            throw new DataProcessingException("Error while adding attribute level persistence-suppression configuration");
        }
    }

    private void deleteData(List<InstKpiAttrPersistenceSuppressionBean> newEntries, Handle handle) throws DataProcessingException {
        int[] ids = KPI_DATA_SERVICE.deleteInstanceKpiAttributeLevelPersistSuppress(newEntries, handle);

        if (ids == null || ids.length == 0) {
            log.error("Error while adding attribute level persistence-suppression configuration");
            throw new DataProcessingException("Error while adding attribute level persistence-suppression configuration");
        }
    }
}
