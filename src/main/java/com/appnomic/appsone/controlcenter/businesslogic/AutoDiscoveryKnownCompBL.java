package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.beans.discovery.Attribute;
import com.appnomic.appsone.common.beans.discovery.AttributeAccess;
import com.appnomic.appsone.controlcenter.beans.ADKnownCompAttrBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.Component;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.LdPreloadDetails;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.AutoDiscoveryDataService;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.AutoDiscoComponentMapping;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class AutoDiscoveryKnownCompBL implements BusinessLogic<String, Object, List<com.appnomic.appsone.controlcenter.beans.autodiscovery.Component>>{

    @Override
    public UtilityBean<String> clientValidation(RequestObject requestObject) throws ClientException {
        return null;
    }

    @Override
    public Object serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        return null;
    }

    @Override
    public List<com.appnomic.appsone.controlcenter.beans.autodiscovery.Component> process(Object bean) throws DataProcessingException {
        /*
         * Fetches all known components along with component related info such as
         * 'discovery_pattern' and list of 'attributes'
         */
        Map<Integer, Component> knownCompAttrList = new HashMap<>();
        AutoDiscoveryDataService autoDiscoveryDataService = new AutoDiscoveryDataService();
        List<ADKnownCompAttrBean> knownCompAttrBeans = autoDiscoveryDataService.getADComponentAttributeDetails();
        List<AutoDiscoComponentMapping> autoDiscoComponentMappings = autoDiscoveryDataService.getAutoDiscoComponentMapping();
        if (knownCompAttrBeans.isEmpty()) {
            String log = "Error while fetching known components and their attributes. List is empty.";
            AutoDiscoveryKnownCompBL.log.error(log);
            throw new DataProcessingException(log);
        }

        for (ADKnownCompAttrBean compAttrBean : knownCompAttrBeans) {
            Component component = knownCompAttrList.get(compAttrBean.getId());
            if (component == null) {
                com.appnomic.appsone.controlcenter.beans.autodiscovery.Component knownCompAttr = new Component();
                Attribute attributesPojo = new Attribute();
                List<Attribute> attributesPojoList = new ArrayList<>();
                AttributeAccess accessPojo = new AttributeAccess();
                List<AttributeAccess> accessPojoList = new ArrayList<>();

                knownCompAttr.setComponentId(compAttrBean.getId());
                knownCompAttr.setComponentName(compAttrBean.getName());
                knownCompAttr.setComponentTypeId(compAttrBean.getMstComponentTypeId());
                knownCompAttr.setRelativePathList(Collections.singletonList(compAttrBean.getRelativePath()));
                knownCompAttr.setDiscoveryPattern(compAttrBean.getDiscoveryPattern());

                Map<String, String> autoDiscoComponentMap = autoDiscoComponentMappings
                        .parallelStream()
                        .filter(f -> f.getComponentId() == knownCompAttr.getComponentId() && f.getName() != null)
                        .collect(Collectors.toMap(AutoDiscoComponentMapping::getName, AutoDiscoComponentMapping::getValue));

                String ldPreloadDetails = autoDiscoComponentMap.getOrDefault(Constants.LDPRELOAD_KEY, null);
                if (ldPreloadDetails != null && !ldPreloadDetails.trim().isEmpty()) {
                    LdPreloadDetails ldPreloadDetail = LdPreloadDetails.builder()
                            .value(Arrays.stream(ldPreloadDetails.split(",")).distinct().collect(Collectors.toList()))
                            .build();
                    knownCompAttr.setLdPreloadDetails(ldPreloadDetail);
                }

                knownCompAttr.setJvmArgs(autoDiscoComponentMap.getOrDefault(Constants.JVM_ARGS_KEY, null));

                attributesPojo.setAttributeName(compAttrBean.getAttributeName());

                if (compAttrBean.getMethod() == null && compAttrBean.getValue() == null) {
                    attributesPojo.setIsMandatory(0);
                } else {
                    attributesPojo.setIsMandatory(compAttrBean.getIsMandatory());
                }
                accessPojo.setMethod(compAttrBean.getMethod());
                accessPojo.setValue(compAttrBean.getValue());
                accessPojo.setPriority(compAttrBean.getPriority());
                accessPojoList.add(accessPojo);

                attributesPojo.setAccess(accessPojoList);
                attributesPojoList.add(attributesPojo);

                knownCompAttr.setAttributes(attributesPojoList);
                knownCompAttrList.put(compAttrBean.getId(), knownCompAttr);
            } else {
                Attribute attributesPojo = new Attribute();
                List<Attribute> attributesPojoList;
                AttributeAccess accessPojo = new AttributeAccess();
                List<AttributeAccess> accessPojoList = new ArrayList<>();
                if(component.getAttributes().stream().anyMatch(y-> y.getAttributeName().equals(compAttrBean.getAttributeName()))) {
                    accessPojo.setMethod(compAttrBean.getMethod());
                    accessPojo.setValue(compAttrBean.getValue());
                    accessPojo.setPriority(compAttrBean.getPriority());
                    //
                    attributesPojoList = component.getAttributes();
                    attributesPojoList.stream()
                            .filter(x -> x.getAttributeName().equals(compAttrBean.getAttributeName()))
                            .findFirst().ifPresent(attribute -> attribute.getAccess().add(accessPojo));
                } else {
                    attributesPojo.setAttributeName(compAttrBean.getAttributeName());
                    //
                    if (compAttrBean.getMethod() == null && compAttrBean.getValue() == null) {
                        attributesPojo.setIsMandatory(0);
                    } else {
                        attributesPojo.setIsMandatory(compAttrBean.getIsMandatory());
                    }
                    accessPojo.setMethod(compAttrBean.getMethod());
                    accessPojo.setValue(compAttrBean.getValue());
                    accessPojo.setPriority(compAttrBean.getPriority());
                    accessPojoList.add(accessPojo);
                    attributesPojo.setAccess(accessPojoList);
                    //
                    attributesPojoList = component.getAttributes();
                    attributesPojoList.add(attributesPojo);
                }
                knownCompAttrList.put(compAttrBean.getId(), component);
            }
        }
        return new ArrayList<>(knownCompAttrList.values());
    }
}
