package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.DiscoveryTagsBean;
import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.GroupTagsDataService;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.TagRequestPojo;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

public class TransactionGroupingBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionGroupingBL.class);

    public UtilityBean<TagRequestPojo> clientValidation(RequestObject requestObject) throws RequestException {
        LOGGER.debug("Inside Client validation");
        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(identifier)) {
            throw new RequestException(MessageFormat.format(UIMessages.INVALID_VALUE, Constants.ACCOUNT_IDENTIFIER, identifier));
        }

        return UtilityBean.<TagRequestPojo>builder().accountIdentifier(identifier).build();
    }

    public void serverValidation(UtilityBean<TagRequestPojo> utilityBean, String authToken) throws RequestException {
        LOGGER.debug("Inside Server validation");
        UserAccountBean userAccountBean = getCommonServerValidations(authToken, utilityBean.getAccountIdentifier());
        utilityBean.setAccount(userAccountBean.getAccount());
    }

    public List<String> getTagsName(UtilityBean<TagRequestPojo> utilityBean) {
        List<DiscoveryTagsBean> tagList = GroupTagsDataService.getTransactionTagList(utilityBean.getAccount().getId());
        ArrayList<String> list = new ArrayList<>();
        for(DiscoveryTagsBean tag : tagList){
            list.add(tag.getName());
        }
        return list;
    }

    public UserAccountBean getCommonServerValidations(String authToken, String identifier) throws RequestException {
        return ValidationUtils.commonServerValidations(authToken, identifier);
    }
}
