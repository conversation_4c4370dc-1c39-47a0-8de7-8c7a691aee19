package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ActionScriptBean;
import com.appnomic.appsone.controlcenter.beans.CategoryDetailBean;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ActionScriptDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CategoryDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CommandDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.*;
import com.appnomic.appsone.controlcenter.dao.redis.MasterDataRepo;
import com.appnomic.appsone.controlcenter.exceptions.ActionScriptException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.ActionPojo;
import com.appnomic.appsone.controlcenter.pojo.CommandDetailArgument;
import com.appnomic.appsone.controlcenter.pojo.CommandDetails;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.ViewTypes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import spark.Request;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ActionScriptsBL {

    private static final MasterDataRepo MASTER_DATA_REPO = new MasterDataRepo();

    public ActionPojo clientValidations(Request request) throws ActionScriptException {
        ActionPojo actionPojo;
        try {
            actionPojo = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(request.body(),
                    new TypeReference<ActionPojo>() {
                    });
            if (!actionPojo.validate()) {
                throw new ActionScriptException(UIMessages.INVALID_VALUES_FOR_ONE_OR_MORE_ATTRIBUTES);
            }
        } catch (IOException e) {
            log.error("Error in parsing JSON request. Details: {}", e.getMessage());
            throw new ActionScriptException(UIMessages.INVALID_REQUEST);
        }

        return actionPojo;
    }

    public ActionScriptBean serverValidations(ActionPojo bean, int accountId) throws ActionScriptException {
        if (bean.getIdentifier() == null || bean.getIdentifier().trim().length() == 0) {
            bean.setIdentifier(UUID.randomUUID().toString());
        }

        List<Actions> actions = ActionScriptDataService.getActionScriptDetails();
        bean.setName(bean.getName().trim());
        bean.setIdentifier(bean.getIdentifier().trim());

        if (actions.stream().anyMatch(c -> c.getIdentifier().equalsIgnoreCase(bean.getIdentifier()))) {
            log.error("Action identifier should be unique across all accounts.");
            throw new ActionScriptException(UIMessages.ACTION_INVALID + "Identifier : " + bean.getIdentifier());
        }

        if (actions.stream().filter(c -> c.getAccountId() == accountId)
                .anyMatch(c -> c.getName().equalsIgnoreCase(bean.getName()))) {
            log.error("Action name should be unique in the given account.");
            throw new ActionScriptException(UIMessages.ACTION_INVALID + "Name : " + bean.getName());
        }

        ViewTypes agentType = MASTER_DATA_REPO.getSubType(Constants.AGENT_TYPE, Constants.ACTION_AGENT_TYPE);
        if (agentType == null) {
            log.error("Type details unavailable for type [{}] and subtype [{}]", Constants.AGENT_TYPE, Constants.ACTION_AGENT_TYPE);
            throw new ActionScriptException("Type details unavailable for type " + Constants.AGENT_TYPE + " and subtype " + Constants.ACTION_AGENT_TYPE);
        }

        ViewTypes executionType = MASTER_DATA_REPO.getSubType(Constants.ACTION_EXECUTION_TYPE, Constants.ACTION_EXECUTION_TYPE_SCRIPT);
        if (executionType == null) {
            log.error("Type details unavailable for type [{}] and subtype [{}]", Constants.ACTION_EXECUTION_TYPE, Constants.ACTION_EXECUTION_TYPE_SCRIPT);
            throw new ActionScriptException("Type details unavailable for type " + Constants.ACTION_EXECUTION_TYPE + " and subtype " + Constants.ACTION_EXECUTION_TYPE_SCRIPT);
        }

        ViewTypes commandExecutionType = MASTER_DATA_REPO.getSubType(Constants.ACTION_COMMAND_EXECUTION_TYPE, Constants.ACTION_COMMAND_EXECUTION_TYPE_LONGPOLLING);
        if (commandExecutionType == null) {
            log.error("Type details unavailable for type [{}] and subtype [{}]", Constants.ACTION_COMMAND_EXECUTION_TYPE, Constants.ACTION_COMMAND_EXECUTION_TYPE_LONGPOLLING);
            throw new ActionScriptException("Type details unavailable for type " + Constants.ACTION_COMMAND_EXECUTION_TYPE + " and subtype " + Constants.ACTION_COMMAND_EXECUTION_TYPE_LONGPOLLING);
        }

        ActionScriptBean actionScriptBean = ActionScriptBean.builder()
                .name(bean.getName())
                .identifier(bean.getIdentifier())
                .agentTypeId(agentType.getSubTypeId())
                .executionTypeId(executionType.getSubTypeId())
                .commandExecutionTypeId(commandExecutionType.getSubTypeId())
                .status(1)
                .build();

        actionScriptBean.setStandardTypeId(validateAndGetStandardTypeId(bean.getStandardType()));
        actionScriptBean.setDownloadTypeId(validateAndGetDownloadTypeId(bean.getDownloadType()));
        actionScriptBean.setActionTypeId(validateAndGetActionTypeId(bean.getActionType()));
        actionScriptBean.setCategoryIds(getIdsFromCategoryIdentifiers(bean.getCategories(), accountId));
        actionScriptBean.setCommandDetails(validateAndGetCommandDetails(bean.getCommandDetails(), bean.getActionType()));

        return actionScriptBean;
    }

    public int add(ActionScriptBean bean, String userId, int accountId) throws ActionScriptException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            return dbi.inTransaction((conn, status) -> addActionScript(bean, userId, accountId, conn));
        } catch (Exception e) {
            throw new ActionScriptException(Throwables.getRootCause(e).getMessage());
        }
    }

    private List<Integer> getIdsFromCategoryIdentifiers(List<String> categoryIdentifiers, int accountId) throws ActionScriptException {
        Map<String, Integer> categories = new CategoryDataService().getCategories().stream()
                .filter(c -> c.getAccountId() == accountId || c.getAccountId() == Constants.DEFAULT_ACCOUNT_ID)
                .collect(Collectors.toMap(c -> c.getIdentifier().toLowerCase(), CategoryDetailBean::getId));
        List<Integer> ids = new ArrayList<>();
        for (String category : categoryIdentifiers) {
            if (categories.containsKey(category.toLowerCase())) {
                ids.add(categories.get(category.toLowerCase()));
            } else {
                log.error("{} -  Invalid category : {}", UIMessages.INVALID_CATEGORIES, category);
                throw new ActionScriptException(UIMessages.INVALID_CATEGORIES + " : " + category);
            }
        }
        return ids;
    }

    private int validateAndGetDownloadTypeId(String downloadType) throws ActionScriptException {
        Map<String, Integer> downloadTypes = MASTER_DATA_REPO.getTypeDetailsByTypeName(Constants.ACTION_DOWNLOAD_TYPE)
                .stream()
                .collect(Collectors.toMap(v -> v.getSubTypeName().toLowerCase(), ViewTypes::getSubTypeId));

        if (downloadTypes.containsKey(downloadType.toLowerCase())) {
            return downloadTypes.get(downloadType.toLowerCase());
        } else {
            log.error(UIMessages.ACTION_INVALID_DOWNLOAD_TYPE);
            throw new ActionScriptException(UIMessages.ACTION_INVALID_DOWNLOAD_TYPE);
        }
    }

    private int validateAndGetActionTypeId(String actionType) throws ActionScriptException {
        Map<String, Integer> actionTypes = MASTER_DATA_REPO.getTypeDetailsByTypeName(Constants.ACTIONS)
                .stream()
                .collect(Collectors.toMap(v -> v.getSubTypeName().toLowerCase(), ViewTypes::getSubTypeId));

        if (actionTypes.containsKey(actionType.toLowerCase())) {
            return actionTypes.get(actionType.toLowerCase());
        } else {
            log.error(UIMessages.INVALID_ACTION_TYPE);
            throw new ActionScriptException(UIMessages.INVALID_ACTION_TYPE);
        }
    }

    private int validateAndGetStandardTypeId(String standardType) throws ActionScriptException {
        Map<String, Integer> standardTypes = MASTER_DATA_REPO.getTypeDetailsByTypeName(Constants.ACTION_STANDARD_TYPE)
                .stream()
                .collect(Collectors.toMap(v -> v.getSubTypeName().toLowerCase(), ViewTypes::getSubTypeId));

        if (standardTypes.containsKey(standardType.toLowerCase())) {
            return standardTypes.get(standardType.toLowerCase());
        } else {
            log.error(UIMessages.ACTION_INVALID_STANDARD_TYPE);
            throw new ActionScriptException(UIMessages.ACTION_INVALID_STANDARD_TYPE);
        }
    }

    private CommandDetailsBean validateAndGetCommandDetails(CommandDetails command, String actionType) throws ActionScriptException {
        ViewTypes type = null;
        if (actionType.equals("Forensic Action")) {
            type = MASTER_DATA_REPO.getSubType(Constants.FORENSIC_COMMAND_TYPE, Constants.ACTION_COMMAND_TYPE_EXECUTE);
            log.info("Action type [{}] and subtype [{}]", Constants.FORENSIC_COMMAND_TYPE, Constants.ACTION_COMMAND_TYPE_EXECUTE);
        } else if (actionType.equals("Heal Action")) {
            type = MASTER_DATA_REPO.getSubType(Constants.HEAL_COMMAND_TYPE, Constants.ACTION_COMMAND_TYPE_EXECUTE);
            log.info("Action type [{}] and subtype [{}]", Constants.FORENSIC_COMMAND_TYPE, Constants.ACTION_COMMAND_TYPE_EXECUTE);
        }

        if (type == null) {
            log.error("Type details unavailable for [{}]", actionType);
            throw new ActionScriptException("Type details unavailable for " + actionType);
        }

        Optional<ProducerTypeBean> producerTypeOptional = MasterCache.getProducerTypes().parallelStream()
                .filter(producer -> command.getProducerType().equalsIgnoreCase(producer.getType()))
                .findAny();

        int producerType = 1;

        if (producerTypeOptional.isPresent()) {
            producerType = producerTypeOptional.get().getId();
        }

        CommandDetailsBean bean = CommandDetailsBean.builder()
                .name(command.getName().trim())
                .commandName(command.getCommandName())
                .timeOutInSecs(command.getTimeOutInSecs())
                .commandTypeId(type.getTypeId())
                .actionId(type.getSubTypeId())
                .producerTypeId(producerType)
                .build();

        Map<String, Integer> commandOutputTypes = (MASTER_DATA_REPO.getTypeDetailsByTypeName(Constants.MST_TYPE_COMMAND_OUTPUT_TYPE)).stream()
                .collect(Collectors.toMap(v -> v.getSubTypeName().toLowerCase(), ViewTypes::getSubTypeId));
        if ((commandOutputTypes.containsKey(command.getOutputType().toLowerCase()))) {
            bean.setOutputTypeId(commandOutputTypes.get(command.getOutputType().toLowerCase()));
        } else {
            log.error(UIMessages.COMMAND_OUTPUT_TYPE_INVALID);
            throw new ActionScriptException(UIMessages.COMMAND_OUTPUT_TYPE_INVALID);
        }

        if (command.getIdentifier() == null || command.getIdentifier().trim().length() == 0) {
            command.setIdentifier(UUID.randomUUID().toString());
        }
        List<CommandDetailsBean> list = CommandDataService.getCommandDetails();
        bean.setIdentifier(command.getIdentifier().trim());
        if (list.stream().anyMatch(c -> (c.getIdentifier().equalsIgnoreCase(command.getIdentifier()) ||
                c.getName().equalsIgnoreCase(command.getName())))) {
            log.error(UIMessages.COMMAND_INVALID);
            throw new ActionScriptException(UIMessages.COMMAND_INVALID);
        }
        bean.setCommandArguments(validateAndGetCommandArguments(command.getCommandArguments()));
        return bean;
    }

    private List<CommandDetailArgumentBean> validateAndGetCommandArguments(List<CommandDetailArgument> args) throws ActionScriptException {
        if (args == null || args.isEmpty())
            return Collections.emptyList();
        List<CommandDetailArgumentBean> list = new ArrayList<>();
        CommandDetailArgumentBean bean;

        Map<String, Integer> argTypes = (MASTER_DATA_REPO.getTypeDetailsByTypeName(Constants.MST_TYPE_SCRIPT_PARAM_TYPE)).stream()
                .collect(Collectors.toMap(v -> v.getSubTypeName().toLowerCase(), ViewTypes::getSubTypeId));

        Map<String, Integer> argValueTypes = (MASTER_DATA_REPO.getTypeDetailsByTypeName(Constants.MST_TYPE_ATTRIBUTE_TYPE)).stream()
                .collect(Collectors.toMap(v -> v.getSubTypeName().toLowerCase(), ViewTypes::getSubTypeId));


        for (CommandDetailArgument a : args) {

            bean = CommandDetailArgumentBean.builder()
                    .argumentKey(a.getArgumentKey())
                    .argumentValue(a.getArgumentValue())
                    .defaultValue(a.getDefaultValue())
                    .build();

            if (argTypes.containsKey(a.getArgumentType().toLowerCase())) {
                bean.setArgumentTypeId(argTypes.get(a.getArgumentType().toLowerCase()));
            } else {
                log.error(UIMessages.ACTION_COMMAND_ARGS_DETAILS_TYPE_INVALID);
                throw new ActionScriptException(UIMessages.ACTION_COMMAND_ARGS_DETAILS_TYPE_INVALID);
            }

            if (argValueTypes.containsKey(a.getArgumentValueType().toLowerCase())) {
                bean.setArgumentValueTypeId(argValueTypes.get(a.getArgumentValueType().toLowerCase()));
            } else {
                log.error(UIMessages.ACTION_COMMAND_ARGS_DETAILS_VALUE_TYPE_INVALID);
                throw new ActionScriptException(UIMessages.ACTION_COMMAND_ARGS_DETAILS_VALUE_TYPE_INVALID);
            }
            list.add(bean);
        }


        return list;
    }

    private int addActionScript(ActionScriptBean actionScriptBean, String userId, int accountId, Handle handle) throws ActionScriptException {
        log.trace("Method Invoked : ActionScriptBL/addActionScript");
        Actions actions = Actions.builder()
                .name(actionScriptBean.getName())
                .identifier(actionScriptBean.getIdentifier())
                .standardType(actionScriptBean.getStandardTypeId())
                .agentType(actionScriptBean.getAgentTypeId())
                .accountId(accountId)
                .userDetailsId(userId)
                .actionTypeId(actionScriptBean.getActionTypeId())
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .actionTypeId(actionScriptBean.getActionTypeId())
                .status(actionScriptBean.getStatus())
                .build();
        int id = ActionScriptDataService.addActionScript(actions, handle);

        if (id < 1) {
            log.error("Action is not created.");
            throw new ActionScriptException(UIMessages.INTERNAL_SERVER_ERROR);
        }

        ActionCategoryMapping actionCategoryMapping = ActionCategoryMapping.builder()
                .actionId(id)
                .userDetailsId(userId)
                .commandExecTypeId(actionScriptBean.getCommandExecutionTypeId())
                .downloadTypeId(actionScriptBean.getDownloadTypeId())
                .retries(Constants.ACTION_CATEGORY_RETRIES)
                .timeWindowInSecs(Constants.ACTION_CATEGORY_TIME_WINDOW_SEC)
                .ttlInSecs(Constants.ACTION_CATEGORY_TTL_SEC)
                .actionExecTypeId(actionScriptBean.getExecutionTypeId())
                .build();

        ViewTypes executionType = MASTER_DATA_REPO.getSubType(Constants.ACTION_EXECUTION_TYPE, Constants.ACTION_EXECUTION_TYPE_SCRIPT);
        if (executionType == null) {
            log.error("Type details unavailable for type [{}] and subtype [{}]", Constants.ACTION_EXECUTION_TYPE, Constants.ACTION_EXECUTION_TYPE_SCRIPT);
            throw new ActionScriptException("Type details unavailable for type " + Constants.ACTION_EXECUTION_TYPE + " and subtype " + Constants.ACTION_EXECUTION_TYPE_SCRIPT);
        }

        if (actionScriptBean.getExecutionTypeId() == executionType.getSubTypeId()) {
            actionScriptBean.getCommandDetails().setUserDetails(userId);
            actionScriptBean.getCommandDetails().setCreatedTime(DateTimeUtil.getCurrentTimestampInGMT());
            actionScriptBean.getCommandDetails().setUpdatedTime(DateTimeUtil.getCurrentTimestampInGMT());

            int commandDetailsId = CommandDataService.addCommandDetails(actionScriptBean.getCommandDetails(), handle);
            actionCategoryMapping.setObjectRefTable(Constants.ACTION_COMMAND_DETAILS_TABLE);
            actionCategoryMapping.setObjectId(commandDetailsId);

            List<CommandDetailArgumentBean> args = new ArrayList<>();

            for (CommandDetailArgumentBean a : actionScriptBean.getCommandDetails().getCommandArguments()) {
                args.add(CommandDetailArgumentBean.getInstance(commandDetailsId, userId,
                        a.getArgumentKey(), a.getArgumentValue(), a.getDefaultValue(), a.getArgumentTypeId(), a.getArgumentValueTypeId()));
            }
            if(!args.isEmpty()) {
                CommandDataService.addCommandArguments(args, handle);
            }
        }

        List<Integer> categoryActionIds = addActionScriptCategoryMapping(actionCategoryMapping, actionScriptBean.getCategoryIds(), handle);
        if (categoryActionIds.stream().anyMatch(c -> c < 1)) {
            log.error("Action is not mapped to Category.");
            throw new ActionScriptException(UIMessages.INTERNAL_SERVER_ERROR);
        }
        return id;
    }

    private static List<Integer> addActionScriptCategoryMapping(ActionCategoryMapping actionCategoryMapping, List<Integer> ids, Handle handle) {

        List<ActionCategoryMapping> list = new ArrayList<>();
        ActionCategoryMapping bean;

        for (int categoryId : ids) {
            bean = ActionCategoryMapping.builder()
                    .actionId(actionCategoryMapping.getActionId())
                    .categoryId(categoryId)
                    .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .userDetailsId(actionCategoryMapping.getUserDetailsId())
                    .timeWindowInSecs(actionCategoryMapping.getTimeWindowInSecs())
                    .commandExecTypeId(actionCategoryMapping.getCommandExecTypeId())
                    .downloadTypeId(actionCategoryMapping.getDownloadTypeId())
                    .actionExecTypeId(actionCategoryMapping.getActionExecTypeId())
                    .retries(actionCategoryMapping.getRetries())
                    .ttlInSecs(actionCategoryMapping.getTtlInSecs())
                    .objectId(actionCategoryMapping.getObjectId())
                    .objectRefTable(actionCategoryMapping.getObjectRefTable())
                    .status(1)
                    .build();
            list.add(bean);
        }
        return Arrays.asList(ArrayUtils.toObject(ActionScriptDataService.addActionScriptCategoryMapping(list,handle)));
    }
}
