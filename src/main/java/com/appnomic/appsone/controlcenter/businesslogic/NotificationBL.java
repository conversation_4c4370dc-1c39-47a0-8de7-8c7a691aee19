package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UserAccessDetails;
import com.appnomic.appsone.controlcenter.beans.UserNotificationDetails;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.MasterDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.RulesDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.UserAccessDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.NotificationBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserForensicNotificationMappingBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserNotificationDetailsBean;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.KpiException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.service.NotificationPreferencesDataService;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.UserValidationUtil;
import com.google.common.base.Throwables;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

public class NotificationBL {
    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationBL.class);

    public NotificationsPojo processRequestAndGetNotificationTypeList(AccountBean account, String username, String userDetailsId) throws ControlCenterException {
        String applicableUserId;
        try {
            applicableUserId = UserAccessDataService.getUserIdentifierFromName(username);
        } catch (ControlCenterException e) {
            LOGGER.error("Exception while fetching user identifier. Reason: {}", e.getMessage(), e);
            throw new ControlCenterException("Error while fetching user identifier");
        }

        UserAccessDetails accessDetails = UserValidationUtil.getUserAccessDetails(applicableUserId, account.getIdentifier());

        if (accessDetails == null) {
            LOGGER.error("user does not have access to given account please contact admin");
            throw new ControlCenterException(UIMessages.NOTIFICATION_ACCOUNT_ERROR);
        }

        NotificationsPojo notificationType = new NotificationsPojo();

        Map<String, List<NotificationTypePojo>> metaData = getNotificationType();
        notificationType.setMetaData(metaData);

        List<Integer> appIds = new ArrayList<>();
        List<String> app = accessDetails.getApplicationIdentifiers();

        List<Controller> controllers = getControllersByTypeAndIdentifier(account.getId(), app);

        if (Objects.nonNull(controllers)) {
            controllers.forEach(c -> appIds.add(Integer.valueOf(c.getAppId())));
        }

        DBI dbi = MySQLConnectionManager.getInstance().getHandle();

        dbi.inTransaction((handle, status) -> {

            BindInDataService bindInDataService = new BindInDataService();

            List<NotificationBean> applicationNotificationPreferences = bindInDataService.getApplicationNotificationPreferences(appIds, account.getId(), handle);

            if (!applicationNotificationPreferences.isEmpty()) {
                createDefaultNotificationsPreferences(applicableUserId, applicationNotificationPreferences, userDetailsId, account.getId(), handle);
            }

            List<NotificationBean> notificationUserList = bindInDataService.getUserNotificationDetails(appIds, applicableUserId, account.getId(), handle);

            if (Objects.nonNull(notificationUserList)) {
                List<UserPreferencesPojo> userPreferencesList = setNotification(notificationUserList, controllers);
                notificationType.setPreferences(userPreferencesList);
            }

            UserNotificationDetails userNotificationDetails = NotificationPreferencesDataService.getEmailAndSmsStatus(applicableUserId, handle);

            if (Objects.isNull(userNotificationDetails)) {
                createDefaultNotificationsDetails(applicableUserId, userDetailsId, handle);
                userNotificationDetails = NotificationPreferencesDataService.getEmailAndSmsStatus(applicableUserId, handle);
            }

            if (Objects.nonNull(userNotificationDetails)) {
                notificationType.setEmailNotification(userNotificationDetails.getEmailEnabled() == 1);
                notificationType.setSmsNotification(userNotificationDetails.getSmsEnabled() == 1);
            }

            return notificationType;
        });

        return notificationType;
    }

    private static List<Controller> getControllersByTypeAndIdentifier(int accountId, List<String> appIdentifiers) {
        List<Controller> filtratedControllerList = new ArrayList<>();
        try {
            ViewTypes subTypeBean = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT, Constants.APPLICATION_CONTROLLER_TYPE);
            List<Controller> controllerList = MasterDataService.getControllerList(accountId);
            filtratedControllerList = controllerList.stream().
                    filter(t -> t.getControllerTypeId() == subTypeBean.getSubTypeId()).
                    filter(t -> appIdentifiers.contains(t.getIdentifier())).collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.error("Error occurred while fetching controller details for service name: {} account id: {}", Constants.APPLICATION_CONTROLLER_TYPE, accountId, e);
        }
        return filtratedControllerList;
    }

    void createDefaultNotificationsPreferences(String applicableUser, List<NotificationBean> notificationBeanList,
                                               String userDetailsId, int accountId, Handle handle) throws ControlCenterException {
        List<NotificationBean> addNotificationBeanList = new ArrayList<>();

        notificationBeanList.forEach(nb -> {
            int count = NotificationPreferencesDataService.getNotificationPreferencesForUser(nb.getSignalTypeId(), nb.getSeverityTypeId(), nb.getApplicationId(),
                    applicableUser, accountId, handle);

            if (count == 0) {
                NotificationBean addNotificationBean = createNotificationPreferencesBean(nb.getNotificationTypeId(), nb.getSignalTypeId(),
                        nb.getSeverityTypeId(), nb.getApplicationId(), applicableUser, userDetailsId, accountId);
                addNotificationBeanList.add(addNotificationBean);
            }
        });

        if (!addNotificationBeanList.isEmpty()) {
            int[] notificationId = NotificationPreferencesDataService.addNotificationDetails(addNotificationBeanList, handle);

            if (notificationId.length == 0) {
                LOGGER.error(UIMessages.LIST_NOTIFICATION_ERROR);
                throw new ControlCenterException(UIMessages.NOTIFICATION_DETAILS_ERROR);
            }
        }

        LOGGER.info("[{}] notification preferences added for user [{}].", addNotificationBeanList.size(), applicableUser);
    }

    private static UserNotificationDetailsBean createNotificationDetails(String user, String userId) {
        Timestamp startDate = DateTimeUtil.getCurrentTimestampInGMT();
        int preferenceId = ConfProperties.getInt(Constants.USER_NOTIFICATION_PREFERENCE, Constants.USER_NOTIFICATION_PREFERENCE_DEFAULT);
        int forensicNotificationSuppression = ConfProperties.getInt(Constants.USER_NOTIFICATION_FORENSIC_SUPPRESSION_INTERVAL,
                Constants.USER_NOTIFICATION_FORENSIC_SUPPRESSION_INTERVAL_DEFAULT);
        return UserNotificationDetailsBean.builder()
                .emailEnabled(true)
                .smsEnabled(true)
                .forensicEnabled(Constants.STATUS_INACTIVE)
                .accountId(Constants.DEFAULT_ACCOUNT_ID)
                .applicableUserId(user)
                .userDetailsId(userId)
                .createdTime(startDate)
                .updatedTime(startDate)
                .preferenceId(preferenceId)
                .forensicNotificationSuppression(forensicNotificationSuppression)
                .build();
    }


    void createDefaultNotificationsDetails(String applicableUser, String userDetailsId, Handle handle) throws ControlCenterException {
        UserNotificationDetailsBean createNotificationDetails = createNotificationDetails(applicableUser, userDetailsId);

        int notificationUserId = NotificationPreferencesDataService.addNotificationUserDetails(createNotificationDetails, handle);

        if (notificationUserId == -1) {
            LOGGER.error(UIMessages.LOG_NOTIFICATION_ERROR);
            throw new ControlCenterException(UIMessages.ADD_NOTIFICATION_ERROR);
        }
    }

    private List<UserPreferencesPojo> setNotification(List<NotificationBean> notificationUserList, List<Controller> appList) {
        List<UserPreferencesPojo> userPreferencesList = new ArrayList<>();

        if (!notificationUserList.isEmpty()) {
            for (NotificationBean cm : notificationUserList) {
                UserPreferencesPojo userPreferences = userList(cm, appList);
                userPreferencesList.add(userPreferences);
            }
        }

        return userPreferencesList;
    }

    private Map<String, List<NotificationTypePojo>> getNotificationType() throws ControlCenterException {
        Map<String, List<NotificationTypePojo>> metaData = new HashMap<>();
        List<NotificationTypePojo> notificationTypeList = new ArrayList<>();
        List<ViewTypes> type = MasterCache.getTypeDetailsList(Constants.NOTIFICATION_TYPE);

        if (Objects.isNull(type)) {
            LOGGER.error("Notification type unavailable.");
            throw new ControlCenterException("Type-" + Constants.NOTIFICATION_TYPE + " is not found in DB");
        }

        type.forEach(a -> {
            NotificationTypePojo notificationTypePojo = new NotificationTypePojo();
            notificationTypePojo.setId(a.getSubTypeId());
            notificationTypePojo.setName(a.getSubTypeName());
            notificationTypeList.add(notificationTypePojo);
        });

        metaData.put(Constants.NOTIFICATION_KEY, notificationTypeList);

        return metaData;
    }

    private UserPreferencesPojo userList(NotificationBean nb, List<Controller> appList) {
        UserPreferencesPojo userPreferencesPojo = new UserPreferencesPojo();
        String appName = "";

        Optional<Controller> data = appList.stream()
                .filter(c -> c.getAppId().equals(String.valueOf(nb.getApplicationId())))
                .findAny();

        if (data.isPresent()) {
            appName = data.get().getName();
        }

        userPreferencesPojo.setApplicationName(appName);
        userPreferencesPojo.setApplicationId(nb.getApplicationId());
        userPreferencesPojo.setNotificationTypeId(nb.getNotificationTypeId());
        userPreferencesPojo.setSeverityTypeId(nb.getSeverityTypeId());
        userPreferencesPojo.setSeverityType(RulesDataService.getNameFromMSTSubType(nb.getSeverityTypeId()));
        userPreferencesPojo.setSignalTypeId(nb.getSignalTypeId());
        userPreferencesPojo.setSignalType(RulesDataService.getNameFromMSTSubType(nb.getSignalTypeId()));

        return userPreferencesPojo;
    }

    public void createNotifications(PreferencesPojo userPreferencesPojo, String applicableUser, String userId, int accountId) throws ControlCenterException {
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();

        try {
            dbi.inTransaction((handle, status) -> {
                List<NotificationBean> updateNotificationBeanList = new ArrayList<>();
                List<UserPreferencesPojo> preferencesList = userPreferencesPojo.getPreferences();

                preferencesList.forEach(nb -> {

                    int count = NotificationPreferencesDataService.getNotificationPreferencesForUser(nb.getSignalTypeId(), nb.getSeverityTypeId(), nb.getApplicationId(),
                            applicableUser, accountId, handle);

                    if (count > 0) {
                        NotificationBean updateNotificationBean = createNotificationPreferencesBean(nb.getNotificationTypeId(), nb.getSignalTypeId(),
                                nb.getSeverityTypeId(), nb.getApplicationId(), applicableUser, userId, accountId);
                        updateNotificationBeanList.add(updateNotificationBean);
                    }
                });

                if (!updateNotificationBeanList.isEmpty()) {
                    NotificationPreferencesDataService.updateNotifications(updateNotificationBeanList);
                }

                NotificationPreferencesDataService.updateNotificationUserDetails(userPreferencesPojo.isSmsNotification(), userPreferencesPojo.isEmailNotification(), applicableUser, DateTimeUtil.getCurrentTimestampInGMT());

                return "User notification preferences and/or details successfully updated.";
            });
        } catch (Exception e) {
            if (Throwables.getRootCause(e) instanceof KpiException || Throwables.getRootCause(e) instanceof ControlCenterException) {
                throw (ControlCenterException) Throwables.getRootCause(e);
            } else {
                throw e;
            }
        }
    }

    private static NotificationBean createNotificationPreferencesBean(int notificationTypeId, int signalTypeId, int severityTypeId, int appId,
                                                                      String user, String userId, int accountId) {
        Timestamp startDate = DateTimeUtil.getCurrentTimestampInGMT();
        return NotificationBean.builder()
                .accountId(accountId)
                .notificationTypeId(notificationTypeId)
                .severityTypeId(severityTypeId)
                .signalTypeId(signalTypeId)
                .applicationId(appId)
                .applicableUserId(user)
                .userDetailsId(userId)
                .status(1)
                .createdTime(startDate)
                .updatedTime(startDate)
                .build();
    }

    void createDefaultForensicNotificationPreferences(String applicableUser, List<Integer> applicationIds, int accountId,
                                                      String userDetailsId, Handle handle) throws ControlCenterException {

        Timestamp timestamp = DateTimeUtil.getCurrentTimestampInGMT();
        int forensicNotificationSuppression = ConfProperties.getInt(Constants.USER_NOTIFICATION_FORENSIC_SUPPRESSION_INTERVAL,
                Constants.USER_NOTIFICATION_FORENSIC_SUPPRESSION_INTERVAL_DEFAULT);

        List<UserForensicNotificationMappingBean> userForensicNotificationMappingBeans = applicationIds.parallelStream()
                .map(id -> UserForensicNotificationMappingBean.builder()
                        .accountId(accountId)
                        .applicationId(id)
                        .applicableUserId(applicableUser)
                        .forensicNotificationSuppression(forensicNotificationSuppression)
                        .userDetailsId(userDetailsId)
                        .status(Constants.STATUS_ACTIVE)
                        .createdTime(timestamp)
                        .updatedTime(timestamp)
                        .build())
                .collect(Collectors.toList());

        if (!userForensicNotificationMappingBeans.isEmpty()) {
            NotificationPreferencesDataService.addForensicNotificationConfigurations(userForensicNotificationMappingBeans, handle);
        }

        LOGGER.info("[{}] forensic notification preferences added for user [{}].", userForensicNotificationMappingBeans.size(), applicableUser);
    }
}
