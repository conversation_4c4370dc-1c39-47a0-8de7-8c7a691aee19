package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.AgentBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentStatusDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CommandDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.RulesDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandTriggerBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.PhysicalAgentBean;
import com.appnomic.appsone.controlcenter.dao.opensearch.AgentHealthStatusRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.AgentTriggerStatusPojo;
import com.appnomic.appsone.controlcenter.pojo.CommandTriggerStatusPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.opensearch.AgentCommandsData;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class GetAgentCommandTriggerStatusBL implements BusinessLogic<List<AgentTriggerStatusPojo>, List<AgentTriggerStatusPojo>,
        List<CommandTriggerStatusPojo>> {

    private String accountIdentifier;

    @Override
    public UtilityBean<List<AgentTriggerStatusPojo>> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (StringUtils.isEmpty(requestObject.getBody())) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);

        if (authKey == null || authKey.trim().isEmpty()) {
            log.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (identifier == null || identifier.trim().isEmpty()) {
            log.error("Invalid account identifier. Reason: It is either NULL or empty");
            throw new ClientException("Invalid account identifier");
        }

        List<AgentTriggerStatusPojo> listOfStatusBean;
        try {
            listOfStatusBean = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestObject.getBody(),
                    new TypeReference<List<AgentTriggerStatusPojo>>() {
                    });
            listOfStatusBean = listOfStatusBean.parallelStream().distinct().collect(Collectors.toList());

            for (AgentTriggerStatusPojo agentTriggerStatus : listOfStatusBean) {
                if (!agentTriggerStatus.validate()) {
                    throw new ClientException("Input validation failure");
                }
            }
        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID, e);
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        return UtilityBean.<List<AgentTriggerStatusPojo>>builder()
                .accountIdentifier(identifier)
                .authToken(authKey)
                .pojoObject(listOfStatusBean)
                .build();
    }

    @Override
    public List<AgentTriggerStatusPojo> serverValidation(UtilityBean<List<AgentTriggerStatusPojo>> utilityBean) throws ServerException {
        accountIdentifier = utilityBean.getAccountIdentifier();

        AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier);

        if (account == null) {
            log.error("Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            log.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        List<AgentTriggerStatusPojo> agentTriggerStatusList = utilityBean.getPojoObject();

        return validateAndGetAgentCommandJobIds(agentTriggerStatusList);
    }

    private List<AgentTriggerStatusPojo> validateAndGetAgentCommandJobIds(List<AgentTriggerStatusPojo> agentTriggerStatusList) throws ServerException {
        for (AgentTriggerStatusPojo agentTriggerStatus : agentTriggerStatusList) {
            int physicalAgentId = agentTriggerStatus.getPhysicalAgentId();
            
            PhysicalAgentBean physicalAgentBean = AgentDataService.getPhysicalAgentDetailsUsingId(physicalAgentId);
            if (physicalAgentBean == null) {
                log.error("Invalid physicalAgentId. Reason: physicalAgentId [{}] not mapped to given service.", physicalAgentId);
                throw new ServerException(UIMessages.INVALID_PHYSICAL_AGENT_ID);
            }

            String commandJobId = agentTriggerStatus.getCommandJobId();
            
            String validCommandJobId = AgentStatusDataService.getValidCommandJobId(commandJobId);
            if (validCommandJobId == null) {
                log.error("commandJobId is not valid, commandJobId:{}", commandJobId);
                throw new ServerException("Invalid commandJobId. For more details, refer to application log file.");
            }
        }
        
        return agentTriggerStatusList;
    }

    @Override
    public List<CommandTriggerStatusPojo> process(List<AgentTriggerStatusPojo> agentTriggerStatusPojoList) throws DataProcessingException {
        CommandTriggerStatusPojo commandTriggerStatusPojos;
        List<CommandTriggerStatusPojo> commandTriggerStatusPojosList = new ArrayList<>();

        for (AgentTriggerStatusPojo agentTriggerStatusPojo : agentTriggerStatusPojoList) {
            try {
                commandTriggerStatusPojos = getAgentCommandTriggerStatus(agentTriggerStatusPojo, accountIdentifier);
            } catch (ControlCenterException e) {
                throw new DataProcessingException(e.getMessage());
            }
            commandTriggerStatusPojosList.add(commandTriggerStatusPojos);
        }
        return commandTriggerStatusPojosList;
    }

    private CommandTriggerStatusPojo getAgentCommandTriggerStatus(AgentTriggerStatusPojo agentTriggerStatusPojo, String accountIdentifier) throws ControlCenterException {
        int physicalAgentId = agentTriggerStatusPojo.getPhysicalAgentId();
        String commandJobId = agentTriggerStatusPojo.getCommandJobId();

        CommandTriggerStatusPojo commandTriggerStatusPojo = new CommandTriggerStatusPojo();

        PhysicalAgentBean physicalAgentBean = AgentDataService.getPhysicalAgentDetailsUsingId(physicalAgentId);

        if (null == physicalAgentBean) {
            log.error("Physical agent with id [{}] unavailable.", physicalAgentId);
            throw new ControlCenterException(String.format("Physical agent with id %d unavailable.", physicalAgentId));
        }

        List<AgentBean> agentBeanList = AgentDataService.getAgentsListUsingPhysicalAgentId(physicalAgentId);
        if (agentBeanList == null || agentBeanList.isEmpty()) {
            log.error("There are no agents configured.");
            throw new ControlCenterException(UIMessages.AGENT_CONFIGURED_ERROR);
        }

        commandTriggerStatusPojo.setAgentId(physicalAgentBean.getId());
        commandTriggerStatusPojo.setAgentTypeId(agentBeanList.stream().map(AgentBean::getAgentTypeId).collect(Collectors.toSet()));
        commandTriggerStatusPojo.setIsCommandComplete(physicalAgentBean.getLastCommandExecuted());

        String lastDesired = AgentDataService.getLastDesiredStatus(physicalAgentId);
        if (lastDesired != null) {
            commandTriggerStatusPojo.setLastDesiredStatus(lastDesired);
        }

        if (null != physicalAgentBean.getLastStatusId() && physicalAgentBean.getLastStatusId() > 0) {
            commandTriggerStatusPojo.setCurrentStatus(RulesDataService.getNameFromMSTSubType(physicalAgentBean.getLastStatusId()));
        }

        log.debug("fetching agent health status with following details : acountIdentifier {}," +
                " agentidentifier {}, commandjobid {}",accountIdentifier, physicalAgentBean.getIdentifier(),
                physicalAgentBean.getLastJobId());

        AgentCommandsData agentHealthStatus = new AgentHealthStatusRepo().getAgentCommandDetails(accountIdentifier,
                physicalAgentBean.getIdentifier(), physicalAgentBean.getLastJobId());

        log.debug("Agent health status details fetched with following details {}", agentHealthStatus);

        if (null != agentHealthStatus) {
            commandTriggerStatusPojo.setLastCommandTime(agentHealthStatus.getLastDataReceivedTimeInGMT());
            Map<String, String> dataMap = agentHealthStatus.getMetadata();
            if (Objects.nonNull(dataMap)) {
                commandTriggerStatusPojo.setMetadata(dataMap);
            }
        }

        updateCommandStatusWithTimeoutCheck(commandJobId, commandTriggerStatusPojo, physicalAgentBean);

        return commandTriggerStatusPojo;
    }

    private void updateCommandStatusWithTimeoutCheck(String commandJobId, CommandTriggerStatusPojo commandTriggerStatusPojo, PhysicalAgentBean physicalAgentBean) throws ControlCenterException {
        CommandTriggerBean commandDetails = getAgentCommandDataDetails(commandTriggerStatusPojo, commandJobId, physicalAgentBean.getId());

        if (null != commandDetails) {
            commandTriggerStatusPojo.setLastCommandName(commandDetails.getLastCommandName());

            if (null == commandTriggerStatusPojo.getMetadata().get("Message") && physicalAgentBean.getLastCommandExecuted() == 0) {
                commandTriggerStatusPojo.setStatusMessage(commandDetails.getLastCommandName() + " action is in progress");
            } else {
                commandTriggerStatusPojo.setStatusMessage(commandTriggerStatusPojo.getMetadata().get("Message"));
            }

            if (physicalAgentBean.getLastCommandExecuted() == 0) {
                try {
                    long fromEpochTime = DateTimeUtil.getGMTToEpochTime(String.valueOf(commandDetails.getTriggerTime()));
                    long toEpochTime = DateTimeUtil.getGMTToEpochTime(String.valueOf(new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime())));
                    long diffInMilli = toEpochTime - fromEpochTime;
                    long secondsPassed = (diffInMilli / 1000) / 60;
                    long timeInMinute = commandDetails.getTimeoutInSecs() / 60;
                    if (secondsPassed >= timeInMinute) {
                        CommandDataService.updateCommandFailureStatus(physicalAgentBean.getIdentifier(), commandDetails.getCommandJobId(), physicalAgentBean.getUserDetailsId());
                        log.error("Command [{}] timed out for agent [{}]. Status updated in CC.", commandJobId, physicalAgentBean.getId());
                    }
                } catch (ParseException e) {
                    log.error("ParseException encountered while evaluating the status of triggered command. Reason: {}", e.getMessage(), e);
                    throw new ControlCenterException("Unexpected exception encountered while evaluating the status of the triggered command.");
                }
            }
        }
    }

    private CommandTriggerBean getAgentCommandDataDetails(CommandTriggerStatusPojo commandTriggerStatusPojo, String commandJobId, int physicalAgentId) {
        CommandTriggerBean commandDetails = new CommandTriggerBean();
        if (!commandJobId.equals("")) {
            commandDetails = AgentStatusDataService.getAgentCommandTriggerStatus(physicalAgentId, commandJobId);
            if (Objects.nonNull(commandDetails)) {
                commandTriggerStatusPojo.setDesiredStatus(commandDetails.getDesiredStat());
            }
        }
        return commandDetails;
    }

}
