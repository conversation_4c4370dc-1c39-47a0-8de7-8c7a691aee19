package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.beans.discovery.Connection;
import com.appnomic.appsone.common.beans.discovery.Endpoint;
import com.appnomic.appsone.common.beans.discovery.NetworkInterface;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.enums.Direction;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.AutoDiscoveryDiscoveredConnections;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.AutoDiscoveryServiceMapping;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.common.autodiscovery.Entity;
import com.appnomic.appsone.controlcenter.dao.mysql.AutoDiscoveryDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.AutoDiscoveryDiscConnPojo;
import com.appnomic.appsone.controlcenter.pojo.Controller;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.AutoDiscoveryMapEntityPojo;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.EnvironmentHelper;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class AutoDiscoveryMapEntityBL implements BusinessLogic<AutoDiscoveryMapEntityPojo, AutoDiscoveryMapEntityPojo, List<String>> {
    private static final String CONN_LIST_EMPTY = "No network connections found. List is empty.";
    private static final String ENDPOINTS_LIST_EMPTY = "No endpoints found. List is empty.";
    private static final String INTERFACES_LIST_EMPTY = "No network interfaces found. List is empty.";
    private static final String MAPPING_LIST_EMPTY = "No service mappings found. List is empty.";
    private static final String PROCESS_LIST_EMPTY = "No processes found. List is empty.";

    @Override
    public UtilityBean<AutoDiscoveryMapEntityPojo> clientValidation(RequestObject request) throws ClientException {

        if (request == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (authToken == null || StringUtils.isEmpty(authToken)) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }

        String accountIdentifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (accountIdentifier == null || StringUtils.isEmpty(accountIdentifier)) {
            log.error(UIMessages.ACCOUNT_NULL_OR_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_NULL_OR_EMPTY);
        }

        String requestBody = request.getBody();
        AutoDiscoveryMapEntityPojo autoDiscoveryEntity;
        try {
            autoDiscoveryEntity = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestBody,
                    new TypeReference<AutoDiscoveryMapEntityPojo>() {
                    });
            if (autoDiscoveryEntity != null) {
                if (!autoDiscoveryEntity.validateMandatoryFields()) {
                    log.error("Input Validation failure for Map Entity to Service.");
                    throw new ClientException("Input Validation failure for Map Entity to Service.");
                }
            }
        } catch (IOException e) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        return UtilityBean.<AutoDiscoveryMapEntityPojo>builder()
                .accountIdentifier(accountIdentifier)
                .authToken(authToken)
                .pojoObject(autoDiscoveryEntity)
                .build();
    }

    @Override
    public AutoDiscoveryMapEntityPojo serverValidation(UtilityBean<AutoDiscoveryMapEntityPojo> utilityBean) throws ServerException {

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error(UIMessages.INVALID_ACCOUNT_MESSAGE);
            throw new ServerException(UIMessages.INVALID_ACCOUNT_MESSAGE);
        }

        AutoDiscoveryMapEntityPojo autoDiscoveryEntity = utilityBean.getPojoObject();
        return AutoDiscoveryMapEntityPojo.builder()
                .entityType(autoDiscoveryEntity.getEntityType())
                .serviceIdentifiers(autoDiscoveryEntity.getServiceIdentifiers())
                .serviceMappingIdentifiers(autoDiscoveryEntity.getServiceMappingIdentifiers())
                .environment(autoDiscoveryEntity.getEnvironment())
                .accountId(account.getId())
                .build();
    }

    @Override
    public List<String> process(AutoDiscoveryMapEntityPojo adMapEntity) throws DataProcessingException {
        /**
         * maps hosts or component instances to their respective services
         * and
         * adds connections related to mapped entities to discovered connections table
         */
        List<String> invalidJsonObjects;
        try {
            invalidJsonObjects = mapEntityDetails(adMapEntity.getEntityType(), adMapEntity.getServiceIdentifiers(),
                    adMapEntity.getServiceMappingIdentifiers(), adMapEntity.getEnvironment(), adMapEntity.getAccountId());
            return invalidJsonObjects;
        } catch (Exception e) {
            throw new DataProcessingException(e.getMessage());
        }
    }


    public List<String> mapEntityDetails(Entity entityType, int[] controllerId,
                                         String[] identifiers, String environment, int accountId) throws Exception {

        AutoDiscoveryDataService adDataService = new AutoDiscoveryDataService();
        AutoDiscoveryMapEntityPojo errorMessage = new AutoDiscoveryMapEntityPojo();
        List<String> validEntityList = Arrays.asList(identifiers);
        List<String> inValidEntityList = new ArrayList<>();

        adDataService.validateIdentifiers(entityType, validEntityList, errorMessage.getErrorMessages());

        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        dbi.inTransaction((conn, status) -> {
            try {
                String UImessage = mapEntityToService(validEntityList, entityType, controllerId, errorMessage.getErrorMessages(), environment, accountId, conn);
                inValidEntityList.add(UImessage);
            } catch (DataProcessingException e) {
                throw new RuntimeException(e.getMessage());
            }
            return "Nothing to return";
        });

        for (String valid : validEntityList) {
            if (!(errorMessage.getErrorMessages().get(valid) == null)) inValidEntityList.add(valid);
        }
        return inValidEntityList;
    }

    private String mapEntityToService(List<String> validEntities, Entity entityType, int[] controllerId, Map<String, String> errorMessages, String environment, int accountId,
                                    Handle handle) throws DataProcessingException {

        AutoDiscoveryDataService adDataService = new AutoDiscoveryDataService();
        // Retrieve existing mappings
        List<AutoDiscoveryServiceMapping> serviceMappingList = adDataService.getServiceMappingList(null);
        if (serviceMappingList == null)
            throw new DataProcessingException("Exception while fetching existing mappings.");

        // Retrieve existing processes
        List<Process> processList = adDataService.getProcessList(null);
        if (processList.isEmpty()) log.warn("Retrieved processes list is empty.");

        List<String> incomingMappings = new ArrayList<>();
        List<Controller> controllerList = null;
        // if controller id is null then all existing mappings for that entity will be deleted [un-mapping operation]
        if (controllerId == null) {
            String key = wrongHostServiceEditing(validEntities, entityType, incomingMappings, processList, serviceMappingList, controllerList);
            if (key.equals("WRONG-EDIT")) {
                return key;
            }
            deleteServiceMappings(validEntities, entityType, serviceMappingList, processList, errorMessages, handle);
            return "UNMAPPING";
        }

        controllerList = adDataService.validateServiceIdentifier(controllerId, accountId);
        if (controllerList.isEmpty())
            throw new DataProcessingException("Exception while validating service identifiers.");
        for (Controller controller : controllerList) {
            incomingMappings.add(controller.getIdentifier());
        }

        String key = wrongHostServiceEditing(validEntities, entityType, incomingMappings, processList, serviceMappingList, controllerList);
        if (key.equals("WRONG-EDIT")) {
            return key;
        }

        List<AutoDiscoveryServiceMapping> autoDiscoveryEntityBeanList = createEntityBean(validEntities, entityType, controllerList,
                serviceMappingList, processList, errorMessages, handle);
        // add valid entities to table
        adDataService.addServiceMapping(autoDiscoveryEntityBeanList, handle);
        log.info("Entity/Entities mapped to service(s) successfully.");
        // set last updated time
        adDataService.setLastUpdatedTime(autoDiscoveryEntityBeanList, null);
        // change the environment in case entity is host
        if (entityType.equals(Entity.Host)) {
            EnvironmentHelper envHelper = new EnvironmentHelper();
            int environmentId = envHelper.getEnvironmentId(environment);
            adDataService.setEnvironment(validEntities, environmentId);
        }

        // add connections related to these entities to autodisco_discovered_connections table
        int added_conn_to_table = addToDiscoveredConnections(entityType, validEntities, handle);
        if (added_conn_to_table != 1) {
            String msg = "Could not add entity related connections to discovered connections table.";
            log.error(msg);
            throw new DataProcessingException(msg);
        }
        return "PASS";
    }

    private String wrongHostServiceEditing(List<String> validEntities, Entity entityType, List<String> incomingMappings,
                                           List<Process> processList, List<AutoDiscoveryServiceMapping> serviceMappingList,
                                           List<Controller> controllerList) {
        // check if user tries to edit mappings from component instances on hosts page
        if (entityType.equals(Entity.Host)) {
            for (String validEntity : validEntities) {
                List<Process> processes = processList.stream().filter(p -> p.getHostIdentifier().equals(validEntity) && p.getComponentId() != 0).collect(Collectors.toList());
                for (Process p : processes) {
                    List<AutoDiscoveryServiceMapping> mappings = serviceMappingList.stream().filter(m -> m.getServiceMappingIdentifier().equals(p.getProcessIdentifier())).distinct().collect(Collectors.toList());
                    List<String> compInstanceMappings = new ArrayList<>();
                    for (AutoDiscoveryServiceMapping map : mappings) {
                        compInstanceMappings.add(map.getServiceIdentifier());
                        // if a component instance is already mapped to a service then host also gets automatically mapped to the same service,
                        // so we can omit incoming mappings already present to host
                        if (controllerList != null) controllerList.removeIf(c -> c.getIdentifier().equals(map.getServiceIdentifier()));
                    }
                    if (!incomingMappings.containsAll(compInstanceMappings)) {
                        log.error("Cannot edit component instance service mappings here.");
                        return "WRONG-EDIT";
                    }
                }
            }
        }
        return "PASS";
    }

    private List<AutoDiscoveryServiceMapping> createEntityBean(List<String> validEntities, Entity entityType,
                                                               List<Controller> controllerList, List<AutoDiscoveryServiceMapping> serviceMappingList,
                                                               List<Process> processList, Map<String, String> errorMessages, Handle handle) {

        List<AutoDiscoveryServiceMapping> autoDiscoveryEntityBeanList = new ArrayList<>();

        // deleted old service mappings if required
        if (validEntities.size() < 2) { // mapped services and discovered connections are not deleted for bulk actions
            // delete previously mapped services and related connections
            deleteServiceMappings(validEntities, entityType, serviceMappingList, processList, errorMessages, handle);
        }

        long time = System.currentTimeMillis();
        for (String validEntity : validEntities) {
            for (Controller sid : controllerList) {
                AutoDiscoveryServiceMapping autoDiscoveryEntityBean = new AutoDiscoveryServiceMapping();
                autoDiscoveryEntityBean.setServiceIdentifier(sid.getIdentifier());
                autoDiscoveryEntityBean.setEntityType(entityType);
                autoDiscoveryEntityBean.setServiceMappingIdentifier(validEntity);
                autoDiscoveryEntityBean.setLastUpdatedTime(time);
                autoDiscoveryEntityBeanList.add(autoDiscoveryEntityBean);
                // if CompInstance is mapped to a service then the host of the CompInstance should also be mapped to the service
                if (entityType.equals(Entity.CompInstance)) {
                    String hostIdentifier = processList.stream().filter(x -> x.getProcessIdentifier().equals(validEntity))
                            .findFirst().get().getHostIdentifier();

                    autoDiscoveryEntityBean = new AutoDiscoveryServiceMapping();
                    autoDiscoveryEntityBean.setServiceIdentifier(sid.getIdentifier());
                    autoDiscoveryEntityBean.setEntityType(Entity.Host);
                    autoDiscoveryEntityBean.setServiceMappingIdentifier(hostIdentifier);
                    autoDiscoveryEntityBean.setLastUpdatedTime(time);
                    autoDiscoveryEntityBeanList.add(autoDiscoveryEntityBean);
                }
            }
        }
        return autoDiscoveryEntityBeanList;
    }

    public int addToDiscoveredConnections(Entity entityType, List<String> validEntities, Handle handle) throws DataProcessingException {

        AutoDiscoveryDataService adDataService = new AutoDiscoveryDataService();
        List<Connection> adNetworkConnectionsList = adDataService.getNetworkConnections(null);
        if (adNetworkConnectionsList.isEmpty()) { log.error(CONN_LIST_EMPTY); }

        List<Endpoint> adEndpointsList = adDataService.getEndpoints(null);
        if (adEndpointsList.isEmpty()) { log.error(ENDPOINTS_LIST_EMPTY); }

        List<NetworkInterface> adInterfaceList = adDataService.getNetworkInterfacesList(null);
        if (adInterfaceList.isEmpty()) { log.error(INTERFACES_LIST_EMPTY); }

        List<AutoDiscoveryServiceMapping> adServiceMappingList = adDataService.getServiceMappingList(handle);
        if (adServiceMappingList.isEmpty()) { log.error(MAPPING_LIST_EMPTY); }

        List<Process> processes = adDataService.getProcessList(null);
        if (processes.isEmpty()) { log.error(PROCESS_LIST_EMPTY); }

        if (entityType.equals(Entity.Host)) {
            List<Connection> connections = new ArrayList<>();

            for (String hid : validEntities) {
                List<Connection> conns = adNetworkConnectionsList.stream()
                        .filter(con -> con.getHostIdentifier().equals(hid))
                        .collect(Collectors.toList());
                connections.addAll(conns);
            }
            for (Connection conn : connections) {
                AutoDiscoveryDiscConnPojo alignedByDirection = byDirection(conn);
                Endpoint endpointIP = adEndpointsList.stream()
                        .filter(IPBean -> (IPBean.getIpAddress().equals(alignedByDirection.getDestinationIp()) && (IPBean.getPortNo() == alignedByDirection.getDestinationPort())))
                        .findFirst().orElse(null);
                if (endpointIP == null) {
                    String msg = "Endpoint (destination) not found to add connection. IP- " + alignedByDirection.getDestinationIp() +
                            ", Port- " + alignedByDirection.getDestinationPort();
                    log.warn(msg);
                    continue;
                }
                NetworkInterface interfaceIP = adInterfaceList.stream()
                        .filter(IPBean -> (IPBean.getInterfaceIP().equals(alignedByDirection.getSourceIp())))
                        .findFirst().orElse(null);
                if (interfaceIP == null) {
                    String msg = "Network interface (source) not found to add connection. IP- " + alignedByDirection.getSourceIp();
                    log.warn(msg);
                    continue;
                }
                List<AutoDiscoveryServiceMapping> sourceIdentifiersList = adServiceMappingList.stream()
                        .filter(mappingBean -> (mappingBean.getServiceMappingIdentifier().equals(interfaceIP.getHostIdentifier())))
                        .collect(Collectors.toList());
                if (sourceIdentifiersList.isEmpty()) {
                    String msg = "Service mapping not found for source to add connection. Identifier- " + interfaceIP.getHostIdentifier();
                    log.warn(msg);
                    continue;
                }
                List<AutoDiscoveryServiceMapping> destinationIdentifiersList = adServiceMappingList.stream()
                        .filter(mappingBean -> (mappingBean.getServiceMappingIdentifier().equals(endpointIP.getHostIdentifier())))
                        .collect(Collectors.toList());
                if (destinationIdentifiersList.isEmpty()) {
                    String msg = "Service mapping not found for destination to add connection. Identifier- " + endpointIP.getHostIdentifier();
                    log.warn(msg);
                    continue;
                }
                allConnections(conn.getHostIdentifier(), alignedByDirection, sourceIdentifiersList, destinationIdentifiersList, handle);
            }

        } else if (entityType.equals(Entity.CompInstance)) {
            List<Connection> connections = new ArrayList<>();
            List<String> validProcess = new ArrayList<>();

            for (String cid : validEntities) {
                processes.stream().filter(process -> process.getProcessIdentifier().equals(cid))
                        .findFirst().ifPresent(pro -> validProcess.add(pro.getHostIdentifier()));
            }

            for (String cid : validProcess) {
                List<Connection> conns = adNetworkConnectionsList.stream()
                        .filter(con -> con.getHostIdentifier().equals(cid))
                        .collect(Collectors.toList());
                connections.addAll(conns);
            }

            for (Connection conn : connections) {
                AutoDiscoveryDiscConnPojo alignedByDirection = byDirection(conn);
                Endpoint endpointIP = adEndpointsList.stream()
                        .filter(IPBean -> (IPBean.getIpAddress().equals(alignedByDirection.getDestinationIp()) && (IPBean.getPortNo() == alignedByDirection.getDestinationPort())))
                        .findFirst().orElse(null);
                if (endpointIP == null) {
                    String msg = "Endpoint (destination) not found to add connection. IP- " + alignedByDirection.getDestinationIp() +
                            ", Port- " + alignedByDirection.getDestinationPort();
                    log.warn(msg);
                    continue;
                }
                NetworkInterface interfaceIP = adInterfaceList.stream()
                        .filter(IPBean -> (IPBean.getInterfaceIP().equals(alignedByDirection.getSourceIp())))
                        .findFirst().orElse(null);
                if (interfaceIP == null) {
                    String msg = "Network interface (source) not found to add connection. IP- " + alignedByDirection.getSourceIp();
                    log.warn(msg);
                    continue;
                }
                List<AutoDiscoveryServiceMapping> sourceIdentifiersList = adServiceMappingList.stream()
                        .filter(mappingBean -> (mappingBean.getServiceMappingIdentifier().equals(interfaceIP.getHostIdentifier())))
                        .collect(Collectors.toList());
                if (sourceIdentifiersList.isEmpty()) {
                    String msg = "Service mapping not found for source to add connection. Identifier- " + interfaceIP.getHostIdentifier();
                    log.warn(msg);
                    continue;
                }
                List<AutoDiscoveryServiceMapping> destinationIdentifiersList = adServiceMappingList.stream()
                        .filter(mappingBean -> (mappingBean.getServiceMappingIdentifier().equals(endpointIP.getHostIdentifier())))
                        .collect(Collectors.toList());
                if (destinationIdentifiersList.isEmpty()) {
                    String msg = "Service mapping not found for destination to add connection. Identifier- " + endpointIP.getHostIdentifier();
                    log.warn(msg);
                    continue;
                }
                allConnections(conn.getHostIdentifier(), alignedByDirection, sourceIdentifiersList, destinationIdentifiersList, handle);
            }
        }
        return 1;
    }

    public void allConnections(String hostIdentifier, AutoDiscoveryDiscConnPojo adConnectionsPojo,
                               List<AutoDiscoveryServiceMapping> sourceIdentifiersList,
                               List<AutoDiscoveryServiceMapping> destinationIdentifiersList, Handle handle) throws DataProcessingException {
        /**
         * Creates a cartesian product of source identifier and destination identifier
         * in case there is more than one mapping present for a particular host in autodisco_service_mapping
         */
        List<AutoDiscoveryDiscoveredConnections> discoveredConnectionsList = new ArrayList<>();
        AutoDiscoveryDataService adDataService = new AutoDiscoveryDataService();
        List<AutoDiscoveryDiscoveredConnections> existingDiscoveredConnections = adDataService.getDiscoveredConnectionsList(handle);

        for (int i = 0; i < sourceIdentifiersList.size(); i++) {
            for (int j = 0; j < destinationIdentifiersList.size(); j++) {

                int finalI = i;
                int finalJ = j;
                // Check if the source identifier -> destination identifier is already present
                // in autodisco_discovered_connections table
                if (existingDiscoveredConnections.stream()
                        .anyMatch(discon -> (discon.getSourceIdentifier().equals(sourceIdentifiersList.get(finalI).getServiceIdentifier())
                                && discon.getDestinationIdentifier().equals(destinationIdentifiersList.get(finalJ).getServiceIdentifier())))) {
                    continue;
                }
                if (sourceIdentifiersList.get(i).getServiceIdentifier().equals(destinationIdentifiersList.get(j).getServiceIdentifier())) {
                    continue;
                }
                AutoDiscoveryDiscoveredConnections discoveredConnections = new AutoDiscoveryDiscoveredConnections();
                discoveredConnections.setSourceIdentifier(sourceIdentifiersList.get(i).getServiceIdentifier());
                discoveredConnections.setHostIdentifier(hostIdentifier);
                discoveredConnections.setDestinationIdentifier(destinationIdentifiersList.get(j).getServiceIdentifier());
                discoveredConnections.setLastUpdatedTime(adConnectionsPojo.getLastUpdatedTime());
                discoveredConnections.setIsDiscovery(1); // 1 for auto discovery & 0 for manual
                discoveredConnectionsList.add(discoveredConnections);
            }
        }
        // adds all the combinations to the autodisco_discovered_connections table
        if (discoveredConnectionsList.isEmpty()) {
            log.warn("No connections found related to entity.");
        } else {
            adDataService.addDiscoveredConnection(discoveredConnectionsList, handle);
        }
    }

    public AutoDiscoveryDiscConnPojo byDirection(Connection adConnection) throws DataProcessingException {
        /**
         * Sets source and destination IPs by direction of the connection [INCOMING_CONNECTION, OUTGOING_CONNECTION]
         * If direction == OUTGOING_CONNECTION
         *     Source_identifier = Service mapped to local ip / port
         *     dest_identifier = Service mapped to remote ip / port
         * else
         *     Source_identifier = Service mapped to remote ip / port
         *     dest_identifier = Service mapped to local ip / port
         */
        Direction direction = adConnection.getDirection();
        AutoDiscoveryDiscConnPojo adConnectionsPojo = new AutoDiscoveryDiscConnPojo();
        if (direction.equals(Direction.OUTGOING_CONNECTION)) {
            adConnectionsPojo.setSourceIp(adConnection.getLocalIP());
            adConnectionsPojo.setSourcePort(adConnection.getLocalPort());
            adConnectionsPojo.setDestinationIp(adConnection.getRemoteIP());
            adConnectionsPojo.setDestinationPort(adConnection.getRemotePort());
            adConnectionsPojo.setLastUpdatedTime(adConnection.getLastUpdatedTime());
            return adConnectionsPojo;
        } else if (direction.equals(Direction.INCOMING_CONNECTION)) {
            adConnectionsPojo.setSourceIp(adConnection.getRemoteIP());
            adConnectionsPojo.setSourcePort(adConnection.getRemotePort());
            adConnectionsPojo.setDestinationIp(adConnection.getLocalIP());
            adConnectionsPojo.setDestinationPort(adConnection.getLocalPort());
            adConnectionsPojo.setLastUpdatedTime(adConnection.getLastUpdatedTime());
            return adConnectionsPojo;
        } else {
            String msg = "Please check direction of connection in autodisco_network_connection table. " +
                    "Local IP- " + adConnection.getLocalIP() + ", Local port- " + adConnection.getLocalPort() +
                    ", Remote IP- " + adConnection.getRemoteIP() + ", Remote port- " + adConnection.getRemotePort();
            log.error(msg);
            throw new DataProcessingException(msg);
        }
    }

    public void deleteServiceMappings(List<String> validEntities, Entity entityType, List<AutoDiscoveryServiceMapping> serviceMappingList,
                                      List<Process> processList, Map<String, String> errorMessages, Handle handle) {
        AutoDiscoveryDataService adDataService = new AutoDiscoveryDataService();
        for (String validEntity : validEntities) {
            if (!serviceMappingList.isEmpty()) {
                List<String> mappings = new ArrayList<>();
                if (errorMessages.get(validEntity) == null) {
                    if (entityType.equals(Entity.Host)) {
                        List<Process> existingCompInstanceList = processList.stream().filter(x -> x.getHostIdentifier().equals(validEntity))
                                .collect(Collectors.toList());
                        List<AutoDiscoveryServiceMapping> existingHostMappings = serviceMappingList.stream()
                                .filter(x -> x.getServiceMappingIdentifier().equals(validEntity)).collect(Collectors.toList());
                        // CompInstance's list of a host
                        if (!existingHostMappings.isEmpty()) {
                            for (Process instance : existingCompInstanceList) {
                                // we will not delete CompInstance mappings if a host is mapped to new service
                                List<AutoDiscoveryServiceMapping> existingCompMappings = serviceMappingList.stream()
                                        .filter(x -> x.getServiceMappingIdentifier()
                                                .equals(instance.getProcessIdentifier())).collect(Collectors.toList());
                                for (AutoDiscoveryServiceMapping mapping : existingCompMappings) {
                                    if (mappings.contains(mapping.getServiceIdentifier())) continue;
                                    mappings.add(mapping.getServiceIdentifier());
                                }
                            }
                            adDataService.deleteExistingMappingsAndConnections(validEntity, mappings, handle);
                        }
                    } else if (entityType.equals(Entity.CompInstance)) {
                        String hostIdentifier = processList.stream().filter(x -> x.getProcessIdentifier().equals(validEntity))
                                .findFirst().get().getHostIdentifier();
                        /**
                         * fetch existing mappings for the compinstance and delete the same mapped to the host since
                         * when a compinstance is mapped to service we also create a mapping from the host to that service
                         */
                        List<AutoDiscoveryServiceMapping> existingCompMappings = serviceMappingList.stream().filter(x -> x.getServiceMappingIdentifier().equals(validEntity)).collect(Collectors.toList());
                        if (!existingCompMappings.isEmpty()) {
                            for (AutoDiscoveryServiceMapping mapping : existingCompMappings) {
                                if (mappings.contains(mapping.getServiceIdentifier())) continue;
                                mappings.add(mapping.getServiceIdentifier());
                            }
                            adDataService.deleteExistingMappingsAndConnections(validEntity, mappings, hostIdentifier, handle);
                        }
                    }
                }
            }
        }
    }

}
