package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.ProducerType;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.common.ValueType;
import com.appnomic.appsone.controlcenter.dao.mysql.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompVersionKpiMappingBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ComputedKpiBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ComputedKpiToKpiMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.KpiBean;
import com.appnomic.appsone.controlcenter.dao.redis.ComponentRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.GroupKpi;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.ClusterOperationEnum;
import com.heal.configuration.pojos.ComponentCommonVersion;
import com.heal.configuration.pojos.ComponentKpiEntity;
import com.heal.configuration.pojos.KpiCategoryDetails;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class AddCustomKpiBL implements BusinessLogic<CustomKpiFromUI, CustomKpiFromUI, IdPojo> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AddCustomKpiBL.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final KPIDataService kpiDataService = new KPIDataService();
    ComponentRepo componentRepo = new ComponentRepo();

    private String userId;
    private int accountId;
    private String accountIdentifier;

    @Override
    public UtilityBean<CustomKpiFromUI> clientValidation(RequestObject requestObject) throws ClientException {

        if (StringUtils.isEmpty(requestObject.getBody())) {
            LOGGER.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);

        if (authToken == null || authToken.trim().isEmpty()) {
            LOGGER.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty");
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (identifier == null || identifier.trim().isEmpty()) {
            LOGGER.error("Account identifier is NULL or empty.");
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String requestBody = requestObject.getBody();
        CustomKpiFromUI customKpi;

        try {
            customKpi = objectMapper.readValue(requestBody, CustomKpiFromUI.class);
        } catch (IOException e) {
            LOGGER.error("Exception while parsing input request body. Details: {}", e.getMessage());
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        if (!customKpi.validateForInsert()) {
            LOGGER.error("Validation failure of custom KPI details");
            throw new ClientException("Validation failure of custom KPI details");
        }

        return UtilityBean.<CustomKpiFromUI>builder()
                .accountIdentifier(identifier)
                .authToken(authToken)
                .pojoObject(customKpi)
                .build();
    }

    @Override
    public CustomKpiFromUI serverValidation(UtilityBean<CustomKpiFromUI> utilityBean) throws ServerException {
        userId = ValidationUtils.getUserId(utilityBean.getAuthToken());

        if (userId == null) {
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }

        accountId = AccountDataService.getAccountByIdentifier(utilityBean.getAccountIdentifier(), null);

        if (accountId <= 0) {
            LOGGER.error("Account identifier [{}] is invalid", utilityBean.getAccountIdentifier());
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }

        CustomKpiFromUI customKpiFromUI = utilityBean.getPojoObject();
        accountIdentifier = utilityBean.getAccountIdentifier();

        if (customKpiFromUI.getId() > 0) {
            LOGGER.error("kpiId is invalid. Reason: kpiId should be -1 to add the custom KPI.");
            throw new ServerException("kpiId is invalid.");
        }

        if (customKpiFromUI.getIdentifier() == null) {
            String identifier = customKpiFromUI.getName().toLowerCase().replace(" ","_");
            customKpiFromUI.setIdentifier(identifier.concat("-").concat(Long.toString(Instant.now().toEpochMilli())));
        } else if (kpiDataService.checkForKpiUsingIdentifier(customKpiFromUI.getIdentifier(), null) != 0) {
            LOGGER.error("KPI with the specified identifier [{}] already exists", customKpiFromUI.getIdentifier());
            throw new ServerException("KPI with specified identifier already exists");
        }


        int kpiExists = kpiDataService.checkForKpiUsingName(customKpiFromUI.getName(), accountId, null);

        if (kpiExists != 0) {
            LOGGER.error("KPI with name [{}] already exists", customKpiFromUI.getName());
            throw new ServerException("KPI with specified name already exists");
        }

        ViewTypes kpiTypes = MasterCache.getMstTypeForSubTypeName("KPI", customKpiFromUI.getKpiType());

        if (null == kpiTypes || kpiTypes.getSubTypeName().equalsIgnoreCase("Transaction") ||
                (customKpiFromUI.getGroupKpiDetails() != null && !customKpiFromUI.getKpiType().equalsIgnoreCase(customKpiFromUI.getGroupKpiDetails().getKpiType()))) {
            LOGGER.error("KPI type validation failure. Reason: kpiType should be one of Availability, Core, Forensic, " +
                    "FileWatch or ConfigWatch and it should be same as the kpi group type specified.");
            throw new ServerException("KPI type validation failure.");
        }

        ViewTypes dataTypes = ValidationUtils.getDataType(kpiTypes.getSubTypeName(), customKpiFromUI.getDataType());

        if (null == dataTypes) {
            LOGGER.error("dataType validation failure. Reason: dataType [{}] is not available for kpiType [{}].", customKpiFromUI.getDataType(), customKpiFromUI.getKpiType());
            throw new ServerException("dataType of the KPI is invalid");
        }

        ValidationUtils.validateOperationTypes(customKpiFromUI.getClusterOperation(), customKpiFromUI.getRollupOperation());

        ComponentDetailBean componentDetailBean = ComponentDataService.checkIfComponentExists(customKpiFromUI.getComponentId(), customKpiFromUI.getComponentTypeId(),
                customKpiFromUI.getComponentCommonVersionId(), null);

        if (componentDetailBean == null) {
            LOGGER.error("componentId [{}], componentTypeId [{}], and componentCommonVersionId [{}] combination is invalid", customKpiFromUI.getComponentId(), customKpiFromUI.getComponentTypeId(),
                    customKpiFromUI.getComponentCommonVersionId());
            throw new ServerException("componentId, componentTypeId, and componentCommonVersionId combination is invalid");
        }
        customKpiFromUI.setComponentCommonVersionName(componentDetailBean.getCommonVersionName());
        customKpiFromUI.setComponentName(componentDetailBean.getComponentName());
        customKpiFromUI.setComponentVersionId(componentDetailBean.getComponentVersionId());

        if (null != customKpiFromUI.getComputedKpiDetails()) {
            ComputedKpiDetails computedKpiDetails = customKpiFromUI.getComputedKpiDetails();
            int kpisUsed = new BindInDataService().getKpisUsingId(computedKpiDetails.getKpisUsed(), accountId, null);

            if (kpisUsed != computedKpiDetails.getKpisUsed().size()) {
                LOGGER.error("Some or all the KPIs used in the computation do not exist");
                throw new ServerException("Some or all the KPIs used in the computation do not exist");
            }
        }

        try {
            CategoryDetailBean category = new CategoryDataService().getCategoryForKpiTypeAndCategoryId(accountId,
                    MasterCache.getMstTypeForSubTypeName("KPI", customKpiFromUI.getKpiType()).getSubTypeId(),
                    customKpiFromUI.getKpiCategoryDetails().getId());
            if (category != null) {
                customKpiFromUI.getKpiCategoryDetails().setIdentifier(category.getIdentifier());
            } else {
                LOGGER.error("Invalid category details provided.");
                throw new ServerException("Invalid category details.");
            }
        } catch (ControlCenterException e) {
            throw new ServerException(e.getMessage());
        }

        GroupKpi groupKpi = customKpiFromUI.getGroupKpiDetails();

        String groupKpiIdentifier;
        if (groupKpi != null) {

            int groupKpiExists = kpiDataService.checkForGroupKpiUsingName(groupKpi.getGroupKpiName(), accountId, null);

            if (groupKpi.getId() <= 0 && groupKpiExists != 0) {
                LOGGER.error("KPI group with name [{}] already exists", groupKpi.getGroupKpiName());
                throw new ServerException("KPI group with name already exists");
            } else if (groupKpi.getId() > 0 && groupKpiExists == 0) {
                LOGGER.error("KPI group with name [{}] does not exist", groupKpi.getGroupKpiName());
                throw new ServerException("KPI group with name does not exist");
            }

            ValidationUtils.validateAggregationTypes(customKpiFromUI.getClusterAggregation(), customKpiFromUI.getInstanceAggregation());

            if (groupKpi.getId() <= 0) {
                groupKpi.setGroupKpiIdentifier(groupKpi.getGroupKpiName().concat("-").concat(Long.toString(Instant.now().toEpochMilli())));
            }else {
                try {
                    groupKpiIdentifier = kpiDataService.getGroupKpiIdentifier(groupKpi.getId());
                    if (groupKpiIdentifier != null) {
                        groupKpi.setGroupKpiIdentifier(groupKpiIdentifier);
                    }
                } catch (ControlCenterException e) {
                    LOGGER.error("The group kpi identifier not found for given kpiId: {}", groupKpi.getId());
                    throw new ServerException(e.getMessage());
                }
            }
        }

        if (customKpiFromUI.getValueType() != null && !customKpiFromUI.getKpiType().equalsIgnoreCase(Constants.CORE_KPI_TYPE)
                && customKpiFromUI.getValueType() == ValueType.DELTA) {
            LOGGER.error("valueType validation failure. Reason: valueType [{}] is not available for kpiType [{}].", customKpiFromUI.getValueType(), customKpiFromUI.getKpiType());
            throw new ServerException("valueType of the KPI is invalid");
        }
        return customKpiFromUI;
    }

    @Override
    public IdPojo process(CustomKpiFromUI bean) throws DataProcessingException {
        DBI dbi = MySQLConnectionManager.getInstance().getHandle();
        IdPojo id;
        try {
            id = dbi.inTransaction((conn, status) -> addKpiDetails(bean, userId, accountId, conn));
        } catch (Exception e) {
            if (Throwables.getRootCause(e) instanceof DataProcessingException) {
                throw (DataProcessingException) Throwables.getRootCause(e);
            } else {
                throw e;
            }
        }
        return id;
    }

    private IdPojo addKpiDetails(CustomKpiFromUI customKpiFromUI, String userId, int accountId, Handle handle) throws DataProcessingException {
        int groupKpiId = 0;
        if (customKpiFromUI.getGroupKpiDetails() != null) {
            if (customKpiFromUI.getGroupKpiDetails().getId() <= 0) {
                int groupKpiTypeId = MasterCache.getMstTypeForSubTypeName("KPI", customKpiFromUI.getGroupKpiDetails().getKpiType()).getSubTypeId();

                MasterKpiGroupBean groupKpiBean = MasterKpiGroupBean.builder()
                        .name(customKpiFromUI.getGroupKpiDetails().getGroupKpiName())
                        .identifier(customKpiFromUI.getGroupKpiDetails().getGroupKpiIdentifier())
                        .description(customKpiFromUI.getGroupKpiDetails().getDescription())
                        .createdTime(DateTimeUtil.getCurrentTimestampInGMT())
                        .updatedTime(DateTimeUtil.getCurrentTimestampInGMT())
                        .userDetailsId(userId)
                        .accountId(accountId)
                        .kpiTypeId(groupKpiTypeId)
                        .discovery(customKpiFromUI.getGroupKpiDetails().getDiscovery())
                        .isCustom(customKpiFromUI.getGroupKpiDetails().getCustom())
                        .status(1)
                        .build();

                groupKpiId = kpiDataService.createGroupKpi(groupKpiBean, handle);

                if (groupKpiId < 1) {
                    throw new DataProcessingException("Error while adding Group KPI");
                }
            } else {
                groupKpiId = customKpiFromUI.getGroupKpiDetails().getId();
            }
        }

        int kpiDataTypeId = MasterCache.getMstTypeForSubTypeName("KPI", customKpiFromUI.getKpiType()).getSubTypeId();
        int clusterAggType = MasterCache.getMstTypeForSubTypeName("AggregationType", customKpiFromUI.getClusterAggregation()).getSubTypeId();
        int instAggType = MasterCache.getMstTypeForSubTypeName("AggregationType", customKpiFromUI.getInstanceAggregation()).getSubTypeId();

        KpiBean kpiBean = KpiBean.builder()
                .status(1)
                .isCustom(1)
                .isComputed(customKpiFromUI.getComputedKpiDetails() != null ? 1 : 0)
                .kpiTypeId(kpiDataTypeId)
                .accountId(accountId)
                .name(customKpiFromUI.getName())
                .identifier(customKpiFromUI.getIdentifier())
                .description(customKpiFromUI.getDescription())
                .groupKpiId(groupKpiId)
                .userId(userId)
                .dataType(customKpiFromUI.getDataType())
                .valueType(customKpiFromUI.getValueType().toString())
                .measureUnits(customKpiFromUI.getKpiUnit())
                .clusterOperation(customKpiFromUI.getClusterOperation())
                .rollupOperation(customKpiFromUI.getRollupOperation())
                .instanceAggregation(instAggType)
                .clusterAggregation(clusterAggType)
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .cronExpression(customKpiFromUI.getCronExpression())
                .resetDeltaValue(customKpiFromUI.getResetDeltaValue())
                .deltaPerSec(customKpiFromUI.getDeltaPerSec())
                .build();

        int kpiId = kpiDataService.createKpi(kpiBean, handle);

        if (kpiId < 1) {
            throw new DataProcessingException("Error while adding KPI details");
        }

        TagDetailsBean tagDetailsBean = MasterCache.getTagDetails("Category");

        if (null != tagDetailsBean) {
            TagMappingDetails tagMappingDetails = TagMappingDetails.builder()
                    .tagId(tagDetailsBean.getId())
                    .tagKey(String.valueOf(customKpiFromUI.getKpiCategoryDetails().getId()))
                    .tagValue(customKpiFromUI.getKpiCategoryDetails().getIdentifier())
                    .objectRefTable("mst_kpi_details")
                    .objectId(kpiId)
                    .accountId(accountId)
                    .userDetailsId(userId)
                    .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .build();

            int tagId = TagsDataService.createDaoAndAddTagMappingDetails(tagMappingDetails, handle);

            if (tagId < 1) {
                throw new DataProcessingException("Error while tagging KPI to provided category");
            }
        }

        CompVersionKpiMappingBean compVersionKpiMapping = CompVersionKpiMappingBean.builder()
                .isCustom(1)
                .kpiDetailsId(kpiId)
                .doAnalytics(customKpiFromUI.getAvailableForAnalytics())
                .commonVersionId(customKpiFromUI.getComponentCommonVersionId())
                .userDetailsId(userId)
                .defaultCollectionInterval(customKpiFromUI.getCollectionInterval())
                .status(1)
                .componentId(customKpiFromUI.getComponentId())
                .componentTypeId(customKpiFromUI.getComponentTypeId())
                .createdTime(DateTimeUtil.getCurrentTimestampInGMT())
                .updatedTime(DateTimeUtil.getCurrentTimestampInGMT())
                .build();

        int id = MasterDataService.insertIntoComponentVersionKpiMapping(compVersionKpiMapping, handle);

        if (id < 1) {
            throw new DataProcessingException("Error while mapping KPI to provided component version");
        }

        if (customKpiFromUI.getComputedKpiDetails() != null) {
            ComputedKpiBean computedKpiBean = ComputedKpiBean.builder()
                    .kpiDetailsId(kpiId)
                    .formula(customKpiFromUI.getComputedKpiDetails().getFormula())
                    .displayFormula(customKpiFromUI.getComputedKpiDetails().getDisplayFormula())
                    .accountId(accountId)
                    .userDetailsId(userId)
                    .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .build();

            int computedKpiId = kpiDataService.addComputedKpi(computedKpiBean, handle);

            if (computedKpiId < 1) {
                throw new DataProcessingException(("Error while adding computed KPI"));
            }

            List<ComputedKpiToKpiMapping> mappingList = customKpiFromUI.getComputedKpiDetails().getKpisUsed()
                    .parallelStream()
                    .map(m -> ComputedKpiToKpiMapping.builder()
                            .accountId(accountId)
                            .userDetailsId(userId)
                            .computedKpiDetailsId(computedKpiId)
                            .computedKpiId(kpiId)
                            .baseKpiId(m)
                            .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                            .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                            .build())
                    .collect(Collectors.toList());

            int[] mappingCount = kpiDataService.mapComputedKpiToMstKpi(mappingList, handle);

            if (mappingCount == null || mappingCount.length != customKpiFromUI.getComputedKpiDetails().getKpisUsed().size()) {
                throw new DataProcessingException(("Error while mapping computed KPI to parent KPIs"));
            }


            int producerId = ProducerDataService.getProducerByTypeId(ProducerType.EXTERNAL.name(), kpiDataTypeId, groupKpiId == 0 ? 0 : 1, null);
            if (producerId <= 0) {
                throw new DataProcessingException("Error while fetching producer ID");
            }

            MasterProducerKpiMappingBean producerKpiMappingBean = MasterProducerKpiMappingBean.builder()
                    .producerId(producerId)
                    .isDefault(1)
                    .userDetailsId(userId)
                    .accountId(accountId)
                    .kpiDetailsId(kpiId)
                    .componentId(customKpiFromUI.getComponentId())
                    .componentTypeId(customKpiFromUI.getComponentTypeId())
                    .componentVersionId(customKpiFromUI.getComponentVersionId())
                    .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .build();

            int producerMapping = ProducerDataService.addProducerMapping(producerKpiMappingBean, handle);

            if (producerMapping <= 0) {
                throw new DataProcessingException("Error while adding producer KPI mapping");
            }


            CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
            List<Integer> compInstanceIds;
            try {
                compInstanceIds = compInstanceDataService.getCompInstanceIdsUsingComponentDetails(customKpiFromUI.getComponentId(),
                        customKpiFromUI.getComponentTypeId(), customKpiFromUI.getComponentCommonVersionId(), accountId, handle);
            } catch (ControlCenterException e) {
                throw new DataProcessingException(e.getMessage());
            }

            if (!compInstanceIds.isEmpty()) {
                if (groupKpiId == 0) {
                    List<CompInstanceKpiDetailsBean> compInstanceKpiDetailsBeans = compInstanceIds.parallelStream()
                            .map(i -> CompInstanceKpiDetailsBean.builder()
                                    .compInstanceId(i)
                                    .mstProducerKpiMappingId(producerMapping)
                                    .mstKpiDetailsId(kpiId)
                                    .mstProducerId(producerId)
                                    .userDetailsId(userId)
                                    .collectionInterval(customKpiFromUI.getCollectionInterval())
                                    .status(1)
                                    .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                                    .build())
                            .collect(Collectors.toList());

                    int[] compInstanceMappings = compInstanceDataService.addMultipleNonGroupComponentInstanceKPI(compInstanceKpiDetailsBeans, handle);

                    if (compInstanceMappings == null || compInstanceMappings.length == 0) {
                        throw new DataProcessingException("Error while adding component instance non-group KPI mapping");
                    }
                } else {
                    int finalGroupKpiId = groupKpiId;
                    List<CompInstanceKpiGroupDetailsBean> compInstanceKpiGroupDetailsBeans = compInstanceIds.parallelStream()
                            .map(i -> CompInstanceKpiGroupDetailsBean.builder()
                                    .attributeValue(Constants.ALL)
                                    .status(1)
                                    .userDetailsId(userId)
                                    .compInstanceId(i)
                                    .mstProducerKpiMappingId(producerMapping)
                                    .collectionInterval(customKpiFromUI.getCollectionInterval())
                                    .mstKpiDetailsId(kpiId)
                                    .isDiscovery(1)
                                    .kpiGroupName(customKpiFromUI.getGroupKpiDetails().getGroupKpiName())
                                    .mstKpiGroupId(finalGroupKpiId)
                                    .mstProducerId(producerId)
                                    .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                                    .aliasName(Constants.ALL)
                                    .build())
                            .collect(Collectors.toList());
                    int[] compInstanceGroupMappings = compInstanceDataService.addMultipleGroupCompInstanceKpiDetails(compInstanceKpiGroupDetailsBeans, handle);

                    if (compInstanceGroupMappings == null || compInstanceGroupMappings.length == 0) {
                        throw new DataProcessingException("Error while adding component instance group KPI mapping");
                    }
                }
            }
        } else {
            if (customKpiFromUI.getGroupKpiDetails() != null && customKpiFromUI.getGroupKpiDetails().getId() > 0) {
                try {
                    List<MasterProducerKpiMappingBean> kpiProducers = ProducerDataService.getProducerIdsForKpiGroupId
                            (customKpiFromUI.getGroupKpiDetails().getId(), accountId, handle);

                    if (kpiProducers.isEmpty()) {
                        LOGGER.info("The KPI group specified is not mapped to any producers. Hence the custom KPI created " +
                                "will not be mapped to any producer.");
                    }

                    kpiProducers.parallelStream().forEach(producer -> {
                        producer.setUserDetailsId(userId);
                        producer.setAccountId(accountId);
                        producer.setKpiDetailsId(kpiId);
                        producer.setComponentId(customKpiFromUI.getComponentId());
                        producer.setComponentTypeId(customKpiFromUI.getComponentTypeId());
                        producer.setComponentVersionId(customKpiFromUI.getComponentCommonVersionId());
                        producer.setCreatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                        producer.setUpdatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
                    });
                    ProducerDataService.addKpiProducersMappings(kpiProducers, handle);

                } catch (ControlCenterException e) {
                    throw new DataProcessingException(e.getMessage());
                }
            }
        }
        addKpiDetailsInRedis(customKpiFromUI, kpiId);

        return IdPojo.builder().id(kpiId).name(customKpiFromUI.getName()).identifier(customKpiFromUI.getIdentifier()).build();
    }

    private void addKpiDetailsInRedis(CustomKpiFromUI customKpiFromUI, int kpiId) {
        List<ComponentKpiEntity> existingComponentKpi = componentRepo.getComponentKpiDetails(accountIdentifier, customKpiFromUI.getComponentName());
        if(!existingComponentKpi.isEmpty() && existingComponentKpi.parallelStream().anyMatch(kpi -> kpi.getId() == kpiId)){
            LOGGER.debug("The kpi detail already exist for the given kpiId: {}, componentName: {} and accountIdentifier:{}", kpiId, customKpiFromUI.getComponentName(), accountIdentifier);
            return;
        }

        KpiCategoryDetails kpiCategoryDetails = KpiCategoryDetails.builder()
                .id(customKpiFromUI.getKpiCategoryDetails().getId())
                .identifier(customKpiFromUI.getKpiCategoryDetails().getIdentifier())
                .name(customKpiFromUI.getKpiCategoryDetails().getName())
                .isWorkLoad(customKpiFromUI.getKpiCategoryDetails().isWorkLoad())
                .build();

        ComponentCommonVersion commonVersionObject = ComponentCommonVersion.builder()
                .commonVersionId(customKpiFromUI.getComponentCommonVersionId())
                .commonVersionName(customKpiFromUI.getComponentCommonVersionName())
                .collectionInterval(customKpiFromUI.getCollectionInterval())
                .build();

        List<ComponentCommonVersion> componentCommonVersionsList = new ArrayList<>();
        componentCommonVersionsList.add(commonVersionObject);

        ComponentKpiEntity newKpiDetail = ComponentKpiEntity.builder()
                .name(customKpiFromUI.getName())
                .id(kpiId)
                .type(customKpiFromUI.getKpiType())
                .status(customKpiFromUI.getStatus())
                .unit(customKpiFromUI.getKpiUnit())
                .aggOperation(ClusterOperationEnum.findByClusterOperation(customKpiFromUI.getClusterOperation()))
                .rollupOperation(ClusterOperationEnum.findByClusterOperation(customKpiFromUI.getRollupOperation()))
                .clusterAggType(customKpiFromUI.getClusterAggregation())
                .instanceAggType(customKpiFromUI.getInstanceAggregation())
                .identifier(customKpiFromUI.getIdentifier())
                .availableForAnalytics(customKpiFromUI.getAvailableForAnalytics())
                .categoryDetails(kpiCategoryDetails)
                .valueType(customKpiFromUI.getValueType().getType())
                .dataType(customKpiFromUI.getDataType())
                .resetDeltaValue(customKpiFromUI.getResetDeltaValue())
                .deltaPerSec(customKpiFromUI.getDeltaPerSec())
                .cronExpression(customKpiFromUI.getCronExpression())
                .description(customKpiFromUI.getDescription())
                .isComputed(customKpiFromUI.getComputedKpiDetails() != null? 1 :0 )
                .commonVersionDetails(componentCommonVersionsList)
                .build();
        if(customKpiFromUI.getGroupKpiDetails() != null){
            newKpiDetail.setDiscovery(customKpiFromUI.getGroupKpiDetails().getDiscovery());
            newKpiDetail.setGroupName(customKpiFromUI.getGroupKpiDetails().getGroupKpiName());
            newKpiDetail.setGroupId(customKpiFromUI.getGroupKpiDetails().getId());
            newKpiDetail.setGroupIdentifier(customKpiFromUI.getGroupKpiDetails().getGroupKpiIdentifier());
            newKpiDetail.setIsGroup(customKpiFromUI.getGroupKpiDetails().getId() > 0);
            newKpiDetail.setGroupStatus(1);
        }
        if(customKpiFromUI.getComputedKpiDetails() != null){
            com.heal.configuration.pojos.ComputedKpiPojo  computedKpiPojo = com.heal.configuration.pojos.ComputedKpiPojo.builder()
                    .computedKpiId(kpiId)
                    .displayFormula(customKpiFromUI.getComputedKpiDetails().getFormula())
                    .formula(customKpiFromUI.getComputedKpiDetails().getDisplayFormula())
                    .baseKpiIds(customKpiFromUI.getComputedKpiDetails().getKpisUsed())
                    .build();
            computedKpiPojo.getBaseKpiIds().forEach(baseKpi -> {
                ComponentKpiEntity componentKpiEntity = existingComponentKpi.parallelStream().filter(kpi -> kpi.getId() == baseKpi).findAny().orElse(null);
                if(componentKpiEntity != null){
                    componentKpiEntity.setIsBaseMetric(1);
                }
            });
            newKpiDetail.setComputedKpiPojo(computedKpiPojo);
        }
        existingComponentKpi.add(newKpiDetail);
        componentRepo.updateComponentKpiDetails(existingComponentKpi, accountIdentifier, customKpiFromUI.getComponentName());
    }
}
