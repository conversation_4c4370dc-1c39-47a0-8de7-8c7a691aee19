package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UserAccountBean;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.WebHookDataBean;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.WebHookPojo;
import com.appnomic.appsone.controlcenter.dao.mysql.WebHookDataService;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import org.skife.jdbi.v2.DBI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.ParseException;


public class WebHookBL {

    private WebHookBL() {
    }

    private static final ObjectMapper obj_mapper = CommonUtils.getObjectMapperWithHtmlEncoder();
    private static final Logger logger = LoggerFactory.getLogger(WebHookBL.class);

    public static WebHookPojo addUpdateClientValidations(Request request) throws RequestException {

        ValidationUtils.commonClientValidations(request);

        if (StringUtils.isEmpty(request.body())) {
            logger.error(UIMessages.REQUEST_NULL);
            throw new RequestException(UIMessages.REQUEST_NULL);
        }

        WebHookPojo webHook;

        try {
            webHook = obj_mapper.readValue(request.body(),
                    new TypeReference<WebHookPojo>() {
                    });

        } catch (IOException e) {
            logger.error(UIMessages.JSON_INVALID + " : {}",e.getMessage());
            throw new RequestException(UIMessages.JSON_INVALID);
        }

        if(webHook == null){
            logger.error(UIMessages.REQUEST_NULL);
            throw new RequestException(UIMessages.REQUEST_NULL);
        }

        webHook.validate();

        return webHook;
    }

    public static WebHookDataBean addUpdateServerValidations(WebHookPojo webHook, String authToken, String accountIdentifier) throws RequestException, ParseException {

        UserAccountBean userAccBean = ValidationUtils.commonServerValidations(authToken, accountIdentifier);
        String userId = userAccBean.getUserId();
        int accountId = userAccBean.getAccount().getId();


        Timestamp date = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());

        return WebHookDataBean.builder()
                    .url(webHook.getUrl().trim())
                    .accountId(accountId)
                    .userDetailsId(userId)
                    .createdTime(date)
                    .updatedTime(date)
                    .build();

    }


    public static int add(WebHookDataBean bean) throws ControlCenterException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();

            return dbi.inTransaction((conn, status) -> {
                if(WebHookDataService.getWebHook(bean.getAccountId(), conn) != null)
                    throw new Exception("WebHook URL already registered for the account. Can't register one more..");
                return WebHookDataService.addWebHook(bean, conn);
            });
        } catch (Exception e) {
            throw new ControlCenterException(Throwables.getRootCause(e).getMessage());
        }
    }

    public static void update(WebHookDataBean bean) throws ControlCenterException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();

             dbi.inTransaction((conn, status) -> {
                int updated = WebHookDataService.updateWebHook(bean.getUrl(), bean.getUpdatedTime(), bean.getAccountId(), bean.getUserDetailsId(), conn);
                if(updated == 0)
                    throw new Exception("Update failed. No WebHook URL already registered for the account..");
                return updated;
            });
        } catch (Exception e) {
            throw new ControlCenterException(Throwables.getRootCause(e).getMessage());
        }
    }



    public static void remGetClientValidations(Request request) throws RequestException {

        ValidationUtils.commonClientValidations(request);

    }

    public static WebHookDataBean remGetServerValidations(String authToken, String accountIdentifier) throws RequestException {

        UserAccountBean bean = ValidationUtils.commonServerValidations(authToken, accountIdentifier);

        return WebHookDataBean.builder()
                .accountId(bean.getAccount().getId())
                .userDetailsId(bean.getUserId())
                .build();

    }

    public static void remove(WebHookDataBean bean) throws ControlCenterException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            int deleted = dbi.inTransaction((conn, status) ->WebHookDataService.remWebHook(bean.getAccountId(), conn));
            if(deleted == 0)
                throw new Exception("Remove failed. No WebHook URL already registered for the account..");

        } catch (Exception e) {
            throw new ControlCenterException(Throwables.getRootCause(e).getMessage());
        }
    }

    public static WebHookDataBean get(WebHookDataBean bean) throws ControlCenterException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            return dbi.inTransaction((conn, status) -> WebHookDataService.getWebHook(bean.getAccountId(), conn));

        } catch (Exception e) {
            throw new ControlCenterException(Throwables.getRootCause(e).getMessage());
        }
    }
}
