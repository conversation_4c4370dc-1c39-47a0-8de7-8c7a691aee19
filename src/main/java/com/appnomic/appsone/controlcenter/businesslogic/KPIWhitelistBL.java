package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.ControllerDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.WhitelistDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.PluginKPIServiceMapping;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.pojo.WhitelistPojo;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Slf4j
public class KPIWhitelistBL implements BusinessLogic<List<String>, UtilityBean<List<String>>, WhitelistPojo> {
    private static final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    @Override
    public UtilityBean<List<String>> clientValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }
        String authToken = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (StringUtils.isEmpty(authToken)) {
            log.error(UIMessages.AUTH_KEY_EMPTY);
            throw new ClientException(UIMessages.AUTH_KEY_EMPTY);
        }
        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);

        if (StringUtils.isEmpty(identifier)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String appId = requestObject.getParams().get(Constants.APPLICATION_ID);
        if (StringUtils.isEmpty(appId)) {
            log.error(UIMessages.APPLICATION_ID_EMPTY);
            throw new ClientException(UIMessages.APPLICATION_ID_EMPTY);
        }

        try {
            Integer.parseInt(appId);
        } catch (NumberFormatException e) {
            log.error(UIMessages.APPLICATION_ID_IS_NOT_NUMBER);
            throw new ClientException(UIMessages.APPLICATION_ID_IS_NOT_NUMBER);
        }

        String serviceId = requestObject.getParams().get(Constants.SERVICE_ID);
        if (StringUtils.isEmpty(serviceId)) {
            log.error(UIMessages.SERVICE_EMPTY_ERROR);
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        }

        try {
            Integer.parseInt(serviceId);
        } catch (NumberFormatException e) {
            log.error(UIMessages.SERVICE_ID_IS_NOT_NUMBER);
            throw new ClientException(UIMessages.SERVICE_ID_IS_NOT_NUMBER);
        }

        List<String> kpiWhitelist = null;
        if (!StringUtils.isEmpty(requestObject.getBody())) {
            try {
                kpiWhitelist = objectMapper.readValue(requestObject.getBody(), new TypeReference<List<String>>() {
                });
            } catch (IOException e) {
                log.error("IOException encountered while parsing request body. Details {}", e.getMessage());
                throw new ClientException(e.getMessage());
            }
        }
        return UtilityBean.<List<String>>builder()
                .accountIdentifier(identifier)
                .serviceId(requestObject.getParams().get(Constants.SERVICE_ID))
                .applicationId(requestObject.getParams().get(Constants.APPLICATION_ID))
                .authToken(authToken)
                .pojoObject(kpiWhitelist).build();
    }

    @Override
    public UtilityBean<List<String>> serverValidation(UtilityBean<List<String>> utilityBean) throws ServerException {
        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            log.error(UIMessages.AUTH_KEY_INVALID);
            throw new ServerException(UIMessages.AUTH_KEY_INVALID);
        }
        AccountBean account = ValidationUtils.validAndGetAccount(utilityBean.getAccountIdentifier());
        if (account == null) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }
        int accountId = account.getId();
        String appId = utilityBean.getApplicationId();
        ControllerDataService ctrldataService = new ControllerDataService();
        int appTypeId = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT, Constants.APPLICATION_CONTROLLER_TYPE).getSubTypeId();
        ControllerBean application = ctrldataService.getControllerByIdAndTypeId(Integer.parseInt(appId), accountId, appTypeId, null);

        if (application == null) {
            log.error("Application with ID [{}] is unavailable for account [{}]", appId, accountId);
            throw new ServerException("ApplicationId is not present for the specified account.");
        }
        String svcId = utilityBean.getServiceId();
        int svcTypeId = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT, Constants.SERVICES_CONTROLLER_TYPE).getSubTypeId();
        ControllerBean service = ctrldataService.getControllerByIdAndTypeId(Integer.parseInt(svcId), accountId, svcTypeId, null);

        if (service == null) {
            log.error("Service with ID [{}] is unavailable for account [{}]", svcId, accountId);
            throw new ServerException("ServiceId is not present for the specified account.");
        }

        if (!ValidationUtils.isValidMapping(accountId, application.getIdentifier(), Integer.parseInt(svcId))) {
            log.error("Service with Name [{}] is not mapped to the application [{}]", service.getName(), application.getName());
            throw new ServerException("Service Name is not mapped to the application.");
        }
        List<String> userKPINames = utilityBean.getPojoObject();
        WhitelistDataService dataService = new WhitelistDataService();
        Optional<String> kpiNotFound = userKPINames.stream()
                .filter(kpiName -> dataService.getKpiId(kpiName) == 0)
                .findAny();
        if (kpiNotFound.isPresent()) {
            log.error("Invalid kpiName [{}] is unavailable for account [{}]", kpiNotFound.get(), accountId);
            throw new ServerException("Invalid kpiName present in request body/payload");
        }

        utilityBean.setAccount(account);
        return utilityBean;
    }

    @Override
    public WhitelistPojo process(UtilityBean<List<String>> bean) throws DataProcessingException {
        return null;
    }

    public String addWhitelist(UtilityBean<List<String>> whitelistBeans) throws DataProcessingException {
        int[] keys;
        try {
            WhitelistDataService dataService = new WhitelistDataService();
            List<PluginKPIServiceMapping> kpiServiceList = new ArrayList<>();
            for (String kpiName : whitelistBeans.getPojoObject()) {
                PluginKPIServiceMapping mapping = new PluginKPIServiceMapping();
                Integer kpiIdFound = dataService.getKpiId(kpiName);
                if (kpiIdFound != 0) {
                    Integer svcId = Integer.valueOf(whitelistBeans.getServiceId());
                    mapping.setKpiId(kpiIdFound);
                    mapping.setServiceId(svcId);
                    mapping.setPluginWhitelistStatus(true);
                    PluginKPIServiceMapping found = dataService.getPluginKPIServiceMapping(kpiIdFound, svcId);
                    if (found == null) {
                        kpiServiceList.add(mapping);
                    }
                }
            }
            if (kpiServiceList.isEmpty() == false) {
                keys = dataService.addKPIWhitelist(kpiServiceList);
            }
            else {
                return "";
            }
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        return Arrays.toString(keys);
    }

    public String updateWhitelist(UtilityBean<List<String>> whitelistBeans) throws DataProcessingException {
        int[] keys;
        try {
            WhitelistDataService dataService = new WhitelistDataService();
            List<PluginKPIServiceMapping> kpiServiceList = new ArrayList<>();
            for (String kpiName : whitelistBeans.getPojoObject()) {
                PluginKPIServiceMapping mapping = new PluginKPIServiceMapping();
                Integer kpiIdFound = dataService.getKpiId(kpiName);
                if (kpiIdFound != 0) {
                    Integer svcId = Integer.valueOf(whitelistBeans.getServiceId());
                    mapping.setKpiId(kpiIdFound);
                    mapping.setServiceId(svcId);
                    mapping.setPluginWhitelistStatus(true);
                    PluginKPIServiceMapping found = dataService.getPluginKPIServiceMapping(kpiIdFound, svcId);
                    if (found == null) {
                        kpiServiceList.add(mapping);
                    }
                }
            }
            if (kpiServiceList.isEmpty() == false) {
                keys = dataService.updateKPIWhitelist(kpiServiceList);
            }
            else {
                return "";
            }
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        return Arrays.toString(keys);
    }

    public String deleteWhitelist(UtilityBean<List<String>> whitelistBeans) throws DataProcessingException {
        int[] keys;
        try {
            WhitelistDataService dataService = new WhitelistDataService();
            List<PluginKPIServiceMapping> kpiServiceList = new ArrayList<>();
            for (String kpiName : whitelistBeans.getPojoObject()) {
                PluginKPIServiceMapping mapping = new PluginKPIServiceMapping();
                mapping.setServiceId(Integer.valueOf(whitelistBeans.getServiceId()));
                mapping.setPluginWhitelistStatus(true);
                Integer kpiIdFound = dataService.getKpiId(kpiName);
                if (kpiIdFound != 0) {
                    mapping.setKpiId(kpiIdFound);
                    kpiServiceList.add(mapping);
                }
            }
            if (kpiServiceList.isEmpty() == false) {
                keys = dataService.deleteKPIWhitelist(kpiServiceList);
            }
            else {
                return "";
            }
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        return Arrays.toString(keys);
    }
}