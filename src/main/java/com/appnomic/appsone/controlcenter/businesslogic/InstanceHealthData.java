package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.UserAccessDetails;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.opensearch.ServiceRepo;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.exceptions.InstanceHealthServiceException;
import com.appnomic.appsone.controlcenter.pojo.InstanceHealthDetails;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.UserValidationUtil;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.CompInstClusterDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.Comparator.nullsFirst;

public class InstanceHealthData {

    private static final Logger LOGGER = LoggerFactory.getLogger(InstanceHealthData.class);
    private static int timeInMin = ConfProperties.getInt(Constants.TIME_BRACKET_PROP, Constants.TIME_BRACKET_DEF);

    public List<InstanceHealthDetails> getInstanceHealthData(AccountBean account, String userId) throws InstanceHealthServiceException {

        long st = System.currentTimeMillis();
        InstanceRepo instanceRepo = new InstanceRepo();
        com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo serviceRepo = new com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo();
        ServiceRepo serviceRepoOS = new ServiceRepo();
        int endTimeBracket = timeInMin * 60 * 1000;

        Timestamp timestamp = DateTimeUtil.getCurrentTimestampInGMT();
        if (Objects.isNull(timestamp)) {
            throw new InstanceHealthServiceException(UIMessages.INSTANCE_HEALTH_SERVICE_TIMESTAMP);
        }
        long currentTimeInGmt = timestamp.getTime();
        long timeBracketDiff = currentTimeInGmt - endTimeBracket;

        Map<Integer, CompInstClusterDetails> compInstancesMap = instanceRepo.getInstances(account.getIdentifier())
                .parallelStream()
                .filter(c -> c.getStatus() == 1)
                .collect(Collectors.toMap(CompInstClusterDetails::getId, Function.identity()));

        if (compInstancesMap.isEmpty()) {
            LOGGER.warn("No component instance data found for accountIdentifier:{}", account.getIdentifier());
            return Collections.emptyList();
        }
        UserAccessDetails accessDetails = UserValidationUtil.getUserAccessDetails(userId, account.getIdentifier());
        if (accessDetails == null) {
            LOGGER.error("User access bean unavailable for userId:{} and account:{}", userId, account.getIdentifier());
            throw new InstanceHealthServiceException("Error while fetching user access bean for user");
        }
        Map<String, Long> instancesHealth = serviceRepoOS.getInstancesHealthForAccount(account.getIdentifier());

        LOGGER.debug("Time taken for getting health instances data is {} ms.", System.currentTimeMillis() - st);
        Set<InstanceHealthDetails> instanceHealthDetails = accessDetails.getServiceIdentifiers()
                .parallelStream()
                .map(s -> serviceRepo.getServiceInstances(account.getIdentifier(), s))
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .map(i -> {
                    CompInstClusterDetails instDetail = compInstancesMap.get(i.getId());
                    if (instDetail == null) {
                        return null;
                    }
                    long instHealthTime = instancesHealth.getOrDefault(i.getIdentifier(), 0L);
                    return InstanceHealthDetails.builder()
                            .id(i.getId())
                            .instanceName(i.getName())
                            .host(instDetail.getHostAddress())
                            .type(instDetail.getComponentTypeName())
                            .lastPostedTime(instHealthTime)
                            .services(instanceRepo.getServices(account.getIdentifier(), i.getIdentifier()).stream().map(BasicEntity::getName).collect(Collectors.toSet()))
                            .dataPostStatus((0L != instHealthTime && instHealthTime >= timeBracketDiff) ? 1: 0)
                            .build();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        return getSortedList(new ArrayList<>(instanceHealthDetails));
    }

    List<InstanceHealthDetails> getSortedList(List<InstanceHealthDetails> instanceHealthDetails){
        if (Objects.isNull(instanceHealthDetails)){
            return new ArrayList<>();
        }
        instanceHealthDetails.sort(Comparator.comparing(InstanceHealthDetails::getDataPostStatus)
                .thenComparing(InstanceHealthDetails::getLastPostedTime, nullsFirst(Comparator.naturalOrder()))
                .thenComparing(InstanceHealthDetails::getInstanceName)
                .thenComparing(InstanceHealthDetails::getType));

               return instanceHealthDetails;
    }
}
