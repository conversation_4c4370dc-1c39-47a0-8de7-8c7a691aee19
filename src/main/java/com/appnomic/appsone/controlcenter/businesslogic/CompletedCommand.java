package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.CompletedCommandBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.connectors.ConnectorDetailsDataService;
import com.appnomic.appsone.controlcenter.exceptions.CommandException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.PhysicalAgentBean;
import com.appnomic.appsone.controlcenter.dao.mysql.AgentDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CommandDataService;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class CompletedCommand {

    private static final Logger LOGGER = LoggerFactory.getLogger(CompletedCommand.class);
    private static ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    private static final String COMMAND_ERROR_MESSAGE = "Command status update failed. For more details, refer to application log file.";
    private static final String COMMAND_UPDATE_ERROR_MESSAGE = "Status update for command with commandID [{}] and commandJobId [{}] failed. Reason: {}";

    public CompletedCommandBean clientValidation(String requestBody) throws CommandException {
        CompletedCommandBean completedCommand;
        try {
            completedCommand = objectMapper.readValue(requestBody, new TypeReference<CompletedCommandBean>() {});
        } catch (IOException e) {
            LOGGER.error("Exception encountered while retrieving CompletedCommand request. Reason: {}", e.getMessage());
            throw new CommandException(UIMessages.INVALID_REQUEST_BODY);
        }

        if(!completedCommand.isValid()) {
            throw new CommandException("CompletedCommandBean validation failure. " +
                    "For more details, refer to application log file.");
        }
        return completedCommand;
    }

    public void serverValidation(CompletedCommandBean commandBean) throws CommandException {
        int existingCommand = CommandDataService.checkForCommand(commandBean.getCommandIdentifier());

        if(existingCommand != 1) {
            throw new CommandException("Command with commandIdentifier " + commandBean.getCommandIdentifier() + " is unavailable");
        }

        int existingAgent = AgentDataService.checkIfAgentExists(commandBean.getAgentIdentifier());

        if(existingAgent == 0) {
            throw new CommandException("Agent with agentIdentifier " + commandBean.getAgentIdentifier() + " is unavailable");
        }
    }

    public void updateCommandStatus(CompletedCommandBean commandBean, String userId) throws CommandException {
        PhysicalAgentBean agent = AgentDataService.getPhysicalAgentDetailsUsingIdentifier(commandBean.getAgentIdentifier());
        if(null == agent) {
            LOGGER.error("Agent with identifier [{}] is unavailable", commandBean.getAgentIdentifier());
        } else {
            LOGGER.debug("PhysicalAgent details: {}", agent);

            if(null == agent.getLastJobId() || !agent.getLastJobId().equalsIgnoreCase(commandBean.getCommandJobId())) {
                LOGGER.error("Agent with identifier [{}] and last command job ID [{}] is unavailable.", commandBean.getAgentIdentifier(), commandBean.getCommandJobId());
                return;
            }
            if(commandBean.getAgentIdentifier().equalsIgnoreCase("SupervisorControllerIdentifier"))
            {
                updateConnectorCommandStatus(commandBean);
            } else {
                updateLastCommandStatus(commandBean, userId);
            }
        }
    }

    private void updateLastCommandStatus(CompletedCommandBean commandBean, String userId) throws CommandException {
        int updatedRow;
        if(null == commandBean.getAgentCurrentState()) {
            updatedRow = updateFailureStatus(commandBean, userId);
        } else {
            ViewTypes viewTypes = MasterCache.getMstTypeForSubTypeName(Constants.MST_TYPE_AGENT_OPS_CMDS, commandBean.getAgentCurrentState());

            if (null == viewTypes) {
                updatedRow = updateFailureStatus(commandBean, userId);
            } else {
                try {
                    updatedRow = CommandDataService.updateCommandStatus(viewTypes.getSubTypeId(), commandBean.getAgentIdentifier(),
                            commandBean.getCommandJobId(), userId);
                } catch (ControlCenterException e) {
                    LOGGER.error(COMMAND_UPDATE_ERROR_MESSAGE, commandBean.getCommandIdentifier(), commandBean.getCommandJobId(), e.getMessage());
                    throw new CommandException(COMMAND_ERROR_MESSAGE);
                }
            }
        }

        if(updatedRow == 0) {
            LOGGER.error("last_command_executed update failed. Agent [{}] with command job ID [{}] is unavailable.",
                    commandBean.getAgentIdentifier(), commandBean.getCommandJobId());
        }
    }

    private void updateConnectorCommandStatus(CompletedCommandBean commandBean) throws CommandException {
        int updatedRow;
        if(null == commandBean.getAgentCurrentState()) {
            return;
        }
        ViewTypes viewTypes = MasterCache.getMstTypeForSubTypeName(Constants.MST_TYPE_AGENT_OPS_CMDS, commandBean.getAgentCurrentState());
        if (null == viewTypes) {
            return;
        }
        try {
            updatedRow = ConnectorDetailsDataService.updateConnectorCommandStatus(commandBean.getCommandJobId(), 0, null);
        } catch (Exception e) {
            LOGGER.error(COMMAND_UPDATE_ERROR_MESSAGE, commandBean.getCommandIdentifier(), commandBean.getCommandJobId(), e.getMessage());
            throw new CommandException(COMMAND_ERROR_MESSAGE);
        }

        if(updatedRow <= 0) {
            LOGGER.error("last_command_executed update failed. Agent [{}] with command job ID [{}] is unavailable.",
                    commandBean.getAgentIdentifier(), commandBean.getCommandJobId());
        }
    }

    private int updateFailureStatus(CompletedCommandBean commandBean, String userId) throws CommandException {
        try {
           return CommandDataService.updateCommandFailureStatus(commandBean.getAgentIdentifier(), commandBean.getCommandJobId(), userId);
        } catch (ControlCenterException e) {
            LOGGER.error(COMMAND_UPDATE_ERROR_MESSAGE, commandBean.getCommandIdentifier(), commandBean.getCommandJobId(), e.getMessage());
            throw new CommandException(COMMAND_ERROR_MESSAGE);
        }
    }
}
