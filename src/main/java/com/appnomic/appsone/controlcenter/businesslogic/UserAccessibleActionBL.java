package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserAttributesBean;
import com.appnomic.appsone.controlcenter.pojo.UserAccessibleActions;
import com.appnomic.appsone.controlcenter.dao.mysql.UserAccessDataService;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.util.List;

public class UserAccessibleActionBL {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserAccessibleActionBL.class);

    public String clientValidation(Request request) throws RequestException, ControlCenterException {
        if(null == request) {
            LOGGER.error("Request object is NULL");
            throw new RequestException("Request object is NULL");
        }

        if(null == request.headers("Authorization")) {
            LOGGER.error("Authorization header is NULL");
            throw new RequestException("Authorization header is NULL");
        }

        String userId;
        try {
            userId = CommonUtils.getUserId(request);
        } catch (ControlCenterException e) {
            LOGGER.error("Exception encountered while fetching userId. Reason: {}", e.getMessage(), e);
            throw new ControlCenterException("");
        }
        return userId;
    }

    public UserAttributesBean serverValidation(String userId) throws ControlCenterException {
        UserAccessDataService dataService = new UserAccessDataService();
        UserAttributesBean userAttributesBean = dataService.getUserAttributeDetails(userId);

        if(null == userAttributesBean) {
            LOGGER.error("User details unavailable for userId [{}]", userId);
            throw new ControlCenterException("User details unavailable");
        }

        return userAttributesBean;
    }

    public UserAccessibleActions getUserAccessibleActions(UserAttributesBean userAttributesBean) throws ControlCenterException {
        UserAccessDataService userAccessDataService = new UserAccessDataService();
        List<String> allowedActions = userAccessDataService.getUserAccessibleActions(userAttributesBean.getAccessProfileId());

        return UserAccessibleActions.builder()
                .profileId(userAttributesBean.getAccessProfileId())
                .profile(userAttributesBean.getAccessProfileName())
                .roleId(userAttributesBean.getRoleId())
                .role(userAttributesBean.getRoleName())
                .isActiveDirectory(0)
                .allowedActions(allowedActions)
                .build();
    }
}
