package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.FileProcessedDetailsBean;
import com.appnomic.appsone.controlcenter.beans.FileUploadDetailsBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ControllerEntity;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.InstallationAttributeBean;
import com.appnomic.appsone.controlcenter.pojo.KeyCloakUserDetails;
import com.appnomic.appsone.controlcenter.dao.mysql.FileUploadDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ServiceThresholdDS;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipInputStream;

public class FileUpload {

    private FileUpload(){}

    private static final Logger LOGGER = LoggerFactory.getLogger(FileUpload.class);

    static File deCompressGZipFile(File gZippedFile, File tarFile) throws IOException {
        FileInputStream fis = new FileInputStream(gZippedFile);
        try (ZipInputStream gZIPInputStream = new ZipInputStream(fis)) {
            try (FileOutputStream fos = new FileOutputStream(tarFile)) {
                byte[] buffer = new byte[1024];
                int len;
                while ((len = gZIPInputStream.read(buffer)) > 0) {
                    fos.write(buffer, 0, len);
                }
            }
        }
        return gZippedFile;

    }

    static void unTarFile(File tarFile, File destFile) throws IOException {
        String os = System.getProperty("os.name").toLowerCase();
        FileInputStream fis = new FileInputStream(tarFile);
        try (TarArchiveInputStream tis = new TarArchiveInputStream(fis)) {
            LOGGER.debug("tis.available():{}" , tis.available());
            TarArchiveEntry tarEntry;

            while ((tarEntry = tis.getNextTarEntry()) != null) {
                File outputFile = new File(destFile + File.separator + tarEntry.getName());
                if (os.contains("win") && outputFile.getName().endsWith(".xz")) {
                    String dir = outputFile.getParent();
                    String fileName = outputFile.getName();
                    fileName = fileName.replace(":", "_");
                    File newFile = new File(dir, fileName);
                    if(!outputFile.renameTo(newFile))
                        LOGGER.error("Couldn't rename the file.");
                    outputFile = newFile;
                }
                LOGGER.debug(outputFile.getCanonicalPath());
                if (tarEntry.isDirectory() && !outputFile.exists()) {
                    outputFile.mkdirs();
                } else {
                    outputFile.getParentFile().mkdirs();
                    FileOutputStream fos = new FileOutputStream(outputFile);
                    IOUtils.copy(tis, fos);
                    if(!outputFile.createNewFile())
                        LOGGER.error("Couldn't create file ");
                    fos.close();
                }
            }
        }
    }

    static String readLines(String filePath) {
        StringBuilder contentBuilder = new StringBuilder();
        try (Stream<String> stream = Files.lines(Paths.get(filePath), StandardCharsets.UTF_8)) {
            stream.forEach(s -> contentBuilder.append(s).append("\n"));
        } catch (IOException e) {
            LOGGER.error("Error occurred while reading the file.filePath:" + filePath, e);
        }
        return contentBuilder.toString();
    }

}
