package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProducerMapping;
import com.appnomic.appsone.controlcenter.dao.redis.*;
import com.appnomic.appsone.controlcenter.exceptions.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.heal.configuration.pojos.*;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.DBI;
import org.skife.jdbi.v2.Handle;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class AddServiceBL implements BusinessLogic<List<ServicePojo>, List<ServiceBean>, List<IdPojo>> {

    private static final int SERVICE_START_HOUR = ConfProperties.getInt(Constants.SERVICE_START_WITHIN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_START_TIME_WITHIN_AN_HOUR);
    private static final int SERVICE_END_HOUR = ConfProperties.getInt(Constants.SERVICE_END_WITHIN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_END_TIME_WITHIN_AN_HOUR);
    private static final int SERVICE_START = ConfProperties.getInt(Constants.SERVICE_START_MORE_THAN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_START_TIME_MORE_THAN_AN_HOUR);
    private static final int SERVICE_END = ConfProperties.getInt(Constants.SERVICE_END_MORE_THAN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_END_TIME_MORE_THAN_AN_HOUR);

    private static final int SOR_PERSISTENCE_HOUR = ConfProperties.getInt(Constants.SERVICE_SOR_PERSISTENCE_WITHIN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_SOR_PERSISTENCE_WITHIN_AN_HOUR);
    private static final int SOR_SUPPRESSION_HOUR = ConfProperties.getInt(Constants.SERVICE_SOR_SUPPRESSION_WITHIN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_SOR_SUPPRESSION_WITHIN_AN_HOUR);
    private static final int SOR_PERSISTENCE = ConfProperties.getInt(Constants.SERVICE_SOR_PERSISTENCE_MORE_THAN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_SOR_PERSISTENCE_MORE_THAN_AN_HOUR);
    private static final int SOR_SUPPRESSION = ConfProperties.getInt(Constants.SERVICE_SOR_SUPPRESSION_MORE_THAN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_SOR_SUPPRESSION_MORE_THAN_AN_HOUR);

    private static final int NOR_PERSISTENCE_HOUR = ConfProperties.getInt(Constants.SERVICE_NOR_PERSISTENCE_WITHIN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_NOR_PERSISTENCE_WITHIN_AN_HOUR);
    private static final int NOR_SUPPRESSION_HOUR = ConfProperties.getInt(Constants.SERVICE_NOR_SUPPRESSION_WITHIN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_NOR_SUPPRESSION_WITHIN_AN_HOUR);
    private static final int NOR_PERSISTENCE = ConfProperties.getInt(Constants.SERVICE_NOR_PERSISTENCE_MORE_THAN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_NOR_PERSISTENCE_MORE_THAN_AN_HOUR);
    private static final int NOR_SUPPRESSION = ConfProperties.getInt(Constants.SERVICE_NOR_SUPPRESSION_MORE_THAN_AN_HOUR_PROPERTY_NAME,
            Constants.SERVICE_NOR_SUPPRESSION_MORE_THAN_AN_HOUR);

    @Override
    public UtilityBean<List<ServicePojo>> clientValidation(RequestObject requestObject) throws ClientException {
        CommonUtils.basicRequestValidation(requestObject);
        List<ServicePojo> services = getServicePojos(requestObject);

        return UtilityBean.<List<ServicePojo>>builder()
                .accountIdentifier(requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER))
                .authToken(requestObject.getHeaders().get(Constants.AUTHORIZATION))
                .pojoObject(services)
                .build();
    }

    private static List<ServicePojo> getServicePojos(RequestObject requestObject) throws ClientException {
        List<ServicePojo> services;
        ObjectMapper obj_mapper = CommonUtils.getObjectMapperWithHtmlEncoder();

        try {
            services = obj_mapper.readValue(requestObject.getBody(), new TypeReference<List<ServicePojo>>() {
            });
        } catch (IOException e) {
            log.error(UIMessages.JSON_INVALID + "err:{}", e.getMessage());
            throw new ClientException(UIMessages.JSON_INVALID);
        }

        if (services.isEmpty()) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        Set<ServicePojo> svcSet = new HashSet<>(services);

        if (svcSet.size() < services.size()) {
            log.error(UIMessages.DUPLICATE_SERVICE);
            throw new ClientException(UIMessages.DUPLICATE_SERVICE);
        }

        for (ServicePojo svc : services) {
            svc.validate();
            Map<String, String> svcError = svc.getError();
            if (!svcError.isEmpty()) {
                String error = svcError.toString();
                log.error(error);
                throw new ClientException(error);
            }

            List<ComponentInstancePojo> instances = svc.getInstances();
            if (instances != null && !instances.isEmpty()) {
                try {
                    ComponentInstanceBL.addClientValidations(instances, svc.getIdentifier());
                } catch (RequestException e) {
                    throw new ClientException(e.getMessage());
                }
            }
        }
        return services;
    }

    @Override
    public List<ServiceBean> serverValidation(UtilityBean<List<ServicePojo>> utilityBean) throws ServerException {
        ControllerDataService controllerDataService = new ControllerDataService();

        UserAccountBean userAccBean;
        try {
            userAccBean = ValidationUtils.commonServerValidations(utilityBean.getAuthToken(), utilityBean.getAccountIdentifier());
        } catch (RequestException e) {
            throw new ServerException(e.getMessage());
        }

        String userId = userAccBean.getUserId();
        int accountId = userAccBean.getAccount().getId();

        int svcTypeId = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT, Constants.SERVICES_CONTROLLER_TYPE).getSubTypeId();

        TagDetailsBean controllerTagDetails = MasterCache.getTagDetails(Constants.CONTROLLER_TAG);
        if (controllerTagDetails == null) {
            log.error("Tag details unavailable for 'Controller'");
            throw new ServerException("Tag details unavailable for 'Controller'");
        }

        int controllerTagId = controllerTagDetails.getId();

        List<TimezoneDetail> timezoneDetails = MasterCache.getTimezones();
        List<ServiceBean> serviceBeanList = new ArrayList<>();
        List<ServicePojo> services = utilityBean.getPojoObject();

        for (ServicePojo svc : services) {
            int isUpdate = 0;
            if (StringUtils.isEmpty(svc.getIdentifier()))
                svc.setIdentifier(UUID.randomUUID().toString());
            if(svc.getType() == null || svc.getType().isEmpty()) {
                svc.setType(Constants.NON_KUBERNETES);
            }
            String layer = svc.getLayer() == null ? null : svc.getLayer().trim();
            List<String> appIdentifiers = svc.getAppIdentifiers();
            String timezoneId = svc.getTimezone();
            List<Integer> appIds = new ArrayList<>();

            String appIdentifier = svc.getAppIdentifier();
            int appId = 0;
                if (appIdentifiers != null && !appIdentifiers.isEmpty()) {
                    for (String id : appIdentifiers) {
                        ControllerBean controllerBean = controllerDataService.getControllerByIdentifierOrName(id.trim(), null, null);
                        if (controllerBean == null) {
                            String err = "App Identifier '" + id + "' does not exist.";
                            log.error(err);
                            throw new ServerException(err);
                        }
                        appIds.add(controllerBean.getId());
                    }
                }

                ControllerBean controllerBean = controllerDataService.getControllerByIdentifierOrName(svc.getIdentifier(), null, null);
                if (controllerBean != null) {
                    int applicationId = controllerDataService.getControllerApplicationId(controllerBean.getId(), controllerTagId, accountId, null);
                    if (controllerBean.getStatus() == 0 && controllerBean.getAccountId() == accountId && appIds.contains(applicationId)) {
                        log.info("Service identifier '" + svc.getIdentifier() + "' already exists with status 0 for acc:{}. So Reactivating..", accountId);
                        isUpdate = 1;
                    } else {
                        String err = "Service identifier '" + svc.getIdentifier() + "' already exists.";
                        log.error(err);
                        throw new ServerException(err);
                    }
                } else {
                    controllerBean = controllerDataService.getControllerByIdentifierOrName(null, svc.getName(), null);
                    if (controllerBean != null) {
                        int applicationId = controllerDataService.getControllerApplicationId(controllerBean.getId(), controllerTagId, accountId, null);
                        if (controllerBean.getControllerTypeId() == svcTypeId && controllerBean.getStatus() == 1 &&
                                controllerBean.getAccountId() == accountId && appIds.contains(applicationId)) {
                            String err = "Service name '" + svc.getName() + "' already exists.";
                            log.error(err);
                            throw new ServerException(err);
                        }
                    }
                }

            List<ComponentInstanceBean> instanceList = null;
            if (svc.getInstances() != null) {
                try {
                    instanceList = ComponentInstanceBL.addServerValidations(svc.getInstances(), userId, accountId, null, null, null);
                } catch (RequestException e) {
                    throw new ServerException(e.getMessage());
                }
            }

            if (null != timezoneId && timezoneDetails.stream().noneMatch(t -> t.getTimeZoneId().equals(timezoneId))) {
                log.error("No active timezone found with timezone id: {}", svc.getTimezone());
                throw new ServerException("No active timezone found with timezone id: " + svc.getTimezone());
            }
            TimezoneDetail timezoneDetail = timezoneDetails.stream().filter(f -> f.getTimeZoneId().equalsIgnoreCase(timezoneId)).findAny().orElse(null);
            if (timezoneDetail == null) {
                log.error("Could not find timeZone details for the given timezoneId");
                throw new ServerException("Could not find timeZone details for the given timezoneId");
            }

            serviceBeanList.add(ServiceBean.builder()
                    .name(svc.getName().trim())
                    .identifier(svc.getIdentifier().trim())
                    .layer(layer)
                    .appIdentifier(appIdentifier)
                    .appId(appId)
                    .appIds(appIds)
                    .appIdentifiers(svc.getAppIdentifiers())
                    .accountId(accountId)
                    .accountIdentifier(utilityBean.getAccountIdentifier())
                    .userId(userId)
                    .type(svc.getType())
                    .instances(instanceList)
                    .isUpdate(isUpdate)
                    .status(1)
                    .timezoneId(timezoneId)
                    .timezoneDetail(timezoneDetail)
                    .entryPointService(svc.getIsEntryPointService() == 1)
                    .build());
        }

        return serviceBeanList;
    }

    @Override
    public List<IdPojo> process(List<ServiceBean> serviceBeanList) throws DataProcessingException {
        try {
            DBI dbi = MySQLConnectionManager.getInstance().getHandle();
            List<IdPojo> idPojo = dbi.inTransaction((conn, status) -> {
                List<IdPojo> list = new ArrayList<>();
                for (ServiceBean serviceBean : serviceBeanList) {
                    list.add(addService(serviceBean, conn));
                }
                return list;
            });
            addServiceToRedis(serviceBeanList, idPojo);
            return idPojo;

        } catch (Exception e) {
            throw new DataProcessingException(Throwables.getRootCause(e).getMessage());
        }
    }

    private void addServiceToRedis(List<ServiceBean> serviceBeanList, List<IdPojo> list) {
        List<ServiceConfiguration> serviceConfigurations = new ArrayList<>();
        List<com.heal.configuration.pojos.Tags> tags = new ArrayList<>();
        List<BasicEntity> applications = new ArrayList<>();
        ApplicationRepo applicationRepo = new ApplicationRepo();
        ServiceRepo serviceRepo = new ServiceRepo();
        AccountRepo accountRepo = new AccountRepo();
        Map<String, IdPojo> serviceDetailMap = list.parallelStream().collect(Collectors.toMap(IdPojo::getName, Function.identity()));


        for (ServiceBean serviceBean : serviceBeanList) {
            serviceConfigurations.add(ServiceConfiguration.builder()
                    .lastModifiedBy(serviceBean.getUserId())
                    .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .startCollectionInterval(SERVICE_START_HOUR)
                    .endCollectionInterval(SERVICE_END_HOUR)
                    .sorPersistence(SOR_PERSISTENCE_HOUR)
                    .sorSuppression(SOR_SUPPRESSION_HOUR)
                    .norPersistence(NOR_PERSISTENCE_HOUR)
                    .norSuppression(NOR_SUPPRESSION_HOUR)
                    .build());

            serviceConfigurations.add(ServiceConfiguration.builder()
                    .lastModifiedBy(serviceBean.getUserId())
                    .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                    .startCollectionInterval(SERVICE_START)
                    .endCollectionInterval(SERVICE_END)
                    .sorPersistence(SOR_PERSISTENCE)
                    .sorSuppression(SOR_SUPPRESSION)
                    .norPersistence(NOR_PERSISTENCE)
                    .norSuppression(NOR_SUPPRESSION)
                    .build());

            tags.add(com.heal.configuration.pojos.Tags.builder()
                    .type(Constants.LAYER_TAG)
                    .value(serviceBean.getLayer().trim())
                    .key(Constants.LAYER_DEFAULT)
                    .build());

            tags.add(com.heal.configuration.pojos.Tags.builder()
                    .type(Constants.TIME_ZONE_TAG)
                    .value(String.valueOf(serviceBean.getTimezoneDetail().getOffset()))
                    .key(String.valueOf(serviceBean.getTimezoneDetail().getId()))
                    .build());

                tags.add(com.heal.configuration.pojos.Tags.builder()
                        .type(Constants.SERVICE_TYPE_TAG)
                        .value(serviceBean.getType().trim())
                        .key(Constants.DEFAULT_TAG_VALUE)
                        .build());

            if (serviceBean.isEntryPointService()) {
                    tags.add(com.heal.configuration.pojos.Tags.builder()
                            .type(Constants.ENTRY_POINT)
                            .value(String.valueOf(1))
                            .key(Constants.LAYER_DEFAULT)
                            .build());
                }

                Service serviceObject = Service.builder()
                        .id(serviceDetailMap.get(serviceBean.getName()).getId())
                        .status(serviceBean.getStatus())
                        .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                        .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                        .name(serviceBean.getName())
                        .type(serviceBean.getType())
                        .identifier(serviceDetailMap.get(serviceBean.getName()).getIdentifier())
                        .lastModifiedBy(serviceBean.getUserId())
                        .serviceConfigurations(serviceConfigurations)
                        .tags(tags)
                        .build();

            List<Rule> rules = buildRulesPojo(serviceBean);
            serviceRepo.updateServiceRules(serviceBean.getAccountIdentifier(), serviceBean.getIdentifier(), rules);
            /* Adding new service to service level key using identifier */
            serviceRepo.updateServiceConfigurationByServiceIdentifier(serviceBean.getAccountIdentifier(), serviceDetailMap.get(serviceBean.getName()).getIdentifier(), serviceObject);
            /* Adding new service to the entire service list */
            BasicEntity basicEntityObject = addNewServiceInServiceList(serviceRepo, serviceBean, serviceDetailMap);
            /* Adding new service detail to the application key, For the applications which are mapped to the new service */
            addServiceMappedToApplications(applicationRepo, serviceBean, basicEntityObject);
            /* Adding all applications mapped to the service in service level application key */
            addApplicationsToService(applications, serviceRepo, accountRepo, serviceBean, serviceDetailMap);
        }
    }

    private List<Rule> buildRulesPojo(ServiceBean serviceBean) {
        log.info("Populating rules for the service {}", serviceBean.getName());
        RulesDataService rulesDataService = new RulesDataService();
        MasterDataRepo masterDataRepo = new MasterDataRepo();
        List<RulesHelperPojo> rulesHelperPojo = rulesDataService.getRulesHelperPojo(serviceBean.getAccountId(), serviceBean.getId());
        List<com.heal.configuration.pojos.ViewTypes> viewTypes = masterDataRepo.getTypes();

        if (!rulesHelperPojo.isEmpty()) {
            return rulesHelperPojo.parallelStream().map(rule -> {
                RegexTypeDetail regexTypeDetail = RegexTypeDetail.builder()
                        .id(rule.getTcpId())
                        .initialPattern(rule.getTcpInitialPattern())
                        .endPattern(rule.getTcpLastPattern())
                        .length(rule.getTcpLength())
                        .build();
                List<PairData> pairDataList = rulesDataService.getPairDataListForHttpPatterns(rule.getId(), rule.getHttpId());
                RequestTypeDetail requestTypeDetail = RequestTypeDetail.builder()
                        .id(rule.getHttpId())
                        .firstUriSegments(rule.getHttpFirstUriSegments())
                        .lastUriSegments(rule.getHttpLastUriSegments())
                        .completeURI(rule.getHttpCompleteURI() != 0)
                        .payloadTypeId(rule.getHttpPayloadTypeId())
                        .payloadTypeName(viewTypes.parallelStream()
                                .filter(v -> v.getSubTypeId() == rule.getHttpPayloadTypeId())
                                .findAny()
                                .orElse(new com.heal.configuration.pojos.ViewTypes()).getSubTypeName())
                        .pairData(pairDataList)
                        .build();
                return Rule.builder()
                        .id(rule.getId())
                        .name(rule.getName())
                        .monitoringEnabled(rule.getEnabled() != 0)
                        .discoveryEnabled(rule.getDiscoveryEnabled() == 1)
                        .order(rule.getOrder())
                        .ruleTypeId(rule.getRuleTypeId())
                        .ruleType(viewTypes.parallelStream()
                                .filter(v -> v.getSubTypeId() == rule.getRuleTypeId())
                                .findAny()
                                .orElse(new com.heal.configuration.pojos.ViewTypes()).getSubTypeName())
                        .isDefault(rule.getIsDefault())
                        .maxTags(rule.getMaxTags())
                        .regexTypeDetails(regexTypeDetail)
                        .requestTypeDetails(requestTypeDetail)
                        .transactionGroups(new ArrayList<>())
                        .build();
            }).collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    private void addApplicationsToService(List<BasicEntity> applications, ServiceRepo serviceRepo, AccountRepo accountRepo, ServiceBean serviceBean, Map<String, IdPojo> serviceDetailMap) {
        List<BasicEntity> allApplications = accountRepo.getAllApplications(serviceBean.getAccountIdentifier());
        for (Integer appId : serviceBean.getAppIds()) {
            allApplications.parallelStream().filter(f -> f.getId() == appId).findAny().ifPresent(applications::add);
        }
        serviceRepo.updateApplicationsByServiceIdentifier(serviceBean.getAccountIdentifier(), serviceDetailMap.get(serviceBean.getName()).getIdentifier(), applications);
    }

    private void addServiceMappedToApplications(ApplicationRepo applicationRepo, ServiceBean serviceBean, BasicEntity basicEntityObject) {
        for (String appIdentifier : serviceBean.getAppIdentifiers()) {
            List<BasicEntity> servicesMappedToApplication = applicationRepo.getServicesMappedToApplication(serviceBean.getAccountIdentifier(), appIdentifier);
            servicesMappedToApplication.add(basicEntityObject);
            applicationRepo.updateServiceApplication(serviceBean.getAccountIdentifier(), appIdentifier, servicesMappedToApplication);
        }
    }

    private BasicEntity addNewServiceInServiceList(ServiceRepo serviceRepo, ServiceBean serviceBean, Map<String, IdPojo> serviceDetailMap) {
        List<BasicEntity> allServicesDetails = serviceRepo.getAllServicesDetails(serviceBean.getAccountIdentifier());
        BasicEntity basicEntityObject = BasicEntity.builder()
                .id(serviceDetailMap.get(serviceBean.getName()).getId())
                .status(serviceBean.getStatus())
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .name(serviceBean.getName())
                .identifier(serviceDetailMap.get(serviceBean.getName()).getIdentifier())
                .lastModifiedBy(serviceBean.getUserId())
                .build();
        allServicesDetails.add(basicEntityObject);
        serviceRepo.updateServiceConfiguration(serviceBean.getAccountIdentifier(), allServicesDetails);
        return basicEntityObject;
    }

    public static IdPojo addService(ServiceBean bean, Handle handle) throws ControlCenterException {

        ViewTypes masterSubTypeBean = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT, Constants.SERVICES_CONTROLLER_TYPE);
        if (masterSubTypeBean == null) {
            String err = Constants.SERVICES_CONTROLLER_TYPE + " is not found in 'masterSubType' table";
            log.error(err);
            throw new ControlCenterException(err);
        }

        ControllerBean controllerBean = ControllerBL.addController(bean, masterSubTypeBean.getSubTypeId(), bean.getIsUpdate(), handle);
        if (controllerBean.getId() == -1) {
            String err = "Unable to add service in controller table";
            log.error(err);
            throw new ControlCenterException(err);
        }
        bean.setId(controllerBean.getId());
        if (bean.isEntryPointService()) {
            TagDetailsBean entryPointTagDetails = MasterCache.getTagDetails(Constants.ENTRY_POINT);
            if (entryPointTagDetails == null) {
                String err = "Unable to fetch tag details for Entry-Point";
                log.error(err);
                throw new ControlCenterException(err);
            }

            int id = TagMappingBL.addTagMapping(entryPointTagDetails.getId(), controllerBean.getId(),
                    Constants.CONTROLLER, "Type", "1", bean.getUserId(), bean.getAccountId(), handle);

            if (id == -1) {
                String err = "Unable to save entry point details.";
                log.error(err);
                throw new ControlCenterException(err);
            }
        }

        if (bean.getInstances() != null && !bean.getInstances().isEmpty()) {
            ComponentInstanceBL.add(bean.getInstances(), controllerBean.getId(), handle);
        }

        long stTime = System.currentTimeMillis();
        addServiceInstance(controllerBean.getId(), bean, handle);
        log.debug("Total time taken to insert service instance into DB and populate redis keys for serviceName: [{}], time: [{}]ms", bean.getName(), System.currentTimeMillis()-stTime);

        return IdPojo.builder()
                .id(controllerBean.getId())
                .name(bean.getName())
                .identifier(bean.getIdentifier())
                .build();
    }


    public static void addServiceInstance(int id, ServiceBean bean, Handle handle) throws ControlCenterException {
        long stTime = System.currentTimeMillis();
        try {
            ServiceInstanceDataService serviceInstanceDataService = new ServiceInstanceDataService();
            MasterComponentBean componentDetailsByName = ComponentDataService.getComponentDetailsByName(Constants.SERVICE_KPIS_COMPONENT_NAME);

            if (componentDetailsByName == null) {
                log.warn("Component not found for componentName: [{}], so skipping insert into service instance related data", Constants.SERVICE_KPIS_COMPONENT_NAME);
                return;
            }

            String createdTime = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());

            ServiceInstanceDetails serviceInstanceDetails = ServiceInstanceDetails.builder()
                    .serviceId(id)
                    .accountId(bean.getAccountId())
                    .serviceIdentifier(bean.getIdentifier())
                    .name(bean.getName())
                    .identifier(String.format("%s_%s", bean.getIdentifier(), Constants.SERVICE_KPIS_COMPONENT_NAME))
                    .commonVersionId(componentDetailsByName.getCommonVersionId())
                    .componentVersionId(componentDetailsByName.getComponentVersionId())
                    .componentId(componentDetailsByName.getId())
                    .componentName(Constants.SERVICE_KPIS_COMPONENT_NAME)
                    .componentTypeId(componentDetailsByName.getComponentTypeId())
                    .componentTypeName(componentDetailsByName.getComponentTypeName())
                    .createdTime(createdTime)
                    .updatedTime(createdTime)
                    .status(1)
                    .lastModifiedBy(bean.getUserId())
                    .build();

            int serviceInstanceId = serviceInstanceDataService.insertServiceInstance(serviceInstanceDetails, handle);

            if (serviceInstanceId == 0) {
                log.error("Error while inserting serviceInstanceDetails: [{}]", serviceInstanceDetails);
                throw new ControlCenterException("Error while inserting serviceInstanceDetails");
            }

            serviceInstanceDetails.setId(serviceInstanceId);

            KPIDataService kpiDataService = new KPIDataService();

            List<ViewCommonVersionKPIsBean> viewCommonVersionGroupKPIsData = MasterDataService.getViewCommonVersionGroupKPIsData(componentDetailsByName.getCommonVersionId(), bean.getAccountId());
            List<ViewCommonVersionKPIsBean> viewCommonVersionNonGroupKPIsData = MasterDataService.getViewCommonVersionNonGroupKPIsData(componentDetailsByName.getCommonVersionId(), bean.getAccountId());

            if (viewCommonVersionGroupKPIsData.isEmpty() && viewCommonVersionNonGroupKPIsData.isEmpty()) {
                log.warn("Kpi details not found for commonVersionId: [{}], accountId: [{}] ", componentDetailsByName.getCommonVersionId(), bean.getAccountId());
                return;
            }

            List<Integer> groupKpiIds = viewCommonVersionGroupKPIsData.stream()
                    .map(ViewCommonVersionKPIsBean::getKpiId)
                    .collect(Collectors.toList());

            List<Integer> nonGroupKpiIds = viewCommonVersionNonGroupKPIsData.stream()
                    .map(ViewCommonVersionKPIsBean::getKpiId)
                    .collect(Collectors.toList());

            nonGroupKpiIds.addAll(groupKpiIds);

            List<ProducerMapping> producerMappings = kpiDataService.getMappingsByKpiDetailsIds(nonGroupKpiIds, null);
            Map<Integer, ProducerMapping> producerVskpiMap = producerMappings.stream()
                    .collect(Collectors.toMap(ProducerMapping::getKpiId, Function.identity()));

            if (producerVskpiMap.isEmpty()) {
                log.warn("ProducerMapping not found for kpiDetailsIds: [{}]", groupKpiIds);
                return;
            }

            List<ServiceInstanceKpiEntity> serviceInstanceKpiEntities = new ArrayList<>();

            if (viewCommonVersionNonGroupKPIsData.isEmpty()) {
                log.debug("Non group kpi not found for commonVersionId: [{}]", componentDetailsByName.getCommonVersionId());
            } else {
                serviceInstanceKpiEntities = viewCommonVersionNonGroupKPIsData.stream()
                        .map(kpi -> {
                            ProducerMapping mappingsByKpiDetailsId = producerVskpiMap.getOrDefault(kpi.getKpiId(), null);
                            if (mappingsByKpiDetailsId == null) {
                                log.warn("Producer not found for kpiId: [{}]", kpi.getKpiId());
                                return null;
                            }

                            return ServiceInstanceKpiEntity.builder()
                                    .serviceInstanceId(serviceInstanceId)
                                    .producerKpiMappingId(mappingsByKpiDetailsId.getProducerMappingId())
                                    .collectionInterval(kpi.getDefaultCollectionInterval())
                                    .status(kpi.getStatus())
                                    .createdTime(createdTime)
                                    .updatedTime(createdTime)
                                    .userDetailsId(bean.getUserId())
                                    .id(kpi.getKpiId())
                                    .name(kpi.getKpiName())
                                    .identifier(kpi.getKpiIdentifier())
                                    .producerId(mappingsByKpiDetailsId.getProducerId())
                                    .notificationsEnabled(1)
                                    .build();
                        }).filter(Objects::nonNull)
                        .collect(Collectors.toList());


            }

            if (!serviceInstanceKpiEntities.isEmpty()) {
                serviceInstanceDataService.insertServiceInstanceKpiDetailsList(serviceInstanceKpiEntities, handle);
            }

            List<CompInstanceKpiGroupDetailsBean> groupKpiDetails = new ArrayList<>();
            if (viewCommonVersionGroupKPIsData.isEmpty()) {
                log.debug("group kpi not found for commonVersionId: [{}]", componentDetailsByName.getCommonVersionId());
            } else {

                groupKpiDetails = viewCommonVersionGroupKPIsData.stream()
                        .map(kpi -> {
                            ProducerMapping mappingsByKpiDetailsId = producerVskpiMap.getOrDefault(kpi.getKpiId(), null);
                            if (mappingsByKpiDetailsId == null) {
                                log.warn("Producer not found for kpiId: [{}]", kpi.getKpiId());
                                return null;
                            }

                            int groupId = kpi.getKpiGroupId();
                            MasterKpiGroupBean groupBean = MasterCache.getGroupKpiDetailList(bean.getAccountId(), groupId);
                            if (groupBean == null) {
                                log.warn("No any group kpi is found in master kpi group details table for group id: [{}]", groupId);
                                return null;
                            }

                            if (groupBean.getDiscovery() != 1){
                                return null;
                            }

                            return CompInstanceKpiGroupDetailsBean.builder()
                                    .compInstanceId(serviceInstanceId)
                                    .mstProducerKpiMappingId(mappingsByKpiDetailsId.getProducerMappingId())
                                    .collectionInterval(kpi.getDefaultCollectionInterval())
                                    .status(kpi.getStatus())
                                    .createdTime(createdTime)
                                    .updatedTime(createdTime)
                                    .userDetailsId(bean.getUserId())
                                    .id(kpi.getKpiId())
                                    .mstProducerId(mappingsByKpiDetailsId.getProducerId())
                                    .notification(1)
                                    .mstKpiGroupId(kpi.getKpiGroupId())
                                    .kpiGroupName(groupBean.getIdentifier())
                                    .isDiscovery(groupBean.getDiscovery())
                                    .attributeValue("ALL")
                                    .attributeStatus(groupBean.getStatus())
                                    .aliasName("ALL")
                                    .isGroup(1)
                                    .build();
                        }).filter(Objects::nonNull)
                        .collect(Collectors.toList());

            }

            if (!groupKpiDetails.isEmpty()) {
                serviceInstanceDataService.insertServiceInstanceGroupKpiDetailsList(groupKpiDetails, handle);
            }

            CompInstanceDataService compInstanceDataService = new CompInstanceDataService();
            List<AttributesViewBean> attributesViewBeanList = compInstanceDataService.
                    getAttributeViewDataByComponentAndCommonVersion(componentDetailsByName.getId(),
                            componentDetailsByName.getCommonVersionId(), null);

            if (attributesViewBeanList.isEmpty()) {
                log.warn("attribute details not found for componentId: [{}], and commonVersionId: [{}]",
                        componentDetailsByName.getId(), componentDetailsByName.getCommonVersionName());
                return;
            }

            List<ServiceInstanceAttributeBean> serviceInstanceAttributeBean = attributesViewBeanList.parallelStream().map(attributesViewBean -> ServiceInstanceAttributeBean.builder()
                    .serviceInstanceId(serviceInstanceId)
                    .attributeValue(attributesViewBean.getDefaultValue())
                    .componentAttributeMappingId(attributesViewBean.getMstComponentAttributeMappingId())
                    .createdTime(createdTime)
                    .updatedTime(createdTime)
                    .userDetailsId(bean.getUserId())
                    .mstCommonAttributesId(attributesViewBean.getAttributeId())
                    .attributeName(attributesViewBean.getAttributeName())
                    .build()).collect(Collectors.toList());

            serviceInstanceDataService.insertServiceInstanceAttribute(serviceInstanceAttributeBean, handle);
            log.debug("Total time taken to create service instance into db for serviceName: [{}], time: [{}]ms", bean.getName(), System.currentTimeMillis()-stTime);

            pushServiceInstanceIntoRedis(bean.getAccountIdentifier(), serviceInstanceDetails, serviceInstanceKpiEntities, groupKpiDetails, attributesViewBeanList);
        } catch (Exception e) {
            log.error("Exception occurred while pushing service Instance Into db for service Name: [{}], Details: ", bean.getName(), e);
        }

    }

    public static void pushServiceInstanceIntoRedis(String accountIdentifier, ServiceInstanceDetails serviceInstanceDetails,
                                                    List<ServiceInstanceKpiEntity> serviceInstanceKpiEntities,
                                                    List<CompInstanceKpiGroupDetailsBean> groupKpiDetails, List<AttributesViewBean> attributesViewBeanList) {

        long stTime = System.currentTimeMillis();
        try {
            ServiceInstanceRepo serviceInstanceRepo = new ServiceInstanceRepo();

            List<ServiceInstanceDetails> serviceInstances = serviceInstanceRepo.getServiceInstances(accountIdentifier).parallelStream()
                    .filter(inst -> inst.getServiceId() != serviceInstanceDetails.getServiceId()).collect(Collectors.toList());

            serviceInstances.add(serviceInstanceDetails);
            serviceInstanceRepo.updateServiceInstances(accountIdentifier, serviceInstances);

            serviceInstanceRepo.updateServiceInstance(accountIdentifier, serviceInstanceDetails);

            List<ServiceInstanceKpiEntity> groupKpiEntities = groupKpiDetails.parallelStream()
                    .map(kpi -> ServiceInstanceKpiEntity.builder()
                            .identifier(kpi.getKpiGroupName())
                            .serviceInstanceId(kpi.getCompInstanceId())
                            .producerKpiMappingId(kpi.getMstProducerKpiMappingId())
                            .collectionInterval(kpi.getCollectionInterval())
                            .status(kpi.getStatus())
                            .createdTime(kpi.getCreatedTime())
                            .updatedTime(kpi.getUpdatedTime())
                            .userDetailsId(kpi.getUserDetailsId())
                            .id(kpi.getId())
                            .producerId(kpi.getMstProducerId())
                            .notificationsEnabled(1)
                            .discovery(kpi.getIsDiscovery())
                            .groupId(kpi.getMstKpiGroupId())
                            .groupName(kpi.getKpiGroupName())
                            .groupIdentifier(kpi.getKpiGroupName())
                            .name(kpi.getKpiGroupName())
                            .build())
                    .collect(Collectors.toList());


            serviceInstanceKpiEntities.addAll(groupKpiEntities);

            serviceInstanceKpiEntities.forEach(kpi -> {
                serviceInstanceRepo.updateServiceInstanceKpiById(accountIdentifier, serviceInstanceDetails.getIdentifier(), kpi);
                serviceInstanceRepo.updateServiceInstanceKpiByIdentifier(accountIdentifier, serviceInstanceDetails.getIdentifier(), kpi);

            });

            serviceInstanceRepo.updateServiceInstanceWiseKpis(accountIdentifier, serviceInstanceDetails.getIdentifier(), serviceInstanceKpiEntities);

            List<InstanceAttributes> attributes = attributesViewBeanList.parallelStream().map(attributesViewBean -> InstanceAttributes.builder()
                    .attributeId(attributesViewBean.getAttributeId())
                    .attributeName(attributesViewBean.getAttributeName())
                    .attributeValue(attributesViewBean.getDefaultValue())
                    .status(attributesViewBean.getStatus())
                    .isUiVisible(attributesViewBean.getIsUiVisible())
                    .isCustom(attributesViewBean.getIsCustom())
                    .attributeType(attributesViewBean.getAttributeType())
                    .build()).collect(Collectors.toList());

            serviceInstanceRepo.updateServiceInstanceWiseAttributes(accountIdentifier, serviceInstanceDetails.getIdentifier(), attributes);
        } catch (Exception e) {
            log.error("Exception occurred while pushing Service  Instance Into Redis for service Name: [{}], Details: ", serviceInstanceDetails.getName(), e);
        }

        log.debug("Total time taken to create service instance related key for serviceName: [{}], time: [{}]ms", serviceInstanceDetails.getName(), System.currentTimeMillis()-stTime);

    }
}
