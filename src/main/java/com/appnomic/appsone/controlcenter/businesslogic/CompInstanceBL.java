package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.controlcenter.beans.HierarchyBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.redis.AccountRepo;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.dao.redis.ServiceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.ComptInstancePojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.CompInstClusterDetails;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class CompInstanceBL implements BusinessLogic<HierarchyBean, HierarchyBean, List<ComptInstancePojo>> {
    AccountRepo accountRepo = new AccountRepo();
    ServiceRepo serviceRepo = new ServiceRepo();
    InstanceRepo instanceRepo = new InstanceRepo();

    @Override
    public UtilityBean<HierarchyBean> clientValidation(RequestObject requestObject) throws ClientException {
        log.trace("clientValidation called");
        HierarchyBean hierarchyBean = new HierarchyBean();

        String accountIdPattern = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(accountIdPattern)) {
            log.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }

        String serviceIdPattern = requestObject.getParams().get(Constants.SERVICE_ID);
        if (StringUtils.isEmpty(serviceIdPattern)) {
            log.error(UIMessages.SERVICE_EMPTY_ERROR);
            throw new ClientException(UIMessages.SERVICE_EMPTY_ERROR);
        }

        int serviceId;
        try {
            serviceId = Integer.parseInt(serviceIdPattern);
        } catch (NumberFormatException e) {
            throw new ClientException(UIMessages.SERVICE_ID_IS_NOT_NUMBER);
        }

        hierarchyBean.setAccountIdString(accountIdPattern);
        hierarchyBean.setServiceIdString(serviceIdPattern);
        hierarchyBean.setServiceId(serviceId);

        return UtilityBean.<HierarchyBean>builder()
                .pojoObject(hierarchyBean).build();
    }

    @Override
    public HierarchyBean serverValidation(UtilityBean<HierarchyBean> utilityBean) throws ServerException {
        log.trace("serverValidation called");
        HierarchyBean hierarchyBean = utilityBean.getPojoObject();

        Account account = accountRepo.getAccountWithAccountIdentifier(hierarchyBean.getAccountIdString());
        if (account == null) {
            log.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }

        BasicEntity serviceDetail = serviceRepo.getServiceConfigurationById(hierarchyBean.getAccountIdString(), Integer.parseInt(hierarchyBean.getServiceIdString()));
        if (serviceDetail == null) {
            log.error(UIMessages.INVALID_SERVICE_ACCOUNT);
            throw new ServerException(UIMessages.INVALID_SERVICE_ACCOUNT);
        }
        hierarchyBean.setServiceIdString(serviceDetail.getIdentifier());

        return hierarchyBean;
    }

    public List<ComptInstancePojo> processAll(HierarchyBean hierarchyBean) throws ControlCenterException {
        log.trace("processAll called");
        try {
            List<ComptInstancePojo> compInstancePojo = new ArrayList<>();

            List<BasicInstanceBean> instanceBeanList = serviceRepo.getServiceInstances(hierarchyBean.getAccountIdString(), hierarchyBean.getServiceIdString());
            if (instanceBeanList.isEmpty()) {
                log.error("The instance details not found for the serviceIdentifier: {} and accountIdentifier: {} ", hierarchyBean.getServiceIdString(), hierarchyBean.getAccountIdString());
                return compInstancePojo;
            }

            Map<Integer, CompInstClusterDetails> instanceDetails = instanceRepo.getInstances(hierarchyBean.getAccountIdString())
                    .stream().collect(Collectors.toMap(CompInstClusterDetails::getId, Function.identity()));

            compInstancePojo = instanceBeanList.stream()
                    .map(f -> {
                        if (!instanceDetails.containsKey(f.getId())) {
                            return null;
                        }
                        CompInstClusterDetails instanceDetail = instanceDetails.get(f.getId());
                        ComptInstancePojo compInstance = new ComptInstancePojo();
                        compInstance.setInstanceId(instanceDetail.getId());
                        compInstance.setInstanceName(instanceDetail.getName());
                        compInstance.setInstanceVersion(instanceDetail.getComponentVersionName());
                        compInstance.setIpAddress(instanceDetail.getHostAddress());
                        compInstance.setInstanceIdentifier(instanceDetail.getIdentifier());
                        return compInstance;
                    }).filter(Objects::nonNull)
                    .collect(Collectors.toList());

            return compInstancePojo;
        } catch (Exception e) {
            log.error("Exception occurred while getting instances details. ", e);
            throw new ControlCenterException("Exception occurred while getting instances details.");
        }
    }

    public List<ComptInstancePojo> getAllInstances(int accountId, int serviceId) throws ControlCenterException {
        log.trace("getAllInstances called");
        Account account = accountRepo.getAccounts().parallelStream().filter(f -> f.getId() == accountId).findFirst().orElse(null);
        if (account == null) {
            log.error("Account detail not found for the given accountId: {}", accountId);
            return new ArrayList<>();
        }

        BasicEntity serviceDetail = serviceRepo.getServiceConfigurationById(account.getIdentifier(), serviceId);
        if (serviceDetail == null) {
            log.error("service detail not found for the given accountIdentifier: {} and serviceId: {}", account.getIdentifier(), serviceId);
            return new ArrayList<>();
        }

        HierarchyBean hierarchyBean = new HierarchyBean();
        hierarchyBean.setAccountId(accountId);
        hierarchyBean.setServiceId(serviceId);
        hierarchyBean.setAccountIdString(account.getIdentifier());
        hierarchyBean.setServiceIdString(serviceDetail.getIdentifier());

        return processAll(hierarchyBean);
    }

    @Override
    public List<ComptInstancePojo> process(HierarchyBean hierarchyBean) throws DataProcessingException {
        log.trace("process called");
        try {
            List<ComptInstancePojo> compInstancePojoList = new ArrayList<>();

            List<BasicInstanceBean> instanceBeanList = serviceRepo.getServiceInstances(hierarchyBean.getAccountIdString(), hierarchyBean.getServiceIdString());
            if (instanceBeanList.isEmpty()) {
                log.error("The instance details not found for the serviceIdentifier: {} and accountIdentifier: {} ", hierarchyBean.getServiceIdString(), hierarchyBean.getAccountIdString());
                return compInstancePojoList;
            }

            Map<Integer, CompInstClusterDetails> instanceDetails = instanceRepo.getInstances(hierarchyBean.getAccountIdString())
                    .stream().collect(Collectors.toMap(CompInstClusterDetails::getId, Function.identity()));

            compInstancePojoList = instanceBeanList.stream()
                    .filter(f -> f.getComponentTypeId() != Constants.COMPONENT_TYPE_HOST_ID)
                    .map(f -> {
                        if (!instanceDetails.containsKey(f.getId())) {
                            return null;
                        }
                        CompInstClusterDetails instanceDetail = instanceDetails.get(f.getId());

                        ComptInstancePojo compInstance = new ComptInstancePojo();
                        compInstance.setInstanceId(instanceDetail.getId());
                        compInstance.setInstanceName(instanceDetail.getName());
                        compInstance.setInstanceVersion(instanceDetail.getComponentVersionName());
                        compInstance.setHostName(instanceDetail.getHostName());
                        if (instanceDetail.getHostId() != 0) {
                            compInstance.setHostId(instanceDetail.getHostId());
                            compInstance.setHostType(instanceDetails.get(instanceDetail.getHostId()).getComponentName());
                            compInstance.setHostVersion(instanceDetails.get(instanceDetail.getHostId()).getComponentVersionName());
                        }
                        compInstance.setIpAddress(instanceDetail.getHostAddress());

                        return compInstance;
                    }).filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (compInstancePojoList.isEmpty()) {
                compInstancePojoList = instanceBeanList.stream()
                        .filter(f -> f.getComponentTypeId() == Constants.COMPONENT_TYPE_HOST_ID)
                        .map(f -> {
                            if (!instanceDetails.containsKey(f.getId())) {
                                return null;
                            }
                            CompInstClusterDetails instanceDetail = instanceDetails.get(f.getId());

                            ComptInstancePojo compInstance = new ComptInstancePojo();
                            compInstance.setHostId(instanceDetail.getId());
                            compInstance.setHostName(instanceDetail.getName());
                            compInstance.setHostType(instanceDetail.getComponentName());
                            compInstance.setHostVersion(instanceDetail.getComponentVersionName());
                            compInstance.setIpAddress(instanceDetail.getHostAddress());

                            return compInstance;
                        }).filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }

            return compInstancePojoList;
        } catch (Exception e) {
            log.error("Exception occurred while getting instances details. ", e);
            throw new DataProcessingException("Exception occurred while getting instances details.");
        }
    }
}
