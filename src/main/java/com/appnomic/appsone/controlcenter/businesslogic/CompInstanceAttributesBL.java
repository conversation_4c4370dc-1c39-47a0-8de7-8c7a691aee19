package com.appnomic.appsone.controlcenter.businesslogic;

import com.appnomic.appsone.common.util.Commons;
import com.appnomic.appsone.controlcenter.beans.CompInstAttributesBean;
import com.appnomic.appsone.controlcenter.beans.HierarchyBean;
import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.AccountDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.ComponentDataService;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.CompInstAttributesPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.AECSBouncyCastleUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.Security;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class CompInstanceAttributesBL implements BusinessLogic<HierarchyBean, List<CompInstAttributesBean>, List<CompInstAttributesPojo>> {

    private static final Logger logger = LoggerFactory.getLogger(CompInstanceAttributesBL.class);

    @Override
    public UtilityBean<HierarchyBean> clientValidation(RequestObject requestObject) throws ClientException {
        HierarchyBean hierarchyBean = new HierarchyBean();

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            logger.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String accountIdPattern = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (StringUtils.isEmpty(accountIdPattern)) {
            logger.error(UIMessages.ACCOUNT_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_EMPTY);
        }
        hierarchyBean.setAccountIdString(accountIdPattern);

        String instanceIdPattern = requestObject.getParams().get(Constants.INSTANCE_ID);
        if (StringUtils.isEmpty(instanceIdPattern)) {
            logger.error(UIMessages.INSTANCE_EMPTY);
            throw new ClientException(UIMessages.INSTANCE_EMPTY);
        }
        hierarchyBean.setInstanceIdString(instanceIdPattern);

        return UtilityBean.<HierarchyBean>builder()
                .pojoObject(hierarchyBean)
                .authToken(authKey)
                .build();
    }

    @Override
    public List<CompInstAttributesBean> serverValidation(UtilityBean<HierarchyBean> utilityBean) throws ServerException {
        HierarchyBean hierarchyBean = utilityBean.getPojoObject();
        int accountId = AccountDataService.getAccountByIdentifier(hierarchyBean.getAccountIdString(), null);
        if (accountId == 0) {
            logger.error(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
            throw new ServerException(UIMessages.ACCOUNT_IDENTIFIER_INVALID);
        }
        hierarchyBean.setAccountId(accountId);

        String userId = ValidationUtils.getUserId(utilityBean.getAuthToken());
        if (userId == null) {
            logger.error("Error while extracting userIdentifier from authorization token. Reason: Could be invalid authorization token");
            throw new ServerException("Error while extracting user details from authorization token");
        }

        int instanceId = Integer.parseInt(hierarchyBean.getInstanceIdString());

        try {
            String compInstanceIdentifier = new CompInstanceDataService().getCompInstanceIdentifierFromId(instanceId, accountId, null);
            if (compInstanceIdentifier == null) {
                logger.error("Validation failure. Invalid instance ID: [{}]", hierarchyBean.getInstanceIdString());
                throw new ServerException(UIMessages.INVALID_INSTANCE);
            }
        } catch (ControlCenterException e) {
            logger.error("Exception occurred while getting instances details ");
            throw new ServerException("Exception occurred while getting instances details ");
        }
        List<CompInstAttributesBean> allValidInstanceAttributes = ComponentDataService.getInstanceMappingAttributes(instanceId, null);
        List<CompInstAttributesBean> instanceAttributes = ComponentDataService.getInstanceAttributes(instanceId, null);
        Map<String, CompInstAttributesBean> compInstAttributesBeanMap = instanceAttributes.stream().collect(Collectors.toMap(CompInstAttributesBean::getAttributeKey, t -> t));
        for (CompInstAttributesBean compInstAttributesBean : allValidInstanceAttributes) {
            CompInstAttributesBean instAttributesBean = compInstAttributesBeanMap.get(compInstAttributesBean.getAttributeKey());
            if (instAttributesBean != null) {
                compInstAttributesBean.setAttributeId(instAttributesBean.getAttributeId());
                compInstAttributesBean.setAttributeValue(instAttributesBean.getAttributeValue());
                compInstAttributesBean.setCreatedTime(instAttributesBean.getCreatedTime());
                compInstAttributesBean.setUpdatedTime(instAttributesBean.getUpdatedTime());
            }
            if (compInstAttributesBean.getUserDetailsId() == null) compInstAttributesBean.setUserDetailsId(userId);
        }
        return allValidInstanceAttributes;
    }

    @Override
    public List<CompInstAttributesPojo> process(List<CompInstAttributesBean> compInstAttributesBeans) throws DataProcessingException {
        List<CompInstAttributesPojo> compInstAttributesPojos = new ArrayList<>();
        for (CompInstAttributesBean compInstAttributesBean : compInstAttributesBeans) {
            CompInstAttributesPojo compInstAttributesPojo = new CompInstAttributesPojo();
            compInstAttributesPojo.setAttributeId(compInstAttributesBean.getAttributeId());
            compInstAttributesPojo.setAttributeName(compInstAttributesBean.getAttributeName());
            compInstAttributesPojo.setAttributeValue(compInstAttributesBean.getAttributeValue());
            compInstAttributesPojo.setAttributeKey(compInstAttributesBean.getAttributeKey().toLowerCase());
            if (compInstAttributesBean.getType().equalsIgnoreCase("password")) {
                String attributeValue = compInstAttributesPojo.getAttributeValue();
                if (attributeValue != null && !attributeValue.isEmpty()) {
                    try {
                        Security.removeProvider(Constants.BC_PROVIDER_NAME);
                        attributeValue = Commons.decrypt(attributeValue);
                    } catch (Exception e) {
                        logger.error("Exception encountered while decrypting the password. Details: {}", e.getMessage(), e);
                        throw new DataProcessingException("Error while decrypting the password from the database.");
                    }
                    try {
                        attributeValue = new AECSBouncyCastleUtil().encrypt(attributeValue);
                    } catch (Exception e) {
                        logger.error("Exception encountered while encrypting the password. Details: {}", e.getMessage(), e);
                        throw new DataProcessingException("Error while encrypting the password from the database.");
                    }
                }
                compInstAttributesPojo.setAttributeValue(attributeValue);
            }

            Map<String, Object> properties = new HashMap<>();
            Map<String, String> metaData = new HashMap<>();
            if (compInstAttributesBean.getOptions() != null) {
                String[] options = compInstAttributesBean.getOptions().split(",");
                for (String option : options) {
                    metaData.put(option.toLowerCase(), option);
                }
            }
            properties.put("options", metaData);
            int min = compInstAttributesBean.getMin();
            int max = compInstAttributesBean.getMax();
            String pattern = compInstAttributesBean.getPattern();
            String type = compInstAttributesBean.getType();
            int required = compInstAttributesBean.getRequired();
            String errorMessage = compInstAttributesBean.getErrorMessage();
            properties.put("min", min);
            properties.put("max", max);
            properties.put("pattern", pattern);
            properties.put("type", type);
            properties.put("required", required);
            properties.put("errorMessage", errorMessage);
            if (compInstAttributesPojo.getAttributeKey().equals("hostaddress")) {
                properties.put("disabled", 1);
            } else {
                properties.put("disabled", 0);
            }
            compInstAttributesPojo.setProperties(properties);
            compInstAttributesPojos.add(compInstAttributesPojo);
        }
        return compInstAttributesPojos;
    }
}
