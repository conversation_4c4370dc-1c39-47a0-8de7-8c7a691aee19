package com.appnomic.appsone.controlcenter.cache;

import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.TestDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

public class DBTestCache {

    private DBTestCache(){
        //Dummy constructor
    }

    private static final Logger logger = LoggerFactory.getLogger(DBTestCache.class);

    private static Deque<DBDetail> dbCache = new LinkedList<>();


    public static void addToCache(String table, Integer id){
        if(MySQLConnectionManager.getInstance().isIntegrationTest() && id > 0){
            List<Integer> list = new ArrayList<>();
            list.add(id);
            dbCache.add(new DBDetail(table, list));
        }
    }

    public static void addToCache(String table, int[] ids){
        if(MySQLConnectionManager.getInstance().isIntegrationTest()){
            List<Integer> list = Arrays.stream(ids).filter(id -> id > 0)
                                       .mapToObj(Integer::valueOf).collect(Collectors.toList());
            dbCache.add(new DBDetail(table, list));
        }
    }

    public static boolean isCached(){
        return !dbCache.isEmpty();
    }

    public static boolean rollback(){
        logger.info("rolling back. cached data available: {} ", !dbCache.isEmpty());
        TestDao dao = MySQLConnectionManager.getInstance().getHandle().open(TestDao.class);
        try {
            while (!dbCache.isEmpty()){
                DBDetail detail = dbCache.removeLast();
                dao.deleteEntry(detail.getTable(), detail.getIds());
                logger.info("entry deleted from table: {} with id: {}", detail.getTable(),
                             detail.getIds());
            }
        }catch (Exception e){
            logger.error("Error in rollback", e);
            return false;
        }finally {
            MySQLConnectionManager.getInstance().getHandle().close(dao);
        }
        return true;
    }

    @Data
    @AllArgsConstructor
    @EqualsAndHashCode
    static class DBDetail{
        String table;
        List<Integer> ids;
    }
}
