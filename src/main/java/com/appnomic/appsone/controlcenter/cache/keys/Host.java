package com.appnomic.appsone.controlcenter.cache.keys;

import com.appnomic.appsone.controlcenter.common.Constants;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> : 4/2/19
 */
@Data
public class Host {
    private String hostAddress;
    private int accountId;
    private int defaultAccountId= Constants.DEFAULT_ACCOUNT_ID;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Host host = (Host) o;
        return accountId == host.accountId &&
                Objects.equals(hostAddress, host.hostAddress);
    }

    @Override
    public int hashCode() {
        return Objects.hash(hostAddress, accountId);
    }
}
