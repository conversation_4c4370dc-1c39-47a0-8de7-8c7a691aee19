/*
 * Copyright (c) 2015-2016 Appnomic Systems Private Limited, Bangalore
 *
 * All rights reserved
 *
 * The source code in this file is licensed and does not fall under any
 * type of open source license.  No form of reproduction is allowed without
 * prior written consent from Appnomic Systems.
 */

/*
 * Created by <PERSON><PERSON> on 18/4/16.
 */
package com.appnomic.appsone.controlcenter.cache;

import com.appnomic.appsone.common.util.ConfProperties;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public enum ThreadPool
{
    INSTANCE;

    public final ThreadPoolExecutor ioBoundExecutor;
    private final int workerThreadCount;
    public final ThreadPoolExecutor hostAvailabilityExecutor;
    private ThreadFactory ioBoundThreadFactory = new ThreadFactoryBuilder().setNameFormat("broker-io-pool-thread %d").build();
    private ThreadFactory hostAvailabilityFactory = new ThreadFactoryBuilder().setNameFormat("host-availability-pool-thread %d").build();

    ThreadPool()
    {
        workerThreadCount = calculateWorkerThreadCount();
        ioBoundExecutor = new ThreadPoolExecutor(workerThreadCount, workerThreadCount, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(workerThreadCount * 100), ioBoundThreadFactory);
        hostAvailabilityExecutor = new ThreadPoolExecutor(workerThreadCount, workerThreadCount, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(workerThreadCount), hostAvailabilityFactory);
    }

    /**
     * method which calculates and returns the number of worker threads depending on the
     * hardware cores and the configured multiplier
     *
     * @return
     */
    private int calculateWorkerThreadCount()
    {
        int cores = Constants.AVAILABLE_CORES;
        float multiplier = ConfProperties.getFloat(Constants.WORKER_THREAD_MULTIPLIER_PROPERTY_NAME, Constants.WORKER_THREAD_DEFAULT_MULTIPLIER);
        return cores * multiplier > 1 ? Math.round(cores * multiplier) : 1;   // returning 1 in case the calculated value is not > 1
    }


    public String getIOWorkerStatistics() {
        return String.format("RMQ worker Thread pool size : %d, Currently Executing Task Count: %d, Waiting Tasks in Queue: %d ,Total Completed Task Count: %d, Total Submitted Task Count: %d",
                ioBoundExecutor.getPoolSize(),
                ioBoundExecutor.getActiveCount(),
                ioBoundExecutor.getQueue().size(),
                ioBoundExecutor.getCompletedTaskCount(),
                ioBoundExecutor.getTaskCount());
    }

    public String getHostAvailabilityWorkerStatistics() {
        return String.format("HostAvailability worker Thread pool size : %d, Currently Executing Task Count: %d, Waiting Tasks in Queue: %d ,Total Completed Task Count: %d, Total Submitted Task Count: %d",
                hostAvailabilityExecutor.getPoolSize(),
                hostAvailabilityExecutor.getActiveCount(),
                hostAvailabilityExecutor.getQueue().size(),
                hostAvailabilityExecutor.getCompletedTaskCount(),
                hostAvailabilityExecutor.getTaskCount());
    }
}