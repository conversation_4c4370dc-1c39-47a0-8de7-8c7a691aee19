package com.appnomic.appsone.controlcenter.cache;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.util.ConfProperties;
import lombok.extern.slf4j.Slf4j;

import java.lang.management.ManagementFactory;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR> on 1/12/20
 */
@Slf4j
public enum CCCache {
    INSTANCE;
    private int requestThreshold = ConfProperties.getInt(Constants.REQUEST_THRESHOLD_PROPERTY,
            Constants.REQUEST_THRESHOLD_PROPERTY_DEFAULT_VALUE);

    private final long schedulerPeriodInMS = ConfProperties.getInt(Constants.HEAL_METRICS_SCHEDULER_PROPERTY_NAME, Constants.HEAL_METRICS_SCHEDULER_PROPERTY_NAME_DEFAULT_VALUE);
    private int requests = 0, unauthorizedRequests = 0, accessDeniedRequests = 0, skipValidationRequests = 0, slowRequests = 0, ccErrors = 0;
    private Map<String, Double> slowRequestDetails = new HashMap<>();
    private Map<Integer, Integer> statusCodes = new HashMap<>();
    private double maxRespTimeInMillSecs = 0.0;

    public int getRequestThreshold() {
        return requestThreshold;
    }

    public void setRequestThreshold(int requestThreshold) {
        this.requestThreshold = requestThreshold;
    }

    public int getRequests() {
        return requests;
    }

    public void updateRequests(int count) {
        this.requests += count;
    }

    public int getUnauthorizedRequests() {
        return unauthorizedRequests;
    }

    public void updateUnauthorizedRequests(int count) {
        this.unauthorizedRequests += count;
    }

    public int getAccessDeniedRequests() {
        return accessDeniedRequests;
    }

    public void updateAccessDeniedRequests(int count) {
        this.accessDeniedRequests += count;
    }

    public int getSkipValidationRequests() {
        return skipValidationRequests;
    }

    public void updateSkipValidationRequests(int count) {
        this.skipValidationRequests += count;
    }

    public int getSlowRequests() {
        return slowRequests;
    }

    public void updateSlowRequests(int count) {
        this.slowRequests += count;
    }

    public int getCCErrors() {
        return ccErrors;
    }

    public void updateCCErrors(int count) {
        this.ccErrors += count;
    }

    public Map<String, Double> getSlowRequestDetails() {
        return slowRequestDetails;
    }

    public void resetSlowRequestDetails() {
        slowRequestDetails = new HashMap<>();
    }

    public void addSlowRequestDetails(String api, Double timeInMillSecs) {
        slowRequestDetails.put(api, timeInMillSecs);
    }

    public Map<Integer, Integer> getStatusCodes() {
        return statusCodes;
    }

    public void resetStatusCodes() {
        statusCodes = new HashMap<>();
    }

    public void updateStatusCodes(Integer statusCode, Integer count) {
        statusCodes.put(statusCode, statusCodes.get(statusCode) == null ? 1 : statusCodes.get(statusCode) + count);
    }

    public double getMaxRespTimeInMillSecs() {
        return maxRespTimeInMillSecs;
    }

    public void updateResponseTime(String url, double respTimeInMillSecs) {
        if (respTimeInMillSecs > this.maxRespTimeInMillSecs) {
            this.maxRespTimeInMillSecs = respTimeInMillSecs;
        }
        if (respTimeInMillSecs >= requestThreshold * 1000) {
            this.updateSlowRequests(1);
            this.addSlowRequestDetails(url, maxRespTimeInMillSecs);
        }
    }

    public void logHealthMetrics() {

        log.info("Memory statistics : ");
        printUsage(Runtime.getRuntime());
        log.info("Available Processors : {}", ManagementFactory.getOperatingSystemMXBean().getAvailableProcessors());
        log.info("System Load Average : {}", ManagementFactory.getOperatingSystemMXBean().getSystemLoadAverage());

        StringBuilder sb = new StringBuilder();
        sb.append(ThreadPool.INSTANCE.getIOWorkerStatistics()).append(", ");
        sb.append(ThreadPool.INSTANCE.getHostAvailabilityWorkerStatistics()).append(", ");
        sb.append("Count of Requests : ").append(getRequests()).append(", ");
        sb.append("Count of Slow Requests : ").append(getSlowRequests()).append(", ");
        sb.append("Count of Unauthorized Requests : ").append(getUnauthorizedRequests()).append(", ");
        sb.append("Count of Access Denied Requests : ").append(getAccessDeniedRequests()).append(", ");
        sb.append("Count of Validation skip Requests : ").append(getSkipValidationRequests()).append(", ");
        sb.append("Errors Count : ").append(getCCErrors()).append(", ");
        sb.append("Max response time in ms : ").append(getMaxRespTimeInMillSecs()).append(", ");
        sb.append("Request Threshold : ").append(getRequestThreshold()).append(", ");

        sb.append("400 Status Count : ").append(getStatusCodes().getOrDefault(400, 0)).append(", ");
        sb.append("401 Status Count : ").append(getStatusCodes().getOrDefault(401, 0)).append(", ");
        sb.append("403 Status Count : ").append(getStatusCodes().getOrDefault(403, 0)).append(", ");
        sb.append("404 Status Count : ").append(getStatusCodes().getOrDefault(404, 0)).append(", ");
        sb.append("500 Status Count : ").append(getStatusCodes().getOrDefault(500, 0)).append(", ");
        sb.append("200 Status Count : ").append(getStatusCodes().getOrDefault(200, 0)).append(", ");

        sb.append("Number of Slow Requests in last ").append(schedulerPeriodInMS).append(" ms : ").append(getSlowRequestDetails().size());

        log.debug("Health metrics details : {}", sb);

    }

    public static void printUsage(Runtime runtime) {
        long total, free, used;
        int mb = 1024 * 1024;

        total = runtime.totalMemory();
        free = runtime.freeMemory();
        used = total - free;
        log.info("Total Memory: {} MB", total / mb);
        log.info("Memory Used: {} MB", used / mb);
        log.info("Memory Free: {} MB", free / mb);
        log.info("Memory Percent Used: {} %", ((double) used / (double) total) * 100);
        log.info("Memory Percent Free: {} %", ((double) free / (double) total) * 100);
    }
}
