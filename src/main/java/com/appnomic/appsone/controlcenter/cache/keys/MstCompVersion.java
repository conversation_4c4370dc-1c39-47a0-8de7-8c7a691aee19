package com.appnomic.appsone.controlcenter.cache.keys;

import com.appnomic.appsone.controlcenter.common.Constants;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> : 4/2/19
 */
@Data
public class MstCompVersion {
    private int mstCompId;
    private String compVersionName;
    private int defaultAccountId = Constants.DEFAULT_ACCOUNT_ID;
    private int accountId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MstCompVersion that = (MstCompVersion) o;
        return mstCompId == that.mstCompId &&
                accountId == that.accountId &&
                Objects.equals(compVersionName, that.compVersionName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(mstCompId, compVersionName, accountId);
    }
}
