package com.appnomic.appsone.controlcenter.pojo;

import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Data
public class ProducerKpiMappingDetails {
    private static final Logger logger = LoggerFactory.getLogger(ProducerKpiMappingDetails.class);
    private String kpiIdentifier;
    private String componentVersionId;
    private String componentName;
    private String componentTypeName;
    private int isDefault = 1;

    public boolean isValid() {
        return (this.getKpiIdentifier() != null && this.getKpiIdentifier().length() > 0 &&
                this.getComponentVersionId() != null && this.getComponentVersionId().length() > 0 &&
                this.getComponentName() != null && this.getComponentName().length() > 0 &&
                this.getComponentTypeName() != null && this.getComponentTypeName().length() > 0 &&
                this.getIsDefault() >= 0 && this.getIsDefault() <= 1);
    }

}
