package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR> on 10/06/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class PostTransactionThresholdPojo {

    private String accountIdentifier;

    private int accountId;
    private String serviceIdentifier;
    private int serviceId;
    private String transactionIdentifier;
    private int transactionId;
    private String thresholdType;
    private String responseTimeType;
    private List<TransactionStaticThresholds> transactionStaticThresholdsList;

    public PostTransactionThresholdPojo(RequestObject request) throws JsonProcessingException {

        accountIdentifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        serviceIdentifier = request.getParams().get(Constants.SERVICE_ID);
        transactionIdentifier = request.getParams().get(Constants.TRANSACTION_IDENTIFIER);
        thresholdType = request.getQueryParams().get("thresholdType")[0];
        responseTimeType = request.getQueryParams().get("responseTimeType")[0];
        transactionStaticThresholdsList = CommonUtils.getObjectMapper().readValue(request.getBody(), new TypeReference<List<TransactionStaticThresholds>>() {
        });

    }

    public boolean isValidParameters() {
        if (StringUtils.isEmpty(this.getAccountIdentifier())) {
            log.error("Account Identifier is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(this.getServiceIdentifier())) {
            log.error("Service Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(this.getTransactionIdentifier())) {
            log.error("Transaction Id is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(this.getThresholdType())) {
            log.error("Threshold type is null/empty");
            return false;
        }

        if (StringUtils.isEmpty(this.getResponseTimeType())) {
            log.error("Response Time type is null/empty");
            return false;
        }

        for (TransactionStaticThresholds thresholds : this.transactionStaticThresholdsList) {
            if (StringUtils.isEmpty(thresholds.getKpiId())) {
                log.error("Kpi Id is null/empty");
                return false;
            }

            if (StringUtils.isEmpty(thresholds.getKpiName())) {
                log.error("Kpi name is null/empty");
                return false;
            }

            if (StringUtils.isEmpty(thresholds.getKpiDataType())) {
                log.error("Kpi data type is null/empty");
                return false;
            }

            if (StringUtils.isEmpty(thresholds.getKpiUnit())) {
                log.error("Kpi unit is null/empty");
                return false;
            }

            if (StringUtils.isEmpty(String.valueOf(thresholds.getCategoryId()))) {
                log.error("Category Id is null/empty");
                return false;
            }

            if (StringUtils.isEmpty(thresholds.getCategoryName())) {
                log.error("Category name is null/empty");
                return false;
            }

            try {
                Integer.parseInt(thresholds.getKpiId());
            } catch (NumberFormatException e) {
                log.error("Invalid value found where number is required for kpi id. {}", e.getMessage());
                return false;
            }

            if (thresholds.isUserDefinedSOR()) {
                if (thresholds.getUserThresholds() == null || thresholds.getUserThresholds().isEmpty()) {
                    log.error("User Thresholds is null/empty.");
                    return false;
                }
                if (StringUtils.isEmpty(thresholds.getUserDefinedOperationType())) {
                    log.error("User defined operation type is null/empty");
                    return false;
                }
                if (thresholds.getUserDefinedOperationType().equalsIgnoreCase("not between")
                        && thresholds.getUserThresholds().get("MAX") < thresholds.getUserThresholds().get("MIN")) {
                    log.error("MAX threshold value less than MIN threshold value for \"not between\" operation.");
                    return false;
                }
            }

            if (thresholds.getPersistence() != null && thresholds.getPersistence() <= 0) {
                log.error("Persistence value can't be negative or zero.");
                return false;
            }

            if (thresholds.getSuppression() != null && thresholds.getSuppression() <= 0) {
                log.error("Suppression value can't be negative or zero.");
                return false;
            }

            if (thresholds.getPersistence() == null && (thresholds.getSuppression() != null && thresholds.getSuppression() > 0)) {
                log.error("Persistence is null/empty and suppression is non-null and greater than 0.");
                return false;
            }

            if (thresholds.getSuppression() == null && (thresholds.getPersistence() != null && thresholds.getPersistence() > 0)) {
                log.error("Suppression is null/empty and persistence is non-null and greater than 0.");
                return false;
            }


            if (thresholds.getPersistence() != null && thresholds.getPersistence() > 1440) {
                log.error("Persistence value can't be more than 1440");
                return false;
            }

            if (thresholds.getSuppression() != null && thresholds.getSuppression() > 1440) {
                log.error("Suppression value can't be more than 1440");
                return false;
            }

        }

        try {
            this.serviceId = Integer.parseInt(serviceIdentifier);
            this.transactionId = Integer.parseInt(transactionIdentifier);
        } catch (NumberFormatException e) {
            log.error("Invalid value found where number is required. {}", e.getMessage());
            return false;
        }
        return true;
    }

}
