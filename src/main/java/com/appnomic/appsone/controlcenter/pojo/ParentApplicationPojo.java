package com.appnomic.appsone.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
@Slf4j
public class ParentApplicationPojo {
    private String parentApplication;

    public boolean validate() {

        if (this.parentApplication == null || this.parentApplication.length() > 128 || this.parentApplication.trim().isEmpty()) {
            log.error("Invalid parent application {}. It is either NULL or empty or is greater than 128 characters", this.parentApplication);
            return false;
        }
        return true;
    }
}

