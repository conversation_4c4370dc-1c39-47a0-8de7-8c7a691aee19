package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import lombok.Data;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;

@Data
public class RulePojo {
    private int dataId;
    private String id;
    private String applicableTo;
    private String kpiAttribute;
    private String description;
    private String operationType;
    private Timestamp updatedTime;
    private Map<String,Double> thresholds;
    private String unit;
    private Map<String, String> errorMessage = new HashMap<>();
    private static final String operationTypeError="operationType";

    public void validate(String kpiType) {

        this.kpiAttribute = Constants.ALL;
        if ((this.applicableTo == null) || this.applicableTo.isEmpty()) {
            errorMessage.put("applicableTo", "applicableTo value can not be null or empty.");
        } else {
            try {
                Applicable.valueOf(applicableTo.trim());
            } catch (IllegalArgumentException e) {
                errorMessage.put("applicableTo", "Invalid applicable value");
            }
        }

        if (this.id == null || this.id.isEmpty()) {
            errorMessage.put("kpiId", "kpiId value can not be null or empty.");
        }

        if (operationType == null || operationType.trim().length() == 0) {
            errorMessage.put("operationType", "operationType should not be empty or null");
        } else {
            OperationTypeEnum findByType = OperationTypeEnum.findByType(operationType.trim());
            if (findByType == null) {
                errorMessage.put("operationType", "Invalid operation Type value");
            }

            ViewTypes types;
            if (!kpiType.equals(Constants.CORE_KPI_TYPE)) {
                types = MasterCache.getMstTypeForSubTypeName(Constants.AVAILABILITY_OPERATIONS_TYPE, operationType);
            } else {
                types = MasterCache.getMstTypeForSubTypeName(Constants.OPERATIONS_TYPE, operationType);
            }

            if (types == null) {
                errorMessage.put("operationType", "No any sub type is found for given operation type");
            }

            if (kpiType.equals(Constants.CORE_KPI_TYPE)) {
                if (this.thresholds.size() <= 0) {
                    errorMessage.put("threshold", "threshold value can not be null or empty.");
                }
                try {
                    thresholds.forEach((key, value) -> Thresholds.valueOf(key));
                } catch (IllegalArgumentException e) {
                    errorMessage.put("threshold", "Invalid threshold value");
                }
            }
        }
    }
}
