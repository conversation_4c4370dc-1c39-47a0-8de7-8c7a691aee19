package com.appnomic.appsone.controlcenter.pojo;

public enum OptInEnum {
    REQUEST_TO_BE_SENT(0,"Request to be sent"),
    AWAITING_CALLBACK(1, "Awaiting Callback"),
    ACCEPTED(2, "Accepted"),
    REJECTED(3, "Rejected");


    private final String status;
    private final int statusCode;

    OptInEnum(int statusCode, String status) {
        this.status = status;
        this.statusCode = statusCode;
    }

    public String getStatus() {
        return status;
    }

    public int getStatusCode(){
        return statusCode;
    }

    @Override
    public String toString() {
        return status;
    }
}
