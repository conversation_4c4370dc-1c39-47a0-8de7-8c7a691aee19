package com.appnomic.appsone.controlcenter.pojo;

import lombok.Data;

@Data
public class AllKpiList {

    private int kpiId;
    private String kpiName;
    private String identifier;
    private int isCustom;
    private String kpiUnits;
    private String clusterOperation;
    private String kpiType;
    private int groupId;
    private String groupName;
    private int isDiscovery;
    private int groupStatus;
    private String rollupOperation;
    private int clusterAggType;
    private int instanceAggType;
    private String valueType;
    private String dataType;
    private int isInfo;
    private int isBaseMetric;
    private String computationExpression;
    private int resetDeltaValue;
    private int deltaPerSec;
    private String cronExpression;
}
