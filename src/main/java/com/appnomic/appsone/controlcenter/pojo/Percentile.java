package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.Operation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Percentile {

    private static final Logger LOGGER = LoggerFactory.getLogger(Percentile.class);

    private int id;
    private String name;
    private int value;
    private Operation operation;

    public boolean validate() {
        if (this.name != null && (this.name.trim().isEmpty() || this.name.length() > 128)) {
            LOGGER.error("'name' is either empty or contains more than 128 characters.");
            return false;
        }

        if (this.value > 100 || this.value < 1) {
            LOGGER.error("'value' should be in range 1 - 100 (exclusive).");
            return false;
        }
        return true;
    }
}
