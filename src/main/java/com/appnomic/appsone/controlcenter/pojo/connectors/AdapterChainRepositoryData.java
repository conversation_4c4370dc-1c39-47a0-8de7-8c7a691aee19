package com.appnomic.appsone.controlcenter.pojo.connectors;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class AdapterChainRepositoryData {
    private int id;
    private String name;
    private String className;
    private String autoConnect;
    private String poolMinSize;
    private String poolMaxSize;

    public AdapterChainRepositoryData(Integer id,String name, String className, String autoConnect, String poolMinSize, String poolMaxSize) {
        this.id = id;
        this.name = name;
        this.className = className;
        this.autoConnect = autoConnect;
        this.poolMinSize = poolMinSize;
        this.poolMaxSize = poolMaxSize;
    }

    public static final List<AdapterChainRepositoryData> getSapRepositoryData(){
        List<AdapterChainRepositoryData> adapterChainRepositoryData = new ArrayList<>();

        adapterChainRepositoryData.add(new AdapterChainRepositoryData(1,"SapReposetory",
                "com.appnomic.heal.sap.reposetory.SapReposetory","true","1","3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(2,"NameValueRepository",
                "com.appnomic.heal.etladapter.repositories.NameValueRepository","true","1","3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(3,"LogAnalyzerKPIMaster",
                "com.appnomic.heal.etladapter.etl.logscan.repository.LogAnalyzerKPIMaster","true","1","3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(4,"AppsoneRepository",
                "com.appnomic.heal.etladapter.repositories.AppsoneRepository", "true", "1", "3"));
        return adapterChainRepositoryData;
    }

    public static List<AdapterChainRepositoryData> getAppDynamicsRepositoryData() {

        List<AdapterChainRepositoryData> adapterChainRepositoryData = new ArrayList<>();
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(1,"AppdynamicsKPIMaster",
                "com.appnomic.heal.appdynamics.repository.AppdynamicsKPIMaster", "true", "1", "3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(2,"NameValueRepository",
                "com.appnomic.heal.etladapter.repositories.NameValueRepository", "true", "1", "3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(3,"LogAnalyzerKPIMaster",
                "com.appnomic.heal.etladapter.etl.logscan.repository.LogAnalyzerKPIMaster", "true", "1", "3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(4,"AppsoneRepository",
                "com.appnomic.heal.etladapter.repositories.AppsoneRepository", "true", "1", "3"));
        return adapterChainRepositoryData;

    }

    public static List<AdapterChainRepositoryData> getAzureRepositoryData() {

        List<AdapterChainRepositoryData> adapterChainRepositoryData = new ArrayList<>();
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(1,"AzureKPIMaster",
                "com.appnomic.heal.azure.repository.AzureKPIMaster", "true", "1", "3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(2,"NameValueRepository",
                "com.appnomic.heal.etladapter.repositories.NameValueRepository", "true", "1", "3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(3,"LogAnalyzerKPIMaster",
                "com.appnomic.heal.etladapter.etl.logscan.repository.LogAnalyzerKPIMaster", "true", "1", "3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(4,"AppsoneRepository",
                "com.appnomic.heal.etladapter.repositories.AppsoneRepository", "true", "1", "3"));
        return adapterChainRepositoryData;
    }

    public static List<AdapterChainRepositoryData> getKubernetesRepositoryData() {

        List<AdapterChainRepositoryData> adapterChainRepositoryData = new ArrayList<>();
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(1,"KubernetesKPIMaster",
                "com.appnomic.heal.kubernetes.repository.KubernetesKPIMaster", "true", "1", "3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(2,"NameValueRepository",
                "com.appnomic.heal.etladapter.repositories.NameValueRepository", "true", "1", "3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(3,"LogAnalyzerKPIMaster",
                "com.appnomic.heal.etladapter.etl.logscan.repository.LogAnalyzerKPIMaster", "true", "1", "3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(4,"PrometheusKPIMaster",
                "com.appnomic.heal.prometheus.repository.PrometheusKPIMaster", "true", "1", "3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(5,"AppsoneRepository",
                "com.appnomic.heal.etladapter.repositories.AppsoneRepository", "true", "1", "3"));
        return adapterChainRepositoryData;

    }

    public static List<AdapterChainRepositoryData> getAwsRepositoryData() {

        List<AdapterChainRepositoryData> adapterChainRepositoryData = new ArrayList<>();
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(1,"AWSKPIMaster",
                "com.appnomic.heal.aws.repository.AWSKPIMaster", "true", "1", "3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(2,"NameValueRepository",
                "com.appnomic.heal.etladapter.repositories.NameValueRepository", "true", "1", "3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(3,"LogAnalyzerKPIMaster",
                "com.appnomic.heal.etladapter.etl.logscan.repository.LogAnalyzerKPIMaster", "true", "1", "3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(4,"AppsoneRepository",
                "com.appnomic.heal.etladapter.repositories.AppsoneRepository", "true", "1", "3"));
        return adapterChainRepositoryData;

    }

    public static List<AdapterChainRepositoryData> getDynatraceRepositoryData() {

        List<AdapterChainRepositoryData> adapterChainRepositoryData = new ArrayList<>();
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(1,"DynaTraceReposetory",
                "com.appnomic.heal.dynatrace.reposetory.DynaTraceReposetory", "true", "1", "3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(2,"NameValueRepository",
                "com.appnomic.heal.etladapter.repositories.NameValueRepository", "true", "1", "3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(3,"LogAnalyzerKPIMaster",
                "com.appnomic.heal.etladapter.etl.logscan.repository.LogAnalyzerKPIMaster", "true", "1", "3"));
        adapterChainRepositoryData.add(new AdapterChainRepositoryData(4,"AppsoneRepository",
                "com.appnomic.heal.etladapter.repositories.AppsoneRepository", "true", "1", "3"));
        return adapterChainRepositoryData;

    }
}

