package com.appnomic.appsone.controlcenter.pojo;

import com.heal.configuration.pojos.InstanceAttributes;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AgentInstanceMapping {
    private int agentId;
    private String agentName;
    private String agentType;
    private String agentIdentifier;
    private String physicalAgentIdentifier;
    private List<String> compInstanceIdentifier;
    private Map<String, List<InstanceAttributes>> compInstanceAttributesMap;
}
