package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import java.util.List;

/**
 * <AUTHOR> on 28/04/2022
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class AgentsDataSources {
    private int agentId;
    private List<String> addedDataSources;
    private List<String> deletedDataSources;

    public boolean validate() {
        boolean retVal = true;
        if(agentId <= 0) {
            log.error("Agent Id {} is invalid; should be a greater than zero.", agentId);
            retVal = false;
        }
        for(String dataSource : addedDataSources) {
            if(StringUtils.isEmpty(dataSource)) {
                log.error("Agent data source to be added is null or empty.");
                retVal = false;
            }
        }
        for(String dataSource : deletedDataSources) {
            if(StringUtils.isEmpty(dataSource)) {
                log.error("Agent data source to be deleted is null or empty.");
                retVal = false;
            }
        }
        return retVal;
    }
}