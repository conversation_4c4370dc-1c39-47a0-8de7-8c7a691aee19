package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.beans.UserAttributesBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import lombok.Data;

import java.text.MessageFormat;

@Data
public class OptInRequestPojo {
    Integer optInStatus;
    Long optInLastRequestTime;
    String optInLastRequestTimeStr;
    UserAttributesBean userAttributesBean;
    String contactNumber;

    public void validate() throws ClientException {
        if(null == this.optInStatus){
            throw new ClientException("opt-in status can't be null.");
        }

        if(null == this.optInLastRequestTime){
            throw new ClientException("opt-in last updated time can't be null.");
        }

        if(this.optInStatus<=0 || this.optInStatus>4){
            throw new ClientException(MessageFormat.format("Invalid opt-in status: {0}", this.optInStatus));
        }
        if(null == this.contactNumber){
            throw new ClientException("Contact number can't be null.");
        }
    }
}
