package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> : 7/2/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Network {
    private int id;
    private String name;
    private String protocol;
    private String type;
    private String host;
    private int port;
    private int status;
    private String description;
    private String endpoint;

    public void validate() throws ControlCenterException {
        if (this.id != 0) {
            if (StringUtils.isEmpty(this.name)) throw new ControlCenterException("Data communication name can not be null or empty.");
            if (StringUtils.isEmpty(this.protocol)) throw new ControlCenterException("Data communication protocol can not be null or empty.");
            if (StringUtils.isEmpty(this.host)) throw new ControlCenterException("Data communication host can not be null or empty.");
            if (StringUtils.isEmpty(this.type)) throw new ControlCenterException("Type can not be null or empty.");
            if (this.port == 0) throw new ControlCenterException("Data communication port can not be zero");
        }
    }
}
