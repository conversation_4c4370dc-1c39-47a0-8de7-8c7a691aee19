package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.NotificationPlaceholderBean;
import lombok.Data;

@Data
public class NotificationPlaceholder {

    private int id;
    private String name;
    private String placeholderName;
    private String template;
    private int status;

    public static NotificationPlaceholder getInstance(NotificationPlaceholderBean bean){
        NotificationPlaceholder placeholder = new NotificationPlaceholder();
        placeholder.setId(bean.getId());
        placeholder.setName(bean.getName());
        placeholder.setPlaceholderName(bean.getPlaceholderName());
        placeholder.setTemplate(bean.getTemplate());
        placeholder.setStatus(bean.getStatus());
        return placeholder;
    }

}
