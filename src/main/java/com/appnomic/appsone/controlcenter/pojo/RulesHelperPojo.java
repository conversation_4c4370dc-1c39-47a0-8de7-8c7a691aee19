package com.appnomic.appsone.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RulesHelperPojo {
    private int id;
    private String name;
    private int enabled;
    private int discoveryEnabled;
    private int order;
    private int ruleTypeId;
    private int isDefault;
    private int maxTags;
    private int concernedConfigId;
    private int tcpId;
    private int tcpLength;
    private String tcpInitialPattern;
    private String tcpLastPattern;
    private int httpId;
    private int httpFirstUriSegments;
    private int httpLastUriSegments;
    private int httpCompleteURI;
    private int httpPayloadTypeId;
}
