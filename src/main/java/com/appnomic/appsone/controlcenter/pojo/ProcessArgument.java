package com.appnomic.appsone.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProcessArgument {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProcessArgument.class);

    private int id;
    private String name;
    private String value;
    private String defaultValue;
    private int order;
    private int placeHolder;

    public boolean validate() {

        if (this.name == null || this.name.trim().isEmpty() || this.name.length() > 256) {
            LOGGER.error("'name' in arguments is NULL or empty or contains more than 256 characters");
            return false;
        }

        if (this.value == null || this.value.trim().isEmpty() || this.value.length() > 256) {
            LOGGER.error("'value' in arguments is NULL or empty or contains more than 256 characters");
            return false;
        }

        if (this.defaultValue == null || this.defaultValue.trim().isEmpty() || this.defaultValue.length() > 256) {
            LOGGER.error("'defaultValue' in arguments is NULL or empty or contains more than 256 characters");
            return false;
        }

        if (this.order != 0 && this.order != 1) {
            LOGGER.error("'order' in arguments can only be 0 or 1.");
            return false;
        }

        if (this.placeHolder != 0 && this.placeHolder != 1) {
            LOGGER.error("'placeHolder' in arguments can only be 0 or 1.");
            return false;
        }

        return true;
    }
}
