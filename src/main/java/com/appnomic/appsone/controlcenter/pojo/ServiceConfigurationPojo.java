package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.UIMessages;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ServiceConfigurationPojo {

    private int serviceId;

    private Map<String, String> error = new HashMap<>();

    private static final Logger logger = LoggerFactory.getLogger(ServiceConfigurationPojo.class);

    public void validate(){
        if (serviceId <= 0) {
            logger.error(UIMessages.INVALID_SERVICE);
            error.put("Service ID", UIMessages.INVALID_SERVICE);
        }
    }

}
