package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.RequestException;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.net.URL;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WebHookPojo {
    private String url;

    private Map<String, String> error = new HashMap<>();

    private static final Logger logger = LoggerFactory.getLogger(WebHookPojo.class);

    public void validate() throws RequestException {
        if (StringUtils.isEmpty(url)) {
            logger.error(UIMessages.EMPTY_WEBHOOK_URL);
            throw new RequestException(UIMessages.EMPTY_WEBHOOK_URL);
        }
        try{
            new URL(url).toURI();
        }catch (Exception e){
            logger.error(UIMessages.INVALID_WEBHOOK_URL);
            throw new RequestException(UIMessages.INVALID_WEBHOOK_URL);
        }
    }

}
