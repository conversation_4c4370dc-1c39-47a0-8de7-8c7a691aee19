package com.appnomic.appsone.controlcenter.pojo;

import lombok.Data;

@Data
public class ServiceEndPoint {
    private String ipAddress;
    private Integer portNumber;
    public ServiceEndPoint(String ip, String port) {
        this.ipAddress = ip;
        try {
            this.portNumber = Integer.valueOf(port);
        } catch (NumberFormatException nex) {
            this.portNumber = -1;
        }
    }
}
