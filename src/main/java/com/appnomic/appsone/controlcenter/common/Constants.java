package com.appnomic.appsone.controlcenter.common;

import com.appnomic.appsone.controlcenter.util.ConfProperties;
import org.apache.http.HttpStatus;

import java.util.Arrays;
import java.util.List;

public class Constants {

    private Constants() {

    }

    // ECDSA constants
    public static final String BC_PROVIDER_NAME = "BC";
    public static final String KEYS_PAIRS_FOLDER = "keys.generation.folder";
    public static final String KEYS_PAIRS_FOLDER_DEFAULT = "/opt/jboss/keycloak/standalone/keys/";

    //Tag details name
    public static final String ACCOUNT_TAG = "Account";
    public static final String CONTROLLER_TAG = "Controller";
    public static final String JIM_AGENT_TYPE = "Behaviour";
    public static final String TXN_REQUEST_TYPE = "request-type";
    public static final String SOURCE_REQUEST_TYPE = "source";
    public static final String JIM_AGENT_SUB_TYPE = "JIMAgent";
    public static final String ACTION_AGENT_TYPE = "ForensicAgent";
    public static final String LAYER_TAG = "LayerName";
    public static final String APP_SERVER_COMPONENT_TYPE = "Application Server";
    public static final String LAYER_DEFAULT = "Type";
    public static final String TIME_ZONE_TAG = "Timezone";
    public static final String AGENT_TAG = "Agent";
    public static final String AGENT_SIILENT_WINDOW = "15";
    public static final String SERVICE_TYPE_TAG = "ServiceType";
    public static final String SERVICE_TYPE_DEFAULT = "Type";

    public static final String AUDIT_PARAM_APPLICATION_NAME = "applicationId";
    public static final String AUDIT_PARAM_USER_NAME = "userId";
    public static final String AUDIT_PARAM_ACTIVITY_TYPE = "activityTypeId";
    public static final String INVALID_FROM_TIME = "invalid fromTime provided";
    public static final String INVALID_TO_TIME = "invalid toTime provided";
    public static final String REQUEST_PARAM_FROM_TIME = "fromTime";
    public static final String REQUEST_PARAM_TO_TIME = "toTime";
    public static final String AUDIT_PARAM_SERVICE_NAME = "serviceId";
    public static final String SERVICE_CLUSTER_APPLICATION_KEY = "applicationId";
    //User Management
    public static final String USER_IDENTIFIER = ":userIdentifier";
    public static final String SETUP_KEYCLOAK = "Keycloak";
    public static final String SETUP_AD_INTEGRATION = "AD Integrated";

    public static final String PUBLIC_KEY_STR = "MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQGlqSVUWsPPs7tM+750AjeNP8SfI4KGO1pmFQwqy/tWe2AOs0Ypi0fVwlo7j5FB1KgGiOjcbeKcrDzt9oxV+R1GEd4E/vKF68FHHcCQXW7n28JYclfzlg2F+aAIzVGs1w3v7UxsU+hqlcp3r3XkensgBBfEtLBoAClTZXZ9ubWvi53vnlsbfWGuSKwNdDHmLo=";
    public static final String PRIVATE_KEY_STR = "MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIABctFX/0PD03c7gUZJSAfoM0HD1j5RNKuY7VPuPLotQH5Plu2fqYi72lw0h6asjLb2Us3wCJfQ8voPFVEDa3wF498CBPpLU8oAcGBSuBBAAnoYGVA4GSAAQBJxFztvZjZL6BaCzIYhiwt1JWZfAKtBL2UPhUQAvJb2KErJm+Qmuk3TTtyTeCmb7JgTbcf/IRZeXAfMACNhY/ia1ET9wXh8gDSvHdLVVUM6gRJkAyjBML6YXu3xeGhAMlN+TmkNgrUmrB8FJuJp871YhXsxfHf1NxY4Yf5x5oUneVhnl3YokFkC1HixwtVbg=";

    public static final String USER_SCHEDULER_TIME_PROPERTY = "user.scheduler.time.min";
    public static final String USER_SCHEDULER_TIME_DEFAULT = "1";

    public static final String USER_DORMANT_AFTER_CREATION_DATE_WITHOUT_LOGIN = "user.dormant.creation.time.days";
    public static final String USER_DORMANT_AFTER_CREATION_DATE_WITHOUT_LOGIN_DEFAULT = "30";
    public static final String USER_DORMANT_AFTER_LAST_LOGIN_DATE = "user.dormant.login.time.days";
    public static final String USER_DORMANT_AFTER_LAST_LOGIN_DATE_DEFAULT = "90";

    //User Role
    public static final String USER_ACCESS_ROLE_PROPERTY_NAME = "user.access.roles";
    public static final String USER_ACCESS_ROLE_PROPERTY_VALUE = "1,3";

    public static final String HEAL_METRICS_SCHEDULER_PROPERTY_NAME = "health.metrics.scheduler.milliseconds";
    public static final String HEAL_METRICS_SCHEDULER_PROPERTY_NAME_DEFAULT_VALUE = "10000";

    // Notification related Defaults
    public static final String SEVERE = "Severe";
    public static final String DEFAULT = "Default";
    public static final String EARLY_WARNING = "Early Warning";
    public static final String PROBLEM = "Problem";
    public static final String INFO = "Info";
    public static final String BATCH = "Batch Job";
    public static final String IMMEDIATELY = "Immediately";
    public static final String LONG = "Open for long";
    public static final String TOO_LONG = "Open for too long";
    public static final String OFF = "Off";
    public static final String MIN_OPEN_FOR_LONG = "openForLong.minDuration.time.min";
    public static final String MIN_OPEN_FOR_TOO_LONG = "openForTooLong.minDuration.time.min";
    public static final String MAX_OPEN_FOR_LONG = "openForLong.maxDuration.time.min";
    public static final String MAX_OPEN_FOR_TOO_LONG = "openForTooLong.maxDuration.time.min";
    public static final String MIN_OPEN_FOR_LONG_DEFAULT = "15";
    public static final String MIN_OPEN_FOR_TOO_LONG_DEFAULT = "30";
    public static final String MAX_OPEN_FOR_LONG_DEFAULT = "1440";
    public static final String MAX_OPEN_FOR_TOO_LONG_DEFAULT = "2880";


    public static final int AVAILABLE_CORES = Runtime.getRuntime().availableProcessors();
    public static final String WORKER_THREAD_DEFAULT_MULTIPLIER = "1";
    public static final String WORKER_THREAD_MULTIPLIER_PROPERTY_NAME = "worker.thread.multiplier";

    public static final String AGENT_MODE_AUTO = "Auto";
    public static final String AGENT_MODE_VERBOSE = "Verbose";

    public static final String KPI_NAME_KEY_VALUE = "KEY_VALUE";
    public static final String KPI_NAME_FILE_WATCH = "FILE_WATCH";
    public static final String KEY_VALUE = "KeyValue";
    public static final String FILE_WATCH = "FileWatch";

    public static final String MST_TYPE_ATTRIBUTE_TYPE = "Attribute_Type";
    public static final String MST_TYPE_SCRIPT_PARAM_TYPE = "SCRIPT_Parameter_Type";
    public static final String MST_TYPE_COMMAND_OUTPUT_TYPE = "CommandOutputType";
    public static final String MST_TYPE_COMMAND_TYPE = "CommandType";
    public static final String MST_TYPE_AGENT_OPS_CMDS = "AgentOperationCmds";
    public static final String MST_TYPE_CONF_CMDS = "ConfigurationCmds";
    public static final String MST_SUB_TYPE_TEXT_BOX = "TextBox";
    public static final String MST_SUB_TYPE_COMMAND_LINE = "COMMANDLINE";
    public static final String MST_SUB_TYPE_BLOB = "Blob";
    public static final String MST_SUB_TYPE_EXECUTE = "Execute";
    public static final String MST_SUB_TYPE_SCHEDULED = "Scheduled";
    public static final String MST_SUB_TYPE_RECURRING = "Recurring";
    public static final String VIEW_TYPE_SUB_TYPE_NAME_TRANSACTION_COMMIT = "TransactionCommit";
    public static final String VIEW_TYPE_TYPE_NAME_JOB_TYPES = "JobTypes";
    public static final String VIEW_TYPE_TYPE_NAME_SINK_TYPE = "SinkType";
    public static final String VIEW_TYPE_TYPE_NAME_SCHEDULER_TYPE = "SchedulerType";
    public static final String VIEW_TYPE_SUBTYPE_NAME_RMQ = "RMQ";
    public static final String MST_SUB_TYPE_RECURRING_DAILY = "Daily";
    public static final String MST_SUB_TYPE_RECURRING_WEEKLY = "Weekly";
    public static final String MST_SUB_TYPE_RECURRING_MONTHLY = "Monthly";

    public static final String OPERATOR_LESS_THAN = "lt";
    public static final String OPERATOR_GREATER_THAN = "gte";

    public static final String API_BASE_URL = "/heal-controlcenter/v2.0/api";
    public static final String REPLACE_API_BASE_URL = "/heal-controlcenter";

    public static final String CONF_PROPERTIES_FILE_NAME = "conf.properties";
    public static final String KEYCLOAK_SSO_CONF_FILE_NAME = "keycloak_details.json";
    public static final String HEADER_PROPERTIES_FILE_NAME = "headers_details.json";

    public static final String HTTPS_PORT_PROPERTY_NAME = "https.port";
    public static final String HTTPS_PORT_DEFAULT = "8996";

    public static final String HTTP_HANDLER_MAX_THREADS = "http.handler.max.threads";
    public static final String HTTP_HANDLER_MAX_THREADS_DEFAULT = "100";


    public static final String INVOKED_METHOD = "Invoked method : [{}]";

    public static final String MYSQL_DB_CONNECTION_URL_PROPERTY_NAME = "mysql.server.connection.url";
    public static final String MYSQL_DB_CONNECTION_URL_DEFAULT = "************************************************************************";

    public static final String MYSQL_DB_USERNAME_PROPERTY_NAME = "mysql.database.username";
    public static final String MYSQL_DB_USERNAME_PROPERTY_DEFAULT = "appsone";
    public static final String MYSQL_DB_PASSWORD_PROPERTY_NAME = "mysql.database.password";
    public static final String MYSQL_DB_PASSWORD_PROPERTY_DEFAULT = "";

    public static final String MYSQL_DB_POOL_SIZE_PROPERTY_NAME = "mysql.database.pool.size";
    public static final String MYSQL_DB_POOL_SIZE_DEFAULT = "50";

    public static final String MYSQL_DB_MAX_POOL_SIZE_PROPERTY_NAME = "mysql.database.pool.size.max";
    public static final String MYSQL_DB_MAX_POOL_SIZE_DEFAULT = "100";

    public static final int DEFAULT_TIMEOUT_IN_SECS = 300;

    public static final String MYSQL_DB_DRIVER_NAME_PROPERTY_NAME = "mysql.driver.name";
    public static final String MYSQL_DB_DRIVER_NAME_DEFAULT = "com.mysql.cj.jdbc.Driver";

    public static final String H2_DB_DRIVER_NAME_PROPERT = "h2.driver.name";
    public static final String H2_DB_DRIVER_NAME_PROPERT_DEFAULT = "org.h2.Driver";

    public static final String TIME_BRACKET_DEF = "15";
    public static final String TIME_BRACKET_PROP = "instance.health.bracket.min";


    public static final int STATUS_ACTIVE = 1;
    public static final int STATUS_INACTIVE = 0;
    // scheduler time
    public static final String AGENT_COMMAND_TIMEOUT = "command.timeout";
    public static final String DEFAULT_AGENT_COMMAND_TIMEOUT = "1";
    public static final String DEFAULT_TAG_VALUE = "Type";
    public static final String ENTRY_POINT = "EntryPoint";
    public static final String ENTRY_POINT_TAG_VALUE = "1";

    //Service Default persistence and suppression
    public static final String SERVICE_START_WITHIN_AN_HOUR_PROPERTY_NAME = "service.startTime.lesserThan.hour";
    public static final String SERVICE_END_WITHIN_AN_HOUR_PROPERTY_NAME = "service.endTime.lesserThan.hour";
    public static final String SERVICE_START_TIME_WITHIN_AN_HOUR = "1";
    public static final String SERVICE_END_TIME_WITHIN_AN_HOUR = "59";
    public static final String SERVICE_SOR_PERSISTENCE_WITHIN_AN_HOUR_PROPERTY_NAME = "service.sor.persistence.lesserThan.hour";
    public static final String SERVICE_SOR_SUPPRESSION_WITHIN_AN_HOUR_PROPERTY_NAME = "service.sor.suppression.lesserThan.hour";
    public static final String SERVICE_SOR_PERSISTENCE_WITHIN_AN_HOUR = "5";
    public static final String SERVICE_SOR_SUPPRESSION_WITHIN_AN_HOUR = "10";

    public static final String SERVICE_NOR_PERSISTENCE_WITHIN_AN_HOUR_PROPERTY_NAME = "service.nor.persistence.lesserThan.hour";
    public static final String SERVICE_NOR_SUPPRESSION_WITHIN_AN_HOUR_PROPERTY_NAME = "service.nor.suppression.lesserThan.hour";
    public static final String SERVICE_NOR_PERSISTENCE_WITHIN_AN_HOUR = "2";
    public static final String SERVICE_NOR_SUPPRESSION_WITHIN_AN_HOUR = "5";

    public static final String SERVICE_START_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.startTime.greaterThan.hour";
    public static final String SERVICE_END_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.endTime.greaterThan.hour";
    public static final String SERVICE_START_TIME_MORE_THAN_AN_HOUR = "60";
    public static final String SERVICE_END_TIME_MORE_THAN_AN_HOUR = "1440";

    public static final String SERVICE_SOR_PERSISTENCE_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.sor.persistence.greaterThan.hour";
    public static final String SERVICE_SOR_SUPPRESSION_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.sor.suppression.greaterThan.hour";
    public static final String SERVICE_SOR_PERSISTENCE_MORE_THAN_AN_HOUR = "2";
    public static final String SERVICE_SOR_SUPPRESSION_MORE_THAN_AN_HOUR = "5";

    public static final String SERVICE_NOR_PERSISTENCE_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.nor.persistence.greaterThan.hour";
    public static final String SERVICE_NOR_SUPPRESSION_MORE_THAN_AN_HOUR_PROPERTY_NAME = "service.nor.suppression.greaterThan.hour";
    public static final String SERVICE_NOR_PERSISTENCE_MORE_THAN_AN_HOUR = "2";
    public static final String SERVICE_NOR_SUPPRESSION_MORE_THAN_AN_HOUR = "5";

    public static final String ACTION_STANDARD_TYPE_CUSTOM = "Custom";
    public static final String ACTION_STANDARD_TYPE = "StandardType";
    public static final String ACTION_EXECUTION_TYPE = "ForensicExecType";
    public static final String ACTION_EXECUTION_TYPE_SCRIPT = "Script";
    public static final String ACTION_EXECUTION_TYPE_RESTCLIENT = "RestClient";
    public static final String ACTION_DOWNLOAD_TYPE = "DownloadType";
    public static final String ACTION_DOWNLOAD_TYPE_PDF = "PDF";
    public static final String ACTION_COMMAND_EXECUTION_TYPE_LONGPOLLING = "LongPolling";
    public static final String DEFAULT_ACTION_TYPE = "Forensic Action";
    public static final String ACTION_COMMAND_EXECUTION_TYPE = "CommandExecType";

    public static final String ACTIONS = "Actions";

    public static final int ACTION_CATEGORY_TIME_WINDOW_SEC = 300;
    public static final int ACTION_CATEGORY_RETRIES = 3;
    public static final int ACTION_CATEGORY_TTL_SEC = 300;

    public static final String ACTION_COMMAND_DETAILS_TABLE = "command_details";

    public static final String FORENSIC_COMMAND_TYPE = "ForensicCmds";
    public static final String HEAL_COMMAND_TYPE = "HealActionCmds";
    public static final String ACTION_COMMAND_TYPE_EXECUTE = "Execute";

    // cassandra details
    public static final String CASSANDRA_SERVER_SCHEMA_PROPERTY_NAME = "cassandra.server.schema";
    public static final String CASSANDRA_SERVER_DEFAULT_SCHEMA = "appsone";

    public static final String CASSANDRA_NODES_PROPERTY_NAME = "cassandra.nodes";
    public static final String CASSANDRA_NODES_DEFAULT_VALUE = "127.0.0.1:9042";

    public static final String CASSANDRA_SERVER_USERNAME_PROPERTY_NAME = "cassandra.server.username";
    public static final String CASSANDRA_SERVER_USERNAME_DEFAULT_VALUE = "";

    public static final String CASSANDRA_SERVER_PASSWORD_PROPERTY_NAME = "cassandra.server.password";
    public static final String CASSANDRA_SERVER_PASSWORD_DEFAULT_VALUE = "";

    public static final String CASSANDRA_SERVER_CONNECTIONPOOL_SIZE = "cassandra.connectionpool.size";
    public static final String CASSANDRA_SERVER_CONNECTIONPOOL_SIZE_DEFAULT = "10";

    public static final String CASSANDRA_SSL_MODE = "cassandra.ssl.mode";
    public static final String CASSANDRA_SSL_MODE_DEFAULT = "1";

    public static final String THREAD_POOL_MIN_SIZE_NAME = "threadpool.min.size";
    public static final String THREAD_POOL_MIN_SIZE_DEFAULT_VALUE = "2";

    public static final String THREAD_POOL_MAX_SIZE_NAME = "threadpool.max.size";
    public static final String THREAD_POOL_MAX_SIZE_DEFAULT_VALUE = "10";

    // HTTP Client Constants
    public static final String HTTP_CONNECTION_TIMEOUT_PROPERTY_NAME = "http.client.connection.timeout";
    public static final String HTTP_CONNECTION_TIMEOUT_DEFAULT_VALUE = "100";
    public static final String HTTP_SOCKET_TIMEOUT_DEFAULT_VALUE = "100";
    public static final String HTTP_SOCKET_TIMEOUT_PROPERTY_NAME = "http.client.socket.timeout";
    public static final String HTTP_READ_TIMEOUT_PROPERTY_NAME = "httpclient.read.timeout";
    public static final String HTTP_READ_TIMEOUT_DEFAULT_VALUE = "180";
    public static final String HTTP_WRITE_TIMEOUT_PROPERTY_NAME = "httpclient.write.timeout";
    public static final String HTTP_WRITE_TIMEOUT_DEFAULT_VALUE = "180";

    public static final String UPLOAD_DIRECTORY_PATH = "upload.folder.path";
    public static final String UPLOAD_DIRECTORY_PATH_DEFAULT_VALUE = "./";
    public static final String UPLOAD_CSV_DIRECTORY_PATH = "upload.csv.path";
    public static final String UPLOAD_PATH_DEFAULT_VALUE = "/";
    public static final Integer CASSANDRA_SERVER_DEFAULT_PORT = 9042;
    //key cloak
    public static final String KEYCLOAK_IP = "keycloak.ip";
    public static final String KEYCLOAK_PORT = "keycloak.port";
    public static final String KEYCLOAK_USER = "keycloak.user";
    public static final String KEYCLOAK_PWD = "keycloak.pwd";
    public static final String KEYCLOAK_CLIENT_ID = "keycloak.client.id";
    public static final String KEYCLOAK_CLIENT_ID_DEFAULT = "admin-cli";
    public static final String KEYCLOAK_GRANT_TYPE = "keycloak.grant.type";
    public static final String KEYCLOAK_GRANT_TYPE_DEFAULT = "password";
    public static final String KEYCLOAK_CONNECTION_REFRESH = "keycloak.connection.refresh";
    public static final String KEYCLOAK_CONNECTION_REFRESH_DEFAULT = "30";
    public static final String USER_ACCOUNT_ACCESS_IDENTIFIER = "keycloak.account.identifier";
    public static final String USER_ACCOUNT_ACCESS_IDENTIFIER_DEFAULT = "Accounts";
    public static final String TIMEZONE_TAG_DETAILS_IDETIFIER = "timezone.tagdetail.key";
    public static final String TIMEZONE_TAG_DETAILS_IDETIFIER_DEFAULT = TIME_ZONE_TAG;

    // Cache Constants
    public static final String CACHE_MAXIMUM_SIZE_PROPERTY_NAME = "cache.maximum.size";
    public static final String CACHE_MAXIMUM_SIZE_DEFAULT_VALUE = "5000";
    public static final String CACHE_TIMEOUT_IN_MINUTES_PROPERTY_NAME = "cache.timeout";
    public static final String CACHE_TIMEOUT_IN_MINUTES_DEFAULT_VALUE = "5";
    public static final String CONFIG_DATA_CACHE_TIMEOUT_IN_MINUTES_PROPERTY_NAME = "config.cache.timeout";
    public static final String CONFIG_DATA_CACHE_TIMEOUT_IN_MINUTES_PROPERTY_NAME_DEFAULT = "10";
    public static final String TXN_CONFIG_DATA_CACHE_TIMEOUT_IN_MINUTES_PROPERTY_NAME = "txn.config.cache.timeout";

    public static final String SCRIPT = "Script";

    public static final String TRANSACTION_RESPONSE_TYPE = "ResponseTimeType";
    public static final String TRANSACTION_RESPONSE_SUB_TYPE = "DC";
    public static final String TRANSACTION_SIGNAL_SEVERITY_TYPE = "SignalSeverity";
    public static final String TRANSACTION_SIGNAL_SEVERITY_SUB_TYPE = "Default";
    public static final String ALL_TYPES = "ALL_TYPES";
    public static final String DEFAULT_TYPES = "DEFAULT_TYPES";
    public static final String HOST_ADDRESS = "HostAddress";
    public static final String SEPARATOR = "#%&%#";
    public static final String GLOBAL_ACCOUNT_ID = "global.account.id";
    public static final String GLOBAL_ACCOUNT_ID_DEFAULT = "1";
    public static final String CONTROLLER_TYPE_NAME_DEFAULT = "ControllerType";
    public static final String ANOMALY_SIGNAL_TYPE_NAME_DEFAULT = "AnomalySignalType";
    public static final String ANOMALY_SIGNAL_DESTINATION_DEFAULT = "signal-detector";
    public static final String RULES_TYPE = "RuleType";
    public static final String RULES_TYPE_STANDARD = "Standard";
    public static final String RULES_TYPE_CUSTOM = "Custom";

    public static final String ANOMALY_DESTINATION_TYPE_ID = "anomaly.destination.type.id";
    public static final String ANOMALY_DESTINATION_TYPE_ID_DEFAULT = "412";
    public static final String EW_SIGNAL_CLOSE_TIME = "ew.signal.close.time.mins";
    public static final String EW_SIGNAL_CLOSE_TIME_DEFAULT = "15";
    public static final String PROBLEM_SIGNAL_CLOSE_TIME = "problem.signal.close.time.mins";
    public static final String PROBLEM_SIGNAL_CLOSE_TIME_DEFAULT = "15";
    public static final String INFO_SIGNAL_CLOSE_TIME = "info.signal.close.time.mins";
    public static final String INFO_SIGNAL_CLOSE_TIME_DEFAULT = "15";

    public static final String RULES_MONITORING_ENABLED_DEFAULT = "1";

    public static final String RULES_DISCOVERY_ENABLED_DEFAULT = "1";

    public static final String SUPERVISOR_TYPE = "SupervisorType";
    public static final String OPERATIONS_TYPE = "Operations";
    public static final String OPERATIONS_TYPE_LESSER_THAN = "lesser than";
    public static final String OPERATIONS_TYPE_GREATER_THAN = "greater than";
    public static final String OPERATIONS_TYPE_NOT_BETWEEN = "not between";
    public static final String AVAILABILITY_OPERATIONS_TYPE = "AvailabilityOperations";
    public static final String THRESHOLD_TYPE = "ThresholdType";

    public static final String TRANSACTION_IDENTIFIER_DEFAULT = "Transaction";

    // Default Rule Properties
    public static final String AGENT_RULES_TYPE_NAME = "agentRuleType";
    public static final String AGENT_RULES_TYPE_REQUEST_DATA = "Request Data";
    public static final String AGENT_RULES_TYPE_EJB_DATA = "EJB Data";
    public static final String SEGMENT_URI_TYPE_NAME = "segmentURIType";
    public static final String SEGMENT_URI_TYPE_VALUE = "First";
    public static final String SEGMENT_VALUE_NAME = "segmentValue";
    public static final String SEGMENT_VALUE = "2";
    public static final String TRANSACTION_GROUP_NAME = "transactionGroup";
    public static final String TRANSACTION_GROUP_VALUE = "Default";
    public static final String RULE_NAME = "ruleName";
    public static final String RULE_VALUE = "2";

    public static final String RULE_TYPE = "type";

    public static final String RULE_REQUESTS_COUNT = "requestsCount";

    public static final String RULE_REQUEST_DISCOVERED_TIME = "requestDiscoveredTime";

    public static final String DISCOVERY_TAGS = "discoveryTags";

    public static final String PAYLOAD_TYPE_NAME = "payloadSubType";
    public static final String PAYLOAD_TYPE_VALUE = "Form Data";
    public static final String HTTP_TYPE_NAME = "httpSubType";
    public static final String HTTP_TYPE_VALUE = "GET";
    public static final String PAY_LOAD_TYPE = "PayloadType";
    public static final String QUERY_PARAM = "Query Parameters";
    public static final String PAIR_TYPE = "PairType";
    public static final String HTTP_HEADER_TYPE = "HTTP Header";
    public static final String SINGLE_SEGMENT_VALUE_NAME = "singleSegmentValue";
    public static final String SINGLE_SEGMENT_VALUE = "1";
    public static final String SINGLE_SEGMENT_RULE_NAME = "ruleName";
    public static final String SINGLE_SEGMENT_RULE_VALUE = "1";
    public static final String URI_SEGMENT_FIRST = "firstUriSegments";
    public static final String URI_SEGMENT_LAST = "lastUriSegments";
    public static final String URI_SEGMENT_COMPLETE = "completeUriSegments";
    public static final String URI_SEGMENT_CUSTOM = "customUriSegments";


    public static final String HTTP_METHOD_TYPE = "HTTPMethod";
    public static final String TRANSACTION_ATTRIBUTES_NAME = "transactionAttributes.type";
    public static final String TRANSACTION_ATTRIBUTES_NAME_DEFAULT = "Transaction_Attributes";
    public static final String TRANSACTION_ATTRIBUTE_TCP_TYPE = "transaction.attribute.tcp";
    public static final String TRANSACTION_ATTRIBUTE_TCP_TYPE_DEFAULT = "TCPData";
    public static final String TRANSACTION_ATTRIBUTE_QUERY_PARAMS_TYPE = "transaction.attribute.queryParams";
    public static final String TRANSACTION_ATTRIBUTE_QUERY_PARAMS_TYPE_DEFAULT = "QueryParams";
    public static final String SERVICES_CONTROLLER_TYPE = "Services";
    public static final String APPLICATION_CONTROLLER_TYPE = "Application";
    public static final String SERVICES_LAYER_TYPE = "ServiceLayers";


    public static final int DEFAULT_ACCOUNT_ID = 1;
    public static final String HOST = "Host";
    public static final String TRANSACTION_DEFAULT_GROUP = "transaction.default.group";
    public static final String TRANSACTION_DEFAULT_GROUP_DEFAULT = "DEFAULT";

    public static final String STATIC_FILES_LOCATIONS = "static.files.path";
    public static final String STATIC_FILES_LOCATIONS_DEFAULT = "/public";


    public static final String KEYSTORE_FILE_NAME = "keystore.file.path";
    public static final String KEYSTORE_FILE_NAME_DEFAULT = "/opt/heal-controlcenter/config/appnomic-keystore.jks";

    public static final String KEYSTORE_PASSWORD = "keystore.password";
    public static final String KEYSTORE_PASSWORD_DEFAULT = "";

    public static final String TRUSTSTORE_FILE_NAME = "truststore.file.path";
    public static final String TRUSTSTORE_FILE_NAME_DEFAULT = "";

    public static final String TRUSTSTORE_PASSWORD = "truststore.password";
    public static final String TRUSTSTORE_PASSWORD_DEFAULT = "";

    public static final String CONTROLLER = "controller";
    public static final String ALL_PROFILES = "ALL_PROFILES";
    public static final String OPERATIONS_TYPE_NAME = "Operations";
    public static final String TRANSACTION_KPI_TYPES_NAME = "TransactionKPITypes";
    public static final int SLOW_THRESHOLD_VALUE = 5000;
    public static final String AGENT_TYPE = "Agent";
    public static final String AGENT_DATA_TYPE = "AgentDataType";
    public static final String VIEW_ALL_KPI = "viewAllKpi";
    public static final String KPI_TYPE = "KPI";
    public static final String ALL = "ALL";
    public static final String NONE = "NONE";

    public static final String RULES_TABLE = "rules";
    public static final String AGENT_TABLE = "agent";
    public static final String TXN_GROUP_TABLE_PROPERTY = "transactiongroup.table.name";
    public static final String TXN_GROUP_TABLE = "transaction_group";
    public static final String COMP_INSTANCE_TABLE = "comp_instance";
    public static final String TXN_TABLE = "transaction";
    public static final String TXN_PATTERN_URL = "url";
    public static final String ACCOUNT_TABLE_PROPERTY = "account.table.name";
    public static final String ACCOUNT_TABLE = "account";
    public static final String TABLE_TRANSACTION = "transaction";
    public static final String TABLE_USER_ATTRIBUTES = "user_attributes";

    public static final String ATTRIBUTE_VALUE_SEPARATOR = "|";

    public static final String HTTP_URL_CONTENT_TYPE = "Plain Text";

    public static final String ATTRIBUTE_ACCOUNTS = "Accounts";
    public static final String ATTRIBUTE_CONTACT_NUMBER = "ContactNumber";
    public static final String COMPONENT_TYPE_HOST = "HOST";
    public static final String COMPONENT_TYPE_POD = "Pod";
    public static final String ATTRIBUTE_CONTAINER_NAME = "ContainerName";
    public static final String VERSION = "Version";

    /**
     * XPT Tag names
     */
    public static final String BIZ_ERROR = "biz-error";
    public static final String TECH_ERROR = "tech-error";
    public static final String STATIC_THRESHOLD = "STATIC";
    public static final String CORE_KPI_TYPE = "Core";
    public static final String AVAIL_KPI_TYPE = "Availability";
    public static final String THRESHOLD_DEFINED_BY_USER = "USER";
    public static final String THRESHOLD_DEFINED_BY_SYSTEM = "SYSTEM";
    public static final String FILE_WATCH_KPI_TYPE = "FileWatch";
    public static final String CONFIG_WATCH_KPI_TYPE = "ConfigWatch";
    public static final String FORENSIC_KPI_TYPE = "Forensic";
    public static final int DEFAULT_OPERATION_ID = 0;
    public static final float DEFAULT_MIN_THRESHOLD_VALUE = 0;
    public static final float DEFAULT_MAX_THRESHOLD_VALUE = 0;


    public static final int DEFAULT_PERSISTENCE_VALUE = -1;
    public static final int DEFAULT_SUPPRESSION_VALUE = -1;
    public static final int DEFAULT_EXCLUDE_MAINTENANCE_VALUE = 0;
    public static final int DEFAULT_STATUS = 0;


    public static final String MAX_VALUE = "MAX";
    public static final String MIN_VALUE = "MIN";
    public static final double DEFAULT_MAX_VALUE = 0.0D;
    public static final double DEFAULT_MIN_VALUE = 0.0D;


    /**
     * Messages
     */
    public static final String INVALID_ACCOUNT_ERROR_MESSAGE = "Invalid account id provided";
    public static final String INVALID_PRODUCER_DETAILS_ERROR_MESSAGE = "Invalid producer details";
    public static final String REQUEST_BODY_EMPTY_ERROR_MESSAGE = "Request body cannot be empty";
    public static final String REQUEST_BODY_INVALID_ERROR_MESSAGE = "Invalid data in request body";
    public static final String SUCCESS_MESSAGE = "SUCCESS";
    public static final String INTERNAL_ERROR_MESSAGE = "INTERNAL ERROR";

    public static final String FROM_STRING_QUERY_PARAM_CAN_T_BE_NULL = "from string can't be null.";
    public static final String TO_STRING_QUERY_PARAM_CAN_T_BE_NULL = "to string can't be null.";

    /**
     * Return codes
     */
    public static final int SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE = HttpStatus.SC_BAD_REQUEST;
    public static final int INTERNAL_SERVER_ERROR_STATUS_CODE = HttpStatus.SC_INTERNAL_SERVER_ERROR;
    public static final int SUCCESS_STATUS_CODE = HttpStatus.SC_OK;
    public static final int VALIDATION_FAILED_STATUS_CODE = HttpStatus.SC_BAD_REQUEST;
    public static final int FORBIDDEN_REQUEST = HttpStatus.SC_FORBIDDEN;


    public static final String JSON_PARSE_ERROR = "Exception encountered while parsing the JSON request body.";

    /**
     * Producer attribute keys
     */
    public static final String SCRIPT_TYPE_SCRIPT_NAME_ATTRIBUTE = "script_name";
    public static final String SCRIPT_TYPE_SIGNATURE_ATTRIBUTE = "signature";

    public static final String WMI_TYPE_SCRIPT_NAME_ATTRIBUTE = "script_name";
    public static final String WMI_TYPE_SIGNATURE_ATTRIBUTE = "signature";

    public static final String HTTP_TYPE_URL_ATTRIBUTE = "status_url";

    public static final String JMX_TYPE_URL_ATTRIBUTE = "url";
    public static final String JMX_TYPE_ATTRIBUTE_TYPE_ID = "attribute_type";
    public static final String JMX_TYPE_TARGET_OBJECT_NAME_ATTRIBUTE = "target_object_name";
    public static final String JMX_TYPE_IDENTIFIER = "JMXAttributeDataType";

    public static final String WAS_TYPE_TARGET_OBJECT_NAME_ATTRIBUTE = "target_object_name";
    public static final String WAS_TYPE_MODULE_NAME_ATTRIBUTE = "module";

    public static final String JDBC_TYPE_DRIVER_ATTRIBUTE = "driver";
    public static final String JDBC_TYPE_URL_ATTRIBUTE = "url";
    public static final String JDBC_TYPE_QUERY_ATTRIBUTE = "query";
    public static final String JDBC_TYPE_QUERY_RESULT_ATTRIBUTE = "query_result";
    public static final String JDBC_TYPE_IS_QUERY_ENCRYPTED_ATTRIBUTE = "is_query_encrypted";

    public static final String HTTP_JSON_TYPE_JSON_URL_ATTRIBUTE = "json_url";
    public static final int KUBERNETES_TAG_ID = 8;
    public static final String JPPF_TYPE_TYPEID_ATTRIBUTE = "server_type";
    public static final String JPPF_TYPE_TYPEID_MASTER_TYPE = "JPPFType";
    public static final String CORE_DATA_TYPE = "Core_DataType";
    public static final String AVAILABILITY_DATA_TYPE = "Availability_DataType";
    public static final String FILE_WATCH_DATA_TYPE = "FileWatch_DataType";
    public static final String CONFIG_WATCH_DATA_TYPE = "ConfigWatch_DataType";
    public static final String FORENSIC_DATA_TYPE = "Forensic_DataType";
    public static final String COUNT = "count";
    public static final String NAME = "name";
    public static final String ID = "id";

    public static final String ORDER = "order";
    public static final String MONITOR_STATUS = "monitorStatus";
    public static final String DISCOVERY_STATUS = "discoveryStatus";

    public static final String AGENT_STATUS_RUNNING = "Running";
    public static final String AGENT_STATUS_STOP = "Stopped";
    public static final String DEFAULT_SERVICE = "NoService";
    public static final String SUCCESS = "Success";
    public static final String ACCOUNT_IDENTIFIER = ":identifier";
    public static final String KPI_IDENTIFIER_PATH_PARAMETER = ":kpiIdentifier";
    public static final String CATEGORY_ID = ":categoryId";
    public static final String GROUP_ID = ":groupId";

    public static final String AUTHORIZATION = "Authorization";
    public static final String DATE_TIME = "yyyy-MM-dd HH:mm:ss";
    public static final String JIM_AGENT_TYPE_ID = "2";
    public static final String FORENSIC_AGENT_TYPE_ID = "218";

    public static final String METADATA_FILE_NAME = "metadata.file.name";
    public static final String METADATA_FILE_NAME_DEFAULT_VALUE = "metadatadir";
    public static final String EXTRACTOR_DIRECTORY_PATH = "extract.folder.path";
    public static final String EXTRACTOR_DIRECTORY_PATH_DEFAULT_VALUE = "./";
    public static final String INSTALLATION_MODE_NAME = "InstallationMode";
    public static final String INSTALLATION_MODE_OFFLINE = "Offline";
    public static final String SERVICE_ID = ":serviceId";
    public static final String INSTANCE_ID = ":instanceId";
    public static final String APPLICATION_ID = ":applicationId";
    public static final String SERVICE_IDENTIFIER = ":serviceIdentifier";
    public static final String MAINTENANCE_ID = ":maintenanceId";
    public static final String PARENT_APPLICATION_IDENTIFIER = ":parentApplicationIdentifier";
    public static final String MST_TYPE_MAINTENANCE = "MaintenanceType";
    public static final String MST_TYPE_RECURRING = "RecurringType";

    public static final String COMP_INSTANCE_IDENTIFIER = ":compInstanceIdentifier";
    public static final String SRC_IDENTIFIER = ":sourceIdentifier";
    public static final String DEST_IDENTIFIER = ":destinationIdentifier";
    public static final String AGENT_IDENTIFIER = ":agentIdentifier";
    public static final String TRANSACTION_IDENTIFIER = ":transactionId";
    // HTTP Client Constants
    public static final String FLINK_URL_PROPERTY_NAME = "flink.url";
    public static final String FLINK_URL_DEFAULT_VALUE = "http://localhost:8081";
    public static final String FLINK_JAR_LOCATION_PROPERTY_NAME = "flink.pipeline.jar.location";
    public static final String FLINK_JAR_LOCATION_PROPERTY_DEFAULT_VALUE = "/opt/appnomic/appsone/pipeline/pipeline-5.8.8-SNAPSHOT.jar";
    public static final String FLINK_CONFIG_LOCATION_PROPERTY_NAME = "flink.pipeline.config.location";
    public static final String FLINK_CONFIG_LOCATION_PROPERTY_DEFAULT_VALUE = "/opt/appnomic/appsone/pipeline/config/conf.properties";
    public static final String SOR_PROCESSOR_PROPERTY_NAME = "flink.sor.processor.name";
    public static final String SOR_PROCESSOR_PROPERTY_DEFAULT_VALUE = "pipeline.KPIThresholdProcessor";
    public static final String DOES_NOT_EXIST = "does not exist.";
    public static final String KUBERNETES = "Kubernetes";

    public static final String NON_KUBERNETES = "Non_Kubernetes";
    public static final String SMS_PROTOCOLS = "SMSGatewayProtocols";
    public static final String SMTP_PROTOCOLS = "SMTP Security";
    public static final String SMS_PARAMETER_TYPE_NAME = "SMSParameterTypes";
    public static final String SMS_PLACEHOLDERS = "SMSPlaceHolders";
    public static final String SMS_HTTP_METHODS = "HTTPSMSRequestMethods";
    public static final String PASSWORD = "Password";

    public static final String NOTIFICATION_TYPE_LITERAL = "NotificationType";
    public static final String SIGNAL_SEVERITY_TYPE_LITERAL = "SignalSeverity";
    public static final String SIGNAL_TYPE_LITERAL = "SignalType";
    // Notification
    public static final String NOTIFICATION_TYPE = "NotificationType";
    public static final String NOTIFICATION_KEY = "notificationType";
    public static final String TOTAL = "total";

    //Tagging Grouping
    public static final String MAX_TAGS_ALLOWED = "max.tags.allowed";
    public static final String MAX_TAGS = "5";

    public static final String MAX_CHAR_TAGS_ALLOWED = "max.tags.char";
    public static final String MAX_CHAR_TAGS = "50";

    public static final String RECURRING_SCHEDULER_TIME = "1";
    public static final String RECURRING_SELECTED_ED = "ed";
    public static final String RECURRING_SELECTED_EW = "ew";
    public static final String RECURRING_SELECTED_DATE = "date";

    // Batch process
    public static final String BATCH_JOB_NAME = ":batchJobName";
    public static final String PROCESS_DETAILS_ID = ":processDetailsId";

    //TimeZone
    public static final String DEFAULT_TIME_ZONE = "+00:00";
    public static final String TIME_ZONE_FORMAT = "%s%02d:%02d";
    public static final int DEFAULT_VALUE1 = 0;
    public static final int DEFAULT_VALUE2 = 3600000;
    public static final int DEFAULT_VALUE3 = 60000;
    public static final int DEFAULT_VALUE4 = 60;
    public static final String DB_CONDITION = ") and ";

    public static final String SMS_ACTION_ADD = "add";
    public static final String SMS_ACTION_EDIT = "edit";
    public static final String SMS_ACTION_DELETE = "delete";

    public static final String CATEGORY_CUSTOM = "Custom";
    public static final String CATEGORY_STANDARD = "Standard";

    public static final String SUPERVISOR_IDENTIFIER = ":supervisorIdentifier";
    public static final String SUPERVISOR_MODE = "SupervisorMode";
    public static final String SUPERVISOR_REMOTE_MODE = "REMOTE";
    public static final String SUPERVISOR_LOCAL_MODE = "LOCAL";

    public static final String MONITOR_PORT = "MonitorPort";
    public static final String TFP_DETECT_SERVICE_BY_PORT = "DetectServiceByPort";
    public static final String TFP_DETECT_SERVICE_BY_PORT_DEFAULT_VALUE = "false";
    public static final String TFP_DEFAULT_SERVICE_PORT = "0";

    public static final String USER_NOTIFICATION_PREFERENCE = "user.notification.preference";
    public static final String USER_NOTIFICATION_PREFERENCE_DEFAULT = "308";
    public static final String USER_NOTIFICATION_FORENSIC_SUPPRESSION_INTERVAL = "user.notification.forensic.suppression.interval";
    public static final String USER_NOTIFICATION_FORENSIC_SUPPRESSION_INTERVAL_DEFAULT = "1";

    public static final String INSTANCE_FORENSIC_TRIGGER_SUPPRESSION_INTERVAL = "instance.forensic.trigger.suppression.interval";
    public static final String INSTANCE_FORENSIC_TRIGGER_SUPPRESSION_INTERVAL_DEFAULT = "1";

    public static final String INSTANCE_REMOVAL_STATUS_PERCONA = "instance.removal.status.percona";
    public static final boolean INSTANCE_REMOVAL_STATUS_PERCONA_DEFAULT = false;

    public static final String INSTANCE_REMOVAL_STATUS_OPENSEARCH = "instance.removal.status.opensearch";
    public static final boolean INSTANCE_REMOVAL_STATUS_OPENSEARCH_DEFAULT = false;

    public static final String INSTANCE_REMOVAL_OPENSEARCH_INDEXES = "instance.removal.opensearch.indexes";

    public static final String INSTANCE_REMOVAL_OPENSEARCH_INDEXES_DEFAULT = "heal_collated_kpi:compInstanceIdentifier,heal_raw_kpi:compInstanceIdentifier,heal_kpi_violations:instanceId," +
            "heal_anomalies:instanceId,heal_health_instance_kpi:compInstanceIdentifier,heal_health_instance:compInstanceIdentifier";

    public static final String OPENSEARCH_CONNECTION_IO_REACTOR_SIZE = "opensearch.connection.io.reactor.size";
    public static final String OPENSEARCH_CONNECTION_IO_REACTOR_SIZE_DEFAULT = "2";

    public static final String APPLICATION_PERCENTILES_DEFAULT_CONFIGURATION = "application.percentiles.default";
    public static final String APPLICATION_PERCENTILES_DEFAULT_VALUES = "RESPONSE_TIME_90.0_P, RESPONSE_TIME_95.0_P, RESPONSE_TIME_99.0_P";
    public static final String PERCENTILE_KPIS_IDENTIFIER_SUFFIX = "percentile.kpis.identifier.suffix";
    public static final String PERCENTILE_KPIS_IDENTIFIER_SUFFIX_DEFAULT = "P";

    public static final String AURA_ACCOUNT_ID_KEY = "aura.sync.account";
    public static final String EXTERNAL_TAGS_LITERAL = "ExternalTags";
    public static final String DASHBORAD_URL_LITERAL = "ExternalDashboardUrl";

    public static final String MAINTENANCE_WINDOW_COUNT_DAYS_CONFIG_DATA = "maintenance.window.days.count";
    public static final String MAINTENANCE_WINDOW_COUNT_CONFIG_DATA_DEFAULT = "2";

    public static final String HTTP_CLIENT_MAX_CONNECTIONS = "http.client.max.connections";
    public static final String HTTP_CLIENT_MAX_CONNECTIONS_DEFAULT = "100";

    public static final String HTTP_CLIENT_MAX_CONNECTIONS_PER_ROUTE = "http.client.max.connections.per.route";
    public static final String HTTP_CLIENT_MAX_CONNECTIONS_PER_ROUTE_DEFAULT = "50";

    public static final String AGENT_DETAILS_REQUIRED = "agentDetailRequired";

    public static final String A1_HEALTH_ACCOUNT = "health.account.identifier";

    public static final String COMPONENT_AGENT_SUB_TYPE = "ComponentAgent";
    public static final String AGENT_ID = "agentId";

    public static final String ALLOWED_NAME_CHARACTERS = ConfProperties.getString("allowed.name.characters", "^[a-zA-Z0-9-_.|\\s]*$");
    public static final String ALLOWED_IDENTIFIER_CHARACTERS = ConfProperties.getString("allowed.identifier.characters", "^[a-zA-Z0-9-_.|]*$");

    public static final String REQUEST_THRESHOLD_PROPERTY = "request.threshold.seconds";
    public static final String REQUEST_THRESHOLD_PROPERTY_DEFAULT_VALUE = "3";

    public static final String COMP_INST_RANGE = "comp.instance.range";
    public static final String COMP_INST_RANGE_DEFAULT_VALUE = "1440";

    public static final String GLOBAl_ACCOUNT_IDENTIFIER = "e573f852-5057-11e9-8fd2-b37b61e52317";

    public static final String OPENSEARCH_TRASACTION_IDENTIFIER_AGGS = "TxnIdentifierAggs";
    public static final String OPENSEARCH_TRANSACTION_IDENTIFIER = "txnIdentifier";
    public static final String TIME_IN_GMT = "timeInGMT";

    public static final String TIMESTAMP_FORMAT_INDEX_PATTERN = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX";
    public static final String INDEX_POSTFIX_DATE_FORMAT = "yyyy.MM.dd";

    public static final String INDEX_PREFIX_HEAL_AGENT_COMMANDS = "heal_agent_commands";
    public static final String INDEX_PREFIX_HEAL_HEALTH_AGENT = "heal_health_agent";
    public static final String INDEX_PREFIX_HEAL_SERVICE_KPI_THRESHOLDS = "heal_service_kpi_thresholds";
    public static final String INDEX_PREFIX_HEAL_HEALTH_INSTANCE = "heal_health_instance";
    public static final String INDEX_PREFIX_HEAL_INSTANCE_KPI_THRESHOLDS = "heal_instance_kpi_thresholds";
    public static final String INDEX_PREFIX_HEAL_TXN_KPI_THRESHOLDS = "heal_transaction_kpi_thresholds";
    public static final String INDEX_PREFIX_HEAL_SERVICE_MAINTENANCE_DATA = "heal_service_maintenance_data";
    public static final String INDEX_PREFIX_HEAL_INSTANCE_MAINTENANCE_DATA = "heal_instance_maintenance_data";
    public static final String INDEX_PREFIX_HEAL_AUTO_DISCOVERY_ERRORS = "heal_autodiscovery_errors";
    public static final String INDEX_PREFIX_HEAL_COLLATED_KPI = "heal_collated_kpi";
    public static final String INDEX_PREFIX_HEAL_RAW_TXN = "heal_raw_txn";
    public static final String INDEX_PREFIX_HEAL_SCHEDULED_JOB = "heal_scheduled_job";

    // Redis connectivity
    public static final String REDIS_HOSTS = "redis.hosts";
    public static final String REDIS_USERNAME = "redis.username";
    public static final String REDIS_PASSWORD = "redis.password";
    public static final String REDIS_CLUSTER_MODE = "redis.cluster.mode";
    public static final boolean REDIS_CLUSTER_MODE_DEFAULT = true;
    public static final String REDIS_SSL_ENABLED = "redis.ssl.enabled";
    public static final boolean REDIS_SSL_ENABLED_DEFAULT = true;

    // Data Receiver Connectivity
    public static final String DATA_RECEIVER_KPI_ENDPOINT = "data.receiver.kpi.endpoint";
    public static final String DATA_RECEIVER_KPI_ENDPOINT_DEFAULT = "http://**************:9990/raw-agents-kpi-data";

    public static final String COLON = ":";
    public static final String AGENT_DATA_SOURCES = "DataSources";

    public static final String HAPROXY_PORT_DEFAULT = "9998";
    public static final String HAPROXY_IP_DEFAULT = "haproxy.appnomic";
    public static final String HAPROXY_PORT = "haproxy.port";
    public static final String HAPROXY_IP = "haproxy.ip";
    public static final String ACTION_ADD = "add";
    public static final String ACTION_DELETE = "delete";
    public static final int COMPONENT_TYPE_HOST_ID = 1;
    public static final String HEAL_RAW_EXTERNAL_DATA = "heal_raw_external_data_";
    public static final String INSTALLATION_LOG_NAME = "InstallationLogs";
    public static final String PROCESS_NAME = "process";
    public static final String TAGS_NAME = "tags";
    public static final String DATA_SOURCE_NAME = "datasource";
    public static final String DATA_SOURCE_VALUE = "HealAgent";
    public static final String METADATA_HOST_ADDRESS_NAME = "metadata.HostAddress";

    public static final List<Integer> AZURE_LOADERS_IDS = Arrays.asList(4, 6);

    public static final List<Integer> SAP_RELOAD_CONFIG_IDS = Arrays.asList(1, 2, 3, 5);

    public static final List<Integer> DT_RELOAD_CONFIG_IDS = Arrays.asList(1, 2, 3, 5);

    public static final List<Integer> AZURE_RELOAD_CONFIG_IDS = Arrays.asList(1, 2, 3, 5);

    public static final List<Integer> AZURE_IDS = Arrays.asList(1, 2);

    public static final List<Integer> KPI_MASTER_REPOSETORY_IDS = Arrays.asList(1, 2, 3, 5);

    public static final List<Integer> DYNA_TRACE_LOADER_IDS = Arrays.asList(4, 6);

    public static final String SAP_CONNECTOR_DATABASE_NAME = "dataadapter";

    public static final String DYNA_TRACE_CONNECTOR_DATABASE_NAME = "dataadapter_dynatrace";

    public static final List<Integer> APPD_RELOAD_CONFIG_IDS = Arrays.asList(1, 2, 3, 4);

    public static final List<Integer> APPD_CONFIG_IDS = Arrays.asList(1, 4);
    public static final List<Integer> APPD_WorkerParams_IDS = Arrays.asList(1, 2);

    public static final List<Integer> APPD_LOADERS_ID = Arrays.asList(3, 7);
    public static final List<Integer> APPD_BASE = Arrays.asList(1, 2, 4, 5);
    public static final List<Integer> APPD_ALL = Arrays.asList(1, 2, 3, 4, 5, 6, 7);
    public static final List<Integer> APPD_APP = Arrays.asList(1, 4, 5);

    public static final List<Integer> AWS_LOADER_IDS = Arrays.asList(5, 7);

    public static final List<Integer> AWS_RELOAD_CONFIG_IDS = Arrays.asList(1, 2, 3, 4, 6);
    public static final List<Integer> AWS_REPO_INIT = Arrays.asList(1, 2, 3);
    public static final List<Integer> SAP_EXTRACTOR_IDS = Arrays.asList(1, 2);
    public static final List<Integer> KUB_ALL_CHAINS = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9);
    public static final List<Integer> KUB_EXTRACTOR_CHAINS = Arrays.asList(1, 2, 3);
    public static final List<Integer> KUB_TRANSFORMER_CHAINS = Arrays.asList(4, 5, 6);
    public static final List<Integer> KUB_LOADER_CHAIN = Arrays.asList(7, 8, 9);
    public static final List<Integer> KUB_TOPOLOGY_CHAIN = Arrays.asList(1, 4, 7);
    public static final List<Integer> KUB_PROMETHEUS_CHAIN = Arrays.asList(2, 5, 8);

    public static final int DEFAULT_COLLECTION_INTERVAL = 60;

    // enable the scheduler details

    public static final String ENABLE_COMMAND_OUT_SCHEDULER = "command.out.scheduler.enable";
    public static final String ENABLE_COMMAND_OUT_SCHEDULER_DEFAULT = "1";

    public static final String ENABLE_USER_MAPPING_SCHEDULER = "user.mapping.scheduler.enable";
    public static final String ENABLE_USER_MAPPING_SCHEDULER_DEFAULT = "1";

    public static final String ENABLE_USER_DORMANT_SCHEDULER = "user.dormant.scheduler.enable";
    public static final String ENABLE_USER_DORMANT_SCHEDULER_DEFAULT = "1";

    public static final String ENABLE_MAINTENANCE_WINDOW_SCHEDULER = "maintenance.window.scheduler.enable";
    public static final String ENABLE_MAINTENANCE_WINDOW_SCHEDULER_DEFAULT = "1";

    public static final String DATA_TIMEZONE = "data.timezone";
    public static final String DATA_TIMEZONE_DEFAULT_TIMEZONE = "Asia/Kolkata";

    public static final String SCHEDULER_BULK_OPERATION_LIMIT = "scheduler.bulk.operation.limit";
    public static final String SCHEDULER_BULK_OPERATION_LIMIT_DEFAULT = "3";

    public static final String SYNC_TO_SYSTEM = "sync.to.system";
    public static final String SYNC_TO_SYSTEM_DEFAULT = "-1";

    public static final String PARENT_APPLICATION_TAG = "parent_applications";
    public static final int PARENT_APPLICATION_TAG_ID = 14;
    public static final int SERVICE_TYPE_TAG_ID = 8;

    public static final String LAST_COMMIT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String DEFAULT_MIN_REQ_COUNT = "default.min.request.count";
    public static final String ALLOWED_MIN_REQ_COUNT = "allowed.min.request.count";
    public static final String DEFAULT_MAX_AUTO_ACCEPTED_REQS = "default.max.auto_accepted.requests";
    public static final String ALLOWED_MIN_AUTO_ACCEPTED_REQS = "allowed.min.auto_accepted.requests";
    public static final String ALLOWED_MAX_AUTO_ACCEPTED_REQS = "allowed.max.auto_accepted.requests";
    public static final String MIN_HOLD_DURATION = "min.hold.duration";
    public static final String MAX_HOLD_DURATION = "max.hold.duration";
    public static final String DEFAULT_HOLD_DURATION = "default.hold.duration";

    public static final String DEFAULT_MIN_REQ_COUNT_DEFAULT_VAL = "10";
    public static final String ALLOWED_MIN_REQ_COUNT_DEFAULT_VAL = "10";
    public static final String DEFAULT_MAX_AUTO_ACCEPTED_REQS_DEFAULT_VAL = "100";
    public static final String ALLOWED_MIN_AUTO_ACCEPTED_REQS_DEFAULT_VAL = "10";
    public static final String ALLOWED_MAX_AUTO_ACCEPTED_REQS_DEFAULT_VAL = "100";
    public static final String MIN_HOLD_DURATION_DEFAULT_VAL = "1";
    public static final String MAX_HOLD_DURATION_DEFAULT_VAL = "24";
    public static final String DEFAULT_HOLD_DURATION_DEFAULT_VAL = "8";
    public static final String DEFAULT_SERVICE_SCHEDULER_NAME = "transaction auto acceptance";
    public static final String DEFAULT_SCHEDULER_JOB_STATUS = "SCHEDULED";
    public static final String SCHEDULER_ARGUMENT_NAME = "queue_name";
    public static final String SCHEDULER_ARGUMENT_VALUE = "job-messages";
    public static final String SCHEDULER_ARGUMENT_DEFAULT_VALUE = "job-messages";
    public static final String SCHEDULER_JOB_ARGUMENT_DEFAULT_ARGUMENT_NAME = "serviceIdentifier";

    public static final String TXN_REQUEST_SOURCE_NAME = "job-executor";
    public static final String INSTANCE_KPI_AVAILABILITY = "instance.kpi.availability";
    public static final String INSTANCE_KPI_AVAILABILITY_DEFAULT = "HEAL_HOST_AVAILABILITY";

    public static final String FETCH_OS_DATA_ACCEPT_DISCARD_TXN_FROM_CC_DURATION_DAY = "fetch.os.data.accept.discard.txn.from.cc.duration.day";
    public static final String FETCH_OS_DATA_ACCEPT_DISCARD_TXN_FROM_CC_DURATION_DAY_DEFAULT = "1";

//    public static final String DELETE_TXN_BY_QUERY_OS_BATCH_SIZE = "delete.txn.by.query.os.batch.size";
//    public static final String DELETE_TXN_BY_QUERY_OS_BATCH_SIZE_DEFAULT = "10000";
    public static final String DELETE_TXN_BY_QUERY_OS_TIME_OUT_MINUTE = "delete.txn.by.query.os.time.out.minute";
    public static final String DELETE_TXN_BY_QUERY_OS_TIME_OUT_MINUTE_DEFAULT = "1";
    public static final String SET_TXN_EXPIRY_TIME_SECONDS = "redis.txn.keys.expiry.time.seconds";
    public static final String SET_TXN_EXPIRY_TIME_SECONDS_DEFAULT = "0";

    public static final int PERSIST_SMS_NOTIFICATIONS_DEFAULT = 1;
    public static final int PERSIST_EMAIL_NOTIFICATIONS_DEFAULT = 1;
    public static final String THRESHOLD_TYPE_VALUE = "SOR";

    public static final String TOTAL_KPIS_FOR_REALTIME_THRESHOLD_AGGR_BUCKET = "kpi.aggrs.bucket.size";
    public static final String TOTAL_KPIS_FOR_REALTIME_THRESHOLD_AGGR_BUCKET_DEFAULT = "200";

    public static final String NUMBER_OF_PAST_DAYS_TO_GET_REALTIME_THRESHOLD = "realtime.threshold.past.days";
    public static final String NUMBER_OF_PAST_DAYS_TO_GET_REALTIME_THRESHOLD_DEFAULT = "1";
    public static final String LDPRELOAD_KEY = "ld_preload";
    public static final String JVM_ARGS_KEY = "jvm_args";
    public static final String TRACE_KEYWORD = "TRACE";
    public static final String TRACK_KEYWORD = "TRACK";
    public static final String APPLICATION_KPIS_COMPONENT_NAME = "ApplicationKpis";
    public static final String SERVICE_KPIS_COMPONENT_NAME = "ServiceKpis";

    public static final String FORENSIC_ACTION_TRIGGER_STATUS = "In-progress";
    public static final String SCRIPT_METADATA_ARGS = "METADATA_ARGS";
    public static final String METADATA_ACCOUNT_ID = "AccountId";
    public static final String METADATA_COMMAND_ID = "CommandId";
    public static final String METADATA_AGENT_TYPE = "agentType";
    public static final String METADATA_STATUS = "status";

    //Bulk Forensic actions
    public static final String COMMAND_RETRY_NUMBER = "forensic.command.retry.number";
    public static final String COMMAND_RETRY_NUMBER_DEFAULT = "3";
    public static final String SUPERVISOR_CONTROLLER_TTL = "forensic.command.supervisor.controller.ttl";
    public static final String SUPERVISOR_CONTROLLER_TTL_DEFAULT = "300";

    public static final String HEAL_HEALTH_ACCOUNT_IDENTIFIER = "heal_health";

    public static final String ENVIRONMENT_TYPE_NAME = "Environment";
    public static final String ENVIRONMENT_TYPE_PROD = "PROD";


    public static final String INSTANCE_METADATA_CSV_FILE_HEADERS = "instance.metadata.csv.file.headers";
    public static final String INSTANCE_METADATA_CSV_FILE_HEADERS_DEFAULT = "instance_identifier,environment_name,related_instance_identifier," +
            "memory_size_mb,disk_size_mb,cpu_cores";
    public static final String MULTIPART_FORM_DATA_CONTENT_TYPE = "multipart/form-data";
    public static final String OPENSEARCH_BATCH_SIZE = "opensearch.batch.size";
    public static final String OPENSEARCH_BATCH_SIZE_DEFAULT = "5000";

    public static final String HOST_AVAILABILITY_REDIS_KEY_EXPIRY_MINUTES= "host.availability.redis.key.expiry.minute";
    public static final String HOST_AVAILABILITY_REDIS_KEY_EXPIRY_MINUTES_DEFAULT= "2";
}
