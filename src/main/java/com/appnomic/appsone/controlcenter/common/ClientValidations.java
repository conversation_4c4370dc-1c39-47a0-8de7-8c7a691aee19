package com.appnomic.appsone.controlcenter.common;

import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * Common class for validating data received from client.
 * As of now validations exist only for request non-null,
 * accountIdentifier and serviceId. Need to add
 * validations for other fields as well.
 */
@Slf4j
public class ClientValidations {

    public static void requestNullCheck(RequestObject request) throws ClientException {
        if (request == null) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }
    }

    public static void requestBodyNullCheck(String requestBody) throws ClientException {
        if (requestBody == null || requestBody.isEmpty()) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Request body is null or empty");
            throw new ClientException("Request body is null or empty");
        }
    }

    public static void requestParameterNullCheck(String[] requestParameter) throws ClientException {
        if (requestParameter == null || requestParameter.length == 0) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Request parameter is null or empty");
            throw new ClientException("Request parameter is null or empty");
        }
    }

    public static String authTokenNullCheck(RequestObject request) throws ClientException {
        String authToken = request.getHeaders().get(Constants.AUTHORIZATION);
        if (authToken == null || authToken.trim().isEmpty()) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error(UIMessages.AUTH_KEY_INVALID + " Reason: It is either NULL or empty.");
            throw new ClientException(UIMessages.AUTH_KEY_INVALID);
        }
        return authToken;
    }

    public static String accountNullCheck(RequestObject request) throws ClientException {
        String accountIdentifier = request.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (accountIdentifier == null || accountIdentifier.trim().isEmpty()) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error(UIMessages.ACCOUNT_NULL_OR_EMPTY);
            throw new ClientException(UIMessages.ACCOUNT_NULL_OR_EMPTY);
        }
        return accountIdentifier;
    }

    public static String serviceNullCheck(RequestObject request) throws ClientException {
        String serviceIdStr = request.getParams().get(Constants.SERVICE_IDENTIFIER);
        if (StringUtils.isEmpty(serviceIdStr.trim())) {
            log.error(UIMessages.INVALID_SERVICE, "");
            throw new ClientException(UIMessages.INVALID_SERVICE);
        }
        int serviceId;
        try {
            serviceId = Integer.parseInt(serviceIdStr.trim());
            if (serviceId < 1) {
                log.error(UIMessages.INVALID_SERVICE, serviceId);
                throw new ClientException(UIMessages.INVALID_SERVICE);
            }
        } catch (NumberFormatException e) {
            log.error(UIMessages.INVALID_SERVICE, serviceIdStr);
            throw new ClientException(UIMessages.INVALID_SERVICE);
        }
        return serviceIdStr;
    }

}
