package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.pojo.CommandAuditDetails;
import com.appnomic.appsone.controlcenter.pojo.CommandMetadataAuditDetails;
import org.skife.jdbi.v2.sqlobject.BindBean;
import org.skife.jdbi.v2.sqlobject.GetGeneratedKeys;
import org.skife.jdbi.v2.sqlobject.SqlUpdate;

public interface CommandAuditDao {

    @SqlUpdate("INSERT INTO command_audit_details (supervisor_identifier, agent_identifier, " +
            "command_timeout_in_secs, retries, trigger_time, command_complete_time, exit_status, " +
            "trigger_source, user_details_id, supr_ctrl_ttl_in_secs, violation_time, created_time, command_job_id) VALUES (" +
            ":supervisorIdentifier, :agentIdentifier, :commandTimeout, :retryNumber, :triggerTime, " +
            ":commandCompleteTime, :exitCode, :triggerSource, :userDetailId, :supervisorCtrlTTL, " +
            ":violationTime, :createdTime, :commandJobID)")
    @GetGeneratedKeys
    int addCommandAuditDetails(@BindBean CommandAuditDetails caDetails);


    @SqlUpdate("INSERT INTO command_metadata_audit_details (`command_audit_id`, `created_time`, `key`, `value`)" +
            " VALUES (:commandAuditId, :createdTime, :key, :value)")
    @GetGeneratedKeys
    int addCommandMetadataAuditDetails(@BindBean CommandMetadataAuditDetails metadata);
}
