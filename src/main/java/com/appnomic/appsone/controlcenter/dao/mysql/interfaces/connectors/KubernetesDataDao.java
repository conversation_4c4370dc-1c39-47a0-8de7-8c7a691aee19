package com.appnomic.appsone.controlcenter.dao.mysql.interfaces.connectors;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.ApplicationToKpiMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.PrometheusHealKpis;
import com.appnomic.appsone.controlcenter.pojo.connectors.*;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.Define;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

@UseStringTemplate3StatementLocator
public interface KubernetesDataDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select kpi_name from dataadapter_kubernetes.heal_kpis")
    List<String> getHealKpis();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select kpi.id id, kpi.metric metric, kpi.host_or_comp hostOrComp from dataadapter_kubernetes.prometheus_kpis kpi;")
    List<PrometheusHealKpis> getPrometheusKpis();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update dataadapter_kubernetes.prometheus_kpis " +
            "Set hostOrComp= :host_or_comp " +
            " where metric = :metric;")
    @GetGeneratedKeys
    void updatePrometheusKpis(@BindBean PrometheusHealKpis prometheusHealKpis);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update dataadapter_kubernetes.domain_to_heal_kpi_mappings " +
            "Set heal_kpi_id= :healIdentifier " +
            " where src_kpi_id = :srcId;")
    @GetGeneratedKeys
    void updateDomainToHeal(@BindBean String healIdentifier, int srcId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update dataadapter_kubernetes.heal_kpis " +
            "Set kpi_name= :kpiName , kpi_identifier= :newHealIdentifier" +
            " where kpi_identifier = :oldHealIdentifier;")
    @GetGeneratedKeys
    void updateHealKpi(@BindBean String kpiName, String newHealIdentifier, String oldHealIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into <schema>.kubernetes_config (id,file_name,kube_config)" +
            " values (:id,:fileName,:kubernetesConfig);")
    @GetGeneratedKeys
    void addKubernetesConfig(@Define("schema") String schema, @BindBean List<KubernetesConfiguration> kubernetesConfigurations);

    @SqlUpdate("delete from <schema>.kubernetes_config")
    void deleteKubernetesConfig(@Define("schema") String schema);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into <schema>.prometheus_query_filters (host_or_comp,query, query_value)" +
            " values (:hostOrComponent,:query,:queryValue);")
    @GetGeneratedKeys
    void addPrometheusQueryFilter(@Define("schema") String schema, @BindBean List<PrometheusQueryFilter> prometheusQueryFilters);

    @SqlUpdate("delete from <schema>.prometheus_query_filters")
    void deletePrometheusQueryFilter(@Define("schema") String schema);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into <schema>.prometheus_kpi_response_path(host_or_comp,service_name,kpi_name,pod_name,pod_namespace)" +
            " values(:hostOrComponent,:serviceName,:kpiName,:podName,:podNamespace);")
    @GetGeneratedKeys
    void addPrometheusKpiResponsePaths(@Define("schema") String schema, @BindBean List<PrometheusKpiResponsePath> prometheusKpiResponsePaths);

    @SqlUpdate("delete from <schema>.prometheus_kpi_response_path")
    void deletePrometheusKpiResponsePaths(@Define("schema") String schema);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into <schema>.prometheus_application(id,host_port,username,password)" +
            " values(:id,:hostPort,:userName,:password);")
    @GetGeneratedKeys
    void addPrometheusApplications(@Define("schema") String schema, @BindBean List<PrometheusApplication> prometheusApplications);

    @SqlUpdate("delete from <schema>.prometheus_application")
    void deletePrometheusApplications(@Define("schema") String schema);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into <schema>.a1_logscan_endpt(host,port,protocol)" +
            " values(:hostIp,:port,:type);")
    @GetGeneratedKeys
    void addA1LogscanEndpt(@Define("schema") String schema, @BindBean List<A1LogscanEndpoint> a1LogscanEndpoints);

    @SqlUpdate("delete from <schema>.a1_logscan_endpt")
    void deleteA1LogscanEndpt(@Define("schema") String schema);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into <schema>.elastic_search_response_path(time_stamp,uri,status_code,method,response_time,pod_name,namespace_name)" +
            " values(:timestamp,:uri,:statusCode,:method,:responseTime,:podName,:namespaceName);")
    @GetGeneratedKeys
    void addElasticSearchResponsePath(@Define("schema") String schema, @BindBean List<ElasticSearchResponsePath> elasticSearchResponsePaths);

    @SqlUpdate("delete from <schema>.elastic_search_response_path")
    void deleteElasticSearchResponsePath(@Define("schema") String schema);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into <schema>.prometheus_kpis(metric,host_or_comp)" +
            " values(:metric,:hostOrComp);")
    @GetGeneratedKeys
    int [] addPrometheusKpis(@Define("schema") String schema, @BindBean List<PrometheusHealKpis> prometheusHealKpis);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into <schema>.prometheus_application_kpi_map (application_id, kpi_id) " +
            "values (:application_id, :kpi_id);")
    @GetGeneratedKeys
    void addKubernetesApplicationKpiMapping(@Define("schema") String schema, @BindBean List<ApplicationToKpiMapping> applicationToKpiMappings);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into <schema>.Heal_access_token (host_port,client_id,user_name,password,grant_type) " +
            "values (:hostPort,:clientId,:userName,:password,:grantType);")
    @GetGeneratedKeys
    void addKubernetesHealAccessToken(@Define("schema") String schema, @BindBean List<KubernetesHealAccessToken> kubernetesHealAccessTokens);

    @SqlUpdate("delete from <schema>.Heal_access_token")
    void deleteKubernetesHealAccessToken(@Define("schema") String schema);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into <schema>.heal_instance_creation_payload (image, namespace,host_ins_component_name,host_ins_component_version,host_ins_service_identifier,component_ins_component_name,component_ins_component_version,component_ins_service_identifier) " +
            "values (:image,:namespace,:hostInstanceCompName,:hostInstanceCompVersion,:hostInstanceServiceIdentifier,:componentInstanceComponentName,:componentInstanceComponentVersion,:componentInstanceServiceIdentifier);")
    @GetGeneratedKeys
    void addKubernetesHealInstanceCreationPayloads(@Define("schema") String schema, @BindBean List<KubernetesHealInstanceCreationPayload> kubernetesHealInstanceCreationPayloads);

    @SqlUpdate("delete from <schema>.heal_instance_creation_payload")
    void deleteKubernetesHealInstanceCreationPayloads(@Define("schema") String schema);

}
