package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProcessHostDetailsBean {

    private int id;
    private String hostAddress;
    private String directoryPath;
    private int processDetailsId;
    private String createdTime;
    private String updatedTime;
    private int status;
    private String userDetailsId;

    private List<ProcessArgumentBean> arguments;

}