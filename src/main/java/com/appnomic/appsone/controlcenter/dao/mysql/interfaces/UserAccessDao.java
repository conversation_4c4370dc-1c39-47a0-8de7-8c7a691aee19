package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.UserAccessBean;
import com.appnomic.appsone.controlcenter.beans.UserBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.RoutesInformation;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserAttributesBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserProfileBean;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface UserAccessDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mr.name roleName, mr.id roleId, map.id accessProfileId, map.name accessProfileName " +
            "from mst_roles mr join mst_access_profiles map join user_attributes ua on mr.id=ua.mst_role_id and mr.id=map.mst_role_id " +
            "and map.id=mst_access_profile_id where ua.user_identifier=:user_identifier")
    UserAttributesBean getRoleProfileInfoForUserId(@Bind("user_identifier") String userId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select vrd.action_identifier from mst_access_profile_mapping mapm join view_route_details vrd " +
            "on mst_big_feature_id=vrd.big_feature_id where mst_access_profile_id=:profile_id and dashboard_name='ControlCenter'")
    List<String> getUserAccessibleActions(@Bind("profile_id") int profileId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select vrd.action_id actionId, vrd.action_identifier actionIdentifier, vrd.route_id routeId, vrd.request_method requestMethod, vrd.base_url baseUrl, " +
            "vrd.path_info pathInfo, vrd.path_info_regex pathInfoInRegex, vrd.big_feature_identifier bigFeatureIdentifier from view_route_details vrd " +
            "join mst_access_profile_mapping mapm on vrd.big_feature_id=mapm.mst_big_feature_id " +
            "where mapm.mst_access_profile_id=:profile_id and vrd.dashboard_name='ControlCenter'")
    List<RoutesInformation> getAccessibleRoutesForUser(@Bind("profile_id") int profileId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select access_details accessDetailsJson, user_identifier userId from user_access_details where user_identifier=:user_identifier")
    UserAccessBean getUserAccessDetails(@Bind("user_identifier") String userIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select map.id id,map.name name,mr.name roleName from mst_access_profiles map,mst_roles mr where mr.id=map.mst_role_id")
    List<UserProfileBean> getUserProfiles();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name from mst_roles where ui_visible = 1")
    List<IdPojo> getRoles();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from mst_access_profiles where name=:profileName")
    int getProfileIdForHealAdmin(@Bind("profileName") String profileName);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mb.name from mst_access_profile_mapping ma, mst_big_features mb " +
            "where ma.mst_access_profile_id=:profileId and ma.mst_big_feature_id=mb.id and mb.ui_visible=1")
    List<String> getAccessProfileMapping(@Bind("profileId") int profileId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select user_identifier from user_attributes where username=:username")
    String getUserIdentifierFromName(@Bind("username") String username);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select ua.user_identifier from user_attributes ua,user_access_details uad where uad.user_identifier=ua.user_identifier")
    List<String> getUserIdentifiers();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from appsonekeycloak.USER_ENTITY")
    List<String> getKeycloakUserIdentifiers();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select first_name firstName, last_name lastName, email, id, username, enabled from appsonekeycloak.USER_ENTITY where username = :username")
    UserBean getUserDetailsFromUsername(@Bind("username") String username);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select first_name firstName, last_name lastName, email, id, username, enabled from appsonekeycloak.USER_ENTITY")
    List<UserBean> getUserDetailsFromKeycloak();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,username name, user_identifier identifier from user_attributes where status = 1")
    List<IdPojo> getActiveUsers();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mst_access_profile_id from user_attributes where user_identifier =:userIdentifier")
    int getUserProfileId(@Bind("userIdentifier") String userIdentifier);

}
