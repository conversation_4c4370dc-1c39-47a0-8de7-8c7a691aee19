package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR> on 19/12/19
 */


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceCommandArgument {
    private int id;
    private int serviceId;
    private int agentTypeId;
    private int commandId;
    private int commandArgumentId;
    private String argumentValue;
    private String userDetailsId;
    private Timestamp createdTime;
    private Timestamp updatedTime;
}
