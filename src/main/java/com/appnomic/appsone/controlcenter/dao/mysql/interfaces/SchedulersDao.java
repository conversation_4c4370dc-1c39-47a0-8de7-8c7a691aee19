package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.heal.configuration.entities.ScheduledJobArguments;
import com.heal.configuration.entities.ScheduledJobDetails;
import com.heal.configuration.entities.SchedulerArguments;
import com.heal.configuration.entities.SchedulerDetails;
import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.BindBean;
import org.skife.jdbi.v2.sqlobject.GetGeneratedKeys;
import org.skife.jdbi.v2.sqlobject.SqlUpdate;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

public interface SchedulersDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE scheduler_job_details SET job_status=:jobStatus where id=:scheduledJobId")
    void updateScheduledJobStatus(@Bind("scheduledJobId") int scheduledJobId, @Bind("jobStatus") String jobStatus);

    @SqlUpdate("insert into scheduler_details (name, type_id, sink_type_id, start_time, end_time, cron_expression, " +
            "created_time, updated_time, user_details_id, account_id, status) values (:name, :typeId, :sinkTypeId, " +
            ":startTime, :endTime, :cronExpression, :createdTime, :updatedTime, :userDetailsId, :accountId, :status)")
    @GetGeneratedKeys
    int insertSchedulerDetails(@BindBean SchedulerDetails schedulerDetailsBean);

    @SqlUpdate("INSERT INTO scheduler_arguments (argument_name, argument_value, default_value, is_placeholder, scheduler_id, created_time, " +
            "updated_time, user_details_id) values (:argumentName, :argumentValue, :defaultValue, " +
            ":placeholder, :schedulerId, :createdTime, :updatedTime, :userDetailsId)")
    void insertSchedulerArguments(@BindBean SchedulerArguments schedulerArguments);

    @SqlUpdate("INSERT INTO scheduler_job_details (name, scheduler_id, implementation_id, created_time, updated_time, user_details_id, " +
            "status, job_status) values (:name, :schedulerId, :implementationId, " +
            ":createdTime, :updatedTime, :userDetailsId, :status, :jobStatus)")
    @GetGeneratedKeys
    int insertSchedulerJobDetails(@BindBean ScheduledJobDetails schedulerJobDetails);

    @SqlUpdate("INSERT INTO scheduler_job_arguments (argument_name, argument_value, default_value, is_placeholder, scheduler_job_id, created_time, " +
            "updated_time, user_details_id) values (:argumentName, :argumentValue, :defaultValue, " +
            ":placeholder, :schedulerJobId, :createdTime, :updatedTime, :userDetailsId)")
    void insertSchedulerJobArguments(@BindBean ScheduledJobArguments scheduledJobArguments);
}
