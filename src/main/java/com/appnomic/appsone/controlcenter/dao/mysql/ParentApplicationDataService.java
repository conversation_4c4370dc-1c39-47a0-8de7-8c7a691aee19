package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ParentApplicationDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.heal.configuration.entities.ParentApplicationBean;
import com.heal.configuration.pojos.ParentApplication;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.Handle;

import java.util.List;

@Slf4j
public class ParentApplicationDataService extends AbstractDaoService<ParentApplicationDao> {

    public int addParentApplication(ParentApplicationBean parentApplicationBean, Handle handle) throws ControlCenterException {
        ParentApplicationDao parentApplicationDAO = getDaoConnection(handle, ParentApplicationDao.class);
        try {
            log.debug("Adding Parent Application");
            int id = parentApplicationDAO.addParentApplication(parentApplicationBean);
            log.info("Parent Application {} added Successfully", parentApplicationBean);

            return id;
        } catch (Exception e) {
            log.error("Error Adding Parent Application", e);
            throw new ControlCenterException("Error Adding Parent Application");
        } finally {
            closeDaoConnection(handle, parentApplicationDAO);
        }
    }

    public void addParentApplications(List<ParentApplicationBean> parentApplicationBeans, Handle handle) throws ControlCenterException {
        ParentApplicationDao parentApplicationDAO = getDaoConnection(handle, ParentApplicationDao.class);
        try {
            log.debug("Adding Parent Application");
            parentApplicationDAO.addParentApplications(parentApplicationBeans);
            log.info("Parent Application {} added Successfully", parentApplicationBeans);
        } catch (Exception e) {
            log.error("Error Adding Parent Application", e);
            throw new ControlCenterException("Error Adding Parent Application");
        } finally {
            closeDaoConnection(handle, parentApplicationDAO);
        }
    }

    public List<ParentApplicationBean> getParentApplicationsByIdentifiers(List<ParentApplicationBean> parentApplicationBeans, Handle handle) {
        ParentApplicationDao parentApplicationDAO = getDaoConnection(handle, ParentApplicationDao.class);
        try {
            return parentApplicationDAO.getParentApplicationsByIdentifiers(parentApplicationBeans);
        } catch (Exception e) {
            log.error("Error occurred while getting Parent Application for identifier:{}", parentApplicationBeans, e);
        } finally {
            closeDaoConnection(handle, parentApplicationDAO);
        }
        return null;
    }

    public IdPojo deleteParentApplication(Handle conn, ParentApplication parentApplication) {
        ParentApplicationDao parentApplicationDAO = getDaoConnection(conn, ParentApplicationDao.class);
        try {
            parentApplicationDAO.deleteParentApplicationByIdentifier(parentApplication.getIdentifier(), parentApplication.getAccountId());
            return IdPojo.builder().id(parentApplication.getId()).name(parentApplication.getName()).identifier(parentApplication.getIdentifier()).build();

        } catch (Exception e) {
            log.error("Error occurred while deleting Parent Application for identifier:{}", parentApplication.getIdentifier(), e);
        } finally {
            closeDaoConnection(conn, parentApplicationDAO);
        }
        return null;
    }

    public void editParentApplication(int parentId, String name, int accountId, Handle handle) {
        ParentApplicationDao parentApplicationDAO = getDaoConnection(handle, ParentApplicationDao.class);
        try {
            parentApplicationDAO.editName(name, parentId, accountId);
        } catch(Exception e) {
            log.error("Error while editing parent application:{}", name);
        } finally {
            closeDaoConnection(handle, parentApplicationDAO);
        }
    }
}
