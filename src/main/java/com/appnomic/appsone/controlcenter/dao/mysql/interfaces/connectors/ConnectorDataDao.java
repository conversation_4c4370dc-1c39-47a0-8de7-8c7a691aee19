package com.appnomic.appsone.controlcenter.dao.mysql.interfaces.connectors;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.*;
import com.appnomic.appsone.controlcenter.pojo.connectors.ConnectorDBData;
import com.appnomic.appsone.controlcenter.pojo.connectors.ConnectorUploadResponse;
import com.appnomic.appsone.controlcenter.pojo.connectors.WorkerParameter;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.Define;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.sql.Timestamp;
import java.util.List;

@UseStringTemplate3StatementLocator
public interface ConnectorDataDao {


    @SqlUpdate("UPDATE connector_template_data " +
            "SET upload_error = :error , upload_error_time = :time " +
            "WHERE mst_connector_details_id = :connectorId AND account_id in (1, :accountId);")
    void updateUploadError(@Bind("error") String error, @Bind("time") Timestamp time, @Bind("connectorId") int connectorId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into <schema>.heal_kpis(kpi_id,kpi_name,kpi_identifier,is_group,group_name)" +
            " values(:kpiId,:kpiName,:kpiIdentifier,:isGroupKpi,:groupName);")
    @GetGeneratedKeys
    int[] addHealKpis(@Define("schema") String schema, @BindBean List<HealKpi> healKpi);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update <schema>.heal_kpis " +
            "Set kpi_name = :kpiName , kpi_identifier = :kpiIdentifier, is_group = :isGroupKpi, group_name = :groupName " +
            " where kpi_id = :kpiId;")
    @GetGeneratedKeys
    void updateHealKpis(@Define("schema") String schema, @BindBean List<HealKpi> healKpis);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into <schema>.domain_to_heal_kpi_mappings(domain,heal_kpi_id,src_kpi_id)" +
            " values(:domainName,:healIdentifier,:sourceId);")
    @GetGeneratedKeys
    int[] addDomainToHealKpiMapping(@Define("schema") String schema, @BindBean List<DomainToHealKpiMapping> domainToHealKpiMapping);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select * from <schema>.domain_to_heal_kpi_mappings ")
    List<DomainToHealKpiMapping> getDomainToHealKpi(@Define("schema") String schema);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select * from <schema>.heal_kpis ")
    List<HealKpi> getHealKpi(@Define("schema") String schema);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into <schema>.adapter_chain_config (id,chain_id,sleep_interval,reload_interval_inminutes," +
            "processor_thread_pool_size,loader_thread_pool_size,pull_historic_data,historic_start_date," +
            "historic_end_date,historic_interval,test_start_date,test_end_date,add_sysout_loader," +
            "disable_chain,disable_chain_reload,back_pressure_strategy,back_pressure_max_size,delta_inminutes," +
            "extractor_id,pre_processor )" +
            "values(:id,:chainName,:sleepInterval,:reloadInterval,:processorThreadPoolSize,:loaderThreadPoolSize," +
            ":pullHistoricData,:historicStartDate,:historicEndDate,:historicInterval,:testStartDate,:testEndDate," +
            ":addSysLoader,:disableChain,:disableChainReload,:backPressureStrategy,:backPressureMaxSize,:deltaInMinutes," +
            ":extractorId,:preProcessor);")
    @GetGeneratedKeys
    int[] addConnectorSettings(@Define("schema") String schema, @BindBean List<ConnectorSettingsEntity> connectorSettingsEntity);

    @SqlUpdate("delete from <schema>.adapter_chain_config")
    void deleteConnectorSettings(@Define("schema") String schema);

    @SqlUpdate("update <schema>.adapter_chain_config set template_data_id=:connectorId where id=:chainConfigId;")
    @GetGeneratedKeys
    int updateConnectorDataIdInAdapterChainConfig(@Define("schema") String schema, @Bind("connectorId") Integer connectorId, @Bind("chainConfigId") Integer chainConfigId);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id FROM mst_connector_template where mst_connector_details_id=:connectorId" +
            " and is_default=1 and account_id=:accountId;")
    int getConnectorTemplateId(@Bind("connectorId") Integer id, @Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("insert into connector_template_data (mst_connector_details_id, " +
            "mst_connector_template_id, template_data_content, account_id, created_time, " +
            "updated_time, user_details_id, status, upload_status) " +
            "values (:connectorDetailsId,:connectorTemplateId, :templateDataContent, :accountId, " +
            ":createdTime, :updatedTime,:userDetailsId, :status, :uploadStatus);")
    @GetGeneratedKeys
    List<Integer> addTheConnectorTemplateDataDetails(@BindBean ConnectorTemplateDataBean connectorTemplateDataBean);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select name from mst_connector_details where id=:connectorId and account_id=:accountId;")
    @GetGeneratedKeys
    String getConnectorDetailsBasedOnId(@Bind("connectorId") Integer connectorId, @Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select name as name,description as features,created_time as createdOn,configured as status,user_details_id as createdBy" +
            " from mst_connector_details where id=:connectorId and account_id=:accountId;")
    ConnectorUploadResponse getConnectorDataBasedOnId(@Bind("connectorId") Integer connectorId, @Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO <schema>.worker_parameters (worker_id, name, value) " +
            "VALUES (:chainWorkerId, :name, :value);")
    @GetGeneratedKeys
    int[] addWorkerParameters(@Define("schema") String schema, @BindBean List<WorkerParameter> workerParameters);

    @SqlUpdate("delete from <schema>.worker_parameters")
    void deleteWorkerParameters(@Define("schema") String schema);

    @SqlUpdate("update connector_template_data set status=0 " +
            "where mst_connector_details_id=:connectorId and mst_connector_template_id=:templateId and account_id=1;")
    @GetGeneratedKeys
    int updateStatusOfTemplate(@Bind("connectorId") Integer connectorId, @Bind("templateId") Integer templateId, @Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO <schema>.a1_logscan_heal_instance_mapper" +
            " (logscan_instance_name, heal_agent_uid, heal_instance_name) VALUES (:sourceInstanceName, :agentIdentifier,:healInstanceName);")
    @GetGeneratedKeys
    int[] addLogscanHealInstanceMapper(@Define("schema") String schema, @BindBean List<LogscanHealInstanceMapper> logscanHealInstanceMappers);

    @SqlUpdate("delete from <schema>.a1_logscan_heal_instance_mapper")
    void deleteLogscanHealInstanceMapper(@Define("schema") String schema);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO <schema>.adapter_chain_repository (name, class_path, user_name, password, auto_reconnect," +
            " cpool_min_size, cpool_max_size, connection_url)" +
            " VALUES (:name, :className, :username, :password, :autoConnect, :poolMinSize, :poolMaxSize,:connectionUrl);")
    @GetGeneratedKeys
    int[] addAdapterChainRepository(@Define("schema") String schema, @BindBean List<AdapterChainRepository> adapterChainRepositories);

    @SqlUpdate("delete from <schema>.adapter_chain_repository")
    void deleteAdapterChainRepository(@Define("schema") String schema);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id connectorId, name connectorName, sql_data sqlData, db_created dbExists " +
            "FROM mst_connector_template WHERE account_id IN (1, :accountId) " +
            "AND id = :connectorId;")
    ConnectorDBData getDBData(@Bind("connectorId") Integer connectorId, @Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE mst_connector_template SET db_created = :status " +
            "WHERE account_id IN (1, :accountId) " +
            "AND id = :connectorId;")
    void updateDBExistsStatus(@Bind("connectorId") Integer connectorId, @Bind("accountId") Integer accountId, @Bind("status") Integer status);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT ID, mst_connector_details_id connectorDetailsId, mst_connector_template_id " +
            "connectorTemplateId, template_data_content templateDataContent, account_id accountId, " +
            "user_details_id userDetailsId, status, upload_status uploadStatus, created_time createdTime, " +
            "updated_time updatedTime FROM connector_template_data " +
            "WHERE mst_connector_details_id = :connectorId AND account_id in (1, :accountId); ")
    List<ConnectorTemplateDataBean> getConnectorConfigDataBasedOnId(@Bind("connectorId") Integer connectorId, @Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE connector_template_data " +
            "SET template_data_content = :templateDataContent, user_details_id = :userDetailsId, " +
            "status = :status, upload_status = :uploadStatus, created_time = :createdTime, " +
            "updated_time = :updatedTime " +
            "WHERE mst_connector_details_id = :connectorId AND account_id IN (1, :accountId);")
    void updateConnectorConfigDataById(@Bind("accountId") Integer accountId, @Bind("connectorId") Integer connectorId, @Bind("templateDataContent") String templateDataContent, @Bind("userDetailsId") String userDetailsId, @Bind("status") Integer status, @Bind("uploadStatus") Integer uploadStatus, @Bind("createdTime") Timestamp createdTime, @Bind("updatedTime") Timestamp updatedTime);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE mst_connector_details SET configured = :isConfigured " +
            "WHERE account_id IN (1, :accountId) " +
            "AND id = :connectorId;")
    void updateConnectorConfigured(@Bind("connectorId") Integer connectorId, @Bind("accountId") Integer accountId, @Bind("isConfigured") Integer isConfigured);


}