package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class KpiBean {
    private int id;
    private String name;
    private int kpiTypeId;
    private String clusterOperation;
    private String measureUnits;
    private String dataType;
    @EqualsAndHashCode.Exclude
    private int status;
    private int groupKpiId;
    private String valueType;
    private String rollupOperation;
    private int clusterAggregation;
    private int instanceAggregation;
    private int isCustom;
    private int isComputed;
    private String description;
    private int accountId;
    @EqualsAndHashCode.Exclude
    private String createdTime;
    @EqualsAndHashCode.Exclude
    private String updatedTime;
    private String identifier;
    @EqualsAndHashCode.Exclude
    private String userId;
    private int componentId;
    private int componentTypeId;
    private int commonVersionId;
    private String cronExpression;
    private int resetDeltaValue;
    private int deltaPerSec;
}
