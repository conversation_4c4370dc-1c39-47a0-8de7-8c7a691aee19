package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.GroupTagsDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;
import java.util.Set;

public class GroupTagsDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(GroupTagsDataService.class);

    private GroupTagsDataService() {}

    public static List<DiscoveryTagsBean> getTransactionTagList(int accountId) {
        GroupTagsDao groupTagsDao = MySQLConnectionManager.getInstance().getHandle().onDemand(GroupTagsDao.class);
        try {
            return groupTagsDao.getTransactionTags(accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while retrieving the transaction tags list", e);
        } finally {
            MySQLConnectionManager.getInstance().close(groupTagsDao);
        }
        return Collections.emptyList();
    }

    public static DiscoveryTagsBean getTransactionTags(int accountId, String tag, Handle handle) {
        GroupTagsDao groupTagsDao = getGroupTagsDao(handle);
        try {
            return groupTagsDao.getTransactionTags(accountId, tag);
        } catch (Exception e) {
            LOGGER.error("Exception while retrieving the transaction tags", e);
        } finally {
            closeDaoConnection(handle, groupTagsDao);
        }
        return null;
    }

    public static int addTransactionGroups(TransactionGroupsBean txnGrp, Handle handle) {
        GroupTagsDao groupTagsDao = getGroupTagsDao(handle);
        try {
            return groupTagsDao.addTransactionGroups(txnGrp);
        } catch (Exception e) {
            LOGGER.error("Error occurred while adding records to table: transaction_groups ", e);
        } finally {
            closeDaoConnection(handle, groupTagsDao);
        }
        return -1;
    }

    public static int[] removeTags(Set<Integer> discoveryTagIds, int accountId, Handle handle) {
        GroupTagsDao groupTagsDao = getGroupTagsDao(handle);
        try {
            return groupTagsDao.deleteRequestTagMapping(accountId, discoveryTagIds);
        }
        catch (Exception e) {
            LOGGER.error("Error occurred while deleting records to table: transaction_group_mapping ", e);
        } finally {
            closeDaoConnection(handle, groupTagsDao);
        }
        return  new int[0];
    }

    public static int addTransactionGroupMap(TransactionGroupMapBean txnGrpMap, Handle handle) {
        GroupTagsDao groupTagsDao = getGroupTagsDao(handle);
        try {
            return groupTagsDao.addTransactionGroupMap(txnGrpMap);
        } catch (Exception e) {
            LOGGER.error("Error occurred while adding transaction group map to table- transaction_group_mapping : ", e);
        } finally {
            closeDaoConnection(handle, groupTagsDao);
        }
        return -1;
    }

    private static GroupTagsDao getGroupTagsDao(Handle handle){
        if(handle == null){
            return MySQLConnectionManager.getInstance().open(GroupTagsDao.class);
        }
        else{
            return handle.attach(GroupTagsDao.class);
        }
    }

    private static void closeDaoConnection(Handle handle, GroupTagsDao dao){
        if(handle == null){
            MySQLConnectionManager.getInstance().close(dao);
        }
    }
}
