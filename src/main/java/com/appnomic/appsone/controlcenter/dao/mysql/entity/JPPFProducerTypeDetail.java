package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

@Data
public class JPPFProducerTypeDetail extends ProducerTypeDetail {

    private static final Logger LOGGER = LoggerFactory.getLogger(JPPFProducerTypeDetail.class);
    private Integer jppfTypeId;

    public JPPFProducerTypeDetail(int producerId, String createdTime, String updatedTime, String userDetailsId) {
        super(producerId, createdTime, updatedTime, userDetailsId);
    }

    @Override
    public Map<String, String> getDataMap() {
        return new HashMap<>();
    }

    public boolean populate(Map<String, String> attributes) {
        String jppfType = attributes.get(Constants.JPPF_TYPE_TYPEID_ATTRIBUTE);
        if(jppfType != null ) {
            ViewTypes viewTypes = MasterCache.getMstTypeForSubTypeName(
                    Constants.JPPF_TYPE_TYPEID_MASTER_TYPE, jppfType);
            if( viewTypes == null ) {
                LOGGER.error("Invalid entry received: {}", jppfType);
                return false;
            }
            this.setJppfTypeId(viewTypes.getSubTypeId());
        }
        return validate();
    }

    private boolean validate() {
        String emptyFieldMsgTemplate = "Mandatory field [{}] is missing";

        if( this.getJppfTypeId() == null ) {
            LOGGER.error(emptyFieldMsgTemplate, "server_type");
            return false;
        }
        return true;
    }
}
