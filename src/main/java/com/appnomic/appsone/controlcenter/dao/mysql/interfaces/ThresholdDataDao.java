package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.Define;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.sql.Timestamp;
import java.util.List;

public interface ThresholdDataDao {
    @SqlBatch("INSERT INTO comp_instance_kpi_threshold_details (comp_instance_id, kpi_id, operation_id, min_threshold, max_threshold, created_time, " +
            "updated_time, user_details_id, kpi_group_id, attribute_value, start_time, end_time, account_id, sor_persistence, sor_suppression, :status, :severity) " +
            "VALUES (:compInstanceId, :kpiId, :operationId, :minThreshold, :maxThreshold, :createdTime, :updatedTime, :userDetailsId, :kpiGroupId, " +
            ":kpiAttribute, :startTime, :endTime, :accountId, :persistence, :suppression, :status, :severity)")
    void addCompInstanceKpiThreshold(@BindBean List<CompInstKpiThresholdDetailsBean> instKpiThreshold);

    @SqlBatch("INSERT INTO application_threshold_details (created_time, updated_time, user_details_id, application_id, transaction_kpi_type_id, " +
            "operation_id, min_threshold, max_threshold, response_time_type_id, start_time, end_time, coverage_window_profile_id, account_id) " +
            "VALUES (:createdTime, :updatedTime, :userDetailsId, :applicationId, :transactionKpiTypeId, :operationId, :minThreshold, :maxThreshold, " +
            ":responseTimeTypeId, :startTime, :endTime, :coverageWindowProfileId, :accountId)")
    void addApplicationThreshold(@BindBean List<ApplicationThresholdDetailsBean> appThresholdDetailsBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select profile_id profileId,  profile_name profileName,day_option_id dayOptionId, day_option_name dayOptionName, status status, " +
            "account_id accountId, day day, start_hour startHour, start_minute startMinute, end_hour endHour, end_minute endMinute from view_coverage_window_profile_details")
    List<ViewCoverageWinProfDetailsBean> getCoverageWindowsProfiles();

    @SqlBatch("INSERT INTO transaction_response_threshold(transaction_id,slow_threshold_value,created_time, updated_time, user_details_id,response_time_type_id," +
            "start_time,end_time, account_id,coverage_window_profile_id) VALUES (:transactionId,:slowThresholdValue, :createdTime,:updatedTime, " +
            ":userDetailsId,:responseTimeTypeId, :startTime,:endTime, :accountId, :coverageWindowProfileId)")
    void addTransactionResponseThreshold(@BindBean List<TransactionResponseThresholdViolationBean> transactionResponseThresholdBean);

    @SqlBatch("INSERT INTO transaction_threshold_details(created_time,updated_time,user_details_id,transaction_id,transaction_kpi_type_id,operation_id," +
            "min_threshold,max_threshold,response_time_type_id,start_time,end_time,coverage_window_profile_id,account_id) VALUES (:createdTime,:updatedTime, " +
            ":userDetailsId,:transactionId, :transactionKpiTypeId,:operationId, :minThreshold,:maxThreshold, :responseTimeTypeId,:startTime, :endTime," +
            ":coverageWindowProfileId, :accountId)")
    void addTransactionThresholdDetails(@BindBean List<TransactionThresholdDetailsBean> transactionThresholdDetailsBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, created_time createdTime, updated_time updatedTime, user_details_id userDetailsId," +
            " transaction_id transactionId, operation_id operationId, min_threshold minThreshold," +
            " max_threshold maxThreshold, response_time_type_id responseTimeTypeId, start_time startTime," +
            " end_time endTime, account_id accountId, kpi_id kpiId, severity_id severityId, status, persistence," +
            " suppression, defined_by definedBy, exclude_maintenance excludeMaintenance" +
            " from transaction_threshold_details where transaction_id=:transactionId and kpi_id=:kpiId and account_id=:accountId")
    TransactionThresholdDetailsBean getTransactionThreshold(@Bind("accountId") int accountId, @Bind("transactionId") int transactionId, @Bind("kpiId") int kpiId);

    @SqlBatch("INSERT INTO transaction_threshold_details(created_time, updated_time, user_details_id, transaction_id, operation_id, min_threshold, max_threshold," +
            "response_time_type_id, start_time, end_time, account_id, kpi_id, severity_id, status, persistence, suppression, defined_by, exclude_maintenance) VALUES (:createdTime, :updatedTime, " +
            ":userDetailsId, :transactionId, :operationId, :minThreshold, :maxThreshold, :responseTimeTypeId, :startTime, :endTime, " +
            ":accountId, :kpiId, :severityId, :status, :persistence, :suppression, :definedBy, :excludeMaintenance)")
    @GetGeneratedKeys
    int[] addTransactionThreshold(@BindBean List<TransactionThresholdDetailsBean> transactionStaticThresholds);

    @SqlUpdate("UPDATE transaction_threshold_details set created_time = :createdTime, updated_time = :updatedTime, user_details_id = :userDetailsId," +
            " transaction_id = :transactionId, operation_id = :operationId, min_threshold = :minThreshold," +
            " max_threshold = :maxThreshold, response_time_type_id = :responseTimeTypeId, start_time = :startTime," +
            " end_time = :endTime, account_id = :accountId, kpi_id = :kpiId, severity_id = :severityId, status = :status, persistence = :persistence," +
            " suppression = :suppression, defined_by = :definedBy, exclude_maintenance = :excludeMaintenance WHERE id = :id")
    int updateTransactionThreshold(@BindBean TransactionThresholdDetailsBean transactionStaticThresholds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id id, comp_instance_id compInstanceId, kpi_id kpiId, attribute_value kpiAttribute, operation_id operationId, min_threshold minThreshold, max_threshold maxThreshold, " +
            "created_time createdTime, updated_time updatedTime, user_details_id userDetailsId, kpi_group_id kpiGroupId, " +
            "start_time startTime, end_time endTime, account_id accountId, status, severity " +
            "from comp_instance_kpi_threshold_details where comp_instance_id=:comp_instance_id and account_id=:accountId and status=1;")
    List<CompInstKpiThresholdDetailsBean> getCompInstanceThresholdDetailsList(@Bind("comp_instance_id") Integer compInstanceId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id id, comp_instance_id compInstanceId, kpi_id kpiId, attribute_value attributeValue, operation_id operationId, min_threshold minThreshold, " +
            "max_threshold maxThreshold, created_time createdTime, updated_time updatedTime, user_details_id userDetailsId, kpi_group_id kpiGroupId, " +
            "start_time startTime, end_time endTime, account_id accountId, status, severity " +
            "from comp_instance_kpi_threshold_details where comp_instance_id=:compInstanceId and account_id=:accountId and kpi_id=:kpiId")
    List<InstanceKpiAttributeThresholdBean> getCompInstanceThresholdDetail(@Bind("accountId") int accountId, @Bind("compInstanceId") int compInstanceId, @Bind("kpiId") int kpiId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id id,  mst_component_id componentId, kpi_id kpiId, operation_id operationId, min_threshold minThreshold, max_threshold maxThreshold, " +
            "created_time createdTime, updated_time updatedTime, user_details_id userDetailsId, kpi_group_id kpiGroupId, kpi_group_value kpiGroupValue, " +
            "coverage_window_profile_id coverageWindowProfileId, start_time startTime, end_time endTime, mst_common_version_id mstCommonVersionId " +
            "from component_kpi_threshold_details where mst_component_id=:mst_component_id;")
    List<ComponentKpiThresholdDetailsBean> getComponentThresholdDetailsList(@Bind("mst_component_id") Integer mstComponentId);

    @SqlBatch("INSERT INTO component_kpi_threshold_details (mst_component_id, kpi_id, operation_id, min_threshold, max_threshold, created_time, updated_time, " +
            "user_details_id, kpi_group_id, kpi_group_value, coverage_window_profile_id, start_time, end_time,mst_common_version_id, account_id) " +
            "VALUES (:componentId, :kpiId, :operationId, :minThreshold, :maxThreshold, :createdTime, :updatedTime, :userDetailsId, :kpiGroupId, :kpiGroupValue, " +
            ":coverageWindowProfileId, :startTime, :endTime, :mstCommonVersionId, :accountId)")
    void addComponentThresholdDetails(@BindBean List<ComponentKpiThresholdDetailsBean> componentKpiThresholdDetailsBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id id, comp_instance_id compInstanceId, kpi_id kpiId, operation_id operationId, min_threshold minThreshold, max_threshold maxThreshold, " +
            "created_time createdTime, updated_time updatedTime, user_details_id userDetailsId, kpi_group_id kpiGroupId, attribute_value kpiAttribute, " +
            "start_time startTime, end_time endTime, account_id accountId " +
            "from comp_instance_kpi_threshold_details where comp_instance_id=:comp_instance_id and kpi_id = :kpi_id and kpi_group_id = :kpi_group_id " +
            "and <grpKpiValCondition> order by start_time")
    List<CompInstKpiThresholdDetailsBean> getCompInstThrldDetailsListUsingKPIDetails(@Bind("comp_instance_id") Integer compInstanceId,
                                                                                     @Define("grpKpiValCondition") String nullCondition, @Bind("kpi_id") Integer kpiId,
                                                                                     @Bind("kpi_group_id") Integer kpiGroupId, @Bind("attributeVal") String attributeVal);

    @SqlUpdate("update comp_instance_kpi_threshold_details set min_threshold = :minThreshold, max_threshold = :maxThreshold, updated_time = :updatedTime, " +
            "user_details_id = :userId WHERE id = :id")
    void updateComponentInstThresholdDetails(@Bind("minThreshold") float minThreshold, @Bind("maxThreshold") float maxThreshold, @Bind("id") int id,
                                             @Bind("updatedTime") Timestamp updatedTime, @Bind("userId") String userId);

    @SqlUpdate("delete from comp_instance_kpi_threshold_details where id = :id")
    void deleteComponentInstThresholdDetails(@Bind("id") int id);

}
