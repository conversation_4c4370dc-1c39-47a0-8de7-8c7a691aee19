package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.ConnectionDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ImportServicesDao;
import com.appnomic.appsone.controlcenter.dao.opensearch.entity.ConfigKpiDetails;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ServiceKpiThreshold;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.StaticThresholdRules;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;

public class ImportServicesDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ImportServicesDataService.class);

    private ImportServicesDataService() {
        //Dummy constructor to hide the implicit one.
    }

    public static List<ConfigKpiDetails> getConfigkpiList(int serviceId, int accountId) {

        ImportServicesDao servicesDao = MySQLConnectionManager.getInstance().open(ImportServicesDao.class);
        try {
            return servicesDao.getConfigKpiList(serviceId, accountId);
        } catch (
                Exception e) {
            LOGGER.error("Exception while getting config kpi data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(servicesDao);
        }
        return Collections.emptyList();
    }

    public static List<Integer> getConfigkpiList(ServiceKpiThreshold serviceKpiThresholds) {

        ImportServicesDao servicesDao = MySQLConnectionManager.getInstance().open(ImportServicesDao.class);
        try {
            return servicesDao.getConfigKpiList(serviceKpiThresholds.getServiceId(), serviceKpiThresholds.getAccountId(), serviceKpiThresholds.getApplicableTo());
        } catch (Exception e) {
            LOGGER.error("Exception while getting config kpi data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(servicesDao);

        }
        return Collections.emptyList();
    }

    // Get all KPI Threshold
    public static List<ServiceKpiThreshold> getKpiThresholds(int accountId, int serviceId) {

        ImportServicesDao servicesDao = MySQLConnectionManager.getInstance().open(ImportServicesDao.class);
        try {
            List<ServiceKpiThreshold> list = servicesDao.getAllKpiThresholds(accountId, serviceId);
            if (list != null)
                return list;
            return Collections.emptyList();
        } catch (
                Exception e) {
            LOGGER.error("Exception while getting all kpi threshold data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(servicesDao);
        }
        return Collections.emptyList();
    }

    public static List<ServiceKpiThreshold> getConfiguredStaticThresholds(int accountId, int serviceId) {
        ImportServicesDao servicesDao = MySQLConnectionManager.getInstance().open(ImportServicesDao.class);
        try {
            return servicesDao.getConfiguredStaticThresholds(accountId, serviceId);
        } catch (
                Exception e) {
            LOGGER.error("Exception while getting all kpi threshold data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(servicesDao);
        }
        return Collections.emptyList();
    }

    // get unique row for the threshold KPI
    public static ServiceKpiThreshold getStaticThreshold(StaticThresholdRules staticThresholdRules, int accountId, int serviceId) {

        ImportServicesDao servicesDao = MySQLConnectionManager.getInstance().open(ImportServicesDao.class);
        try {
            ServiceKpiThreshold obj = servicesDao.getStaticThreshold(accountId, serviceId, Integer.parseInt(staticThresholdRules.getKpiId()), staticThresholdRules.getKpiLevel(), staticThresholdRules.getKpiAttribute());
            if (obj != null) {
                return obj;
            }
        } catch (
                Exception e) {
            LOGGER.error("Exception while getting all kpi threshold data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(servicesDao);
        }
        return null;
    }

    public static List<ConnectionDetailsBean> getConnectionDetails(int accountId) {

        ImportServicesDao servicesDao = MySQLConnectionManager.getInstance().open(ImportServicesDao.class);
        try {
            List<ConnectionDetailsBean> list = servicesDao.getConnectionDetails(accountId);
            if (list != null)
                return list;
            return Collections.emptyList();
        } catch (Exception e) {
            LOGGER.error("Exception while getting connection details from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(servicesDao);
        }
        return Collections.emptyList();
    }

    public static int addStaticThreshold(ServiceKpiThreshold thresholdBean, boolean isSystem) {
        ImportServicesDao importServicesDao = MySQLConnectionManager.getInstance().open(ImportServicesDao.class);
        try {
            if (isSystem) {
                return importServicesDao.addSystemThreshold(thresholdBean);
            }
            return importServicesDao.addThreshold(thresholdBean);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while adding static threshold. Reason: {}", e.getMessage(), e);
            return -1;
        } finally {
            MySQLConnectionManager.getInstance().close(importServicesDao);
        }
    }

    public static int[] addAvailabilityStaticThreshold(List<ServiceKpiThreshold> thresholdBean, boolean isSystem, Handle handle) {
        ImportServicesDao importServicesDao = getImportServicesDao(handle);
        try {
            if (isSystem) {
                return importServicesDao.addSystemAvailabilityStaticThreshold(thresholdBean);
            }
            return importServicesDao.addAvailabilityStaticThreshold(thresholdBean);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while adding static threshold. Reason: {}", e.getMessage(), e);
            return new int[]{-1};
        } finally {
            closeDaoConnection(handle, importServicesDao);
        }
    }

    public static int updateStaticThreshold(ServiceKpiThreshold thresholdBean, boolean isSystem) {
        ImportServicesDao importServicesDao = MySQLConnectionManager.getInstance().open(ImportServicesDao.class);
        try {
            if (isSystem) {
                return importServicesDao.updateSystemThreshold(thresholdBean);
            }
            return importServicesDao.updateThreshold(thresholdBean);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while updating static threshold. Reason: {}", e.getMessage(), e);
            return -1;
        } finally {
            MySQLConnectionManager.getInstance().close(importServicesDao);
        }
    }

    private static ImportServicesDao getImportServicesDao(Handle handle) {
        if (handle == null) {
            return MySQLConnectionManager.getInstance().open(ImportServicesDao.class);
        } else {
            return handle.attach(ImportServicesDao.class);
        }
    }

    private static void closeDaoConnection(Handle handle, ImportServicesDao dao) {
        if (handle == null) {
            MySQLConnectionManager.getInstance().close(dao);
        }
    }

    // for agent-config api - all threshold with applicable type service id and account id
    public static List<ServiceKpiThreshold> getServiceKpiThresholdsByServiceApplicableTo(int accountId, String applicableTo) {

        ImportServicesDao servicesDao = MySQLConnectionManager.getInstance().open(ImportServicesDao.class);
        try {
            List<ServiceKpiThreshold> list = servicesDao.getServiceKpiThresholdsByServiceApplicableTo(accountId, applicableTo);
            if (list != null)
                return list;
            return Collections.emptyList();
        } catch (
                Exception e) {
            LOGGER.error("Exception while getting all kpi threshold data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(servicesDao);
        }
        return Collections.emptyList();
    }
}
