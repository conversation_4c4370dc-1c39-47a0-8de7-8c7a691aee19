package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.ConstraintViolation;
import javax.validation.constraints.Size;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Data
@NoArgsConstructor
public class HTTPJsonProducerTypeDetail extends ProducerTypeDetail {

    private static final Logger LOGGER = LoggerFactory.getLogger(HTTPJsonProducerTypeDetail.class);

    @Size(min = 1, max = 256, message = "script name length must be less than or equal to 256")
    private String httpStatusUrl;

    public HTTPJsonProducerTypeDetail(int producerId, String createdTime, String updatedTime, String userDetailsId) {
        super(producerId, createdTime, updatedTime, userDetailsId);
    }

    @Override
    public Map<String, String> getDataMap() {
        Map<String, String> map = new HashMap<>();
        map.put("httpStatusUrl", httpStatusUrl);
        return map;
    }

    public boolean populate(Map<String, String> attributes) {
        this.setHttpStatusUrl(attributes.get(Constants.HTTP_JSON_TYPE_JSON_URL_ATTRIBUTE));
        return validate();
    }

    private boolean validate() {
        Set<ConstraintViolation<Object>> violations = ValidationUtils.validateFields(this);

        if( ! violations.isEmpty() ) {
            violations.forEach(it -> LOGGER.error(it.getMessage()));
            return false;
        }

        return true;
    }
}
