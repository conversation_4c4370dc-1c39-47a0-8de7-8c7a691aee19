package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.BatchProcessMappingBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProcessArgumentBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProcessDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProcessHostDetailsBean;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface BatchProcessDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id, name, identifier, account_id accountId, status , created_time createdTime, updated_time " +
            "updatedTime, user_details_id userDetailsId, batch_job_name batchName from process_details")
    List<ProcessDetailsBean> getAllBatchProcessDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id, name, identifier, account_id accountId, status , created_time createdTime, updated_time " +
            "updatedTime, user_details_id userDetailsId from process_details where account_id=:accountId and name=:processName")
    ProcessDetailsBean getBatchProcessDetailsByNameAndAccount(@Bind("accountId") int accountId, @Bind("processName") String processName);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id, name, identifier, account_id accountId, status , created_time createdTime, updated_time " +
            "updatedTime, user_details_id userDetailsId from process_details where identifier=:processIdentifier")
    ProcessDetailsBean getBatchProcessDetailsByIdentifier(@Bind("processIdentifier") String processIdentifier);

    @SqlUpdate("INSERT INTO `process_details`(name, identifier, account_id, status, created_time, updated_time, " +
            "user_details_id) VALUES (:name, :identifier, :accountId, :status , " +
            ":createdTime, :updatedTime, :userDetailsId)")
    @GetGeneratedKeys
    int addBatchProcessDetails(@BindBean ProcessDetailsBean processDetailsBean);

    @SqlBatch("INSERT INTO `batch_process_mapping`(process_id, batch_job_name, created_time, updated_time, user_details_id) VALUES " +
            "(:processId, :batchJobName, :createdTime, :updatedTime, :userDetailsId)")
    @GetGeneratedKeys
    int[] addProcessBatchMapping(@BindBean List<BatchProcessMappingBean> batchProcessMappings);

    @SqlUpdate("UPDATE `process_details` set name = ifnull(:name,name), identifier = ifnull(:identifier,identifier)," +
            " updated_time = :updatedTime where id = :id ")
    void updateBatchProcessDetails(@BindBean ProcessDetailsBean processDetailsBean);

    @SqlUpdate("INSERT INTO `process_host_details` (host_address, directory_path, process_details_id, status, " +
            "created_time, updated_time, user_details_id) VALUES (:hostAddress, :directoryPath, :processDetailsId, " +
            ":status , :createdTime, :updatedTime, :userDetailsId)")
    @GetGeneratedKeys
    int addBatchProcessHostDetails(@BindBean ProcessHostDetailsBean processHostDetailsBean);

    @SqlBatch("INSERT INTO `process_arguments` (process_host_details_id, process_details_id, status, created_time, updated_time, " +
            "user_details_id, name, value, default_value, `order`, is_placeholder) VALUES (:processHostDetailsId, :processDetailsId, :status, " +
            ":createdTime, :updatedTime, :userDetailsId, :name, :value, :defaultValue, :order, :isPlaceholder)")
    void addBatchProcessHostArguments(@BindBean List<ProcessArgumentBean> processArgumentBeans);

    @SqlBatch("UPDATE `process_arguments` set name = ifnull(:name,name), value = ifnull(:value,value), updated_time = " +
            ":updatedTime where id = :id ")
    void updateBatchProcessHostArguments(@BindBean List<ProcessArgumentBean> processArgumentBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, identifier, account_id accountId, status, created_time createdTime, updated_time updatedTime, " +
            "user_details_id userDetailsId from process_details where id=:processId and status = 1")
    ProcessDetailsBean getProcessDetailsWithId(@Bind("processId") int processId);

    @SqlUpdate("UPDATE `process_details` set status = 0 where id = :batchProcessId")
    void disableBatchProcess(@Bind("batchProcessId") Integer batchProcessId);

    @SqlUpdate("UPDATE `process_host_details` set status = 0 where process_details_id = :batchProcessId")
    void disableHostForBatchProcess(@Bind("batchProcessId") Integer batchProcessId);

    @SqlUpdate("UPDATE `process_arguments` set status = 0 where process_details_id = :batchProcessId")
    void disableHostArgsForBatchProcess(@Bind("batchProcessId") Integer batchProcessId);

    @SqlUpdate("DELETE from `process_details` where id = :batchProcessId")
    void deleteBatchProcessDetails(@Bind("batchProcessId") Integer batchProcessId);

    @SqlUpdate("DELETE from `process_host_details` where process_details_id = :batchProcessId")
    void deleteProcessHostDetails(@Bind("batchProcessId") Integer batchProcessId);

    @SqlUpdate("DELETE from `process_arguments` where process_details_id = :batchProcessId")
    void deleteProcessArguments(@Bind("batchProcessId") Integer batchProcessId);
}
