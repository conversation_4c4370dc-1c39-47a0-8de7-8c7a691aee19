package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SMSParameterBean {

    private int id;
    private String parameterName;
    private String parameterValue;
    private int parameterTypeId;
    private String userDetailsId;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private int isPlaceholder;
    private int smsDetailsId;
}
