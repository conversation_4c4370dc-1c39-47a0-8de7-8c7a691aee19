package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ComputedKpiToKpiMapping {

    private int computedKpiDetailsId;
    private int computedKpiId;
    private int baseKpiId;
    private int accountId;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
}
