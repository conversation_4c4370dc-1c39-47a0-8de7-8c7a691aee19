package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.DataCommunicationDetailsBean;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

public interface DataCommunicationDao {
    @SqlUpdate("insert into data_communication_details (name,protocol_id,host,port,status,created_time,updated_time,user_details_id,description,endpoint) values (:name,:protocolId,:host,:port,:status,:createdTime,:updatedTime,:userDetailsId,:description,:endpoint)")
    @GetGeneratedKeys
    public int addDataCommunicationDetails(@BindBean DataCommunicationDetailsBean dataCommunicationDetailsBean);

    //Fetch data communication details
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,protocol_id protocolId,host,port,status,created_time createdTime,updated_time updatedTime,user_details_id userDetailsId,description from data_communication_details where name = :name")
    public DataCommunicationDetailsBean getDataCommunicationDetails(@Bind("name") String name);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,protocol_id protocolId,host,port,status,created_time createdTime,updated_time updatedTime,user_details_id userDetailsId,description,endpoint, forensic_endpoint forensicEndpoint from data_communication_details where id = :id")
    public DataCommunicationDetailsBean getDataCommunicationDetailsById(@Bind("id") int id);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,protocol_id protocolId,host,port,status,created_time createdTime,updated_time updatedTime,user_details_id userDetailsId,description from data_communication_details where host = :host AND port = :port")
    DataCommunicationDetailsBean getDataCommunicationDetailsByHostNPort(@Bind("host") String hostAddress, @Bind("port") int port);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,protocol_id protocolId,host,port,status,created_time createdTime,updated_time updatedTime,user_details_id userDetailsId,description from data_communication_details where host = :host AND port = :port AND endpoint = :endpoint")
    DataCommunicationDetailsBean getDataCommunicationDetailsByHostNPort(@Bind("host") String hostAddress, @Bind("port") int port, String endpoint);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("update data_communication_details set name=ifnull(:name, name), protocol_id=ifnull(:protocolId, protocol_id), " +
            "host=ifnull(:host, host), port=ifnull(:port, port), status=:status, updated_time=:updatedTime, " +
            "user_details_id=:userDetailsId, description=ifnull(:description, description), endpoint=:endpoint where id = :id")
    int updateDataCommunicationDetails(@BindBean DataCommunicationDetailsBean dataCommunicationDetailsBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from data_communication_details where id = :id")
    int deleteDataCommunicationDetails(@Bind("id") int id);

}
