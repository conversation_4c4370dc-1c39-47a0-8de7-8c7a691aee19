package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.NotificationChoiceBean;
import com.appnomic.appsone.controlcenter.beans.UserAttributesBean;
import com.appnomic.appsone.controlcenter.beans.UserInfoBean;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserAccessDetails;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.UserAccessDao;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.UserDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.heal.configuration.pojos.SignalNotificationPreferences;
import com.heal.configuration.pojos.ForensicNotificationPreferences;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.OptInRequestPojo;
import com.heal.configuration.pojos.User;
import com.heal.configuration.pojos.UserTimezone;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

public class UserDataService extends AbstractDaoService<UserDao> {
    private final Logger LOGGER = LoggerFactory.getLogger(UserDataService.class);
    private final String ERROR_MESSAGE = "Exception while getting Users. {} ";

    public UserDataService() {
    }

    private final String ERROR_ADD_USER = "Error while adding user : user_attributes or user_access_details";
    private final String ERROR_UPDATE_USER = "Error while updating user : user_attributes or user_access_details";

    public String getSetup() {
        UserDao userDao = getDaoConnection(null, UserDao.class);
        try {
            return userDao.getSetup();
        } catch (Exception e) {
            LOGGER.error(ERROR_MESSAGE, e.getMessage(), e);
        } finally {
            closeDaoConnection(null, userDao);
        }
        return null;
    }

    public String getTemporaryPassword() {
        LOGGER.trace("Method Invoked : UserDataService/getTemporaryPassword");
        UserDao userDao = getDaoConnection(null, UserDao.class);

        try {
            return userDao.getTemporaryPassword();

        } catch (Exception e) {
            LOGGER.error("Unable to fetch default credentials.", e);
        } finally {
            closeDaoConnection(null, userDao);
        }
        return null;
    }

    public UserAttributesBean getUserAttributes(String userId) throws ControlCenterException {

        LOGGER.debug("Invoking get user attributes");
        UserDao userDao = getDaoConnection(null, UserDao.class);
        try {
            return userDao.getUserAttributes(userId);
        } catch (Exception e) {
            String ERROR_GET_USER_ATTRIBUTES = "Error in getting user attributes from DB";
            LOGGER.error(ERROR_GET_USER_ATTRIBUTES + ". Reason: {}", e.getMessage(), e);
            throw new ControlCenterException(ERROR_GET_USER_ATTRIBUTES);
        } finally {
            closeDaoConnection(null, userDao);
        }
    }

    public void deleteUserAttributesAndAccessDetails(String userId, Handle handle) throws ControlCenterException {
        UserDao userDao = null;
        try {
            LOGGER.debug("DELETE user attributes and user access details row");
            userDao = getDaoConnection(handle, UserDao.class);
            userDao.deleteUserFromUserIdentifier(userId);
            userDao.deleteUserFromUserAccessDetails(userId);
        } catch (Exception e) {
            String ERROR_DELETE_USER_ATTRIBUTES = "Error in deleting user_attributes or user_access_details";
            LOGGER.error(ERROR_DELETE_USER_ATTRIBUTES + ". Reason: {}", e.getMessage(), e);
            throw new ControlCenterException(ERROR_DELETE_USER_ATTRIBUTES);
        } finally {
            closeDaoConnection(handle, userDao);
        }
    }

    public int addUserAttributes(UserAttributesBean user, Handle handle) throws ControlCenterException {
        UserDao userDao = null;
        try {
            LOGGER.debug("Add user in user_attributes.");
            userDao = getDaoConnection(handle, UserDao.class);
            return userDao.addUserAttributes(user);
        } catch (Exception e) {
            LOGGER.error(ERROR_ADD_USER, e);
            throw new ControlCenterException(ERROR_ADD_USER);
        } finally {
            closeDaoConnection(handle, userDao);
        }
    }

    public void addUserAccessDetails(UserAccessDetails user, Handle handle) throws ControlCenterException {
        UserDao userDao = null;
        try {
            LOGGER.debug("Add user in user_access_details.");
            userDao = getDaoConnection(handle, UserDao.class);
            userDao.addUserAccessDetails(user);
        } catch (Exception e) {
            LOGGER.error(ERROR_ADD_USER, e);
            throw new ControlCenterException(ERROR_ADD_USER);
        } finally {
            closeDaoConnection(handle, userDao);
        }
    }

    public void updateUserAttributes(UserAttributesBean user, Handle handle) throws ControlCenterException {
        UserDao userDao = null;
        try {
            LOGGER.debug("Update user in user_attributes.");
            userDao = getDaoConnection(handle, UserDao.class);
            userDao.updateUserAttributes(user);
        } catch (Exception e) {
            LOGGER.error(ERROR_UPDATE_USER, e);
            throw new ControlCenterException(ERROR_UPDATE_USER);
        } finally {
            closeDaoConnection(handle, userDao);
        }
    }

    public void updateStatusForUser(String identifier, int status, String updatedTime, String userId, Handle handle) throws ControlCenterException {
        UserDao userDao = null;
        try {
            LOGGER.debug("Update user status in user_attributes.");
            userDao = getDaoConnection(handle, UserDao.class);
            userDao.updateStatusForUser(identifier, userId, status, updatedTime);
        } catch (Exception e) {
            LOGGER.error(ERROR_UPDATE_USER, e);
            throw new ControlCenterException(ERROR_UPDATE_USER);
        } finally {
            closeDaoConnection(handle, userDao);
        }
    }

    public void updateUserStatusToInactive(String userIdentifier, String superUserIdentifier, Handle handle) throws ControlCenterException {
        UserDao userDao = null;
        try {
            LOGGER.debug("Update user status in user_attributes.");
            userDao = getDaoConnection(handle, UserDao.class);
            userDao.updateUserStatus(userIdentifier, superUserIdentifier);
        } catch (Exception e) {
            LOGGER.error(ERROR_UPDATE_USER, e);
            throw new ControlCenterException(ERROR_UPDATE_USER);
        } finally {
            closeDaoConnection(handle, userDao);
        }
    }

    public void updateUserLastLoginTime(String userIdentifier, String lastLoginTime, Handle handle) throws ControlCenterException {
        UserDao userDao = null;
        try {
            LOGGER.debug("Update user last login time column in user_attributes.");
            userDao = getDaoConnection(handle, UserDao.class);
            userDao.updateUserLastLoginTime(userIdentifier, lastLoginTime);
        } catch (Exception e) {
            LOGGER.error(ERROR_UPDATE_USER, e);
            throw new ControlCenterException(ERROR_UPDATE_USER);
        } finally {
            closeDaoConnection(handle, userDao);
        }
    }

    public void updateUserAccessDetails(UserAccessDetails user, Handle handle) throws ControlCenterException {
        UserDao userDao = null;
        try {
            LOGGER.debug("Update user in user_access_details.");
            userDao = getDaoConnection(handle, UserDao.class);
            userDao.updateUserAccessDetails(user);
        } catch (Exception e) {
            LOGGER.error(ERROR_UPDATE_USER, e);
            throw new ControlCenterException(ERROR_UPDATE_USER);
        } finally {
            closeDaoConnection(handle, userDao);
        }
    }

    public List<UserDetailsBean> getUsers() {
        UserDao userDao = getDaoConnection(null, UserDao.class);
        try {
            List<UserDetailsBean> users = userDao.getUsers();
            if (users == null) {
                return Collections.emptyList();
            }
            return users;
        } catch (Exception e) {
            LOGGER.error(ERROR_MESSAGE, e.getMessage(), e);
        } finally {
            closeDaoConnection(null, userDao);
        }
        return Collections.emptyList();
    }

    public UserInfoBean getUserDetails(String userName) {
        UserDao userDao = getDaoConnection(null, UserDao.class);

        try {
            return userDao.getUserDetails(userName);
        } catch (Exception e) {
            LOGGER.error(ERROR_MESSAGE, e.getMessage(), e);
        } finally {
            closeDaoConnection(null, userDao);
        }

        return null;
    }

    public boolean adEditStatusKeycloak() throws ControlCenterException {
        UserDao userDao = null;
        try {
            userDao = getDaoConnection(null, UserDao.class);
            return userDao.getAdEditStatusKeycloak() > 0;
        } catch (Exception e) {
            LOGGER.error("Unable to fetch status : adEditStatusKeycloak.", e);
            throw new ControlCenterException("Unable to fetch status : adEditStatusKeycloak.");
        } finally {
            closeDaoConnection(null, userDao);
        }
    }


    // This method is provided for testing purposes only.
    public void updateSetup(String setup) {
        UserDao userDao = getDaoConnection(null, UserDao.class);
        try {
            userDao.updateSetup(setup);

        } catch (Exception e) {
            LOGGER.error("Unable to update the setup. {}", e.getMessage(), e);
        } finally {
            closeDaoConnection(null, userDao);
        }
    }

    public List<UserInfoBean> getNotificationUsers() {
        UserDao userDao = getDaoConnection(null, UserDao.class);

        try {
            return userDao.getNotificationUsers();
        } catch (Exception e) {
            LOGGER.error(ERROR_MESSAGE, e.getMessage(), e);
        } finally {
            closeDaoConnection(null, userDao);
        }

        return Collections.emptyList();
    }

    public NotificationChoiceBean getUserNotificationChoiceByIdentifier(String applicableUserId) {
        UserDao userDao = getDaoConnection(null, UserDao.class);
        try {
            return userDao.getUserNotificationChoice(applicableUserId);
        } catch (Exception e) {
            LOGGER.error("Error occurred in getUserNotificationChoice() applicableUserId: {}", applicableUserId);
        } finally {
            closeDaoConnection(null, userDao);
        }
        return null;
    }

    public List<UserInfoBean> getActiveUsers() {
        UserDao userDao = getDaoConnection(null, UserDao.class);

        try {
            return userDao.getActiveUsers();
        } catch (Exception e) {
            LOGGER.error(ERROR_MESSAGE, e.getMessage(), e);
        } finally {
            closeDaoConnection(null, userDao);
        }

        return Collections.emptyList();
    }

    public UserInfoBean getSuperAdmin() {
        UserDao userDao = getDaoConnection(null, UserDao.class);

        try {
            return userDao.getSuperAdmin();
        } catch (Exception e) {
            LOGGER.error(ERROR_MESSAGE, e.getMessage(), e);
        } finally {
            closeDaoConnection(null, userDao);
        }

        return null;
    }

    public UserAttributesBean getUserAttributesByContact(String contactNo) throws ControlCenterException {

        LOGGER.debug("Invoking get user attributes");
        UserDao userDao = getDaoConnection(null, UserDao.class);
        try {
            return userDao.getUserAttributesByContact(contactNo);
        } catch (Exception e) {
            String ERROR_GET_USER_ATTRIBUTES = "Error in getting user attributes from DB";
            LOGGER.error(ERROR_GET_USER_ATTRIBUTES + ". Reason: {}", e.getMessage(), e);
            throw new ControlCenterException(ERROR_GET_USER_ATTRIBUTES);
        } finally {
            closeDaoConnection(null, userDao);
        }
    }

    public void updateUserOptIn(OptInRequestPojo bean, Handle handle) {
        UserDao userDao = null;
        try {
            LOGGER.debug("Update whatsapp opt-in in user_attributes.");
            userDao = getDaoConnection(handle, UserDao.class);
            userDao.updateUserWhatsappOptIn(bean.getOptInStatus(), bean.getOptInLastRequestTimeStr(), bean.getUserAttributesBean().getUserIdentifier());
        } catch (Exception e) {
            LOGGER.error(ERROR_UPDATE_USER, e);
        } finally {
            closeDaoConnection(handle, userDao);
        }
    }

    public User getUserDetailsByUserId(int status, int userId, Handle handle) {
        UserDao userDao = null;
        try {
            LOGGER.debug("Get details of newly added user.");
            userDao = getDaoConnection(handle, UserDao.class);
            return userDao.getUserDetailsByUserId(status, userId);
        } catch (Exception e) {
            LOGGER.error("Error in getting user details from DB", e);
        } finally {
            closeDaoConnection(handle, userDao);
        }
        return null;
    }

    public List<SignalNotificationPreferences> getApplicationWiseUsersByUserIdentifier(List<Integer> applicationIds, int accountId, String applicableUserId, Handle handle) {
        UserDao userDao = null;
        try {
            LOGGER.debug("Getting details of application wise users.");
            userDao = getDaoConnection(handle, UserDao.class);
            return userDao.getApplicationWiseUsersByUserIdentifier(applicationIds, accountId, applicableUserId);
        } catch (Exception e) {
            LOGGER.error("Error in getting application wise users details from DB", e);
        } finally {
            closeDaoConnection(handle, userDao);
        }
        return null;
    }

    public List<ForensicNotificationPreferences> getForensicNotificationPreference(List<Integer> applicationIds, int accountId, String applicableUserId, Handle handle) {
        UserDao userDao = null;
        try {
            LOGGER.debug("Getting details of forensic notification preference.");
            userDao = getDaoConnection(handle, UserDao.class);
            return userDao.getForensicNotificationPreference(applicationIds, accountId, applicableUserId);
        } catch (Exception e) {
            LOGGER.error("Error in getting forensic notification preference from DB", e);
        } finally {
            closeDaoConnection(handle, userDao);
        }
        return null;
    }

    public UserTimezone getUserTimeZone(int userAttributeId, Handle handle) {
        UserDao userDao = null;
        try {
            LOGGER.debug("Getting details of user timeZone.");
            userDao = getDaoConnection(handle, UserDao.class);
            return userDao.getUserTimeZone(userAttributeId);
        } catch (Exception e) {
            LOGGER.error("Error in getting user timezone details from DB", e);
        } finally {
            closeDaoConnection(handle, userDao);
        }
        return null;
    }

    public User getUserDetailsByUserIdentifier(String userIdentifier, Handle handle) {
        UserDao userDao = null;
        try {
            LOGGER.debug("Get details of user.");
            userDao = getDaoConnection(handle, UserDao.class);
            return userDao.getUserDetailsByUserIdentifier(userIdentifier);
        } catch (Exception e) {
            LOGGER.error("Error in getting user details from DB", e);
        } finally {
            closeDaoConnection(handle, userDao);
        }
        return null;
    }

    public List<UserAttributesBean> getAllUser() {
        UserDao userDao = MySQLConnectionManager.getInstance().open(UserDao.class);
        try {
            return userDao.getAllUser();
        } catch (Exception e) {
            LOGGER.error("Error while getting all users", e);
        } finally {
            MySQLConnectionManager.getInstance().close(userDao);
        }
        return new ArrayList<>();
    }

}
