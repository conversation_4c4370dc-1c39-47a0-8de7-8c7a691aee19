package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.HttpPatternDataBean;
import com.appnomic.appsone.controlcenter.pojo.AddRulesPojo;
import com.appnomic.appsone.controlcenter.pojo.RulesHelperPojo;
import com.heal.configuration.pojos.PairData;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;
import org.skife.jdbi.v2.unstable.BindIn;

import java.util.List;

@UseStringTemplate3StatementLocator
public interface RulesDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, is_enabled monitorEnabled, `order`, rule_type_id ruleTypeId, is_default defaultRule from rules r where account_id = :accountId")
    List<RulesBean> getRulesByAccountId(@Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, initial_pattern initialPattern, last_pattern endPattern, length from tcp_patterns where rule_id = :ruleId")
    RegexTypeDetailBean getRegexTypeDetail(@Bind("ruleId") Integer ruleId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, first_uri_segments firstSegment, last_uri_segments lastSegment, complete_uri completeURI, payload_type_id payloadTypeId from http_patterns where rule_id = :ruleId")
    RequestTypeDetailBean getRequestTypeDetail(@Bind("ruleId") Integer ruleId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, pair_type_id pairTypeId, pair_key paramKey, pair_value paramValue from http_pair_data where rule_id = :ruleId and http_pattern_id = :httpPatternId")
    List<PairDataBean> getPairDataList(@Bind("ruleId") Integer ruleId, @Bind("httpPatternId") Integer httpPatternId);

    @SqlUpdate("INSERT INTO rules (account_id, is_enabled,`order`,name,created_time,updated_time,user_details_id,rule_type_id,is_default,discovery_status) VALUES (:accountId,:monitorEnabled,:order,:name,:createdTime, :updatedTime, :userDetails,:ruleTypeId,:isDefault,:discoveryEnabled)")
    @GetGeneratedKeys
    int addRulesDetails(@BindBean RulesBean rulesBean);

    @SqlUpdate("INSERT INTO `http_patterns`(`rule_id`,`account_id`,`http_method_type_id`,`first_uri_segments`,`last_uri_segments`,`complete_uri`,`payload_type_id`,`complete_pattern`, `created_time`, `updated_time`, `user_details_id`, `custom_segments`) VALUES (:ruleId,:accountId,:httpMethodType,:firstUriSegments,:lastUriSegments,:completeURI,:payloadType,:completePattern,:createdTime, :updatedTime, :userDetails,:customSegments)")
    @GetGeneratedKeys
    int addSegmentsDetails(@BindBean RequestTypeDetailBean requestTypeDetailBean);

    @SqlBatch("INSERT INTO `http_pair_data`(`rule_id`, `http_pattern_id`,`account_id`, `pair_type_id`,`pair_key`,`pair_value`, `created_time`, `updated_time`, `user_details_id`) VALUES (:ruleId,:httpPatternId,:accountId,:pairTypeId,:paramKey,:paramValue,:createdTime,:updatedTime,:userDetails)")
    @GetGeneratedKeys
    int[] addHttpPatternsDetails(@BindBean List<PairDataBean> pairDataBeanList);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, pair_type_id pairTypeId, pair_key paramKey, pair_value paramValue from http_pair_data where id in (<newlyAddedPairDataIds>)")
    List<PairDataBean> getHttpPairDataDetails(@BindIn("newlyAddedPairDataIds") List<Integer> newlyAddedPairDataIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select R.id id,R.name name,R.order,R.is_enabled status,R.is_default type, " +
              "R.rule_type_id ruleTypeId from tag_mapping TM join rules R on TM.object_id = R.id " +
              "where TM.object_ref_table = :ruleTable and " +
              "TM.tag_key= :serviceId and TM.tag_id= :tagId and TM.account_id = :accountId " +
              "order by `order` desc")
    List<RuleDetailsBean> getRuleList(@Bind("serviceId") Integer serviceId,@Bind("ruleTable") String ruleTable,@Bind("tagId") Integer tagId,@Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(1) from rules R,tag_mapping TM  where TM.object_id=R.id and TM.object_ref_table = :ruleTable and TM.tag_id= :tagId and lower(R.name)=:ruleName and R.account_id=:accountId and TM.tag_key=:serviceId")
    int getRuleName(@Bind("accountId") Integer accountId, @Bind("serviceId") Integer serviceId, @Bind("ruleTable") String ruleTable, @Bind("tagId") Integer tagId, @Bind("ruleName") String ruleName);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select HP.complete_pattern completePattern,R.name as ruleName,tag_key serviceId from http_patterns HP,rules R ,tag_mapping tm where R.id=HP.rule_id and R.id=tm.object_id and object_ref_table=:objectTable and HP.account_id=:accountId ")
    List<AddRulesPojo> getCompletePattern(@Bind("objectTable")String objectTable,@Bind("accountId") Integer accountId);

    @SqlBatch("UPDATE rules SET `order`=:order,updated_time=:updatedTime where id = :id and account_id=:accountId")
    void updateOrderDetails(@BindBean List<RulesBean> rulesBeansList);

    @SqlUpdate("INSERT INTO `http_patterns`(`rule_id`,`account_id`,`http_method_type_id`,`first_uri_segments`,`last_uri_segments`,`complete_uri`,`payload_type_id`,`complete_pattern`, `created_time`, `updated_time`, `user_details_id`, `custom_segments`) VALUES (:ruleId,:accountId,:httpMethodTypeId,:firstUriSegments,:lastUriSegments,:completeURI,:payloadTypeId,:completePattern,:createdTime, :updatedTime, :userDetails,:customSegments)")
    @GetGeneratedKeys
    int addHttpPatterns(@BindBean HttpPatternDataBean httpPatternDataBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT r.id id, r.name name, r.is_enabled enabled, r.`order` `order`, r.rule_type_id ruleTypeId, r.is_default isDefault, r.max_tags maxTags, r.discovery_status discoveryEnabled,\n" +
            "       tp.id tcpId, tp.initial_pattern tcpInitialPattern, tp.last_pattern tcpLastPattern, tp.length tcpLength,\n" +
            "       hp.id httpId, hp.first_uri_segments httpFirstUriSegments, hp.last_uri_segments httpLastUriSegments, hp.complete_uri httpCompleteURI, hp.payload_type_id httpPayloadTypeId, tm.tag_key concernedConfigId\n" +
            "FROM controller c, tag_mapping tm, rules r\n" +
            "LEFT JOIN tcp_patterns tp ON tp.rule_id = r.id\n" +
            "LEFT JOIN http_patterns hp ON hp.rule_id = r.id\n" +
            "WHERE tag_id = 1 AND object_ref_table = 'rules' AND r.id = tm.object_id AND c.id = tm.tag_key AND c.account_id =:accountId AND tm.tag_key=:serviceId")
    List<RulesHelperPojo> getRulesHelperPojo(@Bind("accountId") int accountId, @Bind("serviceId") int serviceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, pair_type_id, pair_key, pair_value from http_pair_data where rule_id=:id and http_pattern_id=:httpId")
    List<PairData> getDataBeans(@Bind("id")int id, @Bind("httpId")int httpId);
}
