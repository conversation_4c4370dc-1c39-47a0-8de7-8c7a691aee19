package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupKpiBean {
    private int id;
    private String name;
    private String identifier;
    private String description;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private int status;
    private String userId;
    private int accountId;
    private int kpiTypeId;
    private int discovery;
    private String regex;
    private int isCustom;
}
