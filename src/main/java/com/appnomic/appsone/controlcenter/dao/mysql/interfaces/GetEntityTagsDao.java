package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.heal.configuration.pojos.EntityTags;
import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface GetEntityTagsDao {
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select entity_name from entity_tag_mapping where entity_name = :entityName")
    String getValidEntityName(@Bind("entityName") String entityName);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select em.tag_id as id, td.name as name, em.is_mandatory as mandatory " +
            "from entity_tag_mapping em, tag_details td, mst_sub_type mst, mst_type mt " +
            "where mt.id = em.tag_id and mst.mst_type_id = em.tag_id and em.tag_id=td.id and em.entity_name = :entityName")
    List<EntityTags> getEntityTags(@Bind("entityName") String entityName);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mst.id id, mst.name name" +
            " from entity_tag_mapping em, tag_details td, mst_sub_type mst, mst_type mt" +
            " where mst.mst_type_id=em.tag_data_id and mst.mst_type_id = mt.id and mt.id = em.tag_data_id and em.tag_id=td.id and em.entity_name = :entityName")
    List<EntityTags.Values> getValuesForEntityTags(@Bind("entityName") String entityName);
}
