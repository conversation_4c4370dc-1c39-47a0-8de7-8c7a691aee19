package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompInstanceKPIDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.InstanceDetailsForKPI;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.KPIProducerDetailsBean;
import com.appnomic.appsone.controlcenter.pojo.CompInstClusterDetails;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.MetricGroupAttribute;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface MetricDetailsDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select comp_instance_id instanceId, status, mst_common_version_id commonVersionId, ci.name instanceName, " +
            "ci.identifier, host_id hostId, vcs.mst_component_id compId from component_cluster_mapping c join comp_instance " +
            "ci join view_cluster_services vcs where c.cluster_id = vcs.id and c.comp_instance_id = ci.id and ci.status = 1 " +
            "and vcs.service_id=:serviceId and ci.account_id=:accountId")
    List<CompInstClusterDetails> getCompInstanceDetailsForService(@Bind("serviceId") int serviceId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct k.id kpiId, k.name kpiName,k.kpi_type_id kpiTypeId, k.identifier kpiIdentifier, k.measure_units unit, k.data_type dataType, v.producer_id producerId, v.producer_name producerName, " +
            "g.name kpiGroup, g.id kpiGroupId, m.default_collection_interval collectionInterval, ifnull(g.discovery,-1) discovery from " +
            "view_producer_kpis v, mst_kpi_details k left join mst_kpi_group g on k.kpi_group_id = g.id, " +
           " mst_kpi_details s left join mst_sub_type st on s.kpi_type_id=st.id,"+
            "mst_component_version_kpi_mapping m  where v.is_default = 1 and v.mst_kpi_details_id = k.id and " +
            "m.mst_kpi_details_id = k.id and v.mst_component_id = :componentId  and v.account_id in (1, :accountId) " +
            "and k.is_computed = 0")
    List<KPIProducerDetailsBean> getKPIProducerDetailsForComponent(@Bind("componentId") int componentId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct m.producer_id id, p.name name, p.name identifier from mst_producer_kpi_mapping m left " +
            "join mst_producer p on m.producer_id = p.id where mst_kpi_details_id = :kpiId and mst_component_id = " +
            ":componentId and m.account_id in (1, :accountId)")
    List<IdPojo> getProducersForKPI(@Bind("componentId") int componentId, @Bind("kpiId") int kpiId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct c.id instanceKPIMappingId, c.comp_instance_id instanceId, ci.name instanceName, c.status status, c.mst_producer_id producerId, " +
            "c.mst_producer_kpi_mapping_id mstProducerKPIMappingId, 0 isGroup, p.name producerName, c.collection_interval " +
            "collectionInterval from comp_instance_kpi_details c join comp_instance ci on c.comp_instance_id = ci.id left " +
            "join mst_producer p on c.mst_producer_id = p.id where mst_kpi_details_id = :kpiId and mst_component_id = " +
            ":componentId and ci.account_id in (1, :accountId) and ci.status = 1")
    List<InstanceDetailsForKPI> getInstancesForComponentKPI(@Bind("componentId") int componentId, @Bind("kpiId") int kpiId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct c.id instanceKPIMappingId, c.comp_instance_id instanceId, ci.name instanceName, c.status status, c.mst_producer_id producerId, " +
            "c.mst_producer_kpi_mapping_id mstProducerKPIMappingId, 1 isGroup ,c.mst_kpi_group_id groupKpiId, p.name producerName, c.collection_interval " +
            "collectionInterval from comp_instance_kpi_group_details c join comp_instance ci on c.comp_instance_id = ci.id " +
            "left join mst_producer p on c.mst_producer_id = p.id where mst_kpi_details_id = :kpiId and mst_component_id = " +
            ":componentId and ci.account_id in (1, :accountId) and ci.status = 1")
    List<InstanceDetailsForKPI> getInstancesForComponentGroupKPI(@Bind("componentId") int componentId, @Bind("kpiId") int kpiId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct m.id mstProducerKPIMappingId from mst_producer_kpi_mapping m,mst_producer p,comp_instance c " +
            "where c.mst_component_version_id = m.mst_component_version_id and m.producer_id = p.id and p.id = :producerId " +
            "and mst_kpi_details_id = :kpiId and m.mst_component_id = :componentId and c.id = :instanceId and m.account_id in (1, :accountId)")
    int getMstProducerKPIMappingIdForCompInstance(@Bind("producerId") int producerId, @Bind("componentId") int componentId, @Bind("instanceId") int instanceId,
                                                  @Bind("kpiId") int kpiId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update comp_instance_kpi_details set mst_producer_id = :producerId, mst_producer_kpi_mapping_id = " +
            ":mstProducerKPIMappingId,  collection_interval = :collectionInterval, status = :status, updated_time = " +
            ":updatedTime, user_details_id = :userId where comp_instance_id = :instanceId and mst_kpi_details_id = :kpiId")
    void updateInstanceProducerMappingDetailsForNonGroupKPI(@BindBean List<CompInstanceKPIDetailsBean> list);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update comp_instance_kpi_group_details set mst_producer_id = :producerId, mst_producer_kpi_mapping_id = " +
            ":mstProducerKPIMappingId,  collection_interval = :collectionInterval, status = :status, updated_time = " +
            ":updatedTime, user_details_id = :userId where comp_instance_id = :instanceId and mst_kpi_details_id = :kpiId")
    void updateInstanceProducerMappingDetailsForGroupKPI(@BindBean List<CompInstanceKPIDetailsBean> list);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct p.mst_kpi_details_id kpiId, c.id instanceId, p.id mstProducerKPIMappingId, p.producer_id producerId, " +
            "m.default_collection_interval collectionInterval, ifNull(v.group_id,0) groupKpiId, ifNull(v.group_identifier,0) groupName, " +
            "ifNull(v.discovery,0) discovery from mst_producer_kpi_mapping p, mst_component_version_kpi_mapping m, comp_instance c, " +
            "view_all_kpis v where p.mst_kpi_details_id = v.kpiid and p.mst_kpi_details_id = m.mst_kpi_details_id and c.mst_component_version_id = " +
            "p.mst_component_version_id and p.is_default=1 and c.mst_component_id = p.mst_component_id and c.mst_component_id = :componentId and " +
            "p.mst_kpi_details_id = :kpiId and c.id = :instanceId and c.account_id in (1, :accountId)")
    CompInstanceKPIDetailsBean getDefaultCompInstanceKPIMappingDetails(@Bind("componentId") int componentId, @Bind("instanceId") int instanceId,
                                                                       @Bind("kpiId") int kpiId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO `comp_instance_kpi_details` (`comp_instance_id`, `mst_producer_kpi_mapping_id`, " +
            "`collection_interval`, `status`, `created_time`, `updated_time`, `user_details_id`, `mst_kpi_details_id`, " +
            "`mst_producer_id`, `notification`) VALUES (:instanceId, :mstProducerKPIMappingId, :collectionInterval, :status, " +
            ":createdTime, :updatedTime, :userId, :kpiId, :producerId, 1)")
    void addInstanceProducerMappingDetailsForNonGroupKPI(@BindBean List<CompInstanceKPIDetailsBean> list);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO `comp_instance_kpi_group_details` (`comp_instance_id`, `mst_producer_kpi_mapping_id`, `attribute_status`," +
            "`collection_interval`, `status`, `created_time`, `updated_time`, `user_details_id`, `mst_kpi_details_id`, " +
            "`mst_producer_id`, `notification`, `attribute_value`, `is_discovery`, `kpi_group_name`, `mst_kpi_group_id`, `alias_name`) " +
            "VALUES (:instanceId, :mstProducerKPIMappingId, :attributeStatus, :collectionInterval, :status, :createdTime, :updatedTime, " +
            ":userId, :kpiId, :producerId, 1, :attribute, :discovery, :groupName, :groupKpiId, :aliasName)")
    void addInstanceProducerMappingDetailsForGroupKPI(@BindBean List<CompInstanceKPIDetailsBean> list);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct g.id,g.name,g.identifier from mst_kpi_group g left join mst_kpi_details k on g.id = " +
            "k.kpi_group_id join mst_component_version_kpi_mapping m on m.mst_kpi_details_id = k.id where " +
            "g.discovery = 0 and m.mst_component_id = :componentId and g.account_id in (1, :accountId)")
    List<IdPojo> getNonDiscoveredKPIGroupsForComponentId(@Bind("componentId") int componentId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("Select distinct attribute_value value, alias_name aliasName, attribute_status status from comp_instance_kpi_group_details " +
            "where comp_instance_id = :instanceId and mst_kpi_group_id = :groupId")
    List<MetricGroupAttribute> getGroupAttributesForInstance(@Bind("instanceId") int instanceId, @Bind("groupId") int groupId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("update comp_instance_kpi_group_details set attribute_value = ifnull(:newAttribute,attribute_value), " +
            "alias_name = ifnull(:aliasName,alias_name), attribute_status = :status, updated_time = :timestamp, user_details_id = :userId " +
            "where comp_instance_id = :instanceId and mst_kpi_group_id = :groupId and attribute_value = :attribute")
    void updateGroupAttributeForInstances(@Bind("instanceId") int instanceId, @Bind("groupId") int groupId, @Bind("attribute")
            String attribute, @Bind("newAttribute") String newAttribute, @Bind("status") int status,
                                          @Bind("timestamp") String timestamp, @Bind("userId") String userId, @Bind("aliasName") String aliasName);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from comp_instance_kpi_group_details where comp_instance_id = :instanceId and mst_kpi_group_id =" +
            " :groupId and attribute_value = :attribute")
    void deleteGroupAttributeForInstances(@Bind("instanceId") int instanceId, @Bind("groupId") int groupId,
                                          @Bind("attribute") String attribute);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct k.id kpiId, k.name kpiName, v.producer_id producerId, v.producer_name producerName, " +
            "v.mst_producer_kpi_mapping_id mstKpiProducerMappingId, " +
            "g.name kpiGroup, m.default_collection_interval collectionInterval, ifnull(g.discovery,-1) discovery from " +
            "view_producer_kpis v, mst_kpi_details k left join mst_kpi_group g on k.kpi_group_id = g.id, " +
            "mst_component_version_kpi_mapping m, comp_instance c where v.is_default = 1 and v.mst_kpi_details_id = k.id and " +
            "m.mst_kpi_details_id = k.id and v.mst_component_id = :componentId  and v.account_id in (1, :accountId) " +
            "and k.kpi_group_id = :groupId and m.mst_component_id = :componentId and c.id = :instanceId " +
            "and c.mst_component_version_id = v.mst_component_version_id and m.mst_common_version_id = c.mst_common_version_id")
    List<KPIProducerDetailsBean> getKPIsForGroupAndForComponent(@Bind("componentId") int componentId, @Bind("instanceId") int instanceId,
                                                                @Bind("groupId") int groupId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct id from mst_kpi_details where kpi_group_id = :groupId and account_id in (1, :accountId)")
    List<Integer> getKpiIdsForGroup(@Bind("groupId") int groupId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct collection_interval from comp_instance_kpi_group_details where comp_instance_id = :instanceId and mst_kpi_group_id = :groupId")
    List<Integer> getCollectionIntervalForGroup(@Bind("instanceId") int instanceId, @Bind("groupId") int groupId);
}
