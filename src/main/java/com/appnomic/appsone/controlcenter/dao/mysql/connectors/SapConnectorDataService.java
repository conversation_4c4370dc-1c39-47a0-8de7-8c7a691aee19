package com.appnomic.appsone.controlcenter.dao.mysql.connectors;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.*;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.connectors.SapConnectorDataDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
@Slf4j
public class SapConnectorDataService{
    public SapConnectorDataService(){}

    public void updateSapKpi(SapKpi sapKpi)
    {
        SapConnectorDataDao sapConnectorDataDao = getMsqlDataDao();
        try {
            sapConnectorDataDao.updateSapKpis(sapKpi);
        } catch (Exception e) {
            log.error("Error occurred while updating sap KPIs{}", sapKpi, e);
        } finally {
            MySQLConnectionManager.getInstance().close(sapConnectorDataDao);
        }
    }

    public void updateDomainToHealKpiMapping(String healIdentifier, int srcId)
    {
        SapConnectorDataDao sapConnectorDataDao = getMsqlDataDao();
        try {
            sapConnectorDataDao.updateDomainToHeal(healIdentifier, srcId);
        } catch (Exception e) {
            log.error("Error occurred while updating  Domain to Heal mapping  - in Sap, healIdentifer :{}, src_id : {}, error :{}", healIdentifier, srcId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(sapConnectorDataDao);
        }
    }

    public void updateHealKpi(String kpiName, String newHealIdentifier, String oldHealIdentifier)
    {
        SapConnectorDataDao sapConnectorDataDao = getMsqlDataDao();
        try {
            sapConnectorDataDao.updateHealKpi(kpiName, newHealIdentifier, oldHealIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while updating heal_kpis - in Sap, healIdentifer :{}, error :{}", newHealIdentifier, e);
        } finally {
            MySQLConnectionManager.getInstance().close(sapConnectorDataDao);
        }
    }

    public int[] addSapConnectionDetails(List<SapConnectionBean> connectionBean) {
        SapConnectorDataDao sapConnectorDataDao = getMsqlDataDao();
        try {
            sapConnectorDataDao.deleteSapConnectionDetails();
            return sapConnectorDataDao.addSapConnectionDetails(connectionBean);
        } catch (Exception e) {
            log.error("Error occurred while fetching component details compId{}", connectionBean, e);
        } finally {
            MySQLConnectionManager.getInstance().close(sapConnectorDataDao);
        }
        return null;
    }

    public int[] addSapKpi(List<SapKpi> sapKpi){
        SapConnectorDataDao sapConnectorDataDao = getMsqlDataDao();
        try {
            return sapConnectorDataDao.addSapKpis(sapKpi);
        } catch (Exception e) {
            log.error("Error occurred while adding Sap Kpis : {}, error : {}", sapKpi, e);
        } finally {
            MySQLConnectionManager.getInstance().close(sapConnectorDataDao);
        }
        return null;
    }

    public List<SapKpi> getSapKpi(){
        SapConnectorDataDao sapConnectorDataDao = getMsqlDataDao();
        try {
            return sapConnectorDataDao.getSAPKpi();
        } catch (Exception e) {
            log.error("Error occurred while fetching SAP kpis : ", e);
        } finally {
            MySQLConnectionManager.getInstance().close(sapConnectorDataDao);
        }
        return Collections.emptyList();
    }

    public List<String> getHealKpis()
    {
        SapConnectorDataDao sapConnectorDataDao = getMsqlDataDao();
        try {
            return sapConnectorDataDao.getHealKpis();
        } catch (Exception e) {
            log.error("Error occurred while getting heal KPIs{}", (Object) e.getStackTrace());
        } finally {
            MySQLConnectionManager.getInstance().close(sapConnectorDataDao);
        }
        return new ArrayList<>();
    }

    public int[] addSapInstanceDetails(List<SapInstanceBean> sapInstanceBean){
        SapConnectorDataDao sapConnectorDataDao = getMsqlDataDao();
        try {
            sapConnectorDataDao.deleteSapInstanceDetails();
            return sapConnectorDataDao.addSapInstanceDetails(sapInstanceBean);
        } catch (Exception e) {
            log.error("Error occurred while fetching component details compId{}", sapInstanceBean, e);
        } finally {
            MySQLConnectionManager.getInstance().close(sapConnectorDataDao);
        }
        return null;
    }

    public int[] addSapInstanceFunctionalModuleMapping(List<SapInstanceFunctionalModuleMapping> sapInstanceFunctionalModuleMapping){
        SapConnectorDataDao sapConnectorDataDao = getMsqlDataDao();
        try {
            sapConnectorDataDao.deleteSapInstanceFunctionalModuleMapping();
            return sapConnectorDataDao.addSapInstanceFunctionalModuleMapping(sapInstanceFunctionalModuleMapping);
        } catch (Exception e) {
            log.error("Error occurred while fetching component details compId{}", sapConnectorDataDao, e);
        } finally {
            MySQLConnectionManager.getInstance().close(sapConnectorDataDao);
        }
        return null;
    }

    public int updateConnectorTemplateDataIdInAdapterConfigTable(Integer connectorId,Integer configId){
        SapConnectorDataDao sapConnectorDataDao = getMsqlDataDao();
        try {
            return sapConnectorDataDao.updateConnectorDataIdInAdapterChainConfig(connectorId,configId);
        } catch (Exception e) {
            log.error("Error occurred while getting the connectorTemplateId from {}", e.getMessage());
        } finally {
            MySQLConnectionManager.getInstance().close(sapConnectorDataDao);
        }
        return -1;
    }


    private SapConnectorDataDao getMsqlDataDao(){
        return MySQLConnectionManager.getInstance().open(SapConnectorDataDao.class);
    }
}
