package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;


import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.controlcenter.beans.ADKnownCompAttrBean;
import com.appnomic.appsone.controlcenter.beans.ComponentFilesDetailsBean;
import com.appnomic.appsone.controlcenter.beans.ComponentInstanceBean;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.*;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.Host;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AutoDiscoveryIgnoreBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.autodiscovery.DiscoveredAttributes;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.autodiscovery.HostWiseConnections;
import com.appnomic.appsone.controlcenter.pojo.AgentDetails;
import com.appnomic.appsone.controlcenter.pojo.InstanceComponentMappingDetails;
import com.appnomic.appsone.controlcenter.pojo.InstanceServiceApplicationDetailsPojo;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.AutoDiscoComponentMapping;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.HostProcessCountPojo;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;
import org.skife.jdbi.v2.unstable.BindIn;

import java.util.List;

@UseStringTemplate3StatementLocator
public interface AutoDiscoveryDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT host_identifier hostIdentifier, operating_system operatingSystem, serial_number operatingSystemVersion, hostname, platform, " +
            "last_updated_time lastUpdatedTime, environment, last_discovery_run_time lastDiscoveryRunTime, discovery_status discoveryStatus, " +
            "is_ignored isIgnored FROM autodisco_host")
    List<Host> getHostList();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT host_identifier hostIdentifier, operating_system operatingSystem, serial_number operatingSystemVersion, hostname, platform, " +
            "last_updated_time lastUpdatedTime, environment, last_discovery_run_time lastDiscoveryRunTime, discovery_status discoveryStatus, " +
            "is_ignored isIgnored FROM autodisco_host where is_ignored = 0")
    List<Host> getNotIgnoredHostList();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select ah.hostname instanceName, ah.host_identifier instanceIdentifier, ada.attribute_value hostAddress, ah.environment isDR," +
            " ah.discovery_status status, ah.last_discovery_run_time lastDiscoveryRunTime, vc.component_id componentId," +
            " ah.operating_system componentName, vc.component_version_id componentVersionId, ah.serial_number componentVersionName," +
            " vc.common_version_id commonVersionId, vc.common_version_name commonVersionName, vc.component_type_id componentTypeId," +
            " vc.component_type_name componentTypeName, vasm.service_id serviceId, vasm.service_name serviceName," +
            " asm.service_identifier serviceIdentifier, vasm.application_id applicationId, vasm.application_name applicationName," +
            " vasm.application_identifier applicationIdentifier from ((((autodisco_host ah join autodisco_discovered_attributes ada on" +
            " ah.host_identifier = ada.discovered_attributes_identifier and lower(ada.entity_type) = 'host' and" +
            " lower(ada.attribute_name) = 'hostaddress') left join view_components vc on vc.component_name = ah.operating_system and" +
            " vc.component_version_name = ah.serial_number) left join autodisco_service_mapping asm on" +
            " ah.host_identifier = asm.service_mapping_identifier and lower(asm.entity_type) = 'host') left join" +
            " view_application_service_mapping vasm on asm.service_identifier = vasm.service_identifier)" +
            " where ah.is_ignored = 0 and ah.account_id=0 and ah.discovery_status='DISCOVERED_NOT_ADDED_TO_SYSTEM'")
    List<InstanceServiceApplicationDetailsPojo> getHostInstanceServiceAppDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select ap.process_name instanceName, ap.process_identifier instanceIdentifier, ah.hostname hostName," +
            " ap.host_identifier hostIdentifier, ada.attribute_name attributeName, ada.attribute_value attributeValue, ah.environment isDR," +
            " ap.discovery_status status, ah.discovery_status hostStatus, ah.last_discovery_run_time lastDiscoveryRunTime, ap.component_id componentId," +
            " vc.component_name componentName, ap.component_version_id componentVersionId, vc.component_version_name componentVersionName," +
            " vc.common_version_id commonVersionId, vc.common_version_name commonVersionName, ap.component_type_id componentTypeId," +
            " vc.component_type_name componentTypeName, vasm.service_id serviceId, vasm.service_name serviceName," +
            " asm.service_identifier serviceIdentifier, vasm.application_id applicationId, vasm.application_name applicationName," +
            " vasm.application_identifier applicationIdentifier from (((((autodisco_process ap join autodisco_host ah on" +
            " ap.host_identifier = ah.host_identifier) left join autodisco_discovered_attributes ada on" +
            " ap.process_identifier = ada.discovered_attributes_identifier and lower(ada.entity_type) = 'compinstance') left join view_components vc on" +
            " vc.component_id = ap.component_id and vc.component_version_id = ap.component_version_id and" +
            " vc.component_type_id = ap.component_type_id) left join autodisco_service_mapping asm on" +
            " ap.process_identifier = asm.service_mapping_identifier and lower(asm.entity_type) = 'compinstance') left join" +
            " view_application_service_mapping vasm on asm.service_identifier = vasm.service_identifier) where ah.is_ignored = 0 and" +
            " ap.is_blacklisted = '0' and ap.component_id !=0 and ap.is_ignored = 0 and ap.account_id = 0")
    List<InstanceServiceApplicationDetailsPojo> getComponentInstanceServiceAppDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select ah.host_identifier hostIdentifier,count(distinct(ap.process_identifier)) countOfNewProcess from autodisco_host ah," +
            " autodisco_process ap where ah.host_identifier = ap.host_identifier and ah.is_ignored = 0 and" +
            " ah.discovery_status = 'ADDED_TO_SYSTEM' and ap.discovery_status = 'DISCOVERED_NOT_ADDED_TO_SYSTEM' and ap.is_blacklisted = '0'" +
            " and ap.component_id != 0 and ap.is_ignored = 0")
    List<HostProcessCountPojo> getNewProcessCount();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select h.host_identifier hostIdentifier, h.serial_number operatingSystemVersion, h.operating_system operatingSystem, " +
            "h.hostname hostname, h.platform platform, h.last_updated_time lastUpdatedTime, h.environment environment, h.last_discovery_run_time lastDiscoveryRunTime, h.discovery_status discoveryStatus " +
            "from autodisco_host h where host_identifier in (<hostIdentifiers>)")
    List<Host> getHostListByIdentifier(@BindIn("hostIdentifiers") List<String> hostIdentifiers);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT network_identifier networkInterfaceIdentifier, host_identifier hostIdentifier, interface_name interfaceName," +
            " ip_address interfaceIP, interface_type interfaceType,hardware_address hardwareAddress FROM autodisco_network_interfaces")
    List<NetworkInterface> getNetworkInterfacesList();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select local_ip hostAddress, count(distinct remote_ip) noOfConnections, group_concat(distinct remote_ip) listOfRemoteIps from autodisco_network_connection " +
            "where local_ip not in ('127.0.0.1', '::', '0.0.0.0') and remote_ip not in ('127.0.0.1', '::', '0.0.0.0') " +
            "and remote_ip not in (select host_address from comp_instance where is_cluster != 1 and host_id=0 and account_id in(:healHealthAccountId,:accountId)) group by local_ip")
    List<HostWiseConnections> getNumberOfConnectionsForHostIpAddress(@Bind("accountId") int accountId, @Bind("healHealthAccountId") int healHealthAccountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select host_identifier hostIdentifier, count(distinct remote_ip) noOfConnections, group_concat(distinct remote_ip) listOfRemoteIps from autodisco_network_connection " +
            "where local_ip not in ('127.0.0.1', '::', '0.0.0.0') and remote_ip not in ('127.0.0.1', '::', '0.0.0.0') " +
            "and remote_ip not in (select host_address from comp_instance where is_cluster != 1 and host_id=0 and account_id in(:healHealthAccountId,:accountId)) group by host_identifier")
    List<HostWiseConnections> getNumberOfConnectionsForHostIdentifier(@Bind("accountId") int accountId, @Bind("healHealthAccountId") int healHealthAccountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT network_identifier networkInterfaceIdentifier, host_identifier hostIdentifier, interface_name interfaceName," +
            " ip_address interfaceIP, interface_type interfaceType,hardware_address hardwareAddress FROM autodisco_network_interfaces where host_identifier = :hostIdentifier")
    List<NetworkInterface> getHostNetworkInterfacesByIdentifier(@Bind("hostIdentifier") String hostIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT process_identifier processIdentifier, host_identifier hostIdentifier, process_name processName, process_args processArgs," +
            " process_id pid, last_updated_time lastUpdatedTime, is_blacklisted isBlacklisted, component_id componentId," +
            " component_version_id componentVersionId, component_type_id componentTypeId, current_working_directory processCurrentWorkingDirectory," +
            " discovery_status discoveryStatus FROM autodisco_process")
    List<Process> getProcessList();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT process_identifier processIdentifier, host_identifier hostIdentifier, process_name processName, process_args processArgs," +
            " process_id pid, last_updated_time lastUpdatedTime, is_blacklisted isBlacklisted, component_id componentId," +
            " component_version_id componentVersion, component_type_id componentTypeId, current_working_directory processCurrentWorkingDirectory," +
            " discovery_status discoveryStatus FROM autodisco_process where component_id != 0 and is_ignored = 0 and host_identifier = :hostIdentifier")
    List<Process> getHostProcessByIdentifier(@Bind("hostIdentifier") String hostIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT service_mapping_identifier serviceMappingIdentifier, service_identifier serviceIdentifier, entity_type entityType " +
            "FROM autodisco_service_mapping")
    List<AutoDiscoveryServiceMapping> getServiceMappingList();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id, discovered_attributes_identifier discoveredAttributesIdentifier, entity_type entityType, attribute_name attributeName," +
            " attribute_value attributeValue FROM autodisco_discovered_attributes")
    List<AutoDiscoveryDiscoveredAttributes> getDiscoveredAttributesList();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select ah.hostname hostName, ah.host_identifier hostIdentifier, ap.process_name compInstanceName," +
            " ap.process_identifier compInstanceIdentifier, ap.component_id componentId, vc.component_name componentName," +
            " ap.component_version_id componentVersionId, vc.component_version_name componentVersionName, vc.common_version_id commonVersionId," +
            " vc.common_version_name commonVersionName, ap.component_type_id componentTypeId, vc.component_type_name componentTypeName," +
            " ada.attribute_name attributeName, ada.attribute_value attributeValue from autodisco_host ah, autodisco_process ap," +
            " view_components vc, autodisco_discovered_attributes ada where ah.is_ignored = 0 and ah.host_identifier = ap.host_identifier" +
            " and ap.component_id = vc.component_id and ap.process_identifier = ada.discovered_attributes_identifier and" +
            " ada.entity_type = 'CompInstance' and ap.component_version_id = vc.component_version_id and ap.component_type_id = vc.component_type_id and ap.is_ignored = 0 and ah.account_id=0")
    List<InstanceComponentMappingDetails> getInstCompAttributeMapping();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id id, discovered_attributes_identifier discoveredAttributesIdentifier, attribute_name attributeName, " +
            "attribute_value attributeValue, entity_type entityValue from autodisco_discovered_attributes where " +
            "discovered_attributes_identifier = :discoveredAttributesIdentifier")
    List<DiscoveredAttributes> getDiscoveredAttributes(@Bind("discoveredAttributesIdentifier") String discoveredAttributesIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, source_identifier as sourceIdentifier, destination_identifier as destinationIdentifier, last_updated_time as lastUpdatedTime," +
            " is_discovery as isDiscovery, discovery_status as discoveryStatus from autodisco_discovered_connections")
    List<AutoDiscoveryDiscoveredConnections> getDiscoveredConnectionsList();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select adc.id, adc.source_identifier as sourceIdentifier, adc.destination_identifier as destinationIdentifier, ah.last_discovery_run_time" +
            " as lastUpdatedTime, adc.is_discovery as isDiscovery, adc.discovery_status as discoveryStatus from autodisco_discovered_connections adc," +
            " autodisco_host ah where adc.host_identifier = ah.host_identifier and ah.is_ignored = 0")
    List<AutoDiscoveryDiscoveredConnections> getUsefulDiscoveredConnections();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, source_identifier as sourceIdentifier, destination_identifier as destinationIdentifier, last_updated_time as lastUpdatedTime," +
            " is_discovery as isDiscovery, discovery_status as discoveryStatus, host_identifier hostIdentifier from autodisco_discovered_connections where host_identifier = :hostIdentifier")
    List<AutoDiscoveryDiscoveredConnections> getDiscoveredConnectionsListByHost(@Bind("hostIdentifier") String hostIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO `autodisco_service_mapping` (service_mapping_identifier, service_identifier, entity_type) " +
            "VALUES (:serviceMappingIdentifier, :serviceIdentifier, :entityType)")
    void addServiceMapping(@BindBean List<AutoDiscoveryServiceMapping> autoDiscoveryEntityBeanList);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("UPDATE autodisco_host set discovery_status = :discoveryStatus, is_ignored = :isIgnored, ignored_by = :ignoredBy, last_updated_time = :lastUpdatedTime " +
            "WHERE host_identifier = :identifier")
    void setHostIsIgnore(@BindBean List<AutoDiscoveryIgnoreBean> hostIDsList);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("UPDATE autodisco_process set discovery_status = :discoveryStatus, is_ignored = :isIgnored, ignored_by = :ignoredBy, last_updated_time = :lastUpdatedTime " +
            "WHERE process_identifier = :identifier")
    void setProcessIsIgnore(@BindBean List<AutoDiscoveryIgnoreBean> processIDsList);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO `autodisco_discovered_connections` (source_identifier, host_identifier, destination_identifier, " +
            "last_updated_time, is_discovery) VALUES (:sourceIdentifier, :hostIdentifier, :destinationIdentifier, :lastUpdatedTime, :isDiscovery)")
    void addDiscoveredConnections(@BindBean List<AutoDiscoveryDiscoveredConnections> discoveredConnectionsPojo);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT connection_identifier connectionIdentifier, host_identifier hostIdentifier, direction, local_ip localIp, local_port localPort, " +
            "remote_ip remoteIp, remote_port remotePort, last_updated_time lastUpdatedTime FROM autodisco_network_connection")
    List<Connection> getNetworkConnections();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT connection_identifier connectionIdentifier, host_identifier hostIdentifier, direction, local_ip localIp, local_port localPort, " +
            "remote_ip remoteIp, remote_port remotePort, last_updated_time lastUpdatedTime FROM autodisco_network_connection where host_identifier = :hostIdentifier")
    List<Connection> getHostNetworkConnectionsByIdentifier(@Bind("hostIdentifier") String hostIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT ip ipAddress, port portNo, host_identifier hostIdentifier FROM autodisco_endpoint")
    List<Endpoint> getEndpoints();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT ip ipAddress, port portNo, host_identifier hostIdentifier FROM autodisco_endpoint where host_identifier = :hostIdentifier")
    List<Endpoint> getHostEndpointByIdentifier(@Bind("hostIdentifier") String hostIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT hostname name, adh.host_identifier identifier, ip_address hostAddress, last_discovery_run_time lastDiscoveryRunTime, " +
            "last_updated_time lastUpdatedTime, ignored_by ignoredBy FROM autodisco_host adh " +
            "JOIN autodisco_network_interfaces adni ON adni.host_identifier=adh.host_identifier " +
            "WHERE is_ignored = 1 ORDER BY adh.host_identifier")
    List<AutoDiscoveryIgnoredEntities> getIgnoredHosts();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT adp.process_name name, adp.process_identifier identifier, adh.last_discovery_run_time lastDiscoveryRunTime, " +
            "adp.last_updated_time lastUpdatedTime, adp.ignored_by ignoredBy " +
            "FROM autodisco_process adp JOIN autodisco_host adh on adh.host_identifier = adp.host_identifier " +
            "WHERE adp.is_ignored = 1 and adp.component_id != 0 and adp.is_blacklisted = '0'")
    List<AutoDiscoveryIgnoredEntities> getIgnoredProcesses();

    // When an entity is mapped to a new service
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("DELETE FROM autodisco_service_mapping " +
            "WHERE service_mapping_identifier = :validEntity AND service_identifier NOT IN (<mapping>);")
    void deleteHostMapKeepCompMappings(@Bind("validEntity") String validEntity, @BindIn("mapping") List<String> existingCompMappings);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("DELETE FROM autodisco_service_mapping " +
            "WHERE service_mapping_identifier = :validEntity;")
    void deleteEntityService(@Bind("validEntity") String validEntity);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("DELETE FROM autodisco_service_mapping " +
            "WHERE service_mapping_identifier = :hostIdentifier AND service_identifier = :mapping LIMIT 1;")
    void deleteHostMapToCompService(@Bind("hostIdentifier") String hostIdentifier, @Bind("mapping") String mappings);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("DELETE FROM autodisco_discovered_connections WHERE host_identifier=:validEntity")
    void deleteDiscoveredConnections(@Bind("validEntity") String validEntity);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT DISTINCT mc.id id, mc.name name, mcadm.mst_component_type_id mstComponentTypeId, mcadm.path relativePath, mc.discovery_pattern discoveryPattern, mca.attribute_name attributeName, is_mandatory isMandatory, mcadm.method, mcadm.value, mcadm.priority " +
            "FROM (mst_component_attribute_auto_discovery_mapping mcadm JOIN mst_component mc on mc.id = mcadm.mst_component_id) " +
            "JOIN mst_common_attributes mca on mcadm.mst_common_attributes_id = mca.id " +
            "WHERE (mst_component_type_id = 1 or mst_component_type_id = 2 or mst_component_type_id = 3 or mst_component_type_id = 4 or mst_component_type_id = 5 or mst_component_type_id = 7) and path is not null and discovery_pattern is not null;")
    List<ADKnownCompAttrBean> getADComponentAttributeDetail();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id id, mst_component_id mstComponentId, mst_component_version_id mstComponentVersionId, mst_kpi_details_id mstKpiDetailsId, file_name fileName, relative_path relativePath, user_details_id userDetailsId, created_time createdTime, updated_time updatedTime, kpi_type_id kpiTypeId " +
            "FROM mst_component_files " +
            "WHERE (file_name is not null or relative_path is not null)")
    List<ComponentFilesDetailsBean> getConfigurationEntities();


    /*Push Discovery Data*/

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("INSERT INTO autodisco_host (host_identifier, serial_number, operating_system, hostname, platform, last_updated_time, last_discovery_run_time, " +
            "environment, discovery_status) VALUES (:hostIdentifier, :operatingSystemVersion, :operatingSystem, :hostname, :platform, :lastUpdatedTime, " +
            ":lastDiscoveryRunTime, :environment, :discoveryStatus) ON DUPLICATE KEY UPDATE host_identifier = :hostIdentifier, serial_number = :operatingSystemVersion, " +
            "operating_system = :operatingSystem, hostname = :hostname, platform = :platform, last_updated_time = :lastUpdatedTime, last_discovery_run_time = :lastDiscoveryRunTime, " +
            "environment = :environment, discovery_status = :discoveryStatus")
    void pushDiscoveredHosts(@BindBean Host host);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO autodisco_endpoint (endpoint_identifier, ip, port, host_identifier) VALUES (:endpointIdentifier, :ipAddress, :portNo, :hostIdentifier) ON DUPLICATE KEY UPDATE " +
            "endpoint_identifier = :endpointIdentifier, ip = :ipAddress, port = :portNo, host_identifier = :hostIdentifier")
    void pushDiscoveredEndpoints(@BindBean List<Endpoint> endpoints);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO autodisco_network_connection (connection_identifier, connection_type, direction, local_ip, local_port, remote_ip, remote_port, " +
            "host_identifier) VALUES (:connectionIdentifier, :connectionType, :direction, :localIP, :localPort, :remoteIP, :remotePort, :hostIdentifier) ON DUPLICATE KEY UPDATE " +
            "connection_identifier = :connectionIdentifier, connection_type = :connectionType, direction = :direction, local_ip = :localIP, local_port = :localPort, " +
            "remote_ip = :remoteIP, remote_port = :remotePort, host_identifier = :hostIdentifier")
    void pushDiscoveredConnections(@BindBean List<Connection> endpoints);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO autodisco_process (process_identifier, host_identifier, process_name, process_args, process_id, " +
            "component_id, component_version_id, component_type_id, current_working_directory) " +
            "VALUES (:processIdentifier, :hostIdentifier, :processName, :processArgs, :pid, :componentId, :componentVersion, " +
            ":componentTypeId, :processCurrentWorkingDirectory) ON DUPLICATE KEY UPDATE process_identifier = :processIdentifier, host_identifier = :hostIdentifier, process_name = :processName, process_args = :processArgs, " +
            "process_id = :pid, component_id = :componentId, component_version_id = :componentVersion, component_type_id = :componentTypeId, current_working_directory = :processCurrentWorkingDirectory")
    void pushDiscoveredProcesses(@BindBean List<Process> endpoints);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO autodisco_network_interfaces (network_identifier, host_identifier, interface_name, interface_type, ip_address, " +
            "hardware_address) VALUES (:networkInterfaceIdentifier, :hostIdentifier, :interfaceName, :interfaceType, :interfaceIP, :hardwareAddress) ON DUPLICATE KEY UPDATE " +
            "network_identifier = :networkInterfaceIdentifier, host_identifier = :hostIdentifier, interface_name = :interfaceName, interface_type = :interfaceType, ip_address = :interfaceIP, hardware_address = :hardwareAddress")
    void pushDiscoveredNetworkInterface(@BindBean List<NetworkInterface> network);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO autodisco_mount_points (mountPoint_identifier, host_identifier, dir_name, dev_name, type_name)" +
            " VALUES (:mountPointIdentifier, :hostIdentifier, :dirName, :devName, :typeName) ON DUPLICATE KEY UPDATE mountPoint_identifier = :mountPointIdentifier, host_identifier = :hostIdentifier, " +
            "dir_name = :dirName, dev_name = :devName, type_name = :typeName")
    void pushDiscoveredMountPoint(@BindBean List<MountPoint> mountPoints);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO autodisco_discovered_attributes (id, discovered_attributes_identifier, attribute_name, attribute_value, entity_type)" +
            " VALUES (:id, :discoveredAttributesIdentifier, :attributeName, :attributeValue, :entityType)")
    void pushDiscoveredAttributes(@BindBean List<AutoDiscoveryDiscoveredAttributes> attributes);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select host_identifier hostIdentifier, process_identifier processIdentifier, kpi_type kpiType, entity_path entityPath" +
            " from autodisco_configuration_entities where host_identifier = :hostIdentifier")
    List<AutoDiscoveredConfigurationEntities> getDiscoveredConfigurationEntities(@Bind("hostIdentifier") String hostIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO autodisco_configuration_entities (host_identifier, process_identifier, kpi_type, entity_path)" +
            " VALUES (:hostIdentifier, :processIdentifier, :kpiType, :entityPath)")
    void pushDiscoveredConfigurationEntities(@BindBean List<AutoDiscoveredConfigurationEntities> configurationEntities);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO autodisco_pre_requisites (host_identifier, prerequisite_identifier, name, value, name_type, value_type)" +
            " VALUES (:hostIdentifier, :preRequisiteIdentifier, :name, :value, :nameType, :valueType)")
    void pushDiscoveredPreRequisites(@BindBean List<AutoDiscoveredPreRequisites> discoveredPreRequisitesList);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO autodisco_port_availability (host_identifier, prerequisite_identifier, server_name, port_number, address, available)" +
            " VALUES (:hostIdentifier, :preRequisiteIdentifier, :serverName, :portNumber, :address, :available)")
    void pushDiscoveredPortAvailability(@BindBean List<AutoDiscoveredPortAvailability> portAvailabilityList);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("delete from autodisco_discovered_attributes where id = :id")
    void deleteDiscoveredAttributes(@Bind("id") List<Integer> id);

    /*Push Discovery Data END*/

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct service_mapping_identifier serviceMappingIdentifier, service_identifier serviceIdentifier, entity_type entityType " +
            "from autodisco_service_mapping where service_mapping_identifier = :serviceMappingIdentifier")
    List<AutoDiscoveryServiceMapping> getEntityServiceMapping(@Bind("serviceMappingIdentifier") String serviceMappingIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mountPoint_identifier mountPointIdentifier, host_identifier hostIdentifier, dir_name dirName, dev_name devName, type_name typeName " +
            "from autodisco_mount_points where host_identifier = :hostIdentifier")
    List<MountPoint> getHostMountPointByIdentifier(@Bind("hostIdentifier") String hostIdentifier);

    // set last updated time
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("UPDATE autodisco_host set last_updated_time = :lastUpdatedTime WHERE host_identifier = :serviceMappingIdentifier")
    void setHostLastUpdatedTime(@BindBean List<AutoDiscoveryServiceMapping> mappings);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("UPDATE autodisco_process set last_updated_time = :lastUpdatedTime WHERE host_identifier = :serviceMappingIdentifier")
    void setCompInstanceLastUpdateTime(@BindBean List<AutoDiscoveryServiceMapping> mappings);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("UPDATE autodisco_network_connection set last_updated_time = :lastUpdatedTime WHERE host_identifier = :serviceMappingIdentifier")
    void setConnLastUpdatedTime(@BindBean List<AutoDiscoveryServiceMapping> mappings);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE autodisco_host set last_updated_time = :lastUpdatedTime, discovery_status = :discoveryStatus, account_id = :accountId WHERE host_identifier = :hostIdentifier")
    void setHostDiscoveryStatusTimeAccount(@Bind("lastUpdatedTime") String lastUpdatedTime, @Bind("discoveryStatus") String discoveryStatus, @Bind("hostIdentifier") String hostIdentifier, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE autodisco_process set last_updated_time = :lastUpdatedTime, discovery_status = :discoveryStatus, account_id = :accountId WHERE process_identifier = :processIdentifier")
    void setProcessDiscoveryStatusTimeAccount(@Bind("lastUpdatedTime") String lastUpdatedTime, @Bind("discoveryStatus") String discoveryStatus, @Bind("processIdentifier") String processIdentifier, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("UPDATE autodisco_host SET environment=:env WHERE host_identifier=:identifier")
    void setEnvironmentOfHost(@Bind("identifier") List<String> validEntities, @Bind("env") int environment);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,status,host_id  hostId,is_dr  isDR,is_cluster  isCluster,mst_component_version_id  mstComponentVersionId,created_time  createdTime," +
            "updated_time  updatedTime,user_details_id  userDetailsId,account_id  accountId,mst_component_id  mstComponentId,mst_component_type_id mstComponentTypeId,discovery," +
            "host_address  hostAddress,identifier,mst_common_version_id  mstCommonVersionId from comp_instance where host_address = :hostAddress and mst_component_type_id = :mstComponentTypeId and is_cluster = :isCluster")
    ComponentInstanceBean getCompInstForAccountByHostId(@Bind("hostAddress") String hostAddress, @Bind("mstComponentTypeId") int mstComponentTypeId, @Bind("isCluster") int isCluster);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select agent_identifier agentIdentifier, agent_name agentName, agent_type agentTypeName, host_identifier hostIdentifier, instance_identifier instanceIdentifier from autodisco_agents")
    List<AgentDetails> getAllAgentDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into autodisco_agents (agent_identifier, agent_name, agent_type, host_identifier, instance_identifier) values (:agentIdentifier, :agentName, :agentTypeName, :hostIdentifier, :instanceIdentifier)")
    void pushDiscoveredAgent(@BindBean List<AgentDetails> agents);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select agent_identifier agentIdentifier, agent_name agentName, agent_type agentTypeName,host_identifier hostIdentifier, instance_identifier instanceIdentifier from autodisco_agents where host_identifier = :hostIdentifier")
    List<AgentDetails> getAgentDetailsForHostIdentifier(@Bind("hostIdentifier") String hostIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT ah.host_identifier hostIdentifier, ah.operating_system operatingSystem,  ah.hostname, ah.platform, " +
            "ah.last_updated_time lastUpdatedTime,ah.environment FROM autodisco_host ah join autodisco_discovered_attributes ada on " +
            "ah.host_identifier = ada.discovered_attributes_identifier where  ada.attribute_value= :hostAddress")
    List<Host> getHostListByHostIdentifier(@Bind("hostAddress")String hostAddress);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mst_component_id componentId, name name,value value from auto_disco_component_mapping where status = 1")
    List<AutoDiscoComponentMapping> getAutoDiscoComponentMapping();
}
