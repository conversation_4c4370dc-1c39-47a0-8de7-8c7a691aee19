package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.PluginKPIServiceMapping;
import com.appnomic.appsone.controlcenter.pojo.ApplicationWhitelist;
import com.appnomic.appsone.controlcenter.pojo.ServiceWhitelist;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

@UseStringTemplate3StatementLocator
public interface WhitelistDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT value FROM a1_installation_attributes WHERE name='WhitelistActive'")
    boolean isWhitelistActive();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE a1_installation_attributes SET value=:active WHERE name='WhitelistActive'")
    int updateWhitelistActive(@Bind("active") boolean active);

    @SqlBatch("UPDATE controller SET plugin_whitelist_status=true, plugin_supr_interval=:applicationSuppressionInterval WHERE name=:applicationName")
    @GetGeneratedKeys
    int[] updateApplicationWhitelist(@BindBean List<ApplicationWhitelist> whitelistBeans);

    @SqlBatch("UPDATE controller SET plugin_whitelist_status=false WHERE name=:applicationName")
    int [] deleteApplicationWhitelist(@BindBean List<ApplicationWhitelist> whitelistBeans);

    @SqlBatch("UPDATE controller SET plugin_whitelist_status=true, plugin_supr_interval=:serviceSuppressionInterval WHERE name=:serviceName")
    @GetGeneratedKeys
    int[] updateServiceWhitelist(@BindBean List<ServiceWhitelist> whitelistBeans);

    @SqlBatch("UPDATE controller SET plugin_whitelist_status=false WHERE name=:serviceName")
    int [] deleteServiceWhitelist(@BindBean List<ServiceWhitelist> whitelistBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT * from plugin_kpi_service_mapping WHERE kpi_id=:kpiId AND service_id=:serviceId")
    PluginKPIServiceMapping getPluginKPIServiceMapping(@Bind("kpiId") Integer kpiId, @Bind("serviceId")Integer serviceId);

    @SqlBatch("INSERT INTO plugin_kpi_service_mapping (kpi_id, service_id, plugin_whitelist_status, plugin_supr_interval) VALUES (:mapping.kpiId, :mapping.serviceId, '1', '0')")
    @GetGeneratedKeys
    int[] addKPIWhitelist(@BindBean("mapping") List<PluginKPIServiceMapping> kpiServiceList);

    @SqlBatch("UPDATE plugin_kpi_service_mapping SET plugin_whitelist_status=true WHERE kpi_id=:kpiId AND service_id=:serviceId")
    @GetGeneratedKeys
    int[] updateKPIWhitelist(@BindBean List<PluginKPIServiceMapping> kpiServiceList);

    @SqlBatch("UPDATE plugin_kpi_service_mapping SET plugin_whitelist_status=false WHERE kpi_id=:kpiId AND service_id=:serviceId")
    int [] deleteKPIWhitelist(@BindBean List<PluginKPIServiceMapping> kpiServiceList);
}