package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.NotificationSettingsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.NotificationSettingsDao;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.NotificationSettings;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR> 22/05/2020
 */

public class NotificationSettingsDataService {

    private NotificationSettingsDataService() {
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationSettingsDataService.class);

    public static List<NotificationSettingsBean> getNotificationSetting(int accountId) {

        NotificationSettingsDao notificationSettingsDao = openConnection(null);
        try {
            return notificationSettingsDao.getNotificationSettings(accountId);
        }catch (Exception e){
            LOGGER.error("Error while fetching notification settings {}" , e.getMessage(), e);
            throw e;
        } finally {
            closeConnection(null, notificationSettingsDao);
        }
    }

    public static int[] updateNotificationSetting(List<NotificationSettings> settingsList) {

        NotificationSettingsDao notificationSettingsDao = openConnection(null);
        try {
            return notificationSettingsDao.updateNotificationSettings(settingsList);
        }catch (Exception e){
            LOGGER.error("Error while fetching notification settings {}" , e.getMessage(), e);
            throw e;
        } finally {
            closeConnection(null, notificationSettingsDao);
        }
    }

    public static int[] addNotificationSettings(List<NotificationSettings> settingsList) {

        NotificationSettingsDao notificationSettingsDao = openConnection(null);
        try {
            return notificationSettingsDao.addNotificationSettings(settingsList);
        }catch (Exception e){
            LOGGER.error("Error adding notification settings {}" , e.getMessage(), e);
            throw e;
        } finally {
            closeConnection(null, notificationSettingsDao);
        }
    }

    public static NotificationSettingsDao openConnection(Handle handle) {
        if( handle == null ) {
            return MySQLConnectionManager.getInstance().getHandle().open(NotificationSettingsDao.class);
        } else {
            return handle.attach(NotificationSettingsDao.class);
        }
    }

    private static void closeConnection(Handle handle, NotificationSettingsDao dao) {
        if (handle == null) {
            MySQLConnectionManager.getInstance().close(dao);
        }
    }

    public static void updateApplicationNotificationSettings(List<NotificationSettings> settingsList) {

        NotificationSettingsDao notificationSettingsDao = openConnection(null);
        try {
             notificationSettingsDao.updateApplicationNotificationSettings(settingsList);
        }catch (Exception e){
            LOGGER.error("Error adding notification settings {}" , e.getMessage(), e);
            throw e;
        } finally {
            closeConnection(null, notificationSettingsDao);
        }
    }
}
