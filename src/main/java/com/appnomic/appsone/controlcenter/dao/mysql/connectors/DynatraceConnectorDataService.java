package com.appnomic.appsone.controlcenter.dao.mysql.connectors;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.DynatraceEntityBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.DynatraceEntityMetricMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.DynatraceMetricBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.connectors.DynatraceConnectorDataDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import lombok.extern.slf4j.Slf4j;


import java.util.ArrayList;
import java.util.List;

@Slf4j
public class DynatraceConnectorDataService {
    public DynatraceConnectorDataService(){}

    private DynatraceConnectorDataDao getMsqlDataDao(){
        return MySQLConnectionManager.getInstance().open(DynatraceConnectorDataDao.class);
    }

    public int[] addDynatraceEntity(List<DynatraceEntityBean> entityBeans){
        DynatraceConnectorDataDao connectorDataDao = getMsqlDataDao();
        try {
            connectorDataDao.deleteDynatraceEntities();
            return connectorDataDao.addDynatraceEntitys(entityBeans);
        } catch (Exception e) {
            log.error("Error occurred while adding the dynatrace entities {}", e.getMessage());
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
        return null;
    }

    public int[] addDynatraceMetrics(List<DynatraceMetricBean> entityBeans){
        DynatraceConnectorDataDao connectorDataDao = getMsqlDataDao();
        try {
            return connectorDataDao.addDynatraceMetrics(entityBeans);
        } catch (Exception e) {
            log.error("Error occurred while adding the dynatrace metric data {}", e.getMessage());
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
        return null;
    }

    public int[] deleteDynatraceEntityMetricMappings(){
        DynatraceConnectorDataDao connectorDataDao = getMsqlDataDao();
        try {
            connectorDataDao.deleteDynatraceEntityMetricMapping();
        } catch (Exception e) {
            log.error("Error occurred while deleting the dynatrace entity-metric mapping {}", e.getMessage());
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
        return null;
    }

    public int[] addDynatraceEntityMetricMappings(List<DynatraceEntityMetricMapping> entityBeans){
        DynatraceConnectorDataDao connectorDataDao = getMsqlDataDao();
        try {
            return connectorDataDao.addDynatraceEntityMetricMapping(entityBeans);
        } catch (Exception e) {
            log.error("Error occurred while adding the dynatrace entity-metric mapping {}", e.getMessage());
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
        return null;
    }

    public List<String> getDynatraceMetricList()
    {
        DynatraceConnectorDataDao dynatraceConnectorDataDao = getMsqlDataDao();
        try {
            return dynatraceConnectorDataDao.getDynatraceMetricList();
        } catch (Exception e) {
            log.error("Error occurred while fetching kpi ids list", e);
        } finally {
            MySQLConnectionManager.getInstance().close(dynatraceConnectorDataDao);
        }
        return new ArrayList<>();
    }

    public List<String> getDynatraceMetricNames()
    {
        DynatraceConnectorDataDao dynatraceConnectorDataDao = getMsqlDataDao();
        try {
            return dynatraceConnectorDataDao.getDynatraceMetricNames();
        } catch (Exception e) {
            log.error("Error occurred while fetching kpi ids list", e);
        } finally {
            MySQLConnectionManager.getInstance().close(dynatraceConnectorDataDao);
        }
        return new ArrayList<>();
    }
}
