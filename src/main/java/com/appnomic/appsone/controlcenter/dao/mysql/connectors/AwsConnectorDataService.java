package com.appnomic.appsone.controlcenter.dao.mysql.connectors;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.*;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.connectors.AwsConnectorDataDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class AwsConnectorDataService {
    private static final Logger logger = LoggerFactory.getLogger(AwsConnectorDataService.class);

    public AwsConnectorDataService(){}

    public void addAwsCredentials(List<AwsCredentialDetailBean> instanceBeans){
        AwsConnectorDataDao awsConnectorDataDao = getMsqlDataDao();
        try {
            awsConnectorDataDao.deleteAwsCredential();
            awsConnectorDataDao.addAwsCredential(instanceBeans);
        } catch (Exception e) {
            logger.error("Error occurred while adding aws instances {} {}",e,instanceBeans);
        } finally {
            MySQLConnectionManager.getInstance().close(awsConnectorDataDao);
        }
    }

    public void addAwsInstances(List<AwsInstanceBean> instanceBeans){
        AwsConnectorDataDao awsConnectorDataDao = getMsqlDataDao();
        try {
            awsConnectorDataDao.deleteAwsInstances();
            awsConnectorDataDao.addAwsInstances(instanceBeans);
        } catch (Exception e) {
            logger.error("Error occurred while adding aws instances {}", instanceBeans, e);
        } finally {
            MySQLConnectionManager.getInstance().close(awsConnectorDataDao);
        }
    }

    public void addAwsMetrics(List<AwsMetricBean> awsMetricBeans){
        AwsConnectorDataDao awsConnectorDataDao = getMsqlDataDao();
        try {
            awsConnectorDataDao.addAwsMatrics(awsMetricBeans);
        } catch (Exception e) {
            logger.error("Error occurred while adding aws instances {}", awsMetricBeans, e);
        } finally {
            MySQLConnectionManager.getInstance().close(awsConnectorDataDao);
        }
    }

    public List<Integer> getAwsMetricsList()
    {
        AwsConnectorDataDao awsConnectorDataDao = getMsqlDataDao();
        try {
            return awsConnectorDataDao.getAwsMetrics();
        } catch (Exception e) {
            logger.error("Error occurred while fetching aws kpis list{}", e.getMessage());
        } finally {
            MySQLConnectionManager.getInstance().close(awsConnectorDataDao);
        }
        return new ArrayList<>();
    }

    public void addAwsLogs(List<AwsLogsBean> awsLogsBeans){
        AwsConnectorDataDao awsConnectorDataDao = getMsqlDataDao();
        try {
            awsConnectorDataDao.deleteLogsDetails();
            awsConnectorDataDao.addLogsDetails(awsLogsBeans);
        } catch (Exception e) {
            logger.error("Error occurred while adding aws instances {}", awsLogsBeans, e);
        } finally {
            MySQLConnectionManager.getInstance().close(awsConnectorDataDao);
        }
    }

    public void addAwsLogKpis(List<AwsLogKpiBean> awsLogKpiBeans){
        AwsConnectorDataDao awsConnectorDataDao = getMsqlDataDao();
        try {
            awsConnectorDataDao.deleteLogKpis();
            awsConnectorDataDao.addLogKpis(awsLogKpiBeans);
        } catch (Exception e) {
            logger.error("Error occurred while adding aws instances {}", awsLogKpiBeans, e);
        } finally {
            MySQLConnectionManager.getInstance().close(awsConnectorDataDao);
        }
    }

    public void addAwsDimension(List<AwsDimensionBean> awsDimensionBeans){
        AwsConnectorDataDao awsConnectorDataDao = getMsqlDataDao();
        try {
            awsConnectorDataDao.deleteDimension();
            awsConnectorDataDao.addDimension(awsDimensionBeans);
        } catch (Exception e) {
            logger.error("Error occurred while adding aws instances {}", awsDimensionBeans, e);
        } finally {
            MySQLConnectionManager.getInstance().close(awsConnectorDataDao);
        }
    }

    public int[] addAwsCredentialInstanceMapping(List<AwsCredentialInstanceMapping> credentialInstanceMappings){
        AwsConnectorDataDao awsConnectorDataDao = getMsqlDataDao();
        try {
            awsConnectorDataDao.deleteCredentialInstanceMappings();
            return awsConnectorDataDao.addCredentialInstanceMappings(credentialInstanceMappings);
        } catch (Exception e) {
            logger.error("Error occurred while adding aws instances {}", credentialInstanceMappings, e);
        } finally {
            MySQLConnectionManager.getInstance().close(awsConnectorDataDao);
        }
        return null;
    }

    public void addAwsCredentialMetricMapping(List<AwsCredentialMetricMapping> credentialMetricMappings){
        AwsConnectorDataDao awsConnectorDataDao = getMsqlDataDao();
        try {
            awsConnectorDataDao.deleteCredentialMetricMappings();
            awsConnectorDataDao.addCredentialMatricMappings(credentialMetricMappings);
        } catch (Exception e) {
            logger.error("Error occurred while adding aws instances {}", credentialMetricMappings, e);
        } finally {
            MySQLConnectionManager.getInstance().close(awsConnectorDataDao);
        }
    }

    public void addAwsCredentialLogsMapping(List<AwsCredentialLogsMapping> credentialLogsMappings){
        AwsConnectorDataDao awsConnectorDataDao = getMsqlDataDao();
        try {
            awsConnectorDataDao.addCredentialLogsMappings(credentialLogsMappings);
        } catch (Exception e) {
            logger.error("Error occurred while adding aws instances {}", credentialLogsMappings, e);
        } finally {
            MySQLConnectionManager.getInstance().close(awsConnectorDataDao);
        }
    }

    public void addAwsCredentialLogKpiMapping(List<AwsCredentialLogKpiMapping> credentialLogKpiMappings){
        AwsConnectorDataDao awsConnectorDataDao = getMsqlDataDao();
        try {
            awsConnectorDataDao.deleteCredentialLogKpiMappings();
            awsConnectorDataDao.addCredentialLogKpiMappings(credentialLogKpiMappings);
        } catch (Exception e) {
            logger.error("Error occurred while adding aws instances {}", credentialLogKpiMappings, e);
        } finally {
            MySQLConnectionManager.getInstance().close(awsConnectorDataDao);
        }
    }

    public void addAwsLogKpiInstanceMapping(List<AwsLogKpiInstanceMapping> logKpiInstanceMappings){
        AwsConnectorDataDao awsConnectorDataDao = getMsqlDataDao();
        try {
            awsConnectorDataDao.deleteLogKpiInstanceMappings();
            awsConnectorDataDao.addLogKpiInstanceMappings(logKpiInstanceMappings);
        } catch (Exception e) {
            logger.error("Error occurred while adding aws instances {}", logKpiInstanceMappings, e);
        } finally {
            MySQLConnectionManager.getInstance().close(awsConnectorDataDao);
        }
    }

    public void addAwsLogsInstanceMapping(List<AwsLogsInstanceMapping> logsInstanceMappings){
        AwsConnectorDataDao awsConnectorDataDao = getMsqlDataDao();
        try {
            awsConnectorDataDao.deleteLogsInstanceMappigs();
            awsConnectorDataDao.addLogsInstanceMappings(logsInstanceMappings);
        } catch (Exception e) {
            logger.error("Error occurred while adding aws instances {}", logsInstanceMappings, e);
        } finally {
            MySQLConnectionManager.getInstance().close(awsConnectorDataDao);
        }
    }

    public void addAwsMetricDimensionMapping(List<AwsMetricDimensionMapping> metricDimensionMappings){
        AwsConnectorDataDao awsConnectorDataDao = getMsqlDataDao();
        try {
            awsConnectorDataDao.deleteMetricDimensionMappings();
            awsConnectorDataDao.addMetricDimensionMappings(metricDimensionMappings);
        } catch (Exception e) {
            logger.error("Error occurred while adding aws instances {}", metricDimensionMappings, e);
        } finally {
            MySQLConnectionManager.getInstance().close(awsConnectorDataDao);
        }
    }

    private AwsConnectorDataDao getMsqlDataDao(){
        return MySQLConnectionManager.getInstance().open(AwsConnectorDataDao.class);
    }
}
