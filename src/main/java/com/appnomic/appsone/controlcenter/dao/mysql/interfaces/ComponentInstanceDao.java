package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompInstanceControllerTag;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.TagMappingBean;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.heal.configuration.entities.InstanceMetadataBean;
import com.heal.configuration.pojos.InstanceAttributes;
import com.heal.configuration.pojos.InstanceMetadata;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;
import java.util.Set;

public interface ComponentInstanceDao {
    @SqlUpdate("insert into comp_instance (name,host_id,is_DR,is_cluster,mst_component_version_id,created_time,updated_time,user_details_id," +
            "account_id,mst_component_id,mst_component_type_id,discovery,host_address,identifier, mst_common_version_id, parent_instance_id, supervisor_id) " +
            "values (:name,:hostId,:isDR,:isCluster,:mstComponentVersionId,:createdTime,:updatedTime,:userDetailsId,:accountId,:mstComponentId," +
            ":mstComponentTypeId,:discovery,:hostAddress,:identifier, :mstCommonVersionId, :parentId, :supervisorId)")
    @GetGeneratedKeys
    int addComponentInstance(@BindBean ComponentInstanceBean componentInstanceBean);

    @SqlUpdate("update comp_instance set name = :name,host_id = :hostId,is_DR = :isDR, is_cluster = :isCluster, " +
            "mst_component_version_id = :mstComponentVersionId, updated_time = :updatedTime,user_details_id =:userDetailsId, " +
            "mst_component_id =:mstComponentId, mst_component_type_id = :mstComponentTypeId, discovery = :discovery, host_address = :hostAddress, " +
            "mst_common_version_id = :mstCommonVersionId, parent_instance_id = :parentId, status = :status, supervisor_id=:supervisorId " +
            "where identifier =:identifier and account_id = :accountId")
    @GetGeneratedKeys
    int updateComponentInstance(@BindBean ComponentInstanceBean componentInstanceBean);

    @SqlUpdate("insert into comp_instance_attribute_values (attribute_value,comp_instance_id,mst_component_attribute_mapping_id,created_time,updated_time,user_details_id,mst_common_attributes_id,attribute_name) values (:attributeValue,:compInstanceId,:mstComponentAttributeMappingId,:createdTime,:updatedTime,:userDetailsId,:mstCommonAttributesId,:attributeName)")
    @GetGeneratedKeys
    int addAttributesForComponentInstance(@BindBean CompInstanceAttributesBean compInstanceAttributesBean);

    @SqlUpdate("update comp_instance_attribute_values set attribute_value = :attributeValue, updated_time = :updatedTime, user_details_id = :userDetailsId where comp_instance_id = :compInstanceId and mst_component_attribute_mapping_id = :mstComponentAttributeMappingId and mst_common_attributes_id = :mstCommonAttributesId")
    @GetGeneratedKeys
    int updateAttributesForComponentInstance(@BindBean CompInstanceAttributesBean compInstanceAttributesBean);

    @SqlUpdate("insert into comp_instance_kpi_details (comp_instance_id,mst_producer_kpi_mapping_id,collection_interval,created_time,updated_time,user_details_id,mst_kpi_details_id,mst_producer_id) values (:compInstanceId,:mstProducerKpiMappingId,:collectionInterval,:createdTime,:updatedTime,:userDetailsId,:mstKpiDetailsId,:mstProducerId)")
    @GetGeneratedKeys
    int addNonGroupCompInstanceKpiDetails(@BindBean CompInstanceKpiDetailsBean compInstanceKpiDetailsBean);

    @SqlBatch("insert into comp_instance_kpi_details (comp_instance_id,mst_producer_kpi_mapping_id,collection_interval,created_time,updated_time,user_details_id," +
            "mst_kpi_details_id,mst_producer_id) values (:compInstanceId,:mstProducerKpiMappingId,:collectionInterval,:createdTime,:updatedTime," +
            ":userDetailsId,:mstKpiDetailsId,:mstProducerId)")
    @GetGeneratedKeys
    int[] addMultipleNonGroupCompInstanceKpiDetails(@BindBean List<CompInstanceKpiDetailsBean> compInstanceKpiDetailsBeans);

    @SqlUpdate("insert into comp_instance_kpi_group_details (attribute_value,created_time,updated_time,user_details_id," +
            "comp_instance_id,mst_producer_kpi_mapping_id,collection_interval,mst_kpi_details_id," +
            "is_discovery,kpi_group_name,mst_kpi_group_id,mst_producer_id,alias_name) values" +
            " (:attributeValue,:createdTime,:updatedTime,:userDetailsId,:compInstanceId,:mstProducerKpiMappingId,:collectionInterval," +
            ":mstKpiDetailsId,:isDiscovery,:kpiGroupName,:mstKpiGroupId,:mstProducerId,:aliasName)")
    @GetGeneratedKeys
    int addGroupCompInstanceKpiDetails(@BindBean CompInstanceKpiGroupDetailsBean compInstanceKpiDetailsBean);

    @SqlBatch("insert into comp_instance_kpi_group_details (attribute_value,created_time,updated_time,user_details_id,comp_instance_id," +
            "mst_producer_kpi_mapping_id,collection_interval,mst_kpi_details_id,is_discovery,kpi_group_name,mst_kpi_group_id," +
            "mst_producer_id,alias_name) values (:attributeValue,:createdTime,:updatedTime,:userDetailsId,:compInstanceId," +
            ":mstProducerKpiMappingId,:collectionInterval,:mstKpiDetailsId,:isDiscovery,:kpiGroupName,:mstKpiGroupId," +
            ":mstProducerId,:aliasName)")
    @GetGeneratedKeys
    int[] addMultipleGroupCompInstanceKpiDetails(@BindBean List<CompInstanceKpiGroupDetailsBean> compInstanceKpiDetailsBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,status,host_id hostId,is_dr isDR,is_cluster isCluster,mst_component_version_id mstComponentVersionId," +
            "created_time createdTime,updated_time updatedTime,user_details_id userDetailsId,account_id accountId,mst_component_id mstComponentId," +
            "mst_component_type_id mstComponentTypeId,discovery,host_address hostAddress,identifier,mst_common_version_id mstCommonVersionId, supervisor_id supervisorId " +
            "from comp_instance where account_id = :accountId and (name = :name or identifier = :identifier)")
    ComponentInstanceBean getComponentInstance(@Bind("identifier") String identifier, @Bind("name") String name, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,status,host_id hostId,is_dr isDR,is_cluster isCluster,mst_component_version_id mstComponentVersionId," +
            "created_time createdTime,updated_time updatedTime,user_details_id userDetailsId,account_id accountId,mst_component_id mstComponentId," +
            "mst_component_type_id mstComponentTypeId,discovery,host_address hostAddress,identifier,mst_common_version_id mstCommonVersionId, supervisor_id supervisorId " +
            "from comp_instance where account_id = :accountId and status = 1 and (identifier = :identifier or name = :name)")
    ComponentInstanceBean getActiveComponentInstance(@Bind("identifier") String identifier, @Bind("name") String name, @Bind("accountId") int accountId);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select ci.id from comp_instance ci join tag_mapping tm on ci.id = tm.object_id where ci.account_id = :accountId " +
            "and ci.is_cluster = 1 and ci.mst_component_id = :componentId and ci.mst_component_version_id = :componentVersionId " +
            "and ci.mst_component_type_id = :componentTypeId and tm.tag_id = :tagId and tm.tag_key = :serviceId and tm.object_ref_table = 'comp_instance'")
    int getComponentInstanceIdByComponentServiceCluster(@Bind("componentId") int componentId, @Bind("componentVersionId") int componentVersionId, @Bind("componentTypeId") int componentTypeId, @Bind("serviceId") int serviceId, @Bind("tagId") int tagId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select ci.id from comp_instance ci join tag_mapping tm on ci.id = tm.object_id where ci.account_id = :accountId " +
            "and ci.is_cluster = 1 and ci.mst_component_id = :componentId and ci.mst_common_version_id = :commonVersionId " +
            "and ci.mst_component_type_id = :componentTypeId and tm.tag_id = :tagId and tm.tag_key = :serviceId and tm.object_ref_table = 'comp_instance'")
    int getClusterId(@Bind("componentId") int componentId, @Bind("commonVersionId") int commonVersionId, @Bind("componentTypeId") int componentTypeId, @Bind("serviceId") int serviceId, @Bind("tagId") int tagId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select ci.id from comp_instance ci  join tag_mapping tm on ci.id = tm.object_id where ci.account_id = :accountId and " +
            "ci.mst_component_type_id = :componentId and ci.is_cluster = 1 and tm.tag_id = :tagId and tm.tag_key = :serviceId and tm.object_ref_table = 'comp_instance'" +
            " and tm.object_id in (select cm.cluster_id from component_cluster_mapping cm join comp_instance_attribute_values ca " +
            "on ca.comp_instance_id = cm.comp_instance_id where ca.attribute_name = :attributeName and ca.attribute_value = :attributeValue)")
    int getComponentInstanceIdByAttributeServiceCluster(@Bind("componentId") int componentId, @Bind("attributeName") String attributeName, @Bind("attributeValue") String attributeValue, @Bind("serviceId") int serviceId, @Bind("tagId") int tagId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select tm.id id, tm.tag_id tagId, tm.object_id objectId, tm.object_ref_table objectRefTable, tm.tag_key tagKey,tm.tag_value tagValue, tm.account_id accountId, tm. user_details_id userDetailsId from tag_mapping tm join component_cluster_mapping cm on tm.object_id = cm.cluster_id and tm.account_id = cm.account_id where tm.account_id = :accountId and cm.comp_instance_id = :instanceId and tm.tag_id = :tagId and tm.object_ref_table = 'comp_instance';")
    TagMappingBean getComponentInstanceTagMappingDetails(@Bind("instanceId") int instanceId, @Bind("tagId") int tagId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,status,identifier,host_id hostId,is_dr isDR,is_cluster isCluster,mst_component_version_id mstComponentVersionId,created_time createdTime," +
            "updated_time updatedTime,user_details_id userDetailsId,account_id accountId,mst_component_id mstComponentId," +
            "mst_component_type_id mstComponentTypeId,discovery,host_address hostAddress,identifier,mst_common_version_id mstCommonVersionId " +
            "from comp_instance where id = :id and account_id=:accountId")
    ComponentInstanceBean getComponentInstanceById(@Bind("id") int id, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,status,host_id hostId,is_dr isDR,is_cluster isCluster,mst_component_version_id mstComponentVersionId,created_time createdTime," +
            "updated_time updatedTime,user_details_id userDetailsId,account_id accountId,mst_component_id mstComponentId," +
            "mst_component_type_id mstComponentTypeId,discovery,host_address hostAddress,identifier,mst_common_version_id mstCommonVersionId " +
            "from comp_instance where id = :id and identifier = :identifier and account_id=:accountId")
    ComponentInstanceBean getComponentInstanceByIdAndIdentifierAndAccount(@Bind("id") int id, @Bind("identifier") String identifier, @Bind("accountId") int accountId);

    @SqlUpdate("insert into agent_comp_instance_mapping (comp_instance_id,agent_id,created_time) values (:compInstanceId,:agentId,:createdTime)")
    @GetGeneratedKeys
    int addAgentCompInstMapping(@BindBean AgentCompInstMappingBean agentCompInstMappingBean);

    @SqlBatch("insert into agent_comp_instance_mapping (comp_instance_id,agent_id,created_time) values (:compInstanceId,:agentId,:createdTime)")
    @GetGeneratedKeys
    int[] addAgentCompInstMapping(@BindBean List<AgentCompInstMappingBean> agentCompInstMappingBean);


    @SqlUpdate("insert into agent_account_mapping (created_time,agent_id,account_id,user_details_id) values (:createdTime, :agentId, :accountId, :userDetailsId)")
    @GetGeneratedKeys
    int addAgentAccountMapping(@BindBean AgentAccountMappingBean agentAccountMappingBean);


    @SqlUpdate("insert into component_cluster_mapping (created_time,updated_time,user_details_id,account_id,comp_instance_id,cluster_id) " +
            "values (:createdTime,:updatedTime,:userDetailsId,:accountId,:compInstanceId,:clusterId)")
    @GetGeneratedKeys
    int addCompClusterMapping(@BindBean CompClusterMappingBean compClusterMappingBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select cm.id, cm.created_time as createdTime, cm.updated_time as updatedTime, cm.user_details_id as userDetailsId, cm.account_id as accountId, " +
            "ci.id as compInstanceId, cm.cluster_id as clusterId from component_cluster_mapping cm join comp_instance ci on ci.id = cm.comp_instance_id where (ci.identifier = :instanceIdentifier or ci.name = :instanceIName) and cm.account_id = :accountId")
    CompClusterMappingBean getCompClusterDetails(@Bind("instanceIdentifier") String instanceIdentifier, @Bind("instanceIName") String instanceIName, @Bind("accountId") int accountId);

    @SqlBatch("update comp_instance set host_id = :hostId where id = :id")
    void updateInstanceHostIds(@BindBean List<ComponentInstanceBean> details);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select name,status from mst_component where id=:compId ")
    ComponentKpiDetail getComponentDetails(@Bind("compId") Integer compId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,status,host_id hostId,is_dr isDR,is_cluster isCluster,mst_component_version_id mstComponentVersionId," +
            "created_time createdTime,updated_time updatedTime,user_details_id userDetailsId,account_id accountId,mst_component_id mstComponentId," +
            "mst_component_type_id mstComponentTypeId,discovery,host_address hostAddress,identifier,mst_common_version_id mstCommonVersionId, supervisor_id supervisorId " +
            "from comp_instance where (identifier = :identifier or name = :name )and account_id = :accountId and status = 1")
    ComponentInstanceBean getActiveComponentInstanceByIdentifierAndName(@Bind("identifier") String identifier, @Bind("name") String name, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,status,host_id hostId,is_dr isDR,is_cluster isCluster,mst_component_version_id mstComponentVersionId," +
            "created_time createdTime,updated_time updatedTime,user_details_id userDetailsId,account_id accountId,mst_component_id mstComponentId," +
            "mst_component_type_id mstComponentTypeId,discovery,host_address hostAddress,identifier,mst_common_version_id mstCommonVersionId, supervisor_id supervisorId " +
            "from comp_instance where identifier = :identifier and account_id = :accountId")
    ComponentInstanceBean getComponentInstanceByIdentifier(@Bind("identifier") String identifier, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,status,host_id hostId,is_dr isDR,is_cluster isCluster,mst_component_version_id mstComponentVersionId," +
            "created_time createdTime,updated_time updatedTime,user_details_id userDetailsId,account_id accountId,mst_component_id mstComponentId," +
            "mst_component_type_id mstComponentTypeId,discovery,host_address hostAddress,identifier,mst_common_version_id mstCommonVersionId, supervisor_id supervisorId " +
            "from comp_instance where name = :name and account_id = :accountId")
    ComponentInstanceBean getComponentInstanceByName(@Bind("name") String name, @Bind("accountId") int accountId);

    @SqlUpdate("update comp_instance set status = 0 where id = :id")
    void updateComponentInstanceStatusById(@Bind("id") int id);

    @SqlUpdate("DELETE FROM comp_instance_kpi_group_details where comp_instance_id =:instanceId")
    int deleteCompInstanceKpiGroupDetailsWithInstId(@Bind("instanceId") int instanceId);

    @SqlUpdate("DELETE FROM comp_instance_kpi_details where comp_instance_id =:instanceId")
    int deleteCompInstanceKpiDetailsWithInstId(@Bind("instanceId") int instanceId);

    @SqlUpdate("DELETE FROM jim_agent where jvm_id =:instanceId")
    int deleteJimAgentMappingWithInstId(@Bind("instanceId") int instanceId);

    @SqlUpdate("DELETE FROM jim_transaction_mapping where comp_instance_id =:instanceId")
    int deleteJimTransactionMappingWithInstId(@Bind("instanceId") int instanceId);

    @SqlUpdate("DELETE FROM agent_comp_instance_mapping where comp_instance_id =:instanceId")
    int deleteAgentCompInstanceMappingWithInstId(@Bind("instanceId") int instanceId);

    @SqlUpdate("DELETE FROM comp_instance_maintenance_mapping where comp_instance_id =:instanceId")
    int deleteCompInstanceMaintenanceMappingWithInstId(@Bind("instanceId") int instanceId);

    @SqlUpdate("DELETE FROM comp_instance_attribute_values where comp_instance_id =:instanceId")
    int deleteCompInstanceAttributeValuesWithInstId(@Bind("instanceId") int instanceId);

    @SqlUpdate("DELETE FROM comp_instance_kpi_threshold_details where comp_instance_id =:instanceId")
    int deleteCompInstanceKpiThresholdDetailsWithInstId(@Bind("instanceId") int instanceId);

    @SqlUpdate("DELETE FROM comp_instance_forensic_details where comp_instance_id =:instanceId")
    int deleteCompInstanceForensicDetailsWithId(@Bind("instanceId") int instanceId);

    @SqlUpdate("DELETE FROM comp_instance_health where comp_instance_id =:instanceId")
    int deleteCompInstanceHealthWithId(@Bind("instanceId") int instanceId);

    @SqlUpdate("DELETE FROM comp_instance_kpis_health where comp_instance_id =:instanceId")
    int deleteCompInstanceKpisHealthWithId(@Bind("instanceId") int instanceId);

    @SqlUpdate("DELETE FROM comp_instance_kpi_configuration where comp_instance_id =:instanceId")
    int deleteCompInstanceKpiConfigurationWithId(@Bind("instanceId") int instanceId);

    @SqlUpdate("DELETE FROM component_cluster_mapping where comp_instance_id =:instanceId")
    int deleteComponentClusterMappingWithInstId(@Bind("instanceId") int instanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(*) from component_cluster_mapping where cluster_id = :clusterId")
    int checkInstExistanceForCluster(@Bind("clusterId") int clusterId);

    @SqlUpdate("DELETE FROM component_cluster_mapping where cluster_id =:clusterId")
    int deleteComponentClusterMappingWithClusterId(@Bind("clusterId") int clusterId);

    @SqlUpdate("DELETE from tag_mapping where object_id =:clusterId and tag_id = :tagId and object_ref_table = :table")
    int deleteTagMappingWithClusterId(@Bind("clusterId") int clusterId, @Bind("tagId") int tagId, @Bind("table") String table);

    @SqlUpdate("DELETE from tag_mapping where object_id =:clusterId and tag_id = :tagId and object_ref_table = :table" +
            " and tag_key= :serviceId and tag_value = :serviceIdentifier")
    int deleteTagMappingWithClusterIdandService(@Bind("clusterId") int clusterId, @Bind("tagId") int tagId, @Bind("table") String table,
                                                @Bind("serviceId") int serviceId, @Bind("serviceIdentifier") String serviceIdentifier);

    @SqlUpdate("DELETE FROM comp_instance where id =:instanceId")
    int deleteCompInstanceWithInstId(@Bind("instanceId") int instanceId);

    //Fetch attributes from view
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mst_component_attribute_mapping_id  mstComponentAttributeMappingId,mst_common_version_id  mstCommonVersionId," +
            "mst_component_id  mstComponentId,mst_common_name  mstCommonName,attribute_id  attributeId,attribute_name  attributeName," +
            "is_custom  isCustom,status,is_mandatory  isMandatory,default_value  defaultValue, attribute_type attributeType, is_ui_visible isUiVisible " +
            "from view_attributes where mst_component_id = :mstComponentId and mst_common_version_id = :mstCommonVersionId")
    List<AttributesViewBean> getAttributeViewDataByComponentAndCommonVersion(@Bind("mstComponentId") int mstComponentId, @Bind("mstCommonVersionId") int mstCommonVersionId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select DISTINCT p.mst_producer_kpi_mapping_id as mstProducerKpiMappingId, c.default_collection_interval as collectionInterval, p.mst_kpi_details_id as mstKpiDetailsId, p.producer_id as mstProducerId from view_common_version_kpis  c join view_producer_kpis p on c.mst_component_id = p.mst_component_id and c.kpi_id = p.mst_kpi_details_id where c.mst_component_id = :mstComponentId and c.mst_common_version_id = :mstCommonVersionId and p.mst_component_version_id = :mstComponentVersionId and p.mst_component_type_id = :mstComponentTypeId and c.kpi_group_id = 0 and c.status = 1 and p.is_default=1")
    List<CompInstanceKpiDetailsBean> getDefaultCompInstanceKPIsData(@Bind("mstComponentId") int mstComponentId, @Bind("mstCommonVersionId") int mstCommonVersionId, @Bind("mstComponentVersionId") int mstComponentVersionId, @Bind("mstComponentTypeId") int mstComponentTypeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select DISTINCT p.mst_producer_kpi_mapping_id as mstProducerKpiMappingId, p.producer_name producerName, c.default_collection_interval as collectionInterval, p.mst_kpi_details_id as mstKpiDetailsId, p.kpi_name as kpiGroupName, c.kpi_group_id as mstKpiGroupId, p.producer_id as mstProducerId from view_common_version_kpis  c join view_producer_kpis p on c.mst_component_id = p.mst_component_id and c.kpi_id = p.mst_kpi_details_id where c.mst_component_id = :mstComponentId and c.mst_common_version_id = :mstCommonVersionId and p.mst_component_version_id = :mstComponentVersionId and p.mst_component_type_id = :mstComponentTypeId and c.kpi_group_id != 0 and c.status = 1 and p.is_default=1")
    List<CompInstanceKpiGroupDetailsBean> getDefaultCompInstanceGroupKPIsData(@Bind("mstComponentId") int mstComponentId, @Bind("mstCommonVersionId") int mstCommonVersionId, @Bind("mstComponentVersionId") int mstComponentVersionId, @Bind("mstComponentTypeId") int mstComponentTypeId);

    @SqlUpdate("insert into comp_instance_attribute_values (attribute_value,comp_instance_id,mst_component_attribute_mapping_id,created_time," +
            "updated_time,user_details_id,mst_common_attributes_id,attribute_name) " +
            "values (:attributeValue,:compInstanceId,:mstComponentAttributeMappingId,:createdTime,:updatedTime,:userDetailsId," +
            ":mstCommonAttributesId,:attributeKey)")
    @GetGeneratedKeys
    int addAttributesForCompInstance(@BindBean CompInstAttributesBean compInstAttributesBean);

    @SqlUpdate("update comp_instance_attribute_values set attribute_value = :attributeValue, updated_time = :updatedTime " +
            "where comp_instance_id = :compInstanceId and mst_component_attribute_mapping_id = :mstComponentAttributeMappingId " +
            "and mst_common_attributes_id = :mstCommonAttributesId")
    @GetGeneratedKeys
    int updateAttributesForCompInstance(@BindBean CompInstAttributesBean compInstAttributesBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select comp_instance_id instanceId, status, mst_common_version_id commonVersionId, ci.name instanceName, ci.identifier, host_id hostId, " +
            "host_address hostAddress" +
            " from component_cluster_mapping c join comp_instance ci join view_cluster_services vcs " +
            "where c.cluster_id = vcs.id and c.comp_instance_id = ci.id and ci.status = 1 and vcs.service_id=:serviceId and ci.account_id=:accountId")
    List<CompInstClusterDetails> getCompInstanceDetailsForService(@Bind("serviceId") int serviceId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select acim.agent_id agentId from component_cluster_mapping c, comp_instance ci, view_cluster_services vcs, agent_comp_instance_mapping acim " +
            "where c.cluster_id = vcs.id and c.comp_instance_id = ci.id and c.comp_instance_id=acim.comp_instance_id and ci.status = 1 " +
            "and vcs.service_id=:serviceId and ci.account_id=:accountId")
    Set<Integer> getAgentIdForService(@Bind("serviceId") int serviceId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct object_id from tag_mapping tm " +
            "where tm.account_id=:accountId and tm.object_ref_table='agent' and tm.tag_key=:serviceId")
    Set<Integer> getAgentIdForServiceFromTagMapping(@Bind("serviceId") int serviceId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct tag_key tagKey, object_id objectId, tag_value tagValue, object_ref_table objectRefTable, tag_id tagId " +
            "from tag_mapping tm where tm.account_id=:accountId and tm.object_ref_table='agent' and (tm.tag_id=1 or tm.tag_id=2)")
    Set<TagMappingBean> getServiceVsAgentIdsFromTagMapping(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select object_id objectId, tag_key tagKey, tag_value tagValue, c.controller_type_id controllerTypeId, " +
            "c.identifier controllerIdentifier, c.name controllerName, c.id controllerId from tag_mapping tm, " +
            "controller c where c.id=tm.tag_key and tm.account_id=:accountId and tm.object_ref_table='comp_instance' and tm.tag_id=1")
    List<CompInstanceControllerTag> getControllersMappedToCompInstance(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select identifier from comp_instance where id=:id and account_id in (1, :accountId)")
    String getCompInstanceIdentifierFromId(@Bind("id") int id, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from view_component_instance where mst_component_id=:componentId and mst_component_type_id=:componentTypeId and " +
            "common_version_id=:commonVersionId and account_id=:accountId")
    List<Integer> getCompInstanceIdsUsingComponentDetails(@Bind("componentId") int componentId, @Bind("componentTypeId") int componentTypeId,
                                                          @Bind("commonVersionId") int commonVersionId, @Bind("accountId") int accountId);

    @SqlUpdate("update comp_instance set name = :name where id =:id")
    @GetGeneratedKeys
    int editComponentInstance(@Bind("name") String name, @Bind("id") int id);

    @SqlUpdate("update comp_instance set is_DR = :env where identifier =:identifier and account_id = :accountId")
    void updateComponentInstanceEnvDetails(@BindBean CompInstanceEnvDetailsPojo compInstanceEnvDetailsPojo, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select is_DR from comp_instance where identifier =:identifier")
    int getComponentInstanceEnvDetails(@Bind("identifier") String identifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mc.name componentTypeName, ci.host_address hostAddress, ci.name hostInstanceName, ci.host_id hostId from comp_instance ci, mst_component mc " +
            "where ci.mst_component_type_id=1 and ci.mst_component_id=mc.id and ci.is_cluster=0 and ci.status=1 and ci.account_id=:accountId")
    List<HostDetailsBean> getDetailsForAgents(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,status,host_id hostId,is_dr isDR,is_cluster isCluster,mst_component_version_id mstComponentVersionId," +
            "created_time createdTime,updated_time updatedTime,user_details_id userDetailsId,account_id accountId,mst_component_id mstComponentId," +
            "mst_component_type_id mstComponentTypeId,discovery,host_address hostAddress,identifier,mst_common_version_id mstCommonVersionId, supervisor_id supervisorId " +
            "from comp_instance where account_id = :accountId and mst_component_type_id = :componentTypeId and is_cluster = 0")
    List<ComponentInstanceBean> getHostInstance(@Bind("accountId") int accountId, @Bind("componentTypeId") int componentTypeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select ciav.id, ciav.attribute_value attributeValue, ciav.comp_instance_id compInstanceId," +
            " ciav.mst_component_attribute_mapping_id mstComponentAttributeMappingId," +
            " ciav.mst_common_attributes_id mstCommonAttributesId, ciav.attribute_name attributeName" +
            " from comp_instance_attribute_values ciav, mst_common_attributes mca where" +
            " ciav.mst_common_attributes_id = mca.id and mca.attribute_name = :attributeName")
    List<CompInstanceAttributesBean> getInstAttributeMapping(@Bind("attributeName") String attributeName);

    @SqlUpdate("DELETE FROM component_cluster_mapping where comp_instance_id =:instanceId and cluster_id =:clusterId and account_id =:accountId")
    int deleteComponentClusterMapping(@Bind("instanceId") int instanceId, @Bind("clusterId") int clusterId, @Bind("accountId") int accId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,mst_type_id mstTypeId,created_time createdTime,updated_time updatedTime,user_details_id userDetailsId," +
            "account_id accountId,description,is_custom isCustom,status from mst_sub_type where mst_type_id=1 and status=1")
    List<MasterSubTypeBean> getAgentTypes();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(*) count, mst_component_id id from comp_instance where status = 1 and account_id in (1, :accountId) group by mst_component_id")
    List<CountBean> getActiveInstanceCountForComponents(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from comp_instance where mst_component_id = :componentId and account_id in (1, :accountId)")
    List<Integer> getInstanceIdsForComponent(@Bind("componentId") int componentId, @Bind("accountId") int accountId);

    @SqlUpdate("update comp_instance set name = :name,updated_time = :updatedTime, user_details_id =:userDetailsId " +
            "where id = :id and identifier =:identifier and account_id = :accountId")
    int updateInstanceName(@Bind("name") String name, @Bind("updatedTime") String updatedTime, @Bind("userDetailsId") String userDetailsId,
                           @Bind("id") int id, @Bind("identifier") String identifier, @Bind("accountId") int accountId);

    @SqlUpdate("update comp_instance set is_DR = :isDR ,updated_time = :updatedTime, user_details_id =:userDetailsId " +
            "where id = :id and identifier =:identifier and account_id = :accountId")
    int updateInstanceEnvDetails(@Bind("isDR") int environment, @Bind("updatedTime") String updateTime, @Bind("userDetailsId") String userId,
                                 @Bind("id") int instanceId, @Bind("identifier") String instanceIdentifier, @Bind("accountId") int accId);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select vc.id clusterId, vc.name clusterName, vc.identifier clusterIdentifier, vc.service_id serviceId, vc.service_name serviceName," +
            " vc.service_identifier serviceIdentifier, vci.mst_component_id clusterComponentId, vci.component_name clusterComponentName," +
            " vci.mst_component_type_id clusterComponentTypeId, vci.component_type_name clusterComponentTypeName," +
            " vci.mst_component_version_id clusterComponentVersionId, vci.component_version_name clusterComponentVersionName," +
            " vci.common_version_id clusterCommonVersionId, vci.common_version_name clusterCommonVersionName, ccm.comp_instance_id instanceId," +
            " vcii.name instanceName, vcii.identifier instanceIdentifier,vcii.host_address hostAddress," +
            " vcii.mst_component_id instanceComponentId, vcii.component_name instanceComponentName," +
            " vcii.mst_component_type_id instanceComponentTypeId, vcii.component_type_name instanceComponentTypeName," +
            " vcii.mst_component_version_id instanceComponentVersionId, vcii.component_version_name instanceComponentVersionName," +
            " vcii.common_version_id instanceCommonVersionId, vcii.common_version_name instanceCommonVersionName" +
            " from (((view_cluster_services vc join view_component_instance vci on vc.id = vci.id) left join component_cluster_mapping ccm" +
            " on ccm.cluster_id = vc.id) left join view_component_instance vcii on ccm.comp_instance_id = vcii.id)" +
            " where vci.account_id = :accountId and vc.service_id = :serviceId")
    List<InstanceClusterServicePojo> getInstanceClusterServiceDetails(@Bind("serviceId") int serviceId, @Bind("accountId") int accountId);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(distinct host_address) from comp_instance where is_cluster = 0 and account_id = :accountId and" +
            " host_address = :address and status = 1 and mst_component_type_id = 1")
    int checkHostAddressExistance(@Bind("accountId") int accountId, @Bind("address") String hostAddress);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(*) from comp_instance_attribute_values c1, comp_instance_attribute_values c2, comp_instance ci" +
            " where lower(c1.attribute_name) = 'monitorport' and c1.attribute_value = :monitorPort and lower(c2.attribute_name) = 'hostaddress'" +
            " and c2.attribute_value = :address and c1.comp_instance_id = c2.comp_instance_id and ci.id = c1.comp_instance_id " +
            "and ci.account_id = :accountId")
    int checkHostAddressMonitorPortExistance(@Bind("accountId") int accountId, @Bind("address") String hostAddress,
                                             @Bind("monitorPort") String monitorPort);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select vc.id clusterId,vc.name clusterName,vc.identifier clusterIdentifier, vc.service_id serviceId, vc.service_name serviceName," +
            " vc.service_identifier serviceIdentifier,vci.mst_component_id clusterComponentId, vci.component_name clusterComponentName," +
            " vci.mst_component_type_id clusterComponentTypeId, vci.component_type_name clusterComponentTypeName," +
            " vci.mst_component_version_id clusterComponentVersionId, vci.component_version_name clusterComponentVersionName," +
            " vci.common_version_id clusterCommonVersionId, vci.common_version_name clusterCommonVersionName from view_cluster_services vc," +
            " view_component_instance vci where vc.id = vci.id and vc.mst_component_type_id = :componentTypeId and vc.service_id = :serviceId")
    List<InstanceClusterServicePojo> getClusterServiceDetails(@Bind("serviceId") int serviceId, @Bind("componentTypeId") int componentTypeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, identifier, host_id hostId, host_name hostName, is_cluster isCluster," +
            " discovery, host_address hostAddress," +
            " is_DR isDR, mst_component_id mstComponentId, component_name mstComponentName," +
            " mst_component_type_id mstComponentTypeId, component_type_name mstComponentTypeName," +
            " mst_component_version_id mstComponentVersionId, component_version_name componentVersionName," +
            " common_version_id commonVersionId, common_version_name commonVersionName," +
            " updated_time updatedTime from view_component_instance vci where" +
            " account_id = :accountId and status = 1 and is_cluster = 0")
    List<ViewComponentInstanceBean> getActiveInstanceDetailsForAccount(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id clusterId, name clusterName, identifier clusterIdentifier," +
            " host_cluster_id hostClusterId, mst_component_id mstComponentId," +
            " mst_component_type_id mstComponentTypeId, mst_component_version_id" +
            " mstComponentVersionId, service_id serviceId, service_name serviceName," +
            " service_identifier serviceIdentifier from  view_cluster_services")
    List<ViewClusterServicesBean> getAllClusterServices();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select application_id applicationId, application_name applicationName," +
            " application_identifier applicationIdentifier, service_id serviceId," +
            " service_name serviceName, service_identifier serviceIdentifier, account_id accountId from" +
            " view_application_service_mapping where account_id = :accountId")
    List<ViewApplicationServiceMappingBean> getAllServiceApplication(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT count(*) FROM comp_instance WHERE status = :status AND mst_component_type_id = :mstComponentTypeId" +
            " AND is_cluster = :isCluster AND account_id = :accountId")
    int activeLicenseHostsCount(@Bind("accountId") int accountId, @Bind("status") int status,
                                @Bind("mstComponentTypeId") int mstComponentTypeId, @Bind("isCluster") int isCluster);

    //for Integration Testing only
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("update comp_instance set status = :status where mst_component_id = :id")
    void updateCompInstanceStatusByComponentId(@Bind("id") int componentId, @Bind("status") int status);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct comp_instance_id from agent_comp_instance_mapping where agent_id = :agentId")
    List<Integer> getAgentCompInstMapping(@Bind("agentId") int agentId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("DELETE FROM comp_instance where id = :id")
    int deleteInstance(@Bind("id") int instanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into agent_comp_instance_mapping (comp_instance_id, agent_id, created_time) values(:compInstanceId, :agentId, :createdTime)")
    int[] addAgentMappingToInstance(@BindBean List<AgentCompInstMappingBean> agentCompInstMappingBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("delete from agent_comp_instance_mapping where comp_instance_id=:compInstanceId and agent_id=:agentId")
    int[] deleteAgentMappingFromInstance(@BindBean List<AgentCompInstMappingBean> agentCompInstMappingBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from comp_instance_kpi_group_details where mst_kpi_details_id = :kpiId and mst_kpi_group_id = :groupKpiId and comp_instance_id = :compInstanceId")
    int getCompInsKpiIdForGroupKpi(@Bind("kpiId") int kpiId, @Bind("groupKpiId") int groupKpiId, @Bind("compInstanceId") int compInstanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from comp_instance_kpi_details where mst_kpi_details_id = :kpiId and comp_instance_id = :compInstanceId")
    int getCompInsKpiIdForNonGroupKpi(@Bind("kpiId") int kpiId, @Bind("compInstanceId") int compInstanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select cia.id attributeId, mca.attribute_name attributeName, cia.attribute_value attributeValue, cia.comp_instance_id compInstanceId, mcam.is_ui_visible isUiVisible, mca.attribute_type attributeType, mca.status , mca.is_custom isCustom from comp_instance_attribute_values cia, mst_component_attribute_mapping mcam, mst_common_attributes mca where cia.mst_component_attribute_mapping_id = mcam.id and cia.mst_common_attributes_id = mca.id and (mca.account_id = 1 or mca.account_id = :accountId) and comp_instance_id = :compInstId")
    List<InstanceAttributes> getInstanceAttributesForCompInstanceId(@Bind("accountId") int accountId, @Bind("compInstId") int compInstId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select compInstanceKpiId compInstKpiId, kpiid mstKpiId, mst_producer_kpi_mapping_id mstProducerKpiMappingId,\n" +
            "collection_interval collectionInterval, status, mst_producer_id mstProducerId, notification from view_comp_instance_kpis where instance_id=:instanceId")
    List<CompInstKpiMapping> getCompInstKpiMapping(@Bind("instanceId") int instanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from instance_metadata where instance_id = :instanceId")
    int deleteCompInstanceMetadataWithInstId(@Bind("instanceId") int instanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,identifier from view_component_instance where account_id = :accountId and " +
            "identifier = :instanceIdentifier")
    IdPojo getCompInstanceNameAndIdentifierByAccountId(@Bind("instanceIdentifier") String instanceIdentifier, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update instance_metadata set instance_id = :instanceId, environment_id = :environmentId," +
            " related_instance_id = :relatedInstanceId, memory_size_mb = :memorySizeMB, cpu_cores = :cpuCores," +
            " disk_size_mb = :diskSizeMB, created_time = :createdTime, updated_time = :updatedTime," +
            " user_details_id = :userDetailsId, `status` = :status where instance_id = :instanceId")
    int[] updateInstanceMetadata(@BindBean List<InstanceMetadata> instanceMetadata);

    @SqlUpdate("insert into instance_metadata (instance_id,environment_id,created_time,updated_time,user_details_id,status) " +
            "values (:id,:environmentId,:createdTime,:updatedTime,:userDetailsId,:status)")
    @GetGeneratedKeys
    int addInstanceMetadata(@BindBean InstanceMetadataBean instanceMetadataBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(*) from comp_instance where is_cluster = 0 and account_id = :accountId and " +
            "host_address = :address and is_DR = :is_DR and status = 1 and mst_component_type_id = 1")
    int getInstanceDetailsByHostAddress(@Bind("accountId") int accountId, @Bind("address") String hostAddress, @Bind("is_DR") int isDr);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from mst_type where account_id = 1 and status = 1 and type = :type")
    int getEnvTypeIdFromTypeName(@Bind("type") String type);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name from mst_sub_type where account_id = 1 and status = 1 and mst_type_id = :typeId")
    List<ObjPojo> getEnvSubTypeDetails(@Bind("typeId") int typeId);
}
