package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ServiceThresholdDao;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ControllerEntity;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> on 13/3/20
 */
public class ServiceThresholdDS {
    private static final Logger logger = LoggerFactory.getLogger(ServiceThresholdDS.class);

    /**
     * Get service details by not generated SOR.
     * @return
     */
    public static List<ControllerEntity> getServicesByNotSOR(int accountId, int ctrlTypeId, Handle handle) {
        logger.trace("Method Invoked : ControllerDS/getCommandDetails (accountId:{},ctrlTypeId:{})", accountId, ctrlTypeId);
        ServiceThresholdDao dao = getServiceThresholdDao(handle);
        try {
            List<ControllerEntity> list = dao.getServicesBySOR(accountId, ctrlTypeId);
            if(list==null) {
                return Collections.emptyList();
            }
            return  list;
        }
        catch (Exception e) {
            logger.error("Exception while getting services for not generated SOR. accountId:{},ctrlTypeId:{}",
                    accountId, ctrlTypeId , e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return Collections.emptyList();
    }


    private static ServiceThresholdDao getServiceThresholdDao(Handle handle){
        if(handle == null){
            return MySQLConnectionManager.getInstance().open(ServiceThresholdDao.class);
        }
        else{
            return handle.attach(ServiceThresholdDao.class);
        }
    }
    private static void closeDaoConnection(Handle handle, ServiceThresholdDao dao){
        if(handle == null){
            MySQLConnectionManager.getInstance().close(dao);
        }
    }
}
