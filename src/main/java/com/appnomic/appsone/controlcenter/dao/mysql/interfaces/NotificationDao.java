package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.*;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface NotificationDao {
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("insert into sms_details (address, port, country_code, protocol_id , " +
            " http_method , http_relative_url , account_id ," +
            " post_data , post_data_flag, user_details_id, created_time, updated_time, status, is_multi_request, username, password, persist_sms_notifications)" +
            "values (:address, :port, :countryCode, :protocolId, :httpMethod, :httpRelativeUrl, :accountId, :postData, :postDataFlag, " +
            ":userDetailsId, :createdTime, :updatedTime, :status, :isMultiRequest, :username, :password, :persistSmsNotifications)")
    @GetGeneratedKeys
    int addSMSDetails(@BindBean SMSDetailsBean smsDetailsBean);

    @SqlBatch("insert into sms_parameters (parameter_name, parameter_value, parameter_type_id, sms_details_id,  " +
            "user_details_id, created_time, updated_time, is_placeholder)" +
            "values (:parameterName, :parameterValue, :parameterTypeId, :smsDetailsId, " +
            ":userDetailsId, :createdTime, :updatedTime , :isPlaceholder)")
    void addSMSParameter(@BindBean List<SMSParameterBean> smsParameterBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("insert into smtp_details (address, port, username, password, security_id, account_id, from_recipient," +
            "user_details_id, created_time, updated_time, status, persist_email_notifications) " +
            "values(:address, :port, :username, :password, :securityId, :accountId, :fromRecipient," +
            ":userDetailsId, :createdTime, :updatedTime, :status, :persistEmailNotifications)")
    @GetGeneratedKeys
    int addSMTPDetails(@BindBean SMTPDetailsBean smtpDetailsBean);
    @SqlBatch("update sms_parameters set parameter_name = :parameterName, parameter_value = :parameterValue, parameter_type_id = :parameterTypeId,  " +
            "updated_time = :updatedTime, is_placeholder = :isPlaceholder where id = :id" )
    void updateSMSParameter(@BindBean List<SMSParameterBean> smsParameterBeans);

    @SqlUpdate("update smtp_details set address = :address, port = :port, username = :username, password = :password, security_id = :securityId, from_recipient = :fromRecipient," +
            "user_details_id = :userDetailsId, updated_time = :updatedTime, persist_email_notifications = :persistEmailNotifications where account_id = :accountId")
    void updateSmtpDetails(@BindBean SMTPDetailsBean smtpDetailsBean);

    @SqlUpdate("update smtp_details set password = :password where id = :smtpId")
    void updateSmtpPassword(@Bind("password") String password, @Bind("smtpId") int id);

    @SqlUpdate("update smtp_details set status = :status, updated_time = :updatedTime where account_id = :accountId")
    void updateSmtpDetailsStatus(@BindBean SMTPDetailsBean smtpDetailsBean);

    @SqlUpdate("update sms_details set address = :address, port = :port, country_code = :countryCode, protocol_id = :protocolId, " +
            "http_method = :httpMethod, http_relative_url = :httpRelativeUrl, " +
            "user_details_id = :userDetailsId, post_data = :postData, post_data_flag = :postDataFlag, " +
            "updated_time = :updatedTime, is_multi_request = :isMultiRequest, username = :username, password = :password, persist_sms_notifications = :persistSmsNotifications" +
            " where account_id = :accountId")
    void updateSmsDetails(@BindBean SMSDetailsBean smsDetailsBean);

    @SqlUpdate("update sms_details set status = :status, updated_time = :updatedTime where account_id = :accountId")
    void updateSmsDetailsStatus(@BindBean SMSDetailsBean smsDetailsBean);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, address, port, username, password, security_id securityId, " +
            "account_id accountId, from_recipient fromRecipient, status, persist_email_notifications persistEmailNotifications from smtp_details " +
            "where account_id =:accountId")
    SMTPDetailsBean getSMTPDetails(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, address, port, country_code countryCode, protocol_id protocolId, " +
            "http_method httpMethod, http_relative_url httpRelativeUrl, account_id accountId, " +
            "post_data postData, post_data_flag postDataFlag, status,is_multi_request isMultiRequest," +
            "username, password, persist_sms_notifications persistSmsNotifications from sms_details where account_id =:accountId")
    SMSDetailsBean getSMSDetails(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, parameter_name parameterName, parameter_value parameterValue, " +
            "parameter_type_id parameterTypeId , sms_details_id smsDetailsId, " +
            "user_details_id userDetailsId, created_time createdTime, updated_time updatedTime, is_placeholder isPlaceholder from sms_parameters where " +
            "sms_details_id = :smsDetailsId")
    List<SMSParameterBean> getSMSParameters(@Bind("smsDetailsId") int smsDetailsId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, user_details_id userDetailsId, subject, body, " +
            "status, name, template_type_id templateTypeId, to_address toAddress, " +
            "cc_address ccAddress, bcc_address bccAddress, template_status templateStatus from email_templates where " +
            "account_id = :accountId")
    List<EmailTemplateBean> getEmailTemplates(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, user_details_id userDetailsId, sms_content " +
            "smsContent, status, name, template_type_id templateTypeId, mobile_numbers " +
            "mobileNumbers, template_status templateStatus from sms_templates where account_id = :accountId")
    List<SMSTemplateBean> getSMSTemplates(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, placeholder_name placeholderName, template, status from " +
            "notification_placeholders")
    List<NotificationPlaceholderBean> getNotificationPlaceholders();

    @SqlUpdate("delete from smtp_details where account_id = :accountId")
    void deleteSMTPDetailsIT(@Bind("accountId") int accountId);

    @SqlUpdate("delete from sms_details where account_id = :accountId")
    void deleteSMSDetailsIT(@Bind("accountId") int accountId);

    @SqlUpdate("delete from sms_parameters where sms_details_id = :smsDetailsId")
    void deleteSMSParameter(@Bind("smsDetailsId") int smsDetailsId);

    @SqlBatch("delete from sms_parameters where id = :id")
    void deleteSMSParameterRows(@BindBean List<SMSParameterBean> smsParameterBeans);


}
