package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.NotificationSettingsBean;
import com.appnomic.appsone.controlcenter.beans.UserNotificationDetails;
import com.appnomic.appsone.controlcenter.beans.UserNotificationPreferenceBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.NotificationBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserForensicNotificationMappingBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserNotificationDetailsBean;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.Date;
import java.util.List;

public interface SignalNotificationPreferenceDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select unm.notification_type_id notificationTypeId, unm.signal_type_id signalTypeId, " +
            "unm.signal_severity_id signalSeverityId, unm.created_time createdTime, unm.updated_time updatedTime, unm.application_id applicationId, " +
            "vt1.name notificationType, vt2.name signalType, vt3.name signalSeverity, applicable_user_id userId " +
            "from user_notification_mapping unm, mst_sub_type vt1, mst_sub_type vt2, mst_sub_type vt3 " +
            "where unm.account_id =:accountId and " +
            "vt1.id=unm.notification_type_id and vt2.id=unm.signal_type_id and vt3.id=unm.signal_severity_id")
    List<UserNotificationPreferenceBean> getNotificationPreferenceForUser(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select u.is_sms_enabled smsEnabled, u.is_email_enabled emailEnabled from " +
            "user_notifications_details u where u.applicable_user_id=:userId")
    UserNotificationDetails getEmailAndSmsStatus(@Bind("userId") String userId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select ns.notification_type_id typeId, ns.no_of_minutes durationInMin, mst.name typeName from " +
            "notification_settings ns join mst_sub_type mst on mst.id=ns.notification_type_id " +
            "where ns.account_id =:accountId")
    List<NotificationSettingsBean> getNotificationSettings(@Bind("accountId") int accountId);

    @SqlBatch("INSERT INTO user_notification_mapping (applicable_user_id, application_id, notification_type_id, signal_type_id, signal_severity_id, " +
            "account_id,status,created_time, updated_time,user_details_id) VALUES " +
            "(:applicableUserId, :applicationId, :notificationTypeId, :signalTypeId, :severityTypeId, :accountId, :status, " +
            ":createdTime, :updatedTime, :userDetailsId)")
    @GetGeneratedKeys
    int[] addNotificationDetails(@BindBean List<NotificationBean> notificationBean);

    @SqlBatch("INSERT INTO user_forensic_notification_mapping (applicable_user_id, application_id, forensic_notification_suppression, " +
            "account_id, status, created_time, updated_time, user_details_id) VALUES (:applicableUserId, :applicationId, " +
            ":forensicNotificationSuppression, :accountId, :status, :createdTime, :updatedTime, :userDetailsId)")
    void addForensicNotificationConfigurations(@BindBean List<UserForensicNotificationMappingBean> beans);

    @SqlBatch("UPDATE user_notification_mapping SET notification_type_id=:notificationTypeId,updated_time=:updatedTime where signal_type_id=:signalTypeId \n" +
            "and signal_severity_id=:severityTypeId and application_id=:applicationId and applicable_user_id=:applicableUserId and account_id=:accountId")
    @GetGeneratedKeys
    void updateNotifications(@BindBean List<NotificationBean> notificationBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(1) from user_notification_mapping where signal_type_id=:signalTypeId \n" +
            "and signal_severity_id=:severityTypeId and application_id=:applicationId and applicable_user_id=:applicableUserId " +
            "and account_id=:accountId")
    int getNotificationPreferencesForUser(@Bind("signalTypeId") int signalTypeId, @Bind("severityTypeId") int severityTypeId,
                                          @Bind("applicationId") int applicationId, @Bind("applicableUserId") String applicableUserId,
                                          @Bind("accountId") int accountId);

    @SqlUpdate("INSERT INTO user_notifications_details (applicable_user_id,is_sms_enabled,is_email_enabled,account_id,created_time," +
            "updated_time,user_details_id, notification_preference_id, is_forensic_enabled, forensic_notification_suppression) VALUES " +
            "( :applicableUserId, :smsEnabled, :emailEnabled, :accountId,:createdTime, :updatedTime, :userDetailsId, :preferenceId, " +
            ":forensicEnabled, :forensicNotificationSuppression)")
    @GetGeneratedKeys
    int addNotificationUserDetails(@BindBean UserNotificationDetailsBean userNotificationDetailsBean);

    @SqlBatch("UPDATE user_notifications_details SET is_sms_enabled = :smsEnabled, is_email_enabled = :emailEnabled, " +
            "is_forensic_enabled = :forensicEnabled, updated_time = :updatedTime where applicable_user_id = :applicableUserId")
    void updateNotificationDetailsForUsers(@BindBean List<UserNotificationDetailsBean> userNotificationDetailsBeans);


    @SqlUpdate("UPDATE user_notifications_details SET is_sms_enabled=:smsEnabled,is_email_enabled=:emailEnabled,updated_time=:updatedTime where applicable_user_id=:user")
    void updateNotificationUserDetails(@Bind("smsEnabled") boolean smsEnabled, @Bind("emailEnabled") boolean emailEnabled, @Bind("user") String user, @Bind("updatedTime") Date updatedTime);

    @SqlUpdate("DELETE FROM user_notifications_details where applicable_user_id = :applicableUserId and account_id= :accountId")
    void removeNotificationDetailsForUser(@Bind("accountId") int accountId, @Bind("applicableUserId") String applicableUserId);

    @SqlUpdate("DELETE FROM user_notifications_details where applicable_user_id = :applicableUserId")
    void removeNotificationDetailsForUser(@Bind("applicableUserId") String applicableUserId);

    @SqlUpdate("delete from user_notification_mapping where applicable_user_id = :applicableUserId")
    void removeUserNotificationPreferencesForUser(@Bind("applicableUserId") String applicableUserId);

    @SqlUpdate("delete from user_forensic_notification_mapping where applicable_user_id = :applicableUserId")
    void removeForensicNotificationPreferencesForUser(@Bind("applicableUserId") String applicableUserId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select u.applicable_user_id applicableUserId, u.is_sms_enabled smsEnabled, u.is_email_enabled emailEnabled, " +
            "u.is_forensic_enabled forensicEnabled from user_notifications_details u")
    List<UserNotificationDetails> getEmailSmsForensicNotificationStatusForUsers();

}
