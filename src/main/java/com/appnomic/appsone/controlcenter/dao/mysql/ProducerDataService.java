package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.common.ProducerType;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.*;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ProducerDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.ProducerDetailsPojo;
import com.appnomic.appsone.controlcenter.pojo.ProducerKPIMappingDetailsPojo;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

public class ProducerDataService {

    private static final Logger logger = LoggerFactory.getLogger(ProducerDataService.class);

    private ProducerDataService() {
    }

    public static ProducerTypeDetail getProducerTypeDetail(Integer producerId, String producerType) {
        ProducerDao producerDao = MySQLConnectionManager.getInstance().getHandle()
                .open(ProducerDao.class);
        try {
            ProducerType type = ProducerType.valueOf(producerType);
            if (type.equals(ProducerType.SCRIPT)) {
                return producerDao.getSSHProducerTypeDetail(producerId);
            } else if (type.equals(ProducerType.JDBC)) {
                return producerDao.getJDBCProducerTypeDetail(producerId);
            } else if (type.equals(ProducerType.HTTP_JSON)) {
                return producerDao.getHTTPJsonProducerTypeDetail(producerId);
            } else if (type.equals(ProducerType.JMX)) {
                return producerDao.getJMXProducerTypeDetail(producerId);
            } else if (type.equals(ProducerType.HTTP)) {
                return producerDao.getHTTPProducerTypeDetail(producerId);
            } else if (type.equals(ProducerType.WAS)) {
                return producerDao.getWASProducerTypeDetail(producerId);
            } else if (type.equals(ProducerType.WMI)) {
                return producerDao.getWMIProducerTypeDetail(producerId);
            }

        } catch (Exception e) {
            logger.error("Error while getting producer type detail from table {}", e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(producerDao);
        }
        return null;
    }

    public static List<ProducerParameter> getProducerParameter(Integer producerId) {
        ProducerDao producerDao = MySQLConnectionManager.getInstance().getHandle()
                .open(ProducerDao.class);
        try {
            return producerDao.getProducerParameter(producerId);
        } catch (Exception e) {
            logger.error("Error while getting producer parameter from table {}", e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(producerDao);
        }
        return null;
    }

    public static List<ViewProducerKPIsBean> getViewProducerKPIsData(int accountId,
                                                                     int mstKpiDetailsId,
                                                                     int compVersionId) {
        ProducerDao producerDao = MySQLConnectionManager.getInstance().getHandle()
                .open(ProducerDao.class);
        try {
            return producerDao.getProducerKPIs(accountId, mstKpiDetailsId, compVersionId);
        } catch (Exception e) {
            logger.error("Error while getting producer kpi data from table {}", e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(producerDao);
        }
        return null;
    }

    public static MasterProducerBean getProducerWithName(int accountId, String producerName) {
        ProducerDao producerDao = MySQLConnectionManager.getInstance().getHandle()
                .open(ProducerDao.class);
        try {
            return producerDao.getProducerData(accountId, producerName);
        } catch (Exception e) {
            logger.error("Error while getting producer data from table. {}", e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(producerDao);
        }
        return null;
    }

    public static List<ProducerDetailsPojo> getProducerDetailsWithAccId(Integer accountId, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        try {
            return producerDao.getProducerDetailsWithAccId(accountId);
        } catch (Exception e) {
            logger.error("Error while getting producer details with account Id. {}", e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(producerDao);
        }
        return new ArrayList<>();
    }

    public static int getProducerCount(int accountId) {
        ProducerDao producerDao = MySQLConnectionManager.getInstance().getHandle()
                .open(ProducerDao.class);
        try {
            return producerDao.getProducerCount(accountId);
        } catch (Exception e) {
            logger.error("Error while getting producer count from table. {}", e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(producerDao);
        }
        return -1;
    }

    public static List<MasterProducerAttributeBean> getAttributesBasedOnProducerType(int producerTypeId) {
        ProducerDao producerDao = MySQLConnectionManager.getInstance().getHandle()
                .open(ProducerDao.class);
        try {
            return producerDao.getProducerAttributesBasedOnProducerType(producerTypeId);
        } catch (Exception e) {
            logger.error("Error while getting producer attribute data from table. {}", e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(producerDao);
        }
        return Collections.emptyList();
    }

    public static MasterProducerTypeBean getProducerTypeData(int accountId, String producerType) {
        ProducerDao producerDao = MySQLConnectionManager.getInstance().getHandle()
                .open(ProducerDao.class);
        try {
            return producerDao.getProducerTypeData(accountId, producerType);
        } catch (Exception e) {
            logger.error("Error while getting producer type data from table. {}", e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(producerDao);
        }
        return null;
    }

    public static int addProducer(MasterProducerBean masterProducerBean, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        int id;
        try {
            id = producerDao.addProducerData(masterProducerBean);
        } catch (Exception e) {
            logger.error("Error while adding producer data into table. {}", e.getMessage(), e);
            throw e;
        } finally {
            closeConnection(handle, producerDao);
        }
        return id;
    }

    public static int addProducerMapping(MasterProducerKpiMappingBean masterProducerKpiMappingBean,
                                         Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        int id = -1;
        try {
            id = producerDao.addProducerKpiMapping(masterProducerKpiMappingBean);
        } catch (Exception e) {
            logger.error("Error while adding producer kpi mapping data from table. {}", e.getMessage(), e);
        } finally {
            closeConnection(handle, producerDao);
        }
        return id;
    }

    public static int resetIsDefaultKpiProducer(int kpiId, int componentVersionId, int accountId,
                                                int componentId, int componentTypeId, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        int id;
        try {
            id = producerDao.resetIsDefaultFlagForProducerKpi(kpiId, componentVersionId, accountId,
                    componentId, componentTypeId);
        } catch (Exception e) {
            logger.error("Error while resetting default KPI producer data in table. {}", e.getMessage(), e);
            throw e;
        } finally {
            closeConnection(handle, producerDao);
        }
        return id;
    }

    public static int updateDefaultProducerId(int defaultProducerId, int kpiId, int newProducerId, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        int id;
        try {
            id = producerDao.updateDefaultProducerMapping(defaultProducerId, kpiId, newProducerId);
        } catch (Exception e) {
            logger.error("Error while updating default producer id in table. {}", e.getMessage(), e);
            throw e;
        } finally {
            closeConnection(handle, producerDao);
        }
        return id;
    }

    public static int addProducerParameter(ProducerParameterBean producerParameterBean, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        int id;
        try {
            id = producerDao.addProducerParameter(producerParameterBean);
        } catch (Exception e) {
            logger.error("Error while adding producer parameter date into table. {}", e.getMessage(), e);
            throw e;
        } finally {
            closeConnection(handle, producerDao);
        }
        return id;
    }

    public static int addSSHProducerAttributes(SSHProducerTypeDetail sshProducerBean, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        int id;
        try {
            id = producerDao.addSSHProducerAttributes(sshProducerBean);
        } catch (Exception e) {
            logger.error("Error while adding SSH producer data into table. {}", e.getMessage(), e);
            throw e;
        } finally {
            closeConnection(handle, producerDao);
        }
        return id;
    }

    public static int addWMIProducerAttributes(WMIProducerTypeDetail wmiProducerTypeDetail, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        int id;
        try {
            id = producerDao.addWMIProducerAttributes(wmiProducerTypeDetail);
        } catch (Exception e) {
            logger.error("Error while adding WMI producer data into table. {}", e.getMessage(), e);
            throw e;
        } finally {
            closeConnection(handle, producerDao);
        }
        return id;
    }

    public static int addHTTPProducerAttributes(HTTPProducerTypeDetail httpProducerTypeDetail, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        int id;
        try {
            id = producerDao.addHTTPProducerAttributes(httpProducerTypeDetail);
        } catch (Exception e) {
            logger.error("Error while adding HTTP producer data into table. {}", e.getMessage(), e);
            throw e;
        } finally {
            closeConnection(handle, producerDao);
        }
        return id;
    }

    public static int addJMXProducerAttributes(JMXProducerTypeDetail jmxProducerTypeDetail, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        int id;
        try {
            id = producerDao.addJMXProducerAttributes(jmxProducerTypeDetail);
        } catch (Exception e) {
            logger.error("Error while adding JMX producer data into table. {}", e.getMessage(), e);
            throw e;
        } finally {
            closeConnection(handle, producerDao);
        }
        return id;
    }

    public static int addWASProducerAttributes(WASProducerTypeDetail wasProducerTypeDetail, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        int id;
        try {
            id = producerDao.addWASProducerAttributes(wasProducerTypeDetail);
        } catch (Exception e) {
            logger.error("Error while adding WAS producer data into table. {} ", e.getMessage(), e);
            throw e;
        } finally {
            closeConnection(handle, producerDao);
        }
        return id;
    }

    public static int addJDBCProducerAttributes(JDBCProducerTypeDetail jdbcProducerTypeDetail, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        int id;
        try {
            id = producerDao.addJDBCProducerAttributes(jdbcProducerTypeDetail);
        } catch (Exception e) {
            logger.error("Error while adding JDBC producer data into table. {}", e.getMessage(), e);
            throw e;
        } finally {
            closeConnection(handle, producerDao);
        }
        return id;
    }

    public static int addHTTPJSONProducerAttributes(HTTPJsonProducerTypeDetail httpJsonProducerTypeDetail,
                                                    Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        int id;
        try {
            id = producerDao.addHTTPJSONProducerAttributes(httpJsonProducerTypeDetail);
        } catch (Exception e) {
            logger.error("Error while adding HTTP JSON producer data into table. {}", e.getMessage(), e);
            throw e;
        } finally {
            closeConnection(handle, producerDao);
        }
        return id;
    }

    public static int addJPPFProducerAttributes(JPPFProducerTypeDetail jppfProducerTypeDetail, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        int id;
        try {
            id = producerDao.addJPPFProducerAttributes(jppfProducerTypeDetail);
        } catch (Exception e) {
            logger.error("Error while adding JPPF producer data into table. {}", e.getMessage(), e);
            throw e;
        } finally {
            closeConnection(handle, producerDao);
        }
        return id;
    }

    public static List<ProducerKPIMappingDetailsPojo> getProducerKPIMappingDetails(int accountId, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        try {
            return producerDao.getProducerKPIMappingDetails(accountId);
        } catch (Exception e) {
            logger.error("Error while fetching Producer KPI mapping details. {}", e.getMessage(), e);
        } finally {
            closeConnection(handle, producerDao);
        }
        return new ArrayList<>();
    }

    public static SSHProducerTypeDetail getSSHAttribute(int producerId, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        SSHProducerTypeDetail result;
        try {
            result = producerDao.getSSHAttribute(producerId);
        } catch (Exception e) {
            logger.error("Error while fetching SSH producer data from table. {}", e.getMessage(), e);
            throw e;
        } finally {
            closeConnection(handle, producerDao);
        }
        return result;
    }

    public static String getProducerTypeById(int producerId, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        try {
            return producerDao.getProducerTypeById(producerId);
        } catch (Exception e) {
            logger.error("Error while fetching KPI producer type with producer Id from table. {}", e.getMessage(), e);
            return null;
        } finally {
            closeConnection(handle, producerDao);
        }
    }

    public static int getProducerByTypeId(String producerTypeId, int kpiTypeId, int isGroup, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        try {
            return producerDao.getProducerByTypeId(producerTypeId, kpiTypeId, isGroup);
        } catch (Exception e) {
            logger.error("Error while fetching KPI producer type with producer type Id from table. {}", e.getMessage(), e);
            return -1;
        } finally {
            closeConnection(handle, producerDao);
        }
    }

    public static void removeProducerData(int producerId, String typeOfProducer, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        try {
            producerDao.removeProducerMapping(producerId);
            producerDao.removeProducerParameter(producerId);
            ProducerType producerType = ProducerType.valueOf(typeOfProducer.trim());
            switch (producerType) {
                case SCRIPT:
                    producerDao.removeSSHProducerType(producerId);
                    break;

                case WMI:
                    producerDao.removeWMIProducerType(producerId);
                    break;

                case HTTP:
                    producerDao.removeHTTPDProducerType(producerId);
                    break;

                case JMX:
                    producerDao.removeJMXProducerType(producerId);
                    break;

                case WAS:
                    producerDao.removeWASProducerType(producerId);
                    break;

                case JDBC:
                    producerDao.removeJDBCProducerType(producerId);
                    break;

                case HTTP_JSON:
                    producerDao.removeHTTPjsonProducerType(producerId);
                    break;

                case JPPF:
                    producerDao.removeJPPFProducerType(producerId);
                    break;

                case EXTERNAL:
                    // In current impl there is no attributes or parameters for external producers

                default:
                    logger.error("Unknown type received, failed to add attributes. Type: {}", producerType);
            }
            producerDao.removeProducer(producerId);
        } catch (Exception e) {
            logger.error("Error while removing producer data.{} \nPlease remove manually.", e.getMessage(), e);
        } finally {
            closeConnection(handle, producerDao);
        }
    }

    public static int insertDummyProducerType(String typeOfProducer, int accountId, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        try {
            Date date = new Date();
            MasterProducerTypeBean masterProducerTypeBean = new MasterProducerTypeBean();
            masterProducerTypeBean.setType(typeOfProducer);
            masterProducerTypeBean.setDescription("Dummy producer named " + typeOfProducer + " for IT");
            masterProducerTypeBean.setClassname("com.appnomic.appsone.componentagent." + typeOfProducer + "." + typeOfProducer + "Producer");
            masterProducerTypeBean.setStatus(1);
            masterProducerTypeBean.setCreatedTime(DateTimeUtil.getTimeInGMT(date.getTime()));
            masterProducerTypeBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(date.getTime()));
            masterProducerTypeBean.setUserDetailsId("7640123a-fbde-4fe5-9812-581cd1e3a9c1");
            masterProducerTypeBean.setProducerTableName(typeOfProducer + "_producer");
            return producerDao.insertDummyProducerType(masterProducerTypeBean, accountId);
        } catch (Exception e) {
            logger.error("Error while adding producer type.{} \nPlease remove manually.", e.getMessage(), e);
        } finally {
            closeConnection(handle, producerDao);
        }
        return -1;
    }

    public static void removeProducerType(int producerTypeId, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        try {
            producerDao.removeProducerType(producerTypeId);
        } catch (Exception e) {
            logger.error("Error while removing producer type.{} \nPlease remove manually.", e.getMessage(), e);
        } finally {
            closeConnection(handle, producerDao);
        }
    }

    public static void insertDummyProducerAttributes(int producerTypeId, String attributeName, int isMandatory, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        try {
            Date date = new Date();
            MasterProducerAttributeBean masterProducerAttributeBean = new MasterProducerAttributeBean();
            masterProducerAttributeBean.setProducerTypeId(producerTypeId);
            masterProducerAttributeBean.setAttributeName(attributeName);
            masterProducerAttributeBean.setIsCustom(0);
            masterProducerAttributeBean.setStatus(1);
            masterProducerAttributeBean.setIsMandatory(isMandatory);
            masterProducerAttributeBean.setCreatedTime(DateTimeUtil.getTimeInGMT(date.getTime()));
            masterProducerAttributeBean.setUpdatedTime(DateTimeUtil.getTimeInGMT(date.getTime()));
            masterProducerAttributeBean.setUserDetailsId("7640123a-fbde-4fe5-9812-581cd1e3a9c1");
            masterProducerAttributeBean.setDefaultValue(null);
            producerDao.insertDummyProducerAttributes(masterProducerAttributeBean);
        } catch (Exception e) {
            logger.error("Error while adding producer attribute. {} \nPlease remove manually.", e.getMessage(), e);
        } finally {
            closeConnection(handle, producerDao);
        }
    }

    public static void removeProducerAttribute(int producerTypeId, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        try {
            producerDao.removeProducerAttribute(producerTypeId);
        } catch (Exception e) {
            logger.error("Error while removing producer attribute. {} \nPlease remove manually.", e.getMessage(), e);
        } finally {
            closeConnection(handle, producerDao);
        }
    }

    public static List<MasterProducerKpiMappingBean> getProducerIdsForKpiGroupId(int groupKpiId, int accountId, Handle handle)
            throws ControlCenterException {
        ProducerDao producerDao = openConnection(handle);
        try {
            return producerDao.getProducerIdsForKpiGroupId(groupKpiId, accountId);
        } catch (Exception e) {
            logger.error("Error while fetching producers for the KPI group. {}", e.getMessage(), e);
            throw new ControlCenterException("Error while fetching producers for the KPI group.");
        } finally {
            closeConnection(handle, producerDao);
        }
    }

    public static void addKpiProducersMappings(List<MasterProducerKpiMappingBean> kpiProducersMappings, Handle handle)
            throws ControlCenterException {
        ProducerDao producerDao = openConnection(handle);
        try {
            producerDao.addProducerKpiMappings(kpiProducersMappings);
        } catch (Exception e) {
            logger.error("Error while adding producer-kpi mappings data in 'mst_producer_kpi_mapping' table. {}", e.getMessage(), e);
            throw new ControlCenterException("Error while adding producer-kpi mappings data in 'mst_producer_kpi_mapping' table.");
        } finally {
            closeConnection(handle, producerDao);
        }
    }

    public static ProducerDao openConnection(Handle handle) {
        if (handle == null) {
            return MySQLConnectionManager.getInstance().getHandle().open(ProducerDao.class);
        } else {
            return handle.attach(ProducerDao.class);
        }
    }

    private static void closeConnection(Handle handle, ProducerDao dao) {
        if (handle == null) {
            MySQLConnectionManager.getInstance().close(dao);
        }
    }

    public static int getProducer(int accountId, int producerId) {
        ProducerDao producerDao = MySQLConnectionManager.getInstance().getHandle()
                .open(ProducerDao.class);
        try {
            return producerDao.getProducer(accountId, producerId);
        } catch (Exception e) {
            logger.error("Error while getting producer from table. {}", e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(producerDao);
        }
        return -1;
    }

    public static List<ProducerKPIMappingDetailsPojo> getProducerKPIMappingDetails(int accountId, int producerId, Handle handle) {
        ProducerDao producerDao = openConnection(handle);
        try {
            return producerDao.getProducerKPIMappingDetails(accountId, producerId);
        } catch (Exception e) {
            logger.error("Error while fetching Producer KPI mapping details. {}", e.getMessage(), e);
        } finally {
            closeConnection(handle, producerDao);
        }
        return new ArrayList<>();
    }
}
