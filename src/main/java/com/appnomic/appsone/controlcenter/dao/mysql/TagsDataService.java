package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.ControllerBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.TagMappingBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.TagsDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.TagMappingDetails;
import com.appnomic.appsone.controlcenter.pojo.TagPreDefinedData;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> : 30/1/19
 */
public class TagsDataService {
    private static final Logger logger = LoggerFactory.getLogger(TagsDataService.class);
    private static final String ERROR_MESSAGE = "Exception while adding tag mapping -";

    private TagsDataService() {
        //Dummy constructor to hide the implicit one
    }

    public static void addTagMappingDetails(List<TagMappingDetails> tagMappingDetails, Handle handle) {
        TagsDao tagsDao = getTagsDao(handle);
        try {
            tagsDao.addTagMappingDetails(tagMappingDetails);
        } catch (Exception e) {
            logger.error(ERROR_MESSAGE + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, tagsDao);
        }
    }

    public static int addTagMappingDetails(TagMappingDetails tagMappingDetails, TagsDao tagsDao) {
        try {
            return tagsDao.addTagMappingDetails(tagMappingDetails);
        } catch (Exception e) {
            logger.error(ERROR_MESSAGE + e.getMessage(), e);
        }
        return 0;
    }

    public static int addTagMappingDetails(TagMappingBean bean, Handle handle) {
        TagsDao tagsDao = getTagsDao(handle);
        try {
            return tagsDao.addTagMappingDetails(bean);
        } catch (Exception e) {
            logger.error(ERROR_MESSAGE + e.getMessage(), e);
            return -1;
        }
    }

    public static int createDaoAndAddTagMappingDetails(TagMappingDetails tagMappingDetails, Handle handle) {
        TagsDao tagsDao = getTagsDao(handle);

        try {
            return tagsDao.addTagMappingDetails(tagMappingDetails);
        } catch (Exception e) {
            logger.error("Encountered exception while inserting an entry in tag_mapping table. Reason: {}", e.getMessage());
            return -1;
        } finally {
            closeDaoConnection(handle, tagsDao);
        }
    }

    public static TagMappingDetails getTagDetails(Handle handle, int id) {
        TagsDao tagsDao = getTagsDao(handle);

        try {
            return tagsDao.getTagMapping(id);
        } catch (Exception e) {
            logger.error("Encountered exception while inserting an entry in tag_mapping table.", e);
            return new TagMappingDetails();
        } finally {
            closeDaoConnection(handle, tagsDao);
        }
    }

    public static int getTagMappingObjectId(int tagId, String objectRefTable, String tagKey, String tagValue, int accountId, Handle handle) {
        TagsDao tagsDao = getTagsDao(handle);
        try {
            return tagsDao.getTagMappingObjectId(tagId, objectRefTable, tagKey, tagValue, accountId);
        } catch (Exception e) {
            logger.error("Encountered exception while getting object from tag_mapping table.", e);
        } finally {
            closeDaoConnection(handle, tagsDao);
        }
        return -1;
    }

    private static TagsDao getTagsDao(Handle handle) {
        if (handle == null) {
            return MySQLConnectionManager.getInstance().open(TagsDao.class);
        } else {
            return handle.attach(TagsDao.class);
        }
    }

    private static void closeDaoConnection(Handle handle, TagsDao dao) {
        if (handle == null) {
            MySQLConnectionManager.getInstance().close(dao);
        }
    }


    public static void importTagMapping(TagMappingDetails tagMappingDetails) {
        TagsDao tagsDao = MySQLConnectionManager.getInstance().open(TagsDao.class);
        try {
            tagsDao.addTagMappingDetails(tagMappingDetails);
        } catch (Exception e) {
            logger.error(ERROR_MESSAGE + e.getMessage(), e);
        }
    }

    public static int addController(ControllerBean controllerBean) {
        TagsDao tagsDaoAtomicConn = MySQLConnectionManager.getInstance().open(TagsDao.class);
        try {
            return tagsDaoAtomicConn.addController(controllerBean);
        } catch (Exception e) {
            logger.error("Error occurred while creating new tag" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(tagsDaoAtomicConn);
        }
        return -1;
    }

    public static int addController(ControllerBean controllerBean, Handle handle) {
        TagsDao tagsDao = getTagsDao(handle);
        try {
            return tagsDao.addController(controllerBean);
        } catch (Exception e) {
            logger.error("Error occurred while creating new tag" + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, tagsDao);
        }
        return -1;
    }

    public static TagPreDefinedData getTagData(String table, String name, String selectSection, String whereSection, int accountId) {
        TagsDao tagsDaoAtomicConn = MySQLConnectionManager.getInstance().open(TagsDao.class);
        try {
            String whereClause = whereSection.replaceAll(":accountId", accountId + "");
            return tagsDaoAtomicConn.getTagData(table, name, selectSection, whereClause);
        } catch (Exception e) {
            logger.error("Exception while tag details data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(tagsDaoAtomicConn);
        }
        return null;
    }

    public static int getTagMappingId(int tagId, int objectId, String objectRefTable, String tagKey, String tagValue, int accountId) {
        TagsDao tagsDaoAtomicConn = MySQLConnectionManager.getInstance().open(TagsDao.class);
        try {
            return tagsDaoAtomicConn.getTagMappingId(tagId, objectId, objectRefTable, tagKey, tagValue, accountId);
        } catch (Exception e) {
            logger.error("Exception while getting tag mapping id from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(tagsDaoAtomicConn);
        }
        return 0;

    }

    public static int getTagMappingId(int tagId, int objectId, String objectRefTable, String tagKey, String tagValue, int accountId, Handle handle) {
        TagsDao dao = getTagsDao(handle);
        try {
            return dao.getTagMappingId(tagId, objectId, objectRefTable, tagKey, tagValue, accountId);
        } catch (Exception e) {
            logger.error("Exception while getting tag mapping id from table" + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return 0;
    }

    public static TagMappingDetails getTagMappingData(int tagId, int objectId, String objectRefTable, int accountId) {
        TagsDao tagsDaoAtomicConn = MySQLConnectionManager.getInstance().open(TagsDao.class);
        try {
            return tagsDaoAtomicConn.getTagMappingData(tagId, objectId, objectRefTable, accountId);
        } catch (Exception e) {
            logger.error("Exception while getting tag mapping id from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(tagsDaoAtomicConn);
        }
        return null;
    }

    public static TagMappingDetails getTagMappingDataByTagVal(int tagId, int objectId, String objectRefTable, int accountId, String tagValue) {
        TagsDao tagsDaoAtomicConn = MySQLConnectionManager.getInstance().open(TagsDao.class);
        try {
            return tagsDaoAtomicConn.getTagMappingDataByTagVal(tagId, objectId, objectRefTable, accountId, tagValue);
        } catch (Exception e) {
            logger.error("Exception while getting tag mapping id from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(tagsDaoAtomicConn);
        }
        return null;
    }

    public static List<TagMappingDetails> getTagKeyId(int tagId, int objectId, String objectRefTable, String tagKey, int accountId) {
        TagsDao tagsDaoAtomicConn = MySQLConnectionManager.getInstance().open(TagsDao.class);
        List<TagMappingDetails> keyList = new ArrayList<>();
        try {
            return tagsDaoAtomicConn.getTagKeyId(tagId, objectId, objectRefTable, tagKey, accountId);
        } catch (Exception e) {
            logger.error("Exception while getting tag key  from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(tagsDaoAtomicConn);
        }
        return keyList;
    }

    public static List<TagMappingBean> getAgentTags(int tagId, String objectRefTable, int accountId) {
        TagsDao tagsDaoAtomicConn = MySQLConnectionManager.getInstance().open(TagsDao.class);
        List<TagMappingBean> keyList = new ArrayList<>();
        try {
            return tagsDaoAtomicConn.getAgentTags(tagId, objectRefTable, accountId);
        } catch (Exception e) {
            logger.error("Exception while getting tag key  from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(tagsDaoAtomicConn);
        }
        return keyList;
    }

    public static int deleteTagMapping(int tagId, int objId, String refTable, int accountId, Handle handle) {
        TagsDao dao = getTagsDao(handle);
        try {
            dao.deleteTagMapping(tagId,objId,refTable,accountId);
            return 1;
        } catch (Exception e) {
            logger.error("Error occurred while deleting tag mapping details." + e.getMessage(), e);
        }
        finally {
            closeDaoConnection(handle, dao);
        }
        return -1;
    }

    public static int deleteAgentServiceTagMapping(int tagId, int objId, String refTable, String tagKey, String tagValue, int accountId, Handle handle) {
        TagsDao dao = getTagsDao(handle);
        try {
            return dao.deleteAgentServiceTagMapping(tagId,objId,refTable,tagKey,tagValue,accountId);
        } catch (Exception e) {
            logger.error("Error occurred while deleting agent-service tag mapping details." + e.getMessage(), e);
        }
        finally {
            closeDaoConnection(handle, dao);
        }
        return -1;
    }

    public static int deleteTags(int tagId, int objId, String tagKeys, String refTable, int accountId,String userId, Handle handle) {
        TagsDao tagsDao = getTagsDao(handle);
        try {
            tagsDao.deleteEntryTagDetails(tagId,objId,tagKeys,refTable,accountId,userId);
        } catch (Exception e) {
            logger.error("Error occurred while delete tagging" + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle,tagsDao);
        }
        return -1;
    }

    public static void deleteTag(int tagId, int objId, String tagKey, String refTable, int accountId, Handle handle) throws ControlCenterException {
        TagsDao tagsDao = getTagsDao(handle);
        try {
            tagsDao.deleteTag(tagId, objId, tagKey, refTable, accountId);
        } catch (Exception e) {
            logger.error("Error occurred while delete tagging" + e.getMessage(), e);
            throw new ControlCenterException(e.getMessage());
        } finally {
            closeDaoConnection(handle,tagsDao);
        }
    }

    public static List<IdPojo> getTagValue(int tagId, String refTable, int accountId) {
        TagsDao tagsDao = MySQLConnectionManager.getInstance().getHandle().open(TagsDao.class);
        try {
            return tagsDao.getTagValue(tagId,refTable,accountId);
        } catch (Exception e) {
            logger.error("Error occurred while delete tagging" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(tagsDao);
        }
        return Collections.emptyList();
    }

    public static String getTagValueByKey(int tagId, int objectId, String tagKey, String refTable) {
        TagsDao tagsDao = MySQLConnectionManager.getInstance().getHandle().open(TagsDao.class);
        try {
            return tagsDao.getTagValueByKey(tagId, objectId,tagKey,refTable);
        } catch (Exception e) {
            logger.error("Error occurred while delete tagging" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(tagsDao);
        }
        return null;
    }

    public static int getClusterIdForHost(String hostAddress, int accountId, int mstComponentTypeId, Handle handle) {
        TagsDao dao = getTagsDao(handle);
        try {
            return dao.getClusterIdForHost(hostAddress, accountId, mstComponentTypeId);
        } catch (Exception e) {
            logger.error("Exception while getting cluster for host." + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return 0;
    }

    public static int getExistingHostClusterForService(int tagId, String tagValue, int tagKey, String objRefTable, int mstCompTypeId, Handle handle) {
        TagsDao dao = getTagsDao(handle);
        try {
            return dao.getExistingHostClusterForService(tagId, tagValue, tagKey, objRefTable, mstCompTypeId);
        } catch (Exception e) {
            logger.error("Exception while getting cluster for host." + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return 0;
    }

    public static int updateTagMappingDetails(TagMappingBean bean, Handle handle) {
        TagsDao tagsDao = getTagsDao(handle);
        try {
            return tagsDao.updateTagMappingDetails(bean);
        } catch (Exception e) {
            logger.error(ERROR_MESSAGE + e.getMessage(), e);
            return -1;
        }
    }

    public static int deleteTagMapping(int tagMappingId, Handle handle) {
        TagsDao tagsDao = getTagsDao(handle);
        try {
            return tagsDao.deleteTagMappingDetails(tagMappingId);
        } catch (Exception e) {
            logger.error(ERROR_MESSAGE + e.getMessage(), e);
            return 0;
        }
    }

    public static int deleteTagMappingByObjectNTagId(int objectId, int tagId, Handle handle) {
        TagsDao tagsDao = getTagsDao(handle);
        try {
            tagsDao.deleteTagMappingByObjectId(objectId, tagId);
            return 1;
        } catch (Exception e) {
            logger.error(ERROR_MESSAGE + e.getMessage(), e);
            return 0;
        }
    }

    public static int deleteTagMappingByTagValue(int objectId, int tagId, String tagValue, Handle handle) {
        TagsDao tagsDao = getTagsDao(handle);
        try {
            tagsDao.deleteTagMappingByTagValue(objectId, tagId, tagValue);
            return 1;
        } catch (Exception e) {
            logger.error(ERROR_MESSAGE + e.getMessage(), e);
            return 0;
        }
    }
    public static int deleteTagMappingByTagValueAndTagId(int tagId, String tagValue, int accountId, Handle handle) {
        TagsDao tagsDao = getTagsDao(handle);
        try {
            tagsDao.deleteTagMappingByTagValueAndTagId(tagId, tagValue, accountId);
            return 1;
        } catch (Exception e) {
            logger.error(ERROR_MESSAGE + e.getMessage(), e);
            return 0;
        }
    }
    public static void deleteTagMappingByTagValueObjectIdAndTagId(int tagId, int objectId, String tagValue, int accountId, Handle handle) {
        TagsDao tagsDao = getTagsDao(handle);
        try {
            tagsDao.deleteTagMappingByTagValueObjectIdAndTagId(tagId, objectId, tagValue, accountId);
        } catch (Exception e) {
            logger.error(ERROR_MESSAGE + e.getMessage(), e);
        }
    }
}
