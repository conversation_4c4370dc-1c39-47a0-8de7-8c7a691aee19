package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.ServiceTransactionSettingBean;
import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.BindBean;
import org.skife.jdbi.v2.sqlobject.SqlUpdate;

public interface ServiceDao {

    @SqlUpdate("DELETE FROM controller where id = :id")
    void deleteController(@Bind("id") int id);

    @SqlUpdate("DELETE FROM tag_mapping where object_id = :id and object_ref_table = 'controller'")
    void deleteTagMappingDetailsForController(@Bind("id") int id);

    @SqlUpdate("DELETE FROM tag_mapping where tag_key = :serviceId and tag_id = 1")
    void deleteServiceMappingDetails(@Bind("serviceId") int serviceId);

    @SqlUpdate("DELETE FROM connection_details where source_id = :serviceId or destination_id = :serviceId and " +
            "source_ref_object = 'controller' and destination_ref_object = 'controller' and account_id = :accountId")
    void removeServiceConnections(@Bind("serviceId") int serviceId, @Bind("accountId") int accountId);

    @SqlUpdate("DELETE from service_configurations where account_id = :accountId and service_id = :serviceId")
    void deleteServiceConfigurations(@Bind("accountId") int accountId, @Bind("serviceId") int serviceId);

    @SqlUpdate("DELETE from service_kpi_thresholds where account_id = :accountId and service_id = :serviceId")
    void deleteServiceKpiThreshold(@Bind("accountId") int accountId, @Bind("serviceId") int serviceId);

    @SqlUpdate("DELETE FROM service_command_arguments where service_id = :serviceId")
    void deleteServiceCommandArguments(@Bind("serviceId") int serviceId);

    @SqlUpdate("DELETE FROM agent_mode_configuration where service_id = :serviceId")
    void deleteServiceAgentModeConfiguration(@Bind("serviceId") int serviceId);

    @SqlUpdate("DELETE FROM service_maintenance_mapping where service_id = :serviceId")
    void deleteServiceMaintenanceMapping(@Bind("serviceId") int serviceId);

    @SqlUpdate("DELETE FROM plugin_kpi_service_mapping where service_id = :serviceId")
    void deletePluginKpiServiceMapping(@Bind("serviceId") int serviceId);

    @SqlUpdate("DELETE FROM user_forensic_notification_mapping where application_id = :serviceId")
    int deleteServiceUserForensicNotificationMapping(@Bind("serviceId") int serviceId);

    @SqlUpdate("insert into transaction_settings (account_id,service_id,last_committed_time,last_committed_transactions,max_volume_count,max_transactions_limit,created_time,updated_time,user_details_id,auto_commit_duration,scheduler_details_id) values (:accountId,:serviceId,:lastCommitedTime,:lastCommitedTransactions,:maxVolumeCount,:maxTransactionsLimit,:createdTime,:updatedTime,:userDetailsId,:autoCommitDuration,:schedulerDetailsId)")
    void insertServiceTransactionDetails(@BindBean ServiceTransactionSettingBean serviceTransactionSettingBean);

}

