package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProcessDetailsBean {

    private int id;
    private String identifier;
    private String name;
    private String createdTime;
    private String updatedTime;
    private int status;
    private String userDetailsId;
    private int accountId;
    private List<String> batchJobs;
    private List<String> batchJobsToBeAdded;
    private List<String> batchJobsToBeDeleted;
    private List<ProcessHostDetailsBean> hostDetails;
}
