package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.*;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.NotificationDao;
import com.appnomic.appsone.controlcenter.dao.redis.AccountRepo;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.heal.configuration.pojos.SMTPDetails;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class NotificationDataService extends AbstractDaoService<NotificationDao> {

    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationDataService.class);
    private static final String ERROR_DELETE_SMS = "Error while deleting SMS details from DB";
    private static final String ERROR_DELETE_SMS_PARAMS = "Error while deleting SMS Params from DB";
    private static final String INVOKE_DELETE_SMS_PARAM = "invoking delete sms parameter";

    public int addSMTPDetails(SMTPDetailsBean smtpDetailsBean, Handle handle) throws DataProcessingException {

        NotificationDao notificationDao = null;
        try {
            notificationDao = getDaoConnection(handle, NotificationDao.class);
            return notificationDao.addSMTPDetails(smtpDetailsBean);
        } catch (Exception e) {
            LOGGER.error("Error while adding SMTP details. Reason :{}", e.getMessage(), e);
            throw new DataProcessingException("Error while adding SMTP details");
        } finally {
            closeDaoConnection(handle, notificationDao);
        }
    }

    public String updateSMTPDetails(SMTPDetailsBean smtpDetailsBean, Handle handle) throws DataProcessingException {
        AccountRepo accountRepo = new AccountRepo();
        NotificationDao notificationDao = null;
        try {
            notificationDao = getDaoConnection(handle, NotificationDao.class);
            notificationDao.updateSmtpDetails(smtpDetailsBean);
            accountRepo.updateSmtpConfiguration(smtpDetailsBean.getAccIdentifier(), SMTPDetails.builder()
                    .id(smtpDetailsBean.getId())
                    .address(smtpDetailsBean.getAddress())
                    .port(smtpDetailsBean.getPort())
                    .username(smtpDetailsBean.getUsername())
                    .password(smtpDetailsBean.getPassword())
                    .securityId(smtpDetailsBean.getSecurityId())
                    .security(smtpDetailsBean.getSecurity())
                    .fromRecipient(smtpDetailsBean.getFromRecipient())
                    .persistEmailNotifications(smtpDetailsBean.getPersistEmailNotifications())
                    .build());
            return Constants.SUCCESS;
        } catch (Exception e) {
            LOGGER.error("Error while updating SMTP details. Reason :{}", e.getMessage(), e);
            throw new DataProcessingException("Error while updating SMTP details");
        } finally {
            closeDaoConnection(handle, notificationDao);
        }
    }

    public static void updateSMTPPassword(String password, int smtpId) {
        NotificationDao notificationDao = null;
        try {
            notificationDao =
                    MySQLConnectionManager.getInstance().getHandle().open(NotificationDao.class);
            notificationDao.updateSmtpPassword(password, smtpId);
        } catch (Exception e) {
            LOGGER.error("Error while updating SMTP password details. SMTP Id :{}", smtpId, e);
        } finally {
            if (notificationDao != null) {
                MySQLConnectionManager.getInstance().close(notificationDao);
            }
        }
    }

    public int addSMSDetails(SMSDetailsBean smsDetailsBean, Handle handle) throws DataProcessingException {

        NotificationDao notificationDao = null;
        try {
            notificationDao = getDaoConnection(handle, NotificationDao.class);
            return notificationDao.addSMSDetails(smsDetailsBean);
        } catch (Exception e) {
            LOGGER.error("Error while adding SMS details. Reason: {}", e.getMessage(), e);
            throw new DataProcessingException("Error while adding SMS details");
        } finally {
            closeDaoConnection(handle, notificationDao);
        }
    }

    public void updateSMSDetails(SMSDetailsBean smsDetailsBean, Handle handle) throws DataProcessingException {

        NotificationDao notificationDao = null;
        try {
            notificationDao = getDaoConnection(handle, NotificationDao.class);
            notificationDao.updateSmsDetails(smsDetailsBean);
        } catch (Exception e) {
            LOGGER.error("Error while updating SMS details. Reason: {}", e.getMessage(), e);
            throw new DataProcessingException("Error while updating SMS details");
        } finally {
            closeDaoConnection(handle, notificationDao);
        }
    }

    public void updateSMSParameters(List<SMSParameterBean> smsParameterBean, Handle handle) throws DataProcessingException {

        NotificationDao notificationDao = null;
        try {
            notificationDao = getDaoConnection(handle, NotificationDao.class);
            notificationDao.updateSMSParameter(smsParameterBean);
        } catch (Exception e) {
            LOGGER.error("Error while updating SMS parameters. Reason: {}", e.getMessage(), e);
            throw new DataProcessingException("Error while updating SMS parameters");
        } finally {
            closeDaoConnection(handle, notificationDao);
        }
    }

    public void addSMSParameter(List<SMSParameterBean> smsParameterBean, Handle handle) throws DataProcessingException {

        NotificationDao notificationDao = null;
        try {
            notificationDao = getDaoConnection(handle, NotificationDao.class);
            notificationDao.addSMSParameter(smsParameterBean);
        } catch (Exception e) {
            LOGGER.error("Error while adding SMS parameters. Reason: {}", e.getMessage(), e);
            throw new DataProcessingException("Error while adding SMS parameters");
        } finally {
            closeDaoConnection(handle, notificationDao);
        }
    }

    public SMTPDetailsBean getSMTPDetails(int accountId, Handle handle) throws ServerException {

        NotificationDao notificationDao = null;
        try {
            notificationDao = getDaoConnection(handle, NotificationDao.class);
            return notificationDao.getSMTPDetails(accountId);

        } catch (Exception e) {
            LOGGER.error("Error while fetching SMTP details. Reason: {}", e.getMessage(), e);
            throw new ServerException("Error while fetching SMTP details");
        } finally {
            closeDaoConnection(handle, notificationDao);
        }
    }

    public SMSDetailsBean getSMSDetails(int accountId, Handle handle) {
        NotificationDao notificationDao = null;
        try {
            notificationDao = getDaoConnection(handle, NotificationDao.class);
            return notificationDao.getSMSDetails(accountId);
        } catch (Exception e) {
            LOGGER.error("Error while fetching SMS details from DB. Reason: {}", e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, notificationDao);
        }
        return null;
    }


    public void deleteSmsParameters(List<SMSParameterBean> smsParameterBeans, Handle handle) throws DataProcessingException {
        NotificationDao notificationDao = null;
        try {
            LOGGER.debug(INVOKE_DELETE_SMS_PARAM);
            notificationDao = getDaoConnection(handle, NotificationDao.class);
            notificationDao.deleteSMSParameterRows(smsParameterBeans);
        } catch (Exception e) {
            LOGGER.error(ERROR_DELETE_SMS_PARAMS + ". Reason: {}", e.getMessage(), e);
            throw new DataProcessingException(ERROR_DELETE_SMS);
        } finally {
            closeDaoConnection(handle, notificationDao);
        }
    }

    public List<SMSParameterBean> getSMSParameters(int smsDetailsId, Handle handle) throws ServerException {

        NotificationDao notificationDao = null;
        try {
            notificationDao = getDaoConnection(handle, NotificationDao.class);
            return notificationDao.getSMSParameters(smsDetailsId);

        } catch (Exception e) {
            LOGGER.error("Error while fetching SMS parameters. Reason: {}", e.getMessage(), e);
            throw new ServerException("Error while fetching SMS parameters");
        } finally {
            closeDaoConnection(handle, notificationDao);
        }
    }

    public List<EmailTemplateBean> getEmailTemplates(int accountId) {
        NotificationDao notificationDao = null;
        List<EmailTemplateBean> list = new ArrayList<>();
        try {
            notificationDao = getDaoConnection(null, NotificationDao.class);
            list = notificationDao.getEmailTemplates(accountId);
            if (list == null || list.isEmpty()) {
                list = notificationDao.getEmailTemplates(Constants.DEFAULT_ACCOUNT_ID);
            }
        } catch (Exception e) {
            LOGGER.error("Error while getting email templates, accountId: {}", accountId);
        } finally {
            closeDaoConnection(null, notificationDao);
        }
        return list;
    }

    public List<SMSTemplateBean> getSMSTemplates(int accountId) {
        NotificationDao notificationDao = null;
        List<SMSTemplateBean> list = null;
        try {
            notificationDao = getDaoConnection(null, NotificationDao.class);
            list = notificationDao.getSMSTemplates(accountId);
            if (list == null || list.isEmpty()) {
                list = notificationDao.getSMSTemplates(Constants.DEFAULT_ACCOUNT_ID);
            }
        } catch (Exception e) {
            LOGGER.error("Error while getting SMS templates, accountId:{}", accountId, e);
        } finally {
            closeDaoConnection(null, notificationDao);
        }
        return list;
    }

    public List<NotificationPlaceholderBean> getNotificationPlaceholders() throws ControlCenterException {
        NotificationDao notificationDao = null;
        List<NotificationPlaceholderBean> list;
        try {
            notificationDao = getDaoConnection(null, NotificationDao.class);
            list = notificationDao.getNotificationPlaceholders();
            return list;
        } catch (Exception e) {
            LOGGER.error("Error while fetching notification placeholders. Details: {}", e.getMessage());
            throw new ControlCenterException("Error while fetching notification placeholders");
        } finally {
           closeDaoConnection(null, notificationDao);
        }
    }

    public void deleteSmsDetailsAndParameters(int accountId) throws ControlCenterException {
        NotificationDao notificationDao = null;
        try {
            notificationDao = getDaoConnection(null, NotificationDao.class);
            SMSDetailsBean details = notificationDao.getSMSDetails(accountId);
            notificationDao.deleteSMSParameter(details.getId());
            notificationDao.deleteSMSDetailsIT(accountId);
        } catch(Exception e) {
            LOGGER.error("Error while deleting SMS information. Details: {}", e.getMessage());
            throw new ControlCenterException("Error while deleting SMS information");
        } finally {
            closeDaoConnection(null, notificationDao);
        }
    }
}
