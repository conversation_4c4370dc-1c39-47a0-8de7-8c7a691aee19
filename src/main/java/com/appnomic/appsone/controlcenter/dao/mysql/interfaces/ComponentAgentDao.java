package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.ComponentAgentBean;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface ComponentAgentDao {

    @SqlUpdate("insert into component_agent (agent_id,timeout_multiplier,created_time,updated_time,user_details_id,config_operation_mode_id,data_operation_mode_id,data_communication_id) values (:agentId,:timeoutMultiplier,:createdTime,:updatedTime,:userDetailsId,:configOperationModeId,:dataOperationModeId,:dataCommunicationId)")
    @GetGeneratedKeys
    public int addComponentAgent(@BindBean ComponentAgentBean componentAgentBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,agent_id agentId,timeout_multiplier timeoutMultiplier,created_time createdTime,updated_time updatedTime," +
            "user_details_id userDetailsId,config_operation_mode_id configOperationModeId,data_operation_mode_id dataOperationModeId," +
            "data_communication_id dataCommunicationId from component_agent where agent_id = :agentId")
    public ComponentAgentBean getComponentAgentBean(@Bind("agentId") int agentId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,agent_id agentId,timeout_multiplier timeoutMultiplier,created_time createdTime,updated_time updatedTime," +
            "user_details_id userDetailsId,config_operation_mode_id configOperationModeId,data_operation_mode_id dataOperationModeId," +
            "data_communication_id dataCommunicationId from component_agent where data_communication_id = :dataCommunicationId")
    List<ComponentAgentBean> getComponentAgentBeanList(@Bind("dataCommunicationId") int dataCommunicationId);

    @SqlUpdate("update component_agent set timeout_multiplier = COALESCE(NULLIF(:timeoutMultiplier, 0), timeout_multiplier), updated_time = :updatedTime, " +
            "user_details_id = :userDetailsId, config_operation_mode_id = COALESCE(NULLIF(:configOperationModeId, 0), config_operation_mode_id), " +
            "data_operation_mode_id = COALESCE(NULLIF(:dataOperationModeId, 0), data_operation_mode_id), " +
            "data_communication_id = COALESCE(NULLIF(:dataCommunicationId, 0), data_communication_id) " +
            "where agent_id = :agentId")
    int updteComponentAgent(@BindBean ComponentAgentBean componentAgentBean);
}
