package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.pojo.LicenseInfo;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

/**
 * <AUTHOR> - 07-12-2023
 */
public interface LicenseInfoDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select name, value from license_info;")
    List<LicenseInfo> getLicenseInfoDetails();
}
