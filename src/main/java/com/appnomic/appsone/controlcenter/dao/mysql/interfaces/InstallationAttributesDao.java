package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

public interface InstallationAttributesDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select value from a1_installation_attributes where user_details_id = :userId and name = 'InstallationMode'")
    String checkForInstallationMode(@Bind("userId") String userId);
//Change after attribute name finalized
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select value from a1_installation_attributes where user_details_id = :userId and name = 'SetupType'")
    String checkForUnMapAttribute(@Bind("userId") String userId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select value from a1_installation_attributes where name = 'CC-ServicePage-MaintenanceCheck'")
    String checkForServiceMaintenanceFlag();
}
