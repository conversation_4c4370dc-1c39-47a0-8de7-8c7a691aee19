package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecurringDetails {

    private int id;
    private int maintenanceId;
    private int recurringTypeId;
    private String startHrMin;
    private String endHrMin;
    private int duration;
    private String recurringData;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private String userDetails;
}
