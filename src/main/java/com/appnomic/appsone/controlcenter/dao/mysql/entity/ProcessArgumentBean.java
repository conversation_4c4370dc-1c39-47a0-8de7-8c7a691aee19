package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProcessArgumentBean {

    private int id;
    private String name;
    private String value;
    private String defaultValue;
    private int order;
    private int isPlaceholder;
    private int processDetailsId;
    private int processHostDetailsId;
    private String createdTime;
    private String updatedTime;
    private int status;
    private String userDetailsId;
}
