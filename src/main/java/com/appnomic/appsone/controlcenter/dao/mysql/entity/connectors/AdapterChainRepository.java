package com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdapterChainRepository {
    int id;
    String name;
    String className;
    String username;
    String password;
    String autoConnect;
    String poolMinSize;
    String poolMaxSize;
    String connectionUrl;
}
