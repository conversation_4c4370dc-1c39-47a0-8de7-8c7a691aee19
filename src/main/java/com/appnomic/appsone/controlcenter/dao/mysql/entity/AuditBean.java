package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditBean {
    int bigFeatureId;
    int appId;
    int svcId;
    int pageActionId;
    String updatedBy;
    String auditTime;
    String operationType;
    String auditData;
}