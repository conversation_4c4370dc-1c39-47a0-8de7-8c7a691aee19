package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.ConstraintViolation;
import javax.validation.constraints.Size;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Data
@NoArgsConstructor
public class JMXProducerTypeDetail extends ProducerTypeDetail {

    private static final Logger LOGGER = LoggerFactory.getLogger(JMXProducerTypeDetail.class);

    @Size(min = 1, max = 128, message = "target object name length must be less than or equal to 128")
    private String targetObjectName;

    @Size(min = 1, max = 256, message = "url length must be less than or equal to 256")
    private String jmxUrl;
    private Integer attributeDataTypeId;

    public JMXProducerTypeDetail(int producerId, String createdTime, String updatedTime, String userDetailsId) {
        super(producerId, createdTime, updatedTime, userDetailsId);
    }

    @Override
    public Map<String, String> getDataMap() {
        Map<String, String> map = new HashMap<>();
        map.put("targetObjectName", targetObjectName);
        map.put("jmxUrl", jmxUrl);
        String attributeDataType = "";
        if(attributeDataTypeId != null && attributeDataTypeId > 0){
            attributeDataType = MasterCache.getMstSubTypeForSubTypeId(attributeDataTypeId)
                    .getSubTypeName();
        }
        map.put("attributeIdType", attributeDataType);
        return map;
    }

    public boolean populate(Map<String, String> attributes) {
        this.setJmxUrl(attributes.get(Constants.JMX_TYPE_URL_ATTRIBUTE));
        String attributeType = attributes.get(Constants.JMX_TYPE_ATTRIBUTE_TYPE_ID);
        if(attributeType != null ) {
            ViewTypes viewTypes = MasterCache.getMstTypeForSubTypeName(Constants.JMX_TYPE_IDENTIFIER,
                    attributeType);
            if( viewTypes == null ) {
                LOGGER.error("Invalid entry received: {}", attributeType);
                return false;
            }
            this.setAttributeDataTypeId(viewTypes.getSubTypeId());
        }
        this.setTargetObjectName(attributes.get(
                Constants.JMX_TYPE_TARGET_OBJECT_NAME_ATTRIBUTE));
        return validate();
    }

    private boolean validate() {

        Set<ConstraintViolation<Object>> violations = ValidationUtils.validateFields(this);

        if( ! violations.isEmpty() ) {
            violations.forEach(it -> LOGGER.error(it.getMessage()));
            return false;
        }

        return true;
    }
}
