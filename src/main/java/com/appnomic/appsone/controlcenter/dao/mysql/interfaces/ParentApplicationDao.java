package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.heal.configuration.entities.ParentApplicationBean;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface ParentApplicationDao {

    @GetGeneratedKeys
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("INSERT INTO parent_applications (name, identifier, account_id, status, created_time, updated_time, user_details_id) " +
            "VALUES (:name, :identifier, :accountId, :status, :createdTime, :updatedTime, :userId)")
    int addParentApplication(@BindBean ParentApplicationBean parentApplicationBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("INSERT INTO parent_applications (name, identifier, account_id, status, created_time, updated_time, user_details_id) " +
            "VALUES (:name, :identifier, :accountId, :status, :createdTime, :updatedTime, :userId)")
    void addParentApplications(@BindBean List<ParentApplicationBean> parentApplicationBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id, name, identifier, account_id accountId, status, created_time createdTime, updated_time updatedTime, user_details_id userId " +
            "FROM parent_applications where identifier = :identifier and account_id = :accountId")
    List<ParentApplicationBean> getParentApplicationsByIdentifiers(@BindBean List<ParentApplicationBean> parentApplicationIdentifiers);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from parent_applications where identifier = :identifier and account_id = :accountId")
    void deleteParentApplicationByIdentifier(@Bind("identifier") String parentApplicationIdentifier, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("update parent_applications set name = :name where id = :id and account_id = :accountId")
    void editName(@Bind("name") String name, @Bind("id") int id, @Bind("accountId") int accountId);
}
