package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.DataCommunicationDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.DataCommunicationDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR> : 6/2/19
 */
public class DataCommunicationDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DataCommunicationDataService.class);
    private static final String TRACE_STRING = "trace: ";

    public static DataCommunicationDetailsBean getDataCommunicationDetails(String name) {
        DataCommunicationDao dataCommunicationDao = MySQLConnectionManager.getInstance().open(DataCommunicationDao.class);
        try {
            return dataCommunicationDao.getDataCommunicationDetails(name);
        } catch (Exception e) {
            LOGGER.error("Error occurred while fetching data communication details" + e.getMessage(), e);
            LOGGER.debug(TRACE_STRING, e);
        } finally {
            MySQLConnectionManager.getInstance().close(dataCommunicationDao);
        }
        return null;
    }

    public static DataCommunicationDetailsBean getDataCommunicationDetailsById(int id) {
        DataCommunicationDao dataCommunicationDao = MySQLConnectionManager.getInstance().open(DataCommunicationDao.class);
        try {
            return dataCommunicationDao.getDataCommunicationDetailsById(id);
        } catch (Exception e) {
            LOGGER.error("Error occurred while fetching data communication details" + e.getMessage(), e);
            LOGGER.debug(TRACE_STRING, e);
        } finally {
            MySQLConnectionManager.getInstance().close(dataCommunicationDao);
        }
        return null;
    }

    public static DataCommunicationDetailsBean getDataCommunicationDetailsByHostNPort(String hostAddress, int port) {
        DataCommunicationDao dataCommunicationDao = MySQLConnectionManager.getInstance().open(DataCommunicationDao.class);
        try {
            return dataCommunicationDao.getDataCommunicationDetailsByHostNPort(hostAddress, port);
        } catch (Exception e) {
            LOGGER.error("Error occurred while fetching data communication details" + e.getMessage(), e);
            LOGGER.debug(TRACE_STRING, e);
        } finally {
            MySQLConnectionManager.getInstance().close(dataCommunicationDao);
        }
        return null;
    }

    public static DataCommunicationDetailsBean getDataCommunicationDetailsByHostNPort(String hostAddress, int port, String endpoint) {
        DataCommunicationDao dataCommunicationDao = MySQLConnectionManager.getInstance().open(DataCommunicationDao.class);
        try {
            return dataCommunicationDao.getDataCommunicationDetailsByHostNPort(hostAddress, port, endpoint);
        } catch (Exception e) {
            LOGGER.error("Error occurred while fetching data communication details" + e.getMessage(), e);
            LOGGER.debug(TRACE_STRING, e);
        } finally {
            MySQLConnectionManager.getInstance().close(dataCommunicationDao);
        }
        return null;
    }

    public static int addDataCommunicationDetails(DataCommunicationDetailsBean dataCommunicationDetailsBean, Handle handle) {
        DataCommunicationDao dataCommunicationDao = getDataCommunicationDao(handle);
        try {
            return dataCommunicationDao.addDataCommunicationDetails(dataCommunicationDetailsBean);
        } catch (Exception e) {
            LOGGER.error("Error occurred while adding data communication details" + e.getMessage(), e);
            LOGGER.debug(TRACE_STRING, e);
        } finally {
            closeDaoConnection(handle, dataCommunicationDao);
        }
        return 0;
    }

    public static int updateDataCommunicationDetails(DataCommunicationDetailsBean dataCommunicationDetailsBean, Handle handle) {
        DataCommunicationDao dataCommunicationDao = getDataCommunicationDao(handle);
        try {
            return dataCommunicationDao.updateDataCommunicationDetails(dataCommunicationDetailsBean);
        } catch (Exception e) {
            LOGGER.error("Error occurred while adding data communication details" + e.getMessage(), e);
            LOGGER.debug(TRACE_STRING, e);
        } finally {
            closeDaoConnection(handle, dataCommunicationDao);
        }
        return -1;
    }

    public static int deleteDataCommunicationDetails(int dataCommunicationId, Handle handle) {
        DataCommunicationDao dataCommunicationDao = getDataCommunicationDao(handle);
        try {
            return dataCommunicationDao.deleteDataCommunicationDetails(dataCommunicationId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting data communication details" + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, dataCommunicationDao);
        }
        return 0;
    }

    private static DataCommunicationDao getDataCommunicationDao(Handle handle){
        if(handle == null){
            return MySQLConnectionManager.getInstance().open(DataCommunicationDao.class);
        }
        else{
            return handle.attach(DataCommunicationDao.class);
        }
    }

    private static void closeDaoConnection(Handle handle, DataCommunicationDao dao){
        if(handle == null){
            MySQLConnectionManager.getInstance().close(dao);
        }
    }
}
