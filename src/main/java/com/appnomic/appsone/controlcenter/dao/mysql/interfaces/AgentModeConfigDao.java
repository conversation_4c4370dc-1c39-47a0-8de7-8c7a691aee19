package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.AgentModeConfigBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandArgumentBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ServiceCommandArgument;
import com.appnomic.appsone.controlcenter.pojo.AgentSnapshotLevelDetails;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface AgentModeConfigDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id, service_id serviceId, agent_type_id agentTypeId, command_id commandId, " +
            "created_time createdTime, updated_time updatedTime, user_details_id userDetailsId , account_id accountId " +
            "from agent_mode_configuration " +
            "where service_id = :serviceId and account_id = :accountId and agent_type_id = :agentTypeId")
    AgentModeConfigBean getAgentModeConfig(@Bind("serviceId") int serviceId,
                                           @Bind("accountId") int accountId, @Bind("agentTypeId") int agentTypeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT command_mode agentMode, duration_in_mins snapshotDuration, no_of_snapshots snapshotcount , silent_window_in_minis silentWindow from snapshot_levels where identifier = :snapshot_level ;")
    AgentSnapshotLevelDetails getAgentSnapshotDetails(@Bind("snapshot_level")  String snapshot_level);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("INSERT INTO agent_mode_configuration (service_id, agent_type_id, command_id, " +
            "created_time, updated_time, user_details_id, " +
            "account_id) VALUES (:serviceId, :agentTypeId, :commandId, " +
            ":createdTime, :updatedTime, :userDetailsId, " +
            ":accountId)")
    @GetGeneratedKeys
    int addAgentModeConfig(@BindBean AgentModeConfigBean configBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE agent_mode_configuration SET command_id = :commandId, updated_time = " +
            ":updatedTime, user_details_id = :userDetailsId where service_id = :serviceId and account_id = :accountId and " +
            "agent_type_id = :agentTypeId")
    void updateAgentModeConfig(@BindBean AgentModeConfigBean configBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("UPDATE service_command_arguments SET argument_value = :argumentValue, updated_time = :updatedTime, " +
            "user_details_id = :userDetailsId where id = :id and service_id = :serviceId and agent_type_id = :agentTypeId ")
    void updateServiceCommandArguments(@BindBean List<ServiceCommandArgument> svcCmdArguments);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch( "INSERT INTO service_command_arguments ( agent_type_id, service_id, command_id, command_argument_id, " +
            "argument_value, created_time, updated_time, user_details_id) VALUES ( :agentTypeId, :serviceId, :commandId, " +
            ":commandArgumentId, :argumentValue, :createdTime, :updatedTime, :userDetailsId)")
    @GetGeneratedKeys
    int[] addAgentCommandArguments(@BindBean List<ServiceCommandArgument> svcCmdArguments);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT ifnull(scm.id,cm.id) id ,scm.argument_value value, ifnull(scm.argument_value, cm.default_value) " +
            "defaultValue,cm.argument_key argumentKey , cm.id commandId from service_command_arguments scm right join " +
            "command_arguments cm on cm.id = scm.command_argument_id and scm.service_id = :serviceId and scm.agent_type_id = :agentTypeId " +
            " where cm.command_id =:commandId ")
    List<CommandArgumentBean> getServiceCommandArguments(@Bind("commandId") int commandId, @Bind("serviceId") int serviceId,
                                                            @Bind("agentTypeId") int agentTypeId);

}
