package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CompInstanceForensicCategoryBean {

    private int compInstanceId;
    private int categoryId;
    private int actionId;
    private int shouldTrigger;
    private int timeWinInSec;
    private int actionExecTypeId;
    private int downloadTypeId;
    private int retries;
    private int ttlInSec;
    private int commandExecTypeId;
    private Integer suppressionInterval;
    private int objectId;
    private String objectRefTable;
    private String userDetailsId;
    private String createdTime;
    private String updatedTime;
    private int accountId;
    private String accountIdentifier;

}
