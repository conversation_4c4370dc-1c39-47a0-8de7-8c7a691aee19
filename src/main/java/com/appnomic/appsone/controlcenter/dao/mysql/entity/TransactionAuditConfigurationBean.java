package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TransactionAuditConfigurationBean {

    private int id;
    private int transactionId;
    private int auditCaptureTypeId;
    private String displayName;
    private String lookupName;
    private String userDetailsId;
    private String createdTime;
    private String updatedTime;
    private int status;

}
