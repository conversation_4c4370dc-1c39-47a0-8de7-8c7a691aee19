package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.WebHookDataBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.WebHookDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;

public class WebHookDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WebHookDataService.class);

    private WebHookDataService(){}


    public static WebHookDataBean getWebHook(int accountId, Handle handle) {
        WebHookDao dao = getWebHookDao(handle);
        try {
            return dao.getWebHook(accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting webhook for account id:{}, err:", accountId, e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return null;
    }

    public static int addWebHook(WebHookDataBean bean, Handle handle) {
        WebHookDao dao = getWebHookDao(handle);
        try {
            return dao.addWebHook(bean);
        } catch (Exception e) {
            LOGGER.error("Error occurred while creating webhook for account id:{}, err:", bean.getAccountId(), e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return -1;
    }

    public static int remWebHook(int accountId, Handle handle) {
        WebHookDao dao = getWebHookDao(handle);
        try {
            return dao.remWebHook(accountId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while removing webhook for account id:{}, err:", accountId, e);
            closeDaoConnection(handle, dao);
        }
        return -1;
    }

    public static int updateWebHook(String url, Timestamp updatedTime, int accountId, String userId, Handle handle) {
        WebHookDao dao = getWebHookDao(handle);
        try {
            return dao.updateWebHook(url, updatedTime, accountId, userId);
        } catch (Exception e) {
            LOGGER.error("Error occurred while updating webhook for account id:{}, err:", accountId, e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return -1;
    }


    private static WebHookDao getWebHookDao(Handle handle){
        if(handle == null){
            return MySQLConnectionManager.getInstance().open(WebHookDao.class);
        }
        else{
            return handle.attach(WebHookDao.class);
        }
    }
    private static void closeDaoConnection(Handle handle, WebHookDao dao){
        if(handle == null){
            MySQLConnectionManager.getInstance().close(dao);
        }
    }
}
