package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompInstanceForensicCategoryBean;
import com.appnomic.appsone.controlcenter.pojo.CategoryForensicDetails;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;
import org.skife.jdbi.v2.unstable.BindIn;

import java.util.List;
import java.util.Set;

@UseStringTemplate3StatementLocator
public interface CategoryForensicsDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT m.id forensicCategoryMappingId, m.category_id categoryId, c.name categoryName, m.action_id forensicId, " +
            "a.name forensicName,s.id commandId, s.command_name commandName FROM action_category_mapping m, actions a, " +
            "mst_category_details c, command_details s WHERE m.category_id = c.id AND m.action_id = a.id AND m.status = 1 " +
            "AND s.id = m.object_id AND c.id IN (select distinct t.tag_key from mst_component_version_kpi_mapping m LEFT JOIN " +
            "tag_mapping t ON m.mst_kpi_details_id = t.object_id where tag_id = 5 AND m.mst_component_id = :componentId " +
            "AND t.account_id IN (1, :accountId))")
    List<CategoryForensicDetails> getCategoryForensicMappingsForComponentId(@Bind("componentId") int componentId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT comp_instance_id compInstanceId, category_id categoryId, action_id actionId, suppression_interval " +
            "suppressionInterval, should_trigger shouldTrigger FROM comp_instance_forensic_details WHERE " +
            "comp_instance_id = :compInstanceId AND category_id = :categoryId AND action_id = :actionId")
    CompInstanceForensicCategoryBean getCompInstanceForensicCategoryDetailsForInstanceId
            (@Bind("compInstanceId") int compInstanceId, @Bind("categoryId") int categoryId, @Bind("actionId") int actionId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT comp_instance_id compInstanceId, category_id categoryId, action_id actionId, suppression_interval suppressionInterval, " +
            "should_trigger shouldTrigger, time_window_in_secs timeWinInSec, action_exec_type_id actionExecTypeId, download_type_id downloadTypeId, " +
            "retries, ttl_in_secs ttlInSec, command_exec_type_id commandExecTypeId, object_id objectId, object_ref_table objectRefTable " +
            "FROM comp_instance_forensic_details WHERE comp_instance_id = :compInstanceId")
    List<CompInstanceForensicCategoryBean> getCompInstanceForensicDetailsForInstanceId(@Bind("compInstanceId") int compInstanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT category_id categoryId, action_id actionId, time_window_in_secs timeWinInSec, action_exec_type_id " +
            "actionExecTypeId, download_type_id downloadTypeId, retries, ttl_in_secs ttlInSec, command_exec_type_id " +
            "commandExecTypeId, object_id objectId, object_ref_table objectRefTable FROM action_category_mapping " +
            "WHERE status = 1 AND category_id = :categoryId AND action_id = :actionId")
    CompInstanceForensicCategoryBean getActionCategoryMappingDetails(@Bind("categoryId") int categoryId, @Bind("actionId") int actionId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO `comp_instance_forensic_details` (`comp_instance_id`, `category_id`, `action_id`, `created_time`, " +
            "`updated_time`, `user_details_id`, `should_trigger`, `suppression_interval`,`time_window_in_secs`, " +
            "`action_exec_type_id`, `download_type_id`, `retries`, `ttl_in_secs`, `object_id`, `object_ref_table`, " +
            "`command_exec_type_id`) VALUES (:compInstanceId, :categoryId, :actionId, :createdTime, :updatedTime, :userDetailsId, " +
            ":shouldTrigger, :suppressionInterval, :timeWinInSec, :actionExecTypeId, :downloadTypeId, :retries, :ttlInSec, " +
            ":objectId, :objectRefTable, :commandExecTypeId)")
    void addCompInstanceForensicCategoryDetails(@BindBean List<CompInstanceForensicCategoryBean> list);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("UPDATE `comp_instance_forensic_details` SET `suppression_interval` = :suppressionInterval, `should_trigger` " +
            "= :shouldTrigger, `updated_time` = :updatedTime, `user_details_id` = :userDetailsId WHERE `comp_instance_id` = " +
            ":compInstanceId AND `category_id` = :categoryId AND `action_id` = :actionId")
    void updateCompInstanceForensicCategoryDetails(@BindBean List<CompInstanceForensicCategoryBean> list);

    @SqlUpdate("DELETE FROM `comp_instance_forensic_details` WHERE `comp_instance_id` = :instanceId")
    void deleteForensicCategoryMappingByInstanceId(@Bind("instanceId") int instanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT comp_instance_id compInstanceId, category_id categoryId, action_id actionId, suppression_interval suppressionInterval, " +
            "should_trigger shouldTrigger, time_window_in_secs timeWinInSec, action_exec_type_id actionExecTypeId, download_type_id downloadTypeId, " +
            "retries, ttl_in_secs ttlInSec, command_exec_type_id commandExecTypeId, object_id objectId, object_ref_table objectRefTable " +
            "FROM comp_instance_forensic_details WHERE comp_instance_id in (<compInstanceIds>)")
    List<CompInstanceForensicCategoryBean> getCompInstanceForensicDetailsForAllInstanceId(@BindIn("compInstanceIds") Set<String> compInstanceIds);
}
