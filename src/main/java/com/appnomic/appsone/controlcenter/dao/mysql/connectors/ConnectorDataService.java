package com.appnomic.appsone.controlcenter.dao.mysql.connectors;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.*;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.connectors.ConnectorDataDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.connectors.ConnectorDBData;
import com.appnomic.appsone.controlcenter.pojo.connectors.ConnectorUploadResponse;
import com.appnomic.appsone.controlcenter.pojo.connectors.WorkerParameter;
import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;

@Slf4j
public class ConnectorDataService {
    public ConnectorDataService() {
    }


    public void updateUploadError(String error, Timestamp errorDate, int connectorId, int accountId) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            connectorDataDao.updateUploadError(error, errorDate, connectorId, accountId);
        } catch (Exception e) {
            log.error("Error updating upload error.");
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
    }

    public int[] addHealKpi(String schemaName, List<HealKpi> healKpi) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            return connectorDataDao.addHealKpis(schemaName, healKpi);
        } catch (Exception e) {
            log.error("Error occurred while adding Heal kpi details{}", healKpi, e);
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
        return new int[]{};
    }

    public void updateHealKpis(String schemaName, List<HealKpi> healKpiList)
    {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            connectorDataDao.updateHealKpis(schemaName, healKpiList);
        } catch (Exception e) {
            log.error("Error occurred while updating heal KPIs{}", healKpiList, e);
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
    }

    public int[] addDomainToHealKpiMapping(String schemaName, List<DomainToHealKpiMapping> domainToHealKpiMapping) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            return connectorDataDao.addDomainToHealKpiMapping(schemaName, domainToHealKpiMapping);
        } catch (Exception e) {
            log.error("Error occurred while adding domain to heal kpi mapping{}", domainToHealKpiMapping, e);
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
        return new int[]{};
    }

    public List<DomainToHealKpiMapping> getDomainToHealKpiMapping(String schemaName) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            return connectorDataDao.getDomainToHealKpi(schemaName);
        } catch (Exception e) {
            log.error("Error occurred while getting  domain to heal kpi mapping form schema {}", schemaName, e);
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
        return Collections.emptyList();
    }

    public int[] addAdvanceConnectorSettings(String schemaName, List<ConnectorSettingsEntity> connectorSettingsEntity) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            connectorDataDao.deleteConnectorSettings(schemaName);
            return connectorDataDao.addConnectorSettings(schemaName, connectorSettingsEntity);
        } catch (Exception e) {
            log.error("Error occurred while adding connector settings {}", connectorSettingsEntity, e);
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
        return new int[]{};
    }

    public int getTheConnectorTemplateId(int connectorId, int accountId) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            return connectorDataDao.getConnectorTemplateId(connectorId, accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching connector template id {}", e.getMessage());
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
        return -1;
    }

    public void addConnectorDataTemplate(ConnectorTemplateDataBean connectorTemplateDataBean) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            connectorDataDao.addTheConnectorTemplateDataDetails(connectorTemplateDataBean);
        } catch (Exception e) {
            log.error("Error occurred while adding connector data template {}", e.getMessage());
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
    }

    public String getConnectorNameWithConnectorId(Integer connectorId, Integer accountId) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            return connectorDataDao.getConnectorDetailsBasedOnId(connectorId, accountId);
        } catch (Exception e) {
            log.error("Error occurred while getting connector name with connector id {}", e.getMessage());
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
        return null;
    }

    public int updateConnectorTemplateDataIdInAdapterConfigTable(String schemaName, Integer connectorId, Integer configId) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            return connectorDataDao.updateConnectorDataIdInAdapterChainConfig(schemaName, connectorId, configId);
        } catch (Exception e) {
            log.error("Error occurred while updating connector id to config table from {}", connectorId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
        return -1;
    }

    public ConnectorUploadResponse getConnectorDetails(Integer connectorId, Integer accountId) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            return connectorDataDao.getConnectorDataBasedOnId(connectorId, accountId);
        } catch (Exception e) {
            log.error("Error occurred while getting the connector details from id {}", connectorId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
        return ConnectorUploadResponse.builder()
                .name("No Connector")
                .build();
    }

    public int[] addWorkerParameters(String schemaName, List<WorkerParameter> workerParameters) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            connectorDataDao.deleteWorkerParameters(schemaName);
            return connectorDataDao.addWorkerParameters(schemaName, workerParameters);
        } catch (Exception e) {
            log.error("Error occurred while adding worker parameters to db {}", e.getMessage());
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
        return new int[]{};
    }

    public int updateStatusOfTemplate(Integer connectorId, Integer templateId, Integer accountId) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            return connectorDataDao.updateStatusOfTemplate(connectorId, templateId, accountId);
        } catch (Exception e) {
            log.error("Error occurred while adding worker parameters to db {}", e.getMessage());
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
        return -1;
    }

    public int[] addLogscanHealInstanceMapper(String schemaName, List<LogscanHealInstanceMapper> logscanHealInstanceMappers) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            connectorDataDao.deleteLogscanHealInstanceMapper(schemaName);
            return connectorDataDao.addLogscanHealInstanceMapper(schemaName, logscanHealInstanceMappers);
        } catch (Exception e) {
            log.error("Error occurred while adding logscan heal instance mapper to db {}", e.getMessage());
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
        return new int[]{};
    }

    public int[] addAdapterChainRepository(String schemaName, List<AdapterChainRepository> adapterChainRepositories) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            connectorDataDao.deleteAdapterChainRepository(schemaName);
            return connectorDataDao.addAdapterChainRepository(schemaName, adapterChainRepositories);
        } catch (Exception e) {
            log.error("Error occurred while adding logscan heal instance mapper to db {}", e.getMessage());
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
        return new int[]{};
    }

    public ConnectorDBData getConnectorDBData(int accountId, int connectorId) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            return connectorDataDao.getDBData(connectorId, accountId);
        } catch (Exception e) {
            log.error("Error while getting default db file {}", e.getMessage());
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
        return null;
    }

    public void updateDBExistsStatus(int accountId, int connectorId, int status) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            connectorDataDao.updateDBExistsStatus(connectorId, accountId, status);
        } catch (Exception e) {
            log.error("Error while getting default db file {}", e.getMessage());
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
    }

    private ConnectorDataDao getMySqlDataDao() {
        return MySQLConnectionManager.getInstance().open(ConnectorDataDao.class);
    }


    public List<ConnectorTemplateDataBean> getConnectorData(Integer connectorId, Integer accountId) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            return connectorDataDao.getConnectorConfigDataBasedOnId(connectorId, accountId);
        } catch (Exception e) {
            log.error("Error occurred while querying DB for connector configuration data for id [{}]. Details: {}", connectorId, e.getMessage());
        }
        return Collections.emptyList();
    }

    public void updateConnectorNotConfigured(Integer connectorId, Integer accountId) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            connectorDataDao.updateConnectorConfigured(connectorId, accountId, -1);
        } catch (Exception e) {
            log.error("Error occurred while updating DB for connector configuration data for id [{}]. Details: {}", connectorId, e.getMessage());
        } finally {
            MySQLConnectionManager.getInstance().close(connectorDataDao);
        }
    }

    public void updateConnectorConfigDataById(ConnectorTemplateDataBean connectorTemplateDataBean) {
        ConnectorDataDao connectorDataDao = getMySqlDataDao();
        try {
            /*connectorDataDao.updateLastUpdatedTime(connectorTemplateDataBean.getAccountId(), connectorTemplateDataBean.getConnectorDetailsId(),
                    connectorTemplateDataBean.getUpdatedTime());*/
            connectorDataDao.updateConnectorConfigDataById(connectorTemplateDataBean.getAccountId(),
                    connectorTemplateDataBean.getConnectorDetailsId(),
                    connectorTemplateDataBean.getTemplateDataContent(), connectorTemplateDataBean.getUserDetailsId(),
                    connectorTemplateDataBean.getStatus(), connectorTemplateDataBean.getUploadStatus(),
                    connectorTemplateDataBean.getCreatedTime(), connectorTemplateDataBean.getUpdatedTime());
        } catch (Exception e) {
            log.error("Error occurred while updating connector configuration data for id [{}]. Details: {}", connectorTemplateDataBean.getConnectorDetailsId(), e.getMessage());
        }
    }
}
