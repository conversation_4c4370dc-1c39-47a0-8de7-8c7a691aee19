package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.CompInstanceKpiGroupDetailsBean;
import com.appnomic.appsone.controlcenter.beans.KpiMaintenanceStatusBean;
import com.appnomic.appsone.controlcenter.beans.MasterKpiGroupBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.*;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.KPIDataDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.TagMappingDetails;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class KPIDataService extends AbstractDaoService<KPIDataDao> {
    private final Logger logger = LoggerFactory.getLogger(KPIDataService.class);

    public List<KpiDetailsBean> getKpiList(int commonVersionId, int componentId, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.getKpiList(commonVersionId, componentId);
        } catch (Exception e) {
            logger.error("Error occurred while fetching kpi details:", e);
            return new ArrayList<>();
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<PluginKPIServiceMapping> getPluginKPIServiceMapping(int serviceId, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.getPluginKPIServiceMapping(serviceId);
        } catch (Exception e) {
            logger.error("Error occurred while fetching PluginKPIServiceMapping details:", e);
            return new ArrayList<>();
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int createGroupKpi(MasterKpiGroupBean groupKpiBean, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.createGroupKpi(groupKpiBean);
        } catch (Exception e) {
            logger.error("Encountered exception while adding Group KPI details.", e);
            return -1;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int createKpi(KpiBean kpiBean, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.createKpi(kpiBean);
        } catch (Exception e) {
            logger.error("Encountered exception while adding KPI details.", e);
            return -1;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<MasterKpiGroupBean> checkForGroupKpiUsingIdentifier(String identifier, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.checkForGroupKpiUsingIdentifier(identifier);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching Group KPI details.", e);
            return new ArrayList<>();
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int checkForKpiUsingName(String kpiName, int accountId, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.checkForKpiUsingName(kpiName, accountId);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching KPI details.", e);
            return -1;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int checkForKpiUsingIdentifier(String identifier, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.checkForKpiUsingIdentifier(identifier);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching KPI details.", e);
            return -1;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int checkForKpiIdUsingIdentifier(String identifier, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.checkForKpiIdUsingIdentifier(identifier);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching KPI id.", e);
            return -1;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<InstanceKpiAttributeThresholdBean> checkGroupKpiCompInstanceMapping(int instanceId, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.checkGroupKpiCompInstanceMapping(instanceId);
        } catch (Exception e) {
            logger.error("Error while fetching group KPIs mapped to component instance [{}]", instanceId, e);
            throw new ControlCenterException(String.format("Error while fetching group KPIs mapped to component instance [%s]", instanceId));
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<InstKpiAttrPersistenceSuppressionBean> fetchGroupKpiCompInstanceMapping(int instanceId, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.fetchGroupKpiCompInstanceMapping(instanceId);
        } catch (Exception e) {
            logger.error("Error while fetching group KPIs mapped to component instance [{}]", instanceId, e);
            throw new ControlCenterException(String.format("Error while fetching group KPIs mapped to component instance [%s]", instanceId));
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<InstanceKpiAttributeThresholdBean> checkKpiCompInstanceMapping(int instanceId, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.checkKpiCompInstanceMapping(instanceId);
        } catch (Exception e) {
            logger.error("Error while fetching KPIs mapped to component instance [{}]", instanceId, e);
            throw new ControlCenterException(String.format("Error while fetching KPIs mapped to component instance [%s]", instanceId));
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<InstKpiAttrPersistenceSuppressionBean> fetchKpiCompInstanceMapping(int instanceId, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.fetchKpiCompInstanceMapping(instanceId);
        } catch (Exception e) {
            logger.error("Error while fetching KPIs mapped to component instance [{}]", instanceId, e);
            throw new ControlCenterException(String.format("Error while fetching KPIs mapped to component instance [%s]", instanceId));
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public KpiBean fetchKpiUsingKpiId(int id, int accountId, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.fetchKpiUsingKpiId(id, accountId);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching KPI details. Reason: {}", e.getMessage());
            return null;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public KpiCategoryMapping getCategoryDetailsForKpi(Integer kpiId) throws ControlCenterException {
        KPIDataDao dao = getDaoConnection(null, KPIDataDao.class);
        try {
            return dao.getCategoryDetailsForKpi(kpiId);
        } catch (Exception e) {
            logger.error("Exception while getting Category for KPI." + e.getMessage(), e);
            throw new ControlCenterException("Exception while getting Category Details for KPI.");
        } finally {
            closeDaoConnection(null, dao);
        }
    }

    public int checkForGroupKpiUsingName(String name, int accountId, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.checkForGroupKpiUsingName(name, accountId);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching KPI details. Reason: {}", e.getMessage());
            return -1;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int addKpiCount(int accountId, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.getKpiCount(accountId);
        } catch (Exception e) {
            logger.error("Error occurred while getting count from mst_kpi_details", e);
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
        return 0;
    }

    public int addComputedKpi(ComputedKpiBean computedKpiBean, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.addComputedKpi(computedKpiBean);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching KPI details. Reason: {}", e.getMessage());
            return -1;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int[] addInstanceKpiAttributeLevelThresholds(List<InstanceKpiAttributeThresholdBean> thresholdBeans, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.addInstanceKpiAttributeLevelThresholds(thresholdBeans);
        } catch (Exception e) {
            logger.error("Encountered exception while adding KPI threshold details.", e);
            return null;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int[] addInstanceKpiAttributeLevelPersistSuppress(List<InstKpiAttrPersistenceSuppressionBean> thresholdBeans, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.addInstanceKpiAttributeLevelPersistSuppress(thresholdBeans);
        } catch (Exception e) {
            logger.error("Encountered exception while adding KPI threshold details.", e);
            return null;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }
    public int[] deleteInstanceKpiAttributeLevelPersistSuppress(List<InstKpiAttrPersistenceSuppressionBean> thresholdBeans, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.deleteInstanceKpiAttributeLevelPersistSuppress(thresholdBeans);
        } catch (Exception e) {
            logger.error("Encountered exception while adding KPI threshold details.", e);
            return null;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int[] updateInstanceKpiAttributeLevelThresholds(List<InstanceKpiAttributeThresholdBean> thresholdBeans, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.updateInstanceKpiAttributeLevelThresholds(thresholdBeans);
        } catch (Exception e) {
            logger.error("Encountered exception while updating KPI threshold details.", e);
            return null;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int[] updateInstanceKpiAttributeLevelThresholdSeverityAndGenAnomaly(List<InstanceKpiAttributeThresholdBean> thresholdBeans, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.updateInstanceKpiAttributeLevelThresholdSeverityAndGenAnomaly(thresholdBeans);
        } catch (Exception e) {
            logger.error("Encountered exception while updating KPI threshold details.", e);
            return null;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int[] deleteInstanceKpiAttributeLevelThresholds(List<InstanceKpiAttributeThresholdBean> thresholdBeans, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.deleteInstanceKpiAttributeLevelThresholds(thresholdBeans);
        } catch (Exception e) {
            logger.error("Encountered exception while deleting KPI threshold details.", e);
            return null;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int[] updateInstanceKpiAttributePersistenceSuppression(List<InstKpiAttrPersistenceSuppressionBean> thresholdBeans, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.updateInstanceKpiAttributePersistenceSuppression(thresholdBeans);
        } catch (Exception e) {
            logger.error("Encountered exception while updating KPI threshold details.", e);
            return null;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<InstanceKpiAttributeThresholdBean> fetchCompInstanceKpiAttrThresholds(int instanceId, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.fetchCompInstanceKpiAttrThresholds(instanceId);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching KPI details.", e);
            return Collections.emptyList();
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<InstKpiAttrPersistenceSuppressionBean> fetchCompInstanceKpiPerSupValuesForCompInstance(int instanceId, int accountId) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(null, KPIDataDao.class);
        try {
            return kpiDataDao.fetchCompInstanceKpiPerSupValuesForCompInstance(instanceId, accountId);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching KPI persistence-suppression details for component instance [{}]", instanceId, e);
            throw new ControlCenterException("Error while retrieving persistence-suppression configurations for KPIs");
        } finally {
            closeDaoConnection(null, kpiDataDao);
        }
    }


    public List<InstKpiAttrPersistenceSuppressionBean> fetchCompInstanceKpiPerSupValuesForCompInstanceKpi(int instanceId, int kpiId, int accountId) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(null, KPIDataDao.class);
        try {
            return kpiDataDao.fetchCompInstanceKpiPerSupValuesForCompInstanceKpi(instanceId, kpiId, accountId);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching KPI persistence-suppression details for component instance [{}]", instanceId, e);
            throw new ControlCenterException("Error while retrieving persistence-suppression configurations for KPIs");
        } finally {
            closeDaoConnection(null, kpiDataDao);
        }
    }

    public List<CompInstanceKpiGroupDetailsBean> getNonGroupKpiListForCompInstance(int instanceId, int kpiId) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(null, KPIDataDao.class);
        try {
            return kpiDataDao.getNonGroupKpiListForCompInstance(instanceId, kpiId);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching non-group KPIs for component instance [{}]", instanceId, e);
            throw new ControlCenterException("Error while fetching non-group KPIs mapped to the component instance");
        } finally {
            closeDaoConnection(null, kpiDataDao);
        }
    }

    public List<CompInstanceKpiGroupDetailsBean> getGroupKpiListForCompInstance(int instanceId, int groupKpiId) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(null, KPIDataDao.class);
        try {
            return kpiDataDao.getGroupKpiListForCompInstance(instanceId, groupKpiId);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching group KPIs for component instance [{}]", instanceId, e);
            throw new ControlCenterException("Error while fetching group KPIs mapped to the component instance");
        } finally {
            closeDaoConnection(null, kpiDataDao);
        }
    }

    public void deleteCompInstanceThresholdDetails(int instanceId) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(null, KPIDataDao.class);
        try {
            kpiDataDao.deleteCompInstanceThresholdDetails(instanceId);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching group KPIs for component instance [{}]", instanceId, e);
            throw new ControlCenterException("Error while fetching group KPIs mapped to the component instance");
        } finally {
            closeDaoConnection(null, kpiDataDao);
        }
    }

    public int[] mapComputedKpiToMstKpi(List<ComputedKpiToKpiMapping> computedKpiBean, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.mapComputedKpiToMstKpi(computedKpiBean);
        } catch (Exception e) {
            logger.error("Encountered exception while mapping computed KPIs to parent KPI", e);
            return null;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int[] mapExistingComputedKpiToMstKpi(List<ComputedKpiToKpiMapping> computedKpiBean, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.mapExistingComputedKpiToMstKpi(computedKpiBean);
        } catch (Exception e) {
            logger.error("Encountered exception while mapping computed KPIs to parent KPI", e);
            return null;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int getComputedDetailsId(int computedKpiId, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.getComputedDetailsId(computedKpiId);
        } catch (Exception e) {
            logger.error("Encountered exception while mapping computed KPIs to parent KPI", e);
            return -1;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int[] updateKpiDetails(List<KpiBean> kpiBeans, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.updateKpiDetails(kpiBeans);
        } catch (Exception e) {
            logger.error("Encountered exception while updating KPI details. ", e);
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }

        return null;
    }

    public int[] updateKpiCategoryMapping(List<TagMappingDetails> tagMappingDetails, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.updateKpiCategoryMapping(tagMappingDetails);
        } catch (Exception e) {
            logger.error("Encountered exception while updating KPI category mapping details. ", e);
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }

        return null;
    }

    public int[] updateDefaultCollectionIntervalForKPI(List<CompVersionKpiMappingBean> compVersionKpiMappingBeans, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.updateDefaultCollectionIntervalForKPI(compVersionKpiMappingBeans);
        } catch (Exception e) {
            logger.error("Encountered exception while updating default collection interval for KPI. ", e);
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }

        return null;
    }

    public List<Integer> getComputedKpiMapping(int computedKpiId, int accountId, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.getComputedKpiMapping(computedKpiId, accountId);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching KPI Ids mapped to computed KPI [{}]", computedKpiId, e);
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }

        return Collections.emptyList();
    }

    public int deleteComputedKpiMapping(int computedKpiId, int accountId, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.deleteComputedKpiMapping(computedKpiId, accountId);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching KPI Ids mapped to computed KPI [{}]", computedKpiId, e);
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }

        return -1;
    }

    public ComputedKpiBean fetchComputedKpiFormula(int computedKpiId, int accountId, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.fetchComputedKpiFormula(computedKpiId, accountId);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching KPI Ids mapped to computed KPI [{}]", computedKpiId, e);
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }

        return null;
    }

    public int[] statusChangeInstanceLevelNonGroupKpi(List<KpiBean> kpiBeans, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.statusChangeInstanceLevelNonGroupKpi(kpiBeans);
        } catch (Exception e) {
            logger.error("Encountered exception while updating non-group KPI status at instance level", e);
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
        return null;
    }

    public int[] statusChangeInstanceLevelGroupKpi(List<KpiBean> kpiBeans, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.statusChangeInstanceLevelGroupKpi(kpiBeans);
        } catch (Exception e) {
            logger.error("Encountered exception while updating group KPI status at instance level", e);
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }

        return null;
    }

    public int[] updateComputedKpiFormula(List<ComputedKpiBean> computedKpiBeans, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.updateComputedKpiFormula(computedKpiBeans);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching KPI Ids mapped to computed KPIs in bulk", e);
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }

        return null;
    }

    public List<KpiListBean> getKpiList(int accountId, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.getKpiDetails(accountId);
        } catch (Exception e) {
            logger.error("Error occurred while getting count from mst_kpi_details", e);
            throw new ControlCenterException("Error while fetching list of KPIs for provided account");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<ComponentBean> getComponentDetailsForAccount(int accountId, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.getComponentDetailsForAccount(accountId);
        } catch (Exception e) {
            logger.error("Error occurred while getting data from component", e);
            throw new ControlCenterException("Error while fetching components for provided account");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<ComputedKpiBean> getComputedKpiDetails(int accountId, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.getComputedKpiDetails(accountId);
        } catch (Exception e) {
            logger.error("Error occurred while getting details from computed kpi", e);
            throw new ControlCenterException("Error while getting computed KPI details for provided account");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<ComputedKpiBean> getComputedExpression(int accountId, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.getComputedExpression(accountId);
        } catch (Exception e) {
            logger.error("Error occurred while getting details from computed kpi", e);
            throw new ControlCenterException("Error while getting computed KPI details for provided account");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<ComputedKpiBean> getComputationExpressions(Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.getComputationExpressions();
        } catch (Exception e) {
            logger.error("Error occurred while getting details from computed kpi", e);
            throw new ControlCenterException("Error while getting computed KPI details for provided account");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int getKpiId(int accountId, String kpiIdentifier) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(null, KPIDataDao.class);
        try {
            int kpiId = kpiDataDao.getKpiId(kpiIdentifier, accountId);
            if (kpiId == 0) {
                throw new ServerException("Invalid KPI Identifier");
            }
            return kpiId;
        } catch (Exception e) {
            logger.error("Kpi identifier : [{}] not found for AccountId [{}]", kpiIdentifier, accountId);
            throw new ControlCenterException("Invalid KPI Identifier");
        } finally {
            closeDaoConnection(null, kpiDataDao);
        }
    }

    public List<Integer> getComponentIdsForKPI(int kpiId, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.getComponentIdsForKPI(kpiId);
        } catch (Exception e) {
            logger.error("Error occurred while fetching the components mapped to the KPI.", e);
            throw new ControlCenterException("Error occurred while fetching the components mapped to the KPI.");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public void updateCompVersionKpiMappingStatus(List<CompVersionKpiMappingBean> statusModifyCompLevelList, Handle handle)
            throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            kpiDataDao.updateCompVersionKpiMappingStatus(statusModifyCompLevelList);
        } catch (Exception e) {
            logger.error("Encountered exception while updating KPI status at component level. ", e);
            throw new ControlCenterException("Encountered exception while updating KPI status at component level.");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public void updateCompInstanceGroupKpiMappingStatus(List<CompVersionKpiMappingBean> statusModifyCompInstanceLevelList,
                                                        Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            kpiDataDao.updateCompInstanceGroupKpiMappingStatus(statusModifyCompInstanceLevelList);
        } catch (Exception e) {
            logger.error("Encountered exception while updating group KPI status at instance level. ", e);
            throw new ControlCenterException("Encountered exception while updating group KPI status at instance level.");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public void updateCompInstanceNonGroupKpiMappingStatus(List<CompVersionKpiMappingBean> statusModifyCompInstanceLevelList,
                                                           Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            kpiDataDao.updateCompInstanceNonGroupKpiMappingStatus(statusModifyCompInstanceLevelList);
        } catch (Exception e) {
            logger.error("Encountered exception while updating non group KPI status at instance level. ", e);
            throw new ControlCenterException("Encountered exception while updating non group KPI status at instance level.");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int getKPICountMappedToComputedKpi(int kpiId, int accountId, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.kpiMappedToComputedKpi(kpiId, accountId);
        } catch (Exception e) {
            logger.error("Encountered exception while getting the KPI mapping to computed KPI. ", e);
            throw new ControlCenterException("Encountered exception while getting the KPI mapping to computed KPI.");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<CountBean> getKPIsMappedToComputedKpi(int accountId, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.kpisMappedToComputedKpi(accountId);
        } catch (Exception e) {
            logger.error("Encountered exception while getting the KPI mapping to computed KPI. ", e);
            throw new ControlCenterException("Encountered exception while getting the KPI mapping to computed KPI.");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int getGroupKpiDiscovery(int groupKpiId, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.getGroupKpiDiscovery(groupKpiId);
        } catch (Exception e) {
            logger.error("Encountered exception while getting discovery status for group KPI", e);
            throw new ControlCenterException("Encountered exception while getting discovery status for group KPI");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int[] addInstanceKpiMaintenanceStatus(List<KpiMaintenanceStatusBean> kpiMaintenanceStatusBeans, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.addInstanceKpiMaintenanceStatus(kpiMaintenanceStatusBeans);
        } catch (Exception e) {
            logger.error("Encountered exception while adding KPI threshold details.", e);
            return null;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int[] updateInstanceKpiMaintenanceStatus(List<KpiMaintenanceStatusBean> kpiMaintenanceStatusBeans, Handle handle) {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.updateInstanceKpiMaintenanceStatus(kpiMaintenanceStatusBeans);
        } catch (Exception e) {
            logger.error("Encountered exception while adding KPI threshold details.", e);
            return null;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<KpiMaintenanceStatusBean> fetchGroupKpiCompInstMapping(int instanceId, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.fetchGroupKpiCompInstMapping(instanceId);
        } catch (Exception e) {
            logger.error("Error while fetching group KPIs mapped to component instance [{}]", instanceId, e);
            throw new ControlCenterException(String.format("Error while fetching group KPIs mapped to component instance [%s]", instanceId));
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<KpiMaintenanceStatusBean> fetchKpiCompInstMapping(int instanceId, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.fetchKpiCompInstMapping(instanceId);
        } catch (Exception e) {
            logger.error("Error while fetching KPIs mapped to component instance [{}]", instanceId, e);
            throw new ControlCenterException(String.format("Error while fetching KPIs mapped to component instance [%s]", instanceId));
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<KpiMaintenanceStatusBean> fetchCompInstanceKpiMaintenanceStatus(int instanceId, int accountId) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(null, KPIDataDao.class);
        try {
            return kpiDataDao.fetchCompInstanceKpiMaintenanceStatus(instanceId, accountId);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching KPI persistence-suppression details for component instance [{}]", instanceId, e);
            throw new ControlCenterException("Error while retrieving persistence-suppression configurations for KPIs");
        } finally {
            closeDaoConnection(null, kpiDataDao);
        }
    }
    
    

    public void updateInstanceKpiAttributeName(InstanceKpiAttributeThresholdBean thresholdBean, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            kpiDataDao.updateInstanceKpiAttributeNames(thresholdBean);
        } catch (Exception e) {
            logger.error("Error while updating attribute names for thresholds", e);
            throw new ControlCenterException("Error while updating attribute names for thresholds");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public void deleteInstanceKpiAttributeName(InstanceKpiAttributeThresholdBean thresholdBean, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            kpiDataDao.deleteInstanceKpiAttributeName(thresholdBean);
        } catch (Exception e) {
            logger.error("Error while deleting attribute names for thresholds", e);
            throw new ControlCenterException("Error while deleting attribute names for thresholds");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }
    public String getGroupKpiIdentifier(int kpiId) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(null, KPIDataDao.class);
        try {
            return kpiDataDao.getGroupKpiIdentifier(kpiId);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching group KPI identifier details for kpiId: [{}]", kpiId, e);
            throw new ControlCenterException("Error while retrieving identifier for the group KPI");
        } finally {
            closeDaoConnection(null, kpiDataDao);
        }
    }
    public List<Integer> getBaseKpiIds(int accountId, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.getBaseKpiIds(accountId);
        } catch (Exception e) {
            logger.error("Encountered exception while getting baseKpiId for the account", e);
            throw new ControlCenterException("Encountered exception while getting baseKpiId for the account");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public void updateInstanceKpiSeverity(int severity, List<KpiMaintenanceStatusBean> beans, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            kpiDataDao.updateInstanceKpiSeverity(severity, beans);
        } catch (Exception e) {
            logger.error("Encountered exception while updating severity details.", e);
            throw new ControlCenterException("Error while updating severity");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public void updateGroupInstanceKpiAnomaly(int notification, List<KpiMaintenanceStatusBean> beans, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            kpiDataDao.updateGroupInstanceKpiAnomaly(notification, beans);
        } catch (Exception e) {
            logger.error("Encountered exception while updating group notification details", e);
            throw new ControlCenterException("Error while updating notification");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public void updateNonGroupInstanceKpiAnomaly(int notification, List<KpiMaintenanceStatusBean> beans, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            kpiDataDao.updateNonGroupInstanceKpiAnomaly(notification, beans);
        } catch (Exception e) {
            logger.error("Encountered exception while updating nongroup notification details.", e);
            throw new ControlCenterException("Error while updating notification for component instance");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public void updateStatusForCompInstanceKpiThresholds(List<InstanceKpiAttributeThresholdBean> instanceKpiAttributeThresholdBeans, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            kpiDataDao.updateStatusInstanceKpiThresholds(instanceKpiAttributeThresholdBeans);
        } catch (Exception e) {
            logger.error("Encountered exception while updating status for kpi thresholds instance {} - details.", instanceKpiAttributeThresholdBeans, e);
            throw new ControlCenterException("Error while updating for component instance");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<IdPojo> getKpisForComponent(int key, Handle handle) throws DataProcessingException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.getKpisForComponent(key);
        } catch (Exception e) {
            logger.error("Encountered exception while getting kpis for component {} - details.",key);
            throw new DataProcessingException("Error while updating for component instance");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<ProducerMapping> getMappingsByKpiDetailsIds(List<Integer> kpiIds, Handle handle) throws ControlCenterException {
        KPIDataDao kpiDataDao = getDaoConnection(handle, KPIDataDao.class);
        try {
            return kpiDataDao.getMappingsByKpiDetailsIds(kpiIds);
        } catch (Exception e) {
            logger.error("Encountered exception while getting  by kpiIds: [{}] - details.",kpiIds, e);
            throw new ControlCenterException("Error while getting for ProducerMapping");
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public void deleteInstanceKpiAttributeLevelPersistSuppressByGroupID(InstKpiAttrPersistenceSuppressionBean instKpiAttrPersistenceSuppressionBean, Handle conn) {
        KPIDataDao kpiDataDao = getDaoConnection(conn, KPIDataDao.class);
        try {
            kpiDataDao.deleteInstanceKpiAttributeLevelPersistSuppressByGroupId(instKpiAttrPersistenceSuppressionBean);
        } catch (Exception e) {
            logger.error("Encountered exception while deleting attribute value in KPI persistence suppression configuration.", e);
        } finally {
            closeDaoConnection(conn, kpiDataDao);
        }
    }

    public void updateInstanceKpiAttributeValueInPersistSuppress(InstKpiAttrPersistenceSuppressionBean instKpiAttrPersistenceSuppressionBean, Handle conn) {
        KPIDataDao kpiDataDao = getDaoConnection(conn, KPIDataDao.class);
        try {
            kpiDataDao.updateInstanceKpiAttributeValueInPersistSuppress(instKpiAttrPersistenceSuppressionBean);
        } catch (Exception e) {
            logger.error("Encountered exception while upating attribute value in KPI persistence suppression configuration.", e);
        } finally {
            closeDaoConnection(conn, kpiDataDao);
        }
    }

}