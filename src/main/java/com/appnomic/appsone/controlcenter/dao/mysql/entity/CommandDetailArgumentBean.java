package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import java.sql.Timestamp;

import static com.appnomic.appsone.controlcenter.common.Constants.MST_SUB_TYPE_COMMAND_LINE;
import static com.appnomic.appsone.controlcenter.common.Constants.MST_TYPE_SCRIPT_PARAM_TYPE;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommandDetailArgumentBean {

    private int id;
    @NonNull
    private int commandId;
    @NonNull
    private String argumentKey;
    @NonNull
    private String argumentValue;
    private String defaultValue;
    private int argumentTypeId;
    @NonNull
    private int argumentValueTypeId;
    private String userDetails;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private boolean placeHolder = false;

    @JsonIgnore
    public static CommandDetailArgumentBean getInstance(int commandId, String userId, String key, String value){
        int argValueTypeId = MasterCache.getMstTypeForSubTypeName(
                Constants.MST_TYPE_ATTRIBUTE_TYPE, Constants.MST_SUB_TYPE_TEXT_BOX).getSubTypeId();
        int argTypeId = MasterCache.getMstTypeForSubTypeName(
                MST_TYPE_SCRIPT_PARAM_TYPE, MST_SUB_TYPE_COMMAND_LINE).getSubTypeId();

        return CommandDetailArgumentBean.builder()
                .commandId(commandId)
                .argumentKey(key)
                .argumentValue(value)
                .defaultValue(value)
                .userDetails(userId)
                .createdTime(DateTimeUtil.getCurrentTimestampInGMT())
                .updatedTime(DateTimeUtil.getCurrentTimestampInGMT())
                .argumentTypeId(argTypeId)
                .argumentValueTypeId(argValueTypeId)
                .placeHolder(false)
                .build();
    }

    public static CommandDetailArgumentBean getInstance(int commandId, String userId, String key, String value,String defaultValue,
    int argTypeId,int argValueTypeId){

        return CommandDetailArgumentBean.builder()
                .commandId(commandId)
                .argumentKey(key)
                .argumentValue(value)
                .defaultValue(defaultValue)
                .userDetails(userId)
                .createdTime(DateTimeUtil.getCurrentTimestampInGMT())
                .updatedTime(DateTimeUtil.getCurrentTimestampInGMT())
                .argumentTypeId(argTypeId)
                .argumentValueTypeId(argValueTypeId)
                .placeHolder(false)
                .build();
    }
}
