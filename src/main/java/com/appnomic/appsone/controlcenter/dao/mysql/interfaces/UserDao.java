package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.NotificationChoiceBean;
import com.appnomic.appsone.controlcenter.beans.UserAttributesBean;
import com.appnomic.appsone.controlcenter.beans.UserInfoBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserAccessDetails;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.UserDetailsBean;
import com.heal.configuration.pojos.SignalNotificationPreferences;
import com.heal.configuration.pojos.ForensicNotificationPreferences;
import com.heal.configuration.pojos.User;
import com.heal.configuration.pojos.UserTimezone;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;
import org.skife.jdbi.v2.unstable.BindIn;

import java.util.List;
import java.util.Set;

@UseStringTemplate3StatementLocator
public interface UserDao {
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id userId, user_identifier userIdentifier, contact_number contactNumber, email_address emailAddress, user_details_id userDetailsId, " +
            "status, created_time createdTime, updated_time updatedTime, is_timezone_mychoice timeZoneMyChoice, mst_access_profile_id accessProfileId, " +
            "mst_role_id roleId, opt_in_status optInStatus, opt_in_last_request_time optInLastRequestTime FROM user_attributes where user_identifier = :userIdentifier")
    UserAttributesBean getUserAttributes(@Bind("userIdentifier") String userIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from  user_attributes where user_identifier = :userIdentifier")
    void deleteUserFromUserIdentifier(@Bind("userIdentifier") String userIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from user_access_details where user_identifier = :userIdentifier")
    void deleteUserFromUserAccessDetails(@Bind("userIdentifier") String userIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT u.user_identifier id, u.username userName, u.user_details_id updatedBy, a.name userProfile, " +
            "r.name role, u.status, u.updated_time updatedOn FROM user_attributes u, mst_roles r, mst_access_profiles a " +
            "where u.mst_access_profile_id = a.id and u.mst_role_id = r.id and a.mst_role_id = r.id")
    List<UserDetailsBean> getUsers();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT u.id mysqlId, u.user_identifier id, u.username userName, u.mst_access_profile_id profileId, " +
            "u.mst_role_id roleId, u.status, a.access_details accessDetailsJSON, email_address emailId, contact_number contactNumber, " +
            "u.is_timezone_mychoice isTimezoneMychoice, u.is_notifications_timezone_mychoice isNotificationsTimezoneMychoice " +
            "FROM user_attributes u, user_access_details a " +
            "where u.user_identifier = a.user_identifier and u.username = :userName")
    UserInfoBean getUserDetails(@Bind("userName") String userName);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT u.id mysqlId, u.user_identifier id, u.username userName, u.mst_access_profile_id profileId, " +
            "u.mst_role_id roleId, u.status, a.access_details accessDetailsJSON, email_address emailId, " +
            "u.to_emails toEmails, u.cc_emails ccEmails, u.recipients_enabled recipientsEnabled, contact_number contactNumber, " +
            "u.is_timezone_mychoice isTimezoneMychoice, u.is_notifications_timezone_mychoice isNotificationsTimezoneMychoice, " +
            "und.is_sms_enabled smsEnabled, und.is_email_enabled emailEnabled, un.FIRST_NAME firstName, un.LAST_NAME lastName, " +
            "und.notification_preference_id notificationPreferenceId " +
            "FROM user_attributes u, user_access_details a, user_notifications_details und, appsonekeycloak.USER_ENTITY un " +
            "where und.applicable_user_id=u.user_identifier and u.user_identifier = a.user_identifier " +
            "and u.status = 1 and un.id= u.user_identifier and u.mst_access_profile_id in (2, 3, 5) and (und.is_sms_enabled = 1 or und.is_email_enabled = 1)")
    List<UserInfoBean> getNotificationUsers();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT value FROM a1_installation_attributes where name = 'SetupType'")
    String getSetup();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT value FROM a1_installation_attributes where name = 'DefaultCredentials'")
    String getTemporaryPassword();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @GetGeneratedKeys
    @SqlUpdate("INSERT INTO user_attributes (user_identifier, username, status, mst_access_profile_id, mst_role_id, created_time, " +
            "updated_time, user_details_id, email_address, contact_number, last_login_time) VALUES (:userIdentifier, :userName, :status, " +
            ":accessProfileId, :roleId, :createdTime, :updatedTime, :userDetailsId, :emailAddress, :contactNumber, :lastLoginTime)")
    int addUserAttributes(@BindBean UserAttributesBean user);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("INSERT INTO user_access_details (user_identifier, access_details, created_time, updated_time, user_details_id) " +
            "VALUES (:userIdentifier, :accessDetails, :createdTime, :updatedTime, :userDetailsId);")
    void addUserAccessDetails(@BindBean UserAccessDetails user);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE user_access_details SET access_details = :accessDetails, updated_time = :updatedTime, user_details_id " +
            "= :userDetailsId WHERE user_identifier = :userIdentifier")
    void updateUserAccessDetails(@BindBean UserAccessDetails user);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE user_attributes SET status = :status, mst_access_profile_id = :accessProfileId, mst_role_id = :roleId, " +
            "updated_time = :updatedTime, email_address = :emailAddress, contact_number = :contactNumber, user_details_id =:userDetailsId " +
            "WHERE user_identifier = :userIdentifier")
    void updateUserAttributes(@BindBean UserAttributesBean user);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE user_attributes SET status = :status, user_details_id = :superUserIdentifier, updated_time = :updatedTime " +
            "WHERE user_identifier = :userIdentifier")
    void updateStatusForUser(@Bind("userIdentifier") String userIdentifier, @Bind("superUserIdentifier") String superUserIdentifier,
                             @Bind("status") int status, @Bind("updatedTime") String updatedTime);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE a1_installation_attributes SET value = :setup WHERE name = 'SetupType'")
    void updateSetup(@Bind("setup") String setup);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT count(*) from appsonekeycloak.COMPONENT_CONFIG where NAME='editMode' and VALUE='UNSYNCED'")
    int getAdEditStatusKeycloak();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT  u.user_identifier id, u.created_time createdTime, u.last_login_time lastLoginTime," +
            "  u.is_timezone_mychoice isTimezoneMychoice, u.is_notifications_timezone_mychoice isNotificationsTimezoneMychoice," +
            "  un.FIRST_NAME firstName, un.LAST_NAME lastName, u.username userName, u.email_address emailId" +
            "  FROM user_attributes u, appsonekeycloak.USER_ENTITY un  " +
            "  where u.status = 1 and un.id= u.user_identifier and u.mst_access_profile_id != 1")
    List<UserInfoBean> getActiveUsers();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT  u.user_identifier id, u.created_time createdTime, u.last_login_time lastLoginTime," +
            "  u.is_timezone_mychoice isTimezoneMychoice, u.is_notifications_timezone_mychoice isNotificationsTimezoneMychoice," +
            "  un.FIRST_NAME firstName, un.LAST_NAME lastName, u.username userName, u.email_address emailId" +
            "  FROM user_attributes u, appsonekeycloak.USER_ENTITY un  " +
            "  where u.status = 1 and un.id= u.user_identifier and u.mst_access_profile_id = 1")
    UserInfoBean getSuperAdmin();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE user_attributes SET status = 0, user_details_id = :superUserIdentifier WHERE user_identifier = :userIdentifier")
    void updateUserStatus(@Bind("userIdentifier") String userIdentifier, @Bind("superUserIdentifier") String superUserIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE user_attributes SET last_login_time = :lastLoginTime WHERE user_identifier = :userIdentifier")
    void updateUserLastLoginTime(@Bind("userIdentifier") String userIdentifier, @Bind("lastLoginTime") String lastLoginTime);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id userId, user_identifier userIdentifier, contact_number contactNumber, email_address emailAddress, user_details_id userDetailsId, " +
            "status, created_time createdTime, updated_time updatedTime, is_timezone_mychoice timeZoneMyChoice, mst_access_profile_id accessProfileId, " +
            "mst_role_id roleId, opt_in_status optInStatus, opt_in_last_request_time optInLastRequestTime FROM user_attributes where contact_number = :contactNumber")
    UserAttributesBean getUserAttributesByContact(@Bind("contactNumber") String contactNumber);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE user_attributes SET opt_in_status = :optInStatus, opt_in_last_request_time = :optInLastRequestTime WHERE user_identifier = :userIdentifier")
    void updateUserWhatsappOptIn(@Bind("optInStatus") int optInStatus, @Bind("optInLastRequestTime") String optInLastRequestTime, @Bind("userIdentifier") String userIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id notificationChoiceId, applicable_user_id applicableUserId, component_selection componentSelection, category_ids categoryIds, updated_time," +
            " user_details_id FROM user_notification_choice where applicable_user_id=:applicableUserId")
    NotificationChoiceBean getUserNotificationChoice(@Bind("applicableUserId") String applicableUserId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id userId, user_identifier userIdentifier, contact_number contactNumber, email_address emailAddress,username userName," +
            " user_details_id userDetailsId, status, created_time createdTime, updated_time updatedTime, is_timezone_mychoice timeZoneMyChoice, " +
            "mst_access_profile_id accessProfileId, mst_role_id roleId, last_login_time lastLoginTime, opt_in_status optInStatus," +
            " opt_in_last_request_time optInLastRequestTime FROM user_attributes")
    List<UserAttributesBean> getAllUser();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT u.id userAttributesId, u.user_identifier userDetailsId, u.username userName, email_address emailId, to_emails toEmails, cc_emails ccEmails, " +
            "recipients_enabled recipientsEnabled, contact_number contactNumber, u.status status, u.mst_role_id roleId, u.mst_access_profile_id profileId, " +
            "u.is_timezone_mychoice isTimezoneMychoice, u.is_notifications_timezone_mychoice isNotificationsTimezoneMychoice, " +
            "ifnull(und.notification_preference_id , 0) notificationPreferenceId, ifnull(und.is_email_enabled, 0) emailEnabled, ifnull(und.is_sms_enabled, 0) smsEnabled, " +
            "ifnull(und.is_forensic_enabled, 0)  forensicEnabled, ifnull(und.forensic_notification_suppression, -1) forensicSuppression, u.created_time createdTime, u.updated_time updatedTime " +
            "FROM  user_access_details a, user_attributes u " +
            "LEFT OUTER JOIN user_notifications_details und on und.applicable_user_id=u.user_identifier " +
            "where u.status =:status  and u.user_identifier = a.user_identifier " +
            "and (und.is_sms_enabled = 1 or und.is_email_enabled = 1 or und.is_forensic_enabled=1 or  und.is_forensic_enabled is null) and u.id =:userId")
    User getUserDetailsByUserId(@Bind("status") int status, @Bind("userId") int userId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select applicable_user_id userDetailsId, application_id applicationId,notification_type_id notificationTypeId, signal_type_id signalTypeId, signal_severity_id signalSeverityId, status " +
            "from user_notification_mapping where application_id in (<applicationIds>) and account_id =:accountId and applicable_user_id =:applicableUserId ")
    List<SignalNotificationPreferences> getApplicationWiseUsersByUserIdentifier(@BindIn("applicationIds") List<Integer> applicationIds,
                                                                                @Bind("accountId") int accountId, @Bind("applicableUserId") String applicableUserId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select applicable_user_id userDetailsId, forensic_notification_suppression suppression,status, application_id applicationId " +
            "from user_forensic_notification_mapping where application_id in (<applicationIds>) and account_id =:accountId and applicable_user_id =:applicableUserId")
    List<ForensicNotificationPreferences> getForensicNotificationPreference(@BindIn("applicationIds") List<Integer> applicationIds, @Bind("accountId") int accountId,
                                                                            @Bind("applicableUserId") String applicableUserId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select tz.timeoffset offset, tz.offset_name offsetName from mst_timezone tz, tag_mapping tm " +
            "where tm.tag_key=tz.id and tm.object_ref_table='user_attributes' " +
            "and tm.object_id=:userAttributeId and tm.tag_id=2")
    UserTimezone getUserTimeZone(@Bind("userAttributeId") int userAttributeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT u.id userAttributesId, u.user_identifier userDetailsId, u.username userName, email_address emailId, to_emails toEmails, cc_emails ccEmails, " +
            "recipients_enabled recipientsEnabled, contact_number contactNumber, u.status status, u.mst_role_id roleId, u.mst_access_profile_id profileId, " +
            "u.is_timezone_mychoice isTimezoneMychoice, u.is_notifications_timezone_mychoice isNotificationsTimezoneMychoice, " +
            "ifnull(und.notification_preference_id , 0) notificationPreferenceId, ifnull(und.is_email_enabled, 0) emailEnabled, ifnull(und.is_sms_enabled, 0) smsEnabled, " +
            "ifnull(und.is_forensic_enabled, 0)  forensicEnabled, ifnull(und.forensic_notification_suppression, -1) forensicSuppression " +
            "FROM  user_access_details a, user_attributes u " +
            "LEFT OUTER JOIN user_notifications_details und on und.applicable_user_id=u.user_identifier " +
            "where u.user_identifier = a.user_identifier " +
            "and (und.is_sms_enabled = 1 or und.is_email_enabled = 1 or und.is_forensic_enabled=1 or  und.is_forensic_enabled is null) and u.user_identifier =:userId")
    User getUserDetailsByUserIdentifier(@Bind("userId") String userIdentifier);

}

