package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.ConnectionDetailsBean;
import com.appnomic.appsone.controlcenter.dao.opensearch.entity.ConfigKpiDetails;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ServiceKpiThreshold;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;
import org.skife.jdbi.v2.unstable.BindIn;

import java.util.List;

@UseStringTemplate3StatementLocator
public interface ImportServicesDao {

    @SqlUpdate("INSERT INTO `service_kpi_thresholds`(`account_id`,`service_id`,`kpi_id`,`applicable_to`,`operation_type_id`,`min_threshold`,`max_threshold`," +
            "`created_time`,`updated_time`,`user_details_id`,`kpi_attribute`, `status`, `start_time`,`defined_by`,`user_severity`,`system_severity`) VALUES (:accountId,:serviceId,:kpiId,:applicableTo,:operationTypeId,:minThreshold," +
            ":maxThreshold,:createdTime,:updatedTime,:userDetailsId,:kpiAttribute,:status, :startTime, :definedBy, :userSeverity, :systemSeverity)")
    @GetGeneratedKeys
    int addThreshold(@BindBean ServiceKpiThreshold serviceKpiThreshold);

    @SqlUpdate("INSERT INTO `service_kpi_thresholds`(`account_id`,`service_id`,`kpi_id`,`applicable_to`,`sor_operation_type_id`,`sor_min_threshold`,`sor_max_threshold`," +
            "`created_time`,`updated_time`,`user_details_id`,`kpi_attribute`, `status`, `start_time`,`defined_by`) VALUES (:accountId,:serviceId,:kpiId,:applicableTo,:sorOperationTypeId,:sorMinThreshold," +
            ":sorMaxThreshold,:createdTime,:updatedTime,:userDetailsId,:kpiAttribute,:status, :startTime, :definedBy)")
    @GetGeneratedKeys
    int addSystemThreshold(@BindBean ServiceKpiThreshold serviceKpiThreshold);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT sk.id id,sk.kpi_id kpiId,sk.applicable_to applicableTo,sk.operation_type_id operationType, sk.status status,\n" +
            "\t\tsk.kpi_attribute kpiAttribute,sk.min_threshold minThreshold,sk.max_threshold maxThreshold,sk.created_time updateTime,mk.kpi_type_id kpiTypeId\n" +
            "\t\tFROM service_kpi_thresholds sk join mst_kpi_details mk on sk.kpi_id = mk.id \n" +
            "\t\twhere sk.service_id = :serviceId and sk.account_id = :accountId;")
    List<ConfigKpiDetails> getConfigKpiList(@Bind("serviceId") Integer serviceId, @Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT kpi_id FROM service_kpi_thresholds where service_id=:serviceId and account_id = :accountId  and applicable_to=:applicableTo")
    List<Integer> getConfigKpiList(@Bind("serviceId") Integer serviceId, @Bind("accountId") Integer accountId, @Bind("applicableTo") String applicableTo);

    @SqlUpdate("UPDATE service_kpi_thresholds SET status=0 where kpi_id=:kpiId and service_id=:serviceId " +
            "and account_id= :accountId and kpi_attribute=:kpiAttribute and applicable_to=:applicableTo")
    int disableThreshold(@Bind("accountId") int accountId, @Bind("serviceId") int serviceId, @Bind("kpiId") int kpiId,
                         @Bind("kpiAttribute") String kpiAttribute, @Bind("applicableTo") String applicableTo);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE service_kpi_thresholds SET min_threshold=:minThreshold, max_threshold=:maxThreshold, defined_by = :definedBy," +
            "updated_time=:updatedTime, operation_type_id=:operationTypeId, status=:status, start_time=:startTime ,user_severity= :userSeverity, " +
            "system_severity= :systemSeverity, user_details_id= :userDetailsId" +
            " where id= :id")
    @GetGeneratedKeys
    int updateThreshold(@BindBean ServiceKpiThreshold updateThresholds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("UPDATE service_kpi_thresholds SET sor_min_threshold=:sorMinThreshold,sor_max_threshold=:sorMaxThreshold, defined_by = :definedBy," +
            "updated_time=:updatedTime, sor_operation_type_id=:sorOperationTypeId, status=:status, start_time=:startTime,user_details_id= :userDetailsId" +
            " where id= :id")
    @GetGeneratedKeys
    int updateSystemThreshold(@BindBean ServiceKpiThreshold updateThresholds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id, account_id accountId, service_id serviceId, kpi_id kpiId, " +
            "applicable_to applicableTo, operation_type_id operationTypeId, " +
            "min_threshold minThreshold, max_threshold maxThreshold, created_time createdTime, " +
            "updated_time updatedTime, user_details_id userDetailsId, start_time startTime, " +
            "kpi_attribute kpiAttribute, status status FROM service_kpi_thresholds " +
            "where account_id=:accountId and service_id=:serviceId and kpi_id=:kpiId " +
            "and applicable_to=:applicableTo and kpi_attribute=:kpiAttribute")
    ServiceKpiThreshold getStaticThreshold(@Bind("accountId") int accountId, @Bind("serviceId") int serviceId, @Bind("kpiId") int kpiId,
                                           @Bind("applicableTo") String applicableTo, @Bind("kpiAttribute") String kpiAttribute);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id, account_id accountId , service_id serviceId, kpi_id kpiId, " +
              "applicable_to applicableTo, operation_type_id operationTypeId, " +
              "min_threshold minThreshold, max_threshold maxThreshold, created_time createdTime, " +
              "updated_time updatedTime, user_details_id userDetailsId, start_time startTime, " +
              "kpi_attribute kpiAttribute, status status ,sor_operation_type_id sorOperationTypeId, sor_min_threshold sorMinThreshold," +
            " sor_max_threshold sorMaxThreshold,defined_by definedBy, user_severity userSeverity, system_severity systemSeverity" +
            " FROM service_kpi_thresholds  where service_id = :serviceId  " +
              "and account_id = :accountId")
    List<ServiceKpiThreshold> getAllKpiThresholds(@Bind("accountId") int accountId, @Bind("serviceId") int serviceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id, account_id accountId , service_id serviceId, kpi_id kpiId, " +
            "applicable_to applicableTo, operation_type_id operationTypeId, " +
            "min_threshold minThreshold, max_threshold maxThreshold, created_time createdTime, " +
            "updated_time updatedTime, user_details_id userDetailsId, start_time startTime, " +
            "kpi_attribute kpiAttribute, status status ,sor_operation_type_id sorOperationTypeId, sor_min_threshold sorMinThreshold," +
            " sor_max_threshold sorMaxThreshold,defined_by definedBy, user_severity userSeverity, system_severity systemSeverity" +
            " FROM service_kpi_thresholds  where service_id = :serviceId  " +
            "and account_id = :accountId and (sor_operation_type_id IS NOT NULL or operation_type_id IS NOT NULL)")
    List<ServiceKpiThreshold> getConfiguredStaticThresholds(@Bind("accountId") int accountId, @Bind("serviceId") int serviceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id, source_id sourceId, source_ref_object sourceRefObject, " +
            "destination_id destinationId, destination_ref_object destinationRefObject, " +
            "created_time createdTime, updated_time updatedTime, account_id accountId, " +
            "user_details_id userDetailsId, is_discovery isDiscovery " +
            "FROM connection_details where account_id = :accountId")
    List<ConnectionDetailsBean> getConnectionDetails(@Bind("accountId") int accountId);

    @SqlBatch("INSERT INTO `service_kpi_thresholds`(`account_id`,`service_id`,`kpi_id`,`applicable_to`,`operation_type_id`,`min_threshold`,`max_threshold`," +
            "`created_time`,`updated_time`,`user_details_id`,`kpi_attribute`, `status`, `start_time`,`defined_by`,`user_severity`,`system_severity`) VALUES (:accountId,:serviceId,:kpiId,:applicableTo,:operationTypeId,:minThreshold," +
            ":maxThreshold,:createdTime,:updatedTime,:userDetailsId,:kpiAttribute,:status, :startTime, :definedBy, :userSeverity, :systemSeverity)")
    @GetGeneratedKeys
    int[] addAvailabilityStaticThreshold(@BindBean List<ServiceKpiThreshold> serviceKpiThreshold);

    @SqlBatch("INSERT INTO `service_kpi_thresholds`(`account_id`,`service_id`,`kpi_id`,`applicable_to`,`sor_operation_type_id`,`sor_min_threshold`,`sor_max_threshold`," +
            "`created_time`,`updated_time`,`user_details_id`,`kpi_attribute`, `status`, `start_time`,`defined_by`,`user_severity`,`system_severity`) VALUES (:accountId,:serviceId,:kpiId,:applicableTo,:sorOperationTypeId,:sorMinThreshold," +
            ":sorMaxThreshold,:createdTime,:updatedTime,:userDetailsId,:kpiAttribute,:status, :startTime, :definedBy, :userSeverity, :systemSeverity)")
    @GetGeneratedKeys
    int[] addSystemAvailabilityStaticThreshold(@BindBean List<ServiceKpiThreshold> serviceKpiThreshold);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id, account_id accountId , service_id serviceId, kpi_id kpiId, " +
            "applicable_to applicableTo, operation_type_id operationTypeId, " +
            "min_threshold minThreshold, max_threshold maxThreshold, created_time createdTime, " +
            "updated_time updatedTime, user_details_id userDetailsId, start_time startTime, end_time endTime, " +
            "kpi_attribute kpiAttribute, status status, " +
            "defined_by definedBy, user_severity userSeverity, system_severity systemSeverity " +
            "FROM service_kpi_thresholds " +
            "where account_id = :accountId and applicable_to = :applicableTo and status=1")
    List<ServiceKpiThreshold> getServiceKpiThresholdsByServiceApplicableTo(@Bind("accountId") int accountId, @Bind("applicableTo") String applicableTo);

}
