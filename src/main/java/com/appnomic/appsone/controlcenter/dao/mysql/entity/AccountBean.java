package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.*;

import java.sql.Timestamp;

/**
 * <AUTHOR> on 3/12/19
 */
@Data
@ToString()
@NoArgsConstructor
@AllArgsConstructor
public class AccountBean {
    private int id;
    @NonNull
    private String name;
    private Timestamp createdTime;
    private Timestamp updatedTime;
    private int status;
    @ToString.Exclude
    private String privateKey;
    @ToString.Exclude
    private String publicKey;
    @NonNull
    private String userIdDetails;
    @NonNull
    private String identifier;
}
