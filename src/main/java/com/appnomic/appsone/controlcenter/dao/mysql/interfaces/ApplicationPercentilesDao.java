package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.ApplicationPercentilesBean;
import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.BindBean;
import org.skife.jdbi.v2.sqlobject.SqlBatch;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface ApplicationPercentilesDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select ap.id, ap.account_id accountId, ap.application_id applicationId, ap.created_time createdTime, ap.updated_time updatedTime," +
            " ap.user_details_id userDetailsId, mkd.identifier kpiIdentifier from application_percentiles ap," +
            " mst_kpi_details mkd where ap.account_id = :accountId and ap.application_id = :applicationId and ap.mst_kpi_details_id = mkd.id" +
            " and mkd.status=1")
    List<ApplicationPercentilesBean> getPercentilesForApplication(@Bind("accountId") int accountId, @Bind("applicationId") int applicationId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO `application_percentiles` (account_id, application_id, created_time, updated_time, " +
            "user_details_id, display_name, mst_kpi_details_id) VALUES (:accountId, :applicationId, :createdTime, " +
            ":updatedTime, :userDetailsId, :kpiIdentifier, :kpiId)")
    void addPercentilesForApplication(@BindBean List<ApplicationPercentilesBean> addPercentiles);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("UPDATE `application_percentiles` SET display_name = :kpiIdentifier, mst_kpi_details_id = :kpiId," +
            " updated_time = :updatedTime WHERE id = :id ")
    void updatePercentilesForApplication(@BindBean List<ApplicationPercentilesBean> updatePercentiles);
}
