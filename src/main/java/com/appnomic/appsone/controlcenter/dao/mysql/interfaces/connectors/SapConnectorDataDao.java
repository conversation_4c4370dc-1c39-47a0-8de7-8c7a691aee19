package com.appnomic.appsone.controlcenter.dao.mysql.interfaces.connectors;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.*;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface SapConnectorDataDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into dataadapter_sap.sap_connection(id,file_name,name,value) " +
            "values(:id,:fileName,:name,:value);")
    @GetGeneratedKeys
    int[] addSapConnectionDetails(@BindBean List<SapConnectionBean> sapConnectionBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select kpi_name from dataadapter_sap.heal_kpis")
    List<String> getHealKpis();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update dataadapter_sap.heal_kpis " +
            "Set kpi_name= :kpiName , kpi_identifier= :newHealIdentifier" +
            " where kpi_identifier = :oldHealIdentifier;")
    @GetGeneratedKeys
    void updateHealKpi(@BindBean String kpiName, String newHealIdentifier, String oldHealIdentifier);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update dataadapter_sap.domain_to_heal_kpi_mappings " +
            "Set heal_kpi_id= :healIdentifier " +
            " where src_kpi_id = :srcId;")
    @GetGeneratedKeys
    void updateDomainToHeal(@BindBean String healIdentifier, int srcId);

    @SqlUpdate("delete from dataadapter_sap.sap_connection")
    void deleteSapConnectionDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into dataadapter_sap.sap_kpis(id,kpi_string,kpi_type,kpi_aggregator)" +
            " values(:id,:kpiName,:kpiType,:kpiAggregator);")
    @GetGeneratedKeys
    int[] addSapKpis(@BindBean List<SapKpi> sapKpi);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update dataadapter_sap.prometheus_kpis " +
            "Set kpi_string= : kpiName, kpi_type= :kpiType, kpi_aggregator= :kpiAggregator" +
            " where id = :id;")
    @GetGeneratedKeys
    void updateSapKpis(@BindBean SapKpi sapKpi);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("Select kpi.id id, kpi.kpi_string kpiName, "+
            "kpi.kpi_type kpiType, kpi.kpi_aggregator kpiAggregator  from dataadapter_sap.sap_kpis kpi;")
    @GetGeneratedKeys
    List<SapKpi> getSAPKpi();
    
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into dataadapter_sap.sap_instance(id,name,heal_name,file_name)" +
            " values(:id,:instanceName,:healName,:fileName);")
    @GetGeneratedKeys
    int[] addSapInstanceDetails(@BindBean List<SapInstanceBean> sapInstanceBean);

    @SqlUpdate("delete from dataadapter_sap.sap_instance")
    void deleteSapInstanceDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into dataadapter_sap.sap_instance_functional_module_mapping(sap_instance_id,functional_module_id)" +
            " values(:sapInstanceId,:functionalModuleId);")
    @GetGeneratedKeys
    int[] addSapInstanceFunctionalModuleMapping(@BindBean List<SapInstanceFunctionalModuleMapping> moduleMapping);

    @SqlUpdate("delete from dataadapter_sap.sap_instance_functional_module_mapping")
    void deleteSapInstanceFunctionalModuleMapping();

    @SqlUpdate("update dataadapter_sap.adapter_chain_config set connectorDataTemplateId=:connectorId where id=:chainConfigId;")
    @GetGeneratedKeys
    int updateConnectorDataIdInAdapterChainConfig(@Bind("connectorId") Integer connectorId , @Bind("chainConfigId") Integer chainConfigId);

}
