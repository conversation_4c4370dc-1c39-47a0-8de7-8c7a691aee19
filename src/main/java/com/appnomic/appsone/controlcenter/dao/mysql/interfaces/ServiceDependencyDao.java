package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.ConnectionDetailsBean;
import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.BindBean;
import org.skife.jdbi.v2.sqlobject.SqlBatch;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface ServiceDependencyDao {
    @SqlBatch("insert into connection_details ( source_id, source_ref_object, destination_id, destination_ref_object, created_time, updated_time, account_id, user_details_id, is_discovery) values ( :sourceId, :sourceRefObject, :destinationId, :destinationRefObject, :createdTime, :updatedTime, :accountId, :userDetailsId, :isDiscovery)")
    int[] addConnection(@BindBean List<ConnectionDetailsBean> connectionDetailsBeanList);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,source_id sourceId, source_ref_object sourceRefObject, destination_id destinationId, destination_ref_object destinationRefObject, created_time createdTime, updated_time updatedTime, account_id accountId, user_details_id userDetailsId, is_discovery isDiscovery from connection_details where account_id=:accountId")
    List<ConnectionDetailsBean> getServiceConnectionsForAccount(@Bind("accountId") Integer accountId);
}
