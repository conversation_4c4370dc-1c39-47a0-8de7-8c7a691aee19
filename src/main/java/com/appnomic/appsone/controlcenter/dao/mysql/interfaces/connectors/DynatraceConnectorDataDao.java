package com.appnomic.appsone.controlcenter.dao.mysql.interfaces.connectors;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.DynatraceEntityBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.DynatraceEntityMetricMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.DynatraceMetricBean;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;
@UseStringTemplate3StatementLocator
public interface DynatraceConnectorDataDao {
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO `dataadapter_dynatrace`.`entity` (`id`, `name`, `identifier`)" +
            " VALUES (:id,:name,:identifier);")
    @GetGeneratedKeys
    int[] addDynatraceEntitys(@BindBean List<DynatraceEntityBean> dynatraceEntityBeans);

    @SqlUpdate("delete from dataadapter_dynatrace.entity")
    void deleteDynatraceEntities();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO `dataadapter_dynatrace`.`metrix` (`name`, `identifier`, `resolution`) " +
            "VALUES (:name,:identifier,:resolution);")
    @GetGeneratedKeys
    int[] addDynatraceMetrics(@BindBean List<DynatraceMetricBean> dynatraceMetricBeans);

    @SqlUpdate("delete from dataadapter_dynatrace.metrix")
    void deleteDynatraceMetrics();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO `dataadapter_dynatrace`.`entity_metrix_mapping` (`entity_identifier`, `metrix_identifier`) " +
            "VALUES (:entityIdentifier,:metricIdentifier);")
    @GetGeneratedKeys
    int[] addDynatraceEntityMetricMapping(@BindBean List<DynatraceEntityMetricMapping> dynatraceEntityMetricMappings);

    @SqlUpdate("delete from dataadapter_dynatrace.entity_metrix_mapping")
    void deleteDynatraceEntityMetricMapping();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("Select identifier from dataadapter_dynatrace.metrix")
    List<String> getDynatraceMetricList();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("Select name from dataadapter_dynatrace.metrix")
    List<String> getDynatraceMetricNames();

}
