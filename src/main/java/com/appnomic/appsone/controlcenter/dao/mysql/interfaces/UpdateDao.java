package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.xpt.UpdateBean;
import org.skife.jdbi.v2.sqlobject.BindBean;
import org.skife.jdbi.v2.sqlobject.SqlBatch;

import java.util.List;

public interface UpdateDao {
    //update name and status in transaction only if their provided values are not null
    @SqlBatch("update transaction set name=ifnull(:name, name), status=ifnull(:status, status), " +
            "updated_time=:updateTime, user_details_id=:userDetailsId, monitor_enabled = :monitorEnabled, " +
            "audit_enabled = :auditEnabled where id=:id")
    void updateTransaction(@BindBean List<UpdateBean> updateBeanList);

    //update name and status in comp_instance only if their provided values are not null
    @SqlBatch("update comp_instance set name = ifnull(:name, name), status = ifnull(:status, status), updated_time=:updateTime, user_details_id=:userDetailsId where id=:id")
    void updateCompInstance(@BindBean List<UpdateBean> updateBeanList);

    //update name and is_enabled in rules only if their provided values are not null
    @SqlBatch("update rules set name = ifnull(:name, name), is_enabled = ifnull(:monitorEnabled, is_enabled), updated_time=:updateTime, user_details_id=:userDetailsId, discovery_status = ifnull(:discoveryEnabled, discovery_status) where id=:id")
    void updateRules(@BindBean List<UpdateBean> updateBeanList);
}
