package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserForensicNotificationMappingBean {

    private String applicableUserId;
    private int applicationId;
    private int forensicNotificationSuppression;
    private int accountId;
    private String userDetailsId;
    private int status;
    private Timestamp createdTime;
    private Timestamp updatedTime;
}
