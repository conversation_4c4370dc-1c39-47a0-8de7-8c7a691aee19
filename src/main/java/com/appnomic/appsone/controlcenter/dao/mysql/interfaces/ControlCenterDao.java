package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.ServiceTransactionDetailsBean;
import com.appnomic.appsone.controlcenter.pojo.IdValuePojo;
import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface ControlCenterDao {
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select tm.tag_key from tag_mapping tm,agent a where tm.tag_id=:tagId and tm.object_ref_table=:agentType and tm.account_id = :accountId and a.id = tm.object_id  and a.agent_type_id != :agentTypeId")
    List<Integer> getServiceList(@Bind("tagId") Integer tagId,@Bind("agentType") String agentType,@Bind("accountId") Integer accountId,@Bind("agentTypeId") Integer agentTypeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(1) txnCount from tag_mapping TM join transaction T on TM.object_id = T.id where T.status = 1 and TM.object_ref_table = :transaction and TM.tag_key= :tagKey and TM.tag_id= :tagId and TM.account_id = :accountId")
    int getTotalTransaction(@Bind("tagKey") Integer tagKey,@Bind("transaction") String transaction, @Bind("tagId") Integer tagId, @Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select TM.tag_key id, count(1) value from tag_mapping TM join transaction T" +
            " on TM.object_id = T.id where T.status = 1 and TM.object_ref_table = :transaction and" +
            " TM.tag_id= :tagId and TM.account_id = :accountId group by TM.tag_key")
    List<IdValuePojo> getAllTransaction(@Bind("transaction") String transactionTable, @Bind("tagId") Integer tagId, @Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select T.id,T.name,T.created_time capturedTime,T.updated_time updatedTime,T.identifier,TM.tag_value,T.rule_id ruleId," +
            "R.Name rule,ST.http_url url, T.monitor_enabled monitorEnabled, T.audit_enabled auditEnabled " +
            "from tag_mapping TM join transaction T on TM.object_id = T.id  left outer join rules R on T.rule_id=R.id " +
            "join sub_transactions ST on T.id=ST.transaction_id where T.status = 1 and TM.object_ref_table = :transaction " +
            "and TM.tag_key= :serviceId and TM.tag_id= :tagId and TM.account_id = :accountId")
    List<ServiceTransactionDetailsBean> getTotalServiceTransaction(@Bind("serviceId") String serviceId, @Bind("transaction") String transaction, @Bind("tagId") Integer tagId, @Bind("accountId") Integer accountId);
}
