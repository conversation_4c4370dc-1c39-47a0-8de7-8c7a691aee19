package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.CompInstanceKpiGroupDetailsBean;
import com.appnomic.appsone.controlcenter.pojo.ApplicationInstanceAttributeBean;
import com.heal.configuration.pojos.ApplicationInstanceDetails;
import com.heal.configuration.pojos.ApplicationInstanceKpiEntity;
import org.skife.jdbi.v2.sqlobject.BindBean;
import org.skife.jdbi.v2.sqlobject.GetGeneratedKeys;
import org.skife.jdbi.v2.sqlobject.SqlBatch;
import org.skife.jdbi.v2.sqlobject.SqlUpdate;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface ApplicationInstanceDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("INSERT INTO application_instance " +
            "(name, identifier, status, account_id, application_id, " +
            "mst_component_id, mst_component_version_id, mst_common_version_id, " +
            "mst_component_type_id, created_time, updated_time, user_details_id) " +
            "VALUES (:name, :identifier, :status, :accountId, :applicationId, " +
            ":componentId, :componentVersionId, :commonVersionId, " +
            ":componentTypeId, :createdTime, :updatedTime, :lastModifiedBy)")
    @GetGeneratedKeys
    int insertApplicationInstance(@BindBean ApplicationInstanceDetails applicationInstanceDetails);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO application_instance_kpi_details " +
            "(application_instance_id, mst_producer_kpi_mapping_id, collection_interval, " +
            "status, created_time, updated_time, user_details_id, mst_kpi_details_id, " +
            "mst_producer_id, notification) " +
            "VALUES (:applicationInstanceId, :producerKpiMappingId, :collectionInterval, " +
            ":status, :createdTime, :updatedTime, :userDetailsId, :id, :producerId, :notificationsEnabled)")
    void insertApplicationInstanceKpiDetailsList(@BindBean List<ApplicationInstanceKpiEntity> applicationInstanceKpiEntities);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO application_instance_kpi_group_details " +
            "(application_instance_id, mst_producer_kpi_mapping_id, collection_interval, " +
            "status, created_time, updated_time, user_details_id, mst_kpi_details_id, " +
            "mst_producer_id, notification, mst_kpi_group_id, kpi_group_name, is_discovery, " +
            "attribute_value, attribute_status, alias_name) " +
            "VALUES (:compInstanceId, :mstProducerKpiMappingId, :collectionInterval, " +
            ":status, :createdTime, :updatedTime, :userDetailsId, :id, :mstProducerId, " +
            ":notification, :mstKpiGroupId, :kpiGroupName, :isDiscovery, :attributeValue, " +
            ":attributeStatus, :aliasName)")
    void insertApplicationInstanceGroupKpiDetailsList(@BindBean List<CompInstanceKpiGroupDetailsBean> serviceInstanceGroupKpiDetailsList);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO application_instance_attribute_values " +
            "(attribute_value, application_instance_id, mst_component_attribute_mapping_id, " +
            "created_time, updated_time, user_details_id, mst_common_attributes_id, attribute_name) " +
            "VALUES (:attributeValue, :applicationInstanceId, :componentAttributeMappingId, " +
            ":createdTime, :updatedTime, :userDetailsId, :mstCommonAttributesId, :attributeName)")

    void insertApplicationInstanceAttribute(@BindBean List<ApplicationInstanceAttributeBean> applicationInstanceAttributeBean);


}
