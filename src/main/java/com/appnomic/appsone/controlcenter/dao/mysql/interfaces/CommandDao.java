package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandDetailArgumentBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandDetailsBean;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.Date;
import java.util.List;

public interface CommandDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,identifier,command_name commandName,timeout_in_secs timeOutInSecs," +
            "output_type_id outputTypeId,command_type_id commandTypeId, user_details_id userDetails," +
            " created_time createdTime, updated_time updatedTime, action_id actionId, producer_type_id producerTypeId, suppression" +
            " from command_details where id= :id")
    CommandDetailsBean getCommandDetail(@Bind("id") int id);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(*) from command_details where identifier=:identifier")
    int checkIfCommandExists(@Bind("identifier") String identifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select c.id, c.name, c.identifier, c.command_name commandName, c.timeout_in_secs timeOutInSecs," +
            "c.output_type_id outputTypeId,c.command_type_id commandTypeId, c.user_details_id userDetails, " +
            "c.created_time createdTime, c.updated_time updatedTime,cm.is_default isDefault, c.producer_type_id producerTypeId " +
            "from command_details c, agent_commands_mapping cm " +
            "where cm.agent_type_id = :agentTypeId and cm.command_id = c.id and c.name!='SelfHeal'")
    List<CommandDetailsBean> getCommandDetailsByAgentType(@Bind("agentTypeId") int agentTypeId);

    @SqlUpdate("INSERT INTO command_details (name, identifier, command_type_id, command_name, " +
            "timeout_in_secs, output_type_id, created_time, updated_time, user_details_id, action_id, producer_type_id) VALUES " +
            "( :name, :identifier, :commandTypeId, :commandName, :timeOutInSecs, :outputTypeId, " +
            ":createdTime, :updatedTime, :userDetails, :actionId, :producerTypeId)")
    @GetGeneratedKeys
    int addCommandDetails(@BindBean CommandDetailsBean command);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id id, command_id commandId, argument_key argumentKey, argument_value " +
            "argumentValue, default_value defaultValue, argument_value_type_id argumentValueTypeId, " +
            "argument_type_id argumentTypeId, created_time createdTime, updated_time updatedTime, " +
            "user_details_id userDetails, is_placeholder placeHolder from command_arguments " +
            "where command_id = :commandId")
    List<CommandDetailArgumentBean> getCommandArguments(@Bind("commandId") int commandId);

    @SqlBatch("INSERT INTO command_arguments (command_id, argument_key, argument_value, " +
            "default_value, argument_value_type_id, argument_type_id, created_time, updated_time, " +
            "user_details_id, is_placeholder) VALUES (:commandId, :argumentKey, :argumentValue, " +
            ":defaultValue, :argumentValueTypeId, :argumentTypeId, :createdTime, :updatedTime, " +
            ":userDetails, :placeHolder)")
    @GetGeneratedKeys
    int[] addCommandArguments(@BindBean List<CommandDetailArgumentBean> argumentBeans);

    @SqlBatch("UPDATE command_arguments SET argument_value = :argumentValue, default_value " +
            "= :argumentValue, updated_time = :updatedTime, user_details_id = :userDetails " +
            "  where command_id = :commandId and argument_key = :argumentKey")
    void updateCommandArguments(@BindBean List<CommandDetailArgumentBean> argumentBeans);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,identifier,command_name commandName,timeout_in_secs timeOutInSecs," +
            "output_type_id outputTypeId,command_type_id commandTypeId, user_details_id userDetails, " +
            "action_id actionId, producer_type_id producerTypeId, signature, suppression from command_details")
    List<CommandDetailsBean> getCommandDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,identifier,command_name commandName,timeout_in_secs timeOutInSecs," +
            "output_type_id outputTypeId,command_type_id commandTypeId, user_details_id userDetails, " +
            "action_id actionId, producer_type_id producerTypeId from command_details where identifier=:commandIdentifier")
    CommandDetailsBean getAgentVersionCommandDetails(@Bind("commandIdentifier") String commandIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,identifier,command_name commandName,timeout_in_secs timeOutInSecs," +
            "output_type_id outputTypeId,command_type_id commandTypeId, user_details_id userDetails, " +
            "action_id actionId, producer_type_id producerTypeId from command_details where id=:commandId")
    CommandDetailsBean getConnectorCommandDetails(@Bind("commandId") int commandId);

    @SqlUpdate("DELETE FROM command_arguments WHERE command_id = :commandId")
    void deleteCommandArgs(@Bind("commandId") int commandId);

    @SqlUpdate("DELETE FROM command_details WHERE id = :commandId")
    void deleteCommand(@Bind("commandId") int commandId);

    @SqlUpdate("UPDATE physical_agent SET last_status_id =:commandState, is_command_executed=1, user_details_id=:userIdentifier, " +
            "updated_time=:updatedTime WHERE last_job_id=:commandJobId and identifier=:physicalAgentIdentifier")
    int updateCommandStatus(@Bind("commandState") int commandState, @Bind("physicalAgentIdentifier") String physicalAgentIdentifier,
                             @Bind("commandJobId") String commandJobId, @Bind("userIdentifier") String userIdentifier, @Bind("updatedTime") Date updatedTime);

    @SqlUpdate("UPDATE physical_agent SET is_command_executed=1, user_details_id=:userIdentifier, updated_time=:updatedTime " +
            "WHERE last_job_id=:commandJobId and identifier=:physicalAgentIdentifier")
    int updateCommandFailureStatus(@Bind("physicalAgentIdentifier") String physicalAgentIdentifier, @Bind("commandJobId") String commandJobId,
                                    @Bind("userIdentifier") String userIdentifier, @Bind("updatedTime") Date updatedTime);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,identifier,command_name commandName,timeout_in_secs timeOutInSecs," +
            "output_type_id outputTypeId,command_type_id commandTypeId, user_details_id userDetails, " +
            "action_id actionId, producer_type_id producerTypeId, signature, suppression from command_details where command_type_id=93")
    List<CommandDetailsBean> getCommandDetailsByForensicId();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select c.id, c.name, c.identifier, c.command_name commandName, c.timeout_in_secs timeOutInSecs," +
            "c.output_type_id outputTypeId,c.command_type_id commandTypeId, c.user_details_id userDetails, " +
            "c.created_time createdTime, c.updated_time updatedTime,cm.is_default isDefault, c.producer_type_id producerTypeId, c.suppression " +
            "from command_details c, agent_commands_mapping cm " +
            "where cm.agent_type_id = :agentTypeId and cm.command_id = c.id and c.command_type_id = :typeId")
    List<CommandDetailsBean> getForensicCommandDetailsByAgentType(@Bind("agentTypeId") int agentTypeId, @Bind("typeId") int forensicTypeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id id, command_id commandId, argument_key argumentKey, argument_value " +
            "argumentValue, default_value defaultValue, argument_value_type_id argumentValueTypeId, " +
            "argument_type_id argumentTypeId, created_time createdTime, updated_time updatedTime, " +
            "user_details_id userDetails, is_placeholder placeHolder FROM command_arguments")
    List<CommandDetailArgumentBean> getAllCommandArguments();
}
