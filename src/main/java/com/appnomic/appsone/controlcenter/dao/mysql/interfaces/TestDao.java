package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;


import org.skife.jdbi.v2.sqlobject.SqlUpdate;
import org.skife.jdbi.v2.sqlobject.customizers.Define;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.unstable.BindIn;

import java.util.List;

@UseStringTemplate3StatementLocator
public interface TestDao {

    @SqlUpdate("delete from <table> where id in (<ids>)")
    void  deleteEntry(@Define("table") String table, @BindIn("ids") List<Integer> ids);
}
