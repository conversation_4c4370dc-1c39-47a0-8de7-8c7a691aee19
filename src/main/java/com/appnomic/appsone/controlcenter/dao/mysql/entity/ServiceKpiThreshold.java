package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.*;

import java.sql.Timestamp;

/**
 * <AUTHOR> sri<PERSON>
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class  ServiceKpiThreshold
{
    private int id;
    @EqualsAndHashCode.Include
    private Integer accountId;
    @EqualsAndHashCode.Include
    private Integer serviceId;
    @EqualsAndHashCode.Include
    private Integer kpiId;
    private Integer operationTypeId;
    private int status;
    private Double minThreshold;
    private Double maxThreshold;
    @EqualsAndHashCode.Include
    private String applicableTo;
    private String userDetailsId;
    private String kpiAttribute;
    private Timestamp createdTime;
    private Timestamp updatedTime ;
    private Integer sorOperationTypeId;
    private Double sorMinThreshold;
    private Double sorMaxThreshold;
    private String definedBy;
    private Timestamp startTime ;
    private int userSeverity;
    private int systemSeverity;
    private Timestamp endTime;
}
