package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompInstanceMaintenanceMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.MaintenanceDetails;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ServiceMaintenanceMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.MaintenanceWindowDao;
import com.appnomic.appsone.controlcenter.pojo.RecurringBean;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;

public class MaintenanceDataService extends AbstractDaoService<MaintenanceWindowDao> {

    private static final Logger logger = LoggerFactory.getLogger(MaintenanceDataService.class);

    public MaintenanceDetails getMaintenanceWindowDetails(int maintenanceId) {
        MaintenanceWindowDao maintenanceWindowDao = getDaoConnection(null, MaintenanceWindowDao.class);
        try {
            return maintenanceWindowDao.getMaintenanceWindowDetails(maintenanceId);
        } catch (Exception e) {
            logger.error("Exception while retrieving the Maintenance Window List", e);
        } finally {
            closeDaoConnection(null, maintenanceWindowDao);
        }
        return null;
    }

    public int updateMaintenanceWindowDetails(MaintenanceDetails maintenanceDetails, Handle handle) {
        MaintenanceWindowDao maintenanceWindowDao = getDaoConnection(handle, MaintenanceWindowDao.class);
        try {
            return maintenanceWindowDao.updateMaintenanceWindowDetails(maintenanceDetails);
        } catch (Exception e) {
            logger.error("Exception while retrieving the Maintenance Window List", e);
        } finally {
            closeDaoConnection(handle, maintenanceWindowDao);
        }
        return 0;
    }

    public int updateRecurringWindowDetails(RecurringBean recurringBean, int maintenanceId, Handle handle) {
        MaintenanceWindowDao maintenanceWindowDao = getDaoConnection(handle, MaintenanceWindowDao.class);
        try {
            return maintenanceWindowDao.updateRecurringWindowDetails(recurringBean, maintenanceId);
        } catch (Exception e) {
            logger.error("Exception while retrieving the Maintenance Window List", e);
        } finally {
            closeDaoConnection(handle, maintenanceWindowDao);
        }
        return 0;
    }

    public List<MaintenanceDetails> getInstancesByMaintenanceId(int maintenanceId) {
        MaintenanceWindowDao maintenanceWindowDao = getDaoConnection(null, MaintenanceWindowDao.class);
        try {
            return maintenanceWindowDao.getInstancesByMaintenanceId(maintenanceId);
        } catch (Exception e) {
            logger.error("Exception while retrieving the Maintenance Window List", e);
        } finally {
            closeDaoConnection(null, maintenanceWindowDao);
        }
        return Collections.emptyList();
    }

    public void deleteMaintenanceWindow(MaintenanceDetails maintenanceDetails, Handle handle) {
        MaintenanceWindowDao maintenanceWindowDao = getDaoConnection(handle, MaintenanceWindowDao.class);
        try {
            maintenanceWindowDao.deleteMaintenanceWindow(maintenanceDetails);
        } catch (Exception e) {
            logger.error("Exception while Updating the EndTime of Maintenance Window", e);
        } finally {
            closeDaoConnection(handle, maintenanceWindowDao);
        }
    }

    public void updateOngoingMaintenance(MaintenanceDetails maintenanceDetails, Handle handle) {
        MaintenanceWindowDao maintenanceWindowDao = getDaoConnection(handle, MaintenanceWindowDao.class);
        try {
            maintenanceWindowDao.updateEndTime(maintenanceDetails);
        } catch (Exception e) {
            logger.error("Exception while Updating the EndTime of Maintenance Window", e);
        } finally {
            closeDaoConnection(handle, maintenanceWindowDao);
        }
    }

    public void deleteCompInstanceMaintenanceMapping(MaintenanceDetails maintenanceDetails, Handle handle) {
        MaintenanceWindowDao maintenanceWindowDao = getDaoConnection(handle, MaintenanceWindowDao.class);
        try {
            maintenanceWindowDao.deleteCompInstanceMaintenanceMapping(maintenanceDetails);
        } catch (Exception e) {
            logger.error("Exception while Updating the EndTime of Maintenance Window", e);
        } finally {
            closeDaoConnection(handle, maintenanceWindowDao);
        }
    }

    public void deleteServiceMaintenanceMapping(MaintenanceDetails maintenanceDetails, Handle handle) {
        MaintenanceWindowDao maintenanceWindowDao = getDaoConnection(handle, MaintenanceWindowDao.class);
        try {
            maintenanceWindowDao.deleteServiceMaintenanceMapping(maintenanceDetails);
        } catch (Exception e) {
            logger.error("Exception while Updating the EndTime of Maintenance Window", e);
        } finally {
            closeDaoConnection(handle, maintenanceWindowDao);
        }
    }

    public List<MaintenanceDetails> getMaintenanceDetailsByServiceId(int serviceId, int accountId) {
        MaintenanceWindowDao maintenanceWindowDao = getDaoConnection(null, MaintenanceWindowDao.class);
        try {
            return maintenanceWindowDao.getMaintenanceDetailsByServiceId(serviceId, accountId);
        } catch (Exception e) {
            logger.error("Exception while retrieving getMaintenanceWindowsByServiceId", e);
        } finally {
            closeDaoConnection(null, maintenanceWindowDao);
        }
        return Collections.emptyList();
    }

    public List<ServiceMaintenanceMapping> getMaintenanceWindowsByServiceId(int serviceId) {
        MaintenanceWindowDao maintenanceWindowDao = getDaoConnection(null, MaintenanceWindowDao.class);
        try {
            return maintenanceWindowDao.getMaintenanceWindowsByServiceId(serviceId);
        } catch (Exception e) {
            logger.error("Exception while retrieving getMaintenanceWindowsByServiceId", e);
        } finally {
            closeDaoConnection(null, maintenanceWindowDao);
        }
        return Collections.emptyList();
    }

    public List<CompInstanceMaintenanceMapping> getMaintenanceWindowsByCompInstanceId(int instanceId) {
        MaintenanceWindowDao maintenanceWindowDao = getDaoConnection(null, MaintenanceWindowDao.class);
        try {
            return maintenanceWindowDao.getMaintenanceWindowsByCompInstanceId(instanceId);
        } catch (Exception e) {
            logger.error("Exception while retrieving getMaintenanceWindowsByCompInstanceId", e);
        } finally {
            closeDaoConnection(null, maintenanceWindowDao);
        }
        return Collections.emptyList();
    }

    public RecurringBean getRecurringDetails(int maintenanceId) {
        MaintenanceWindowDao maintenanceWindowDao = getDaoConnection(null, MaintenanceWindowDao.class);
        try {
            return maintenanceWindowDao.getRecurringDetails(maintenanceId);
        } catch (Exception e) {
            logger.error("Exception while Updating the EndTime of Maintenance Window", e);
        } finally {
            closeDaoConnection(null, maintenanceWindowDao);
        }
        return null;
    }

    public void deleteRecurringDetails(int recurringId, Handle handle) {
        MaintenanceWindowDao maintenanceWindowDao = getDaoConnection(handle, MaintenanceWindowDao.class);
        try {
            maintenanceWindowDao.deleteRecurringDetails(recurringId);
        } catch (Exception e) {
            logger.error("Exception while Updating the EndTime of Maintenance Window", e);
        } finally {
            closeDaoConnection(handle, maintenanceWindowDao);
        }
    }

}
