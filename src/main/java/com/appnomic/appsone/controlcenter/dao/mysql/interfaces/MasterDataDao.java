package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.*;
import com.appnomic.appsone.controlcenter.pojo.*;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;
import org.skife.jdbi.v2.unstable.BindIn;

import java.util.List;

@UseStringTemplate3StatementLocator
public interface MasterDataDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id id, name name, status status, created_time createdTime, updated_time updatedTime, user_details_id userDetailsId from mst_page_actions where status = 1")
    List<MasterPageActionBean> getPageActionsMasterData();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery(" select id id, name name, identifier identifier, description description, ui_visible uiVisible, status status, dashboard_name dashboardName, created_time createdTime, updated_time updatedTime, user_details_id userDetailsId from mst_big_features where status=1")
    List<MasterBigFeatureBean> getBigFeaturesMasterData();

    //Fetch Component master data
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select vc.component_id id,vc.component_name name,vc.is_custom isCustom,vc.component_status status, " +
            "mc.created_time createdTime,mc.updated_time updatedTime,mc.user_details_id userDetailsId, " +
            "mc.account_id accountId,mc.description description, mc.discovery_pattern discoveryPattern, vc.component_type_name componentTypeName, " +
            "vc.component_version_name componentVersionName, vc.component_version_id componentVersionId, " +
            "vc.component_type_id componentTypeId, vc.common_version_name commonVersionName, " +
            "vc.common_version_id commonVersionId from view_components vc, mst_component mc " +
            "where vc.component_id = mc.id and mc.account_id in (1,:accountId)")
    List<MasterComponentBean> getComponentMasterData(@Bind("accountId") int accountId);

    //Fetch Component master type data
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,description,is_custom isCustom,status,created_time createdTime,updated_time updatedTime," +
            "user_details_id userDetailsId, account_id accountId from mst_component_type where account_id in (1, :accountId)")
    List<MasterComponentTypeBean> getMasterComponentTypesData(@Bind("accountId") int accountId);

    //Fetch Component master type mapping data
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,mst_component_id mstComponentId,mst_component_type_id mstComponentTypeId,created_time  createdTime,user_details_id  userDetailsId,account_id accountId " +
            "from mst_component_mapping where account_id in (1, :accountId)")
    List<MasterComponentMappingBean> getMasterComponentMappingData(@Bind("accountId") int accountId);

    //Fetch Component master version data
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id id ,name name,is_custom  isCustom,status status,version_from  versionFrom,version_to  versionTo,mst_common_version_id  mstCommonVersionId," +
            "mst_component_id  mstComponentId,created_time  createdTime,updated_time  updatedTime,user_details_id  userDetailsId,account_id  accountId from mst_component_version " +
            "where name = :mstCompVersionName and mst_component_id = :mstCompId and account_id in (1,:accountId)")
    MasterComponentVersionBean getMasterComponentVersionData(@Bind("mstCompId") int mstCompId, @Bind("mstCompVersionName") String mstCompVersionName, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id id ,name name,is_custom  isCustom,status status,version_from  versionFrom,version_to  versionTo,mst_common_version_id  mstCommonVersionId," +
            "mst_component_id  mstComponentId,created_time  createdTime,updated_time  updatedTime,user_details_id  userDetailsId,account_id  accountId, auto_discovery_regex autoDiscoveryRegex from mst_component_version")
    List<MasterComponentVersionBean> getAllMasterComponentVersionData();

    //Fetch host Component instances data
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,status,host_id  hostId,is_dr  isDR,is_cluster  isCluster,mst_component_version_id  mstComponentVersionId,created_time  createdTime,updated_time  updatedTime,user_details_id  userDetailsId,account_id  accountId,mst_component_id  mstComponentId,mst_component_type_id  mstComponentTypeId,discovery,host_address  hostAddress,identifier,mst_common_version_id  mstCommonVersionId from comp_instance where mst_component_type_id = :mstCompTypeId and host_address = :hostAddress and is_cluster = 0 and account_id = :accountId")
    ComponentInstanceBean getHostsData(@Bind("hostAddress") String hostAddress, @Bind("mstCompTypeId") int mstCompTypeId, @Bind("accountId") int accountId);

    //Fetch host id
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from mst_component_type where name = :hostName and account_id = 1")
    int getHostsId(@Bind("hostName") String hostName);

    //Fetch Component instances data
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,status,host_id  hostId,is_dr  isDR,is_cluster  isCluster,mst_component_version_id  mstComponentVersionId,created_time  createdTime,updated_time  updatedTime,user_details_id  userDetailsId,account_id  accountId,mst_component_id  mstComponentId,mst_component_type_id  mstComponentTypeId,discovery,host_address  hostAddress,identifier,mst_common_version_id  mstCommonVersionId from comp_instance where (name = :name or  identifier = :name) and account_id = :accountId")
    ComponentInstanceBean getCompInstForAccountComInstName(@Bind("name") String name, @Bind("accountId") int accountId);

    //Fetch Component instances data for account
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select c.id, c.name, c.status, c.host_id hostId, c.is_dr isDR, c.is_cluster isCluster, c.mst_component_version_id mstComponentVersionId, " +
            "c.created_time createdTime, c.updated_time updatedTime, c.user_details_id userDetailsId, c.account_id accountId, c.mst_component_id mstComponentId," +
            "m.name mstComponentName, c.mst_component_type_id mstComponentTypeId, c.discovery, c.host_address hostAddress, c.identifier, " +
            "c.mst_common_version_id mstCommonVersionId from comp_instance c, mst_component m where c.account_id = :accountId and m.id = c.mst_component_id")
    List<ComponentInstanceBean> getCompInstForAccount(@Bind("accountId") int accountId);

    //Fetch attributes from view
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mst_component_attribute_mapping_id  mstComponentAttributeMappingId,mst_common_version_id  mstCommonVersionId,mst_component_id  mstComponentId,mst_common_name  mstCommonName,attribute_id  attributeId,attribute_name  attributeName,is_custom  isCustom,status,is_mandatory  isMandatory,default_value  defaultValue from view_attributes where mst_common_version_id = :mstCommonVersionId")
    List<AttributesViewBean> getAttributeViewData(@Bind("mstCommonVersionId") int mstCommonVersionId);

    //Fetch common version kpi from view
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mst_component_id  mstComponentId,kpi_id  kpiId,kpi_name  kpiName,kpi_identifier  kpiIdentifier, mst_common_version_id  mstCommonVersionId," +
            "kpi_group_id  kpiGroupId,kpi_type_id  kpiTypeId,default_collection_interval  defaultCollectionInterval,status,default_operation_id  defaultOperationId," +
            "default_threshold  defaultThreshold,account_id  accountId from view_common_version_kpis where mst_common_version_id = :mstCommonVersionId and kpi_group_id = 0 and account_id in (1, :accountId)")
    List<ViewCommonVersionKPIsBean> getViewCommonVersionNonGroupKPIsData(@Bind("mstCommonVersionId") int mstCommonVersionId, @Bind("accountId") int accountId);

    //Fetch common version kpi from view
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select DISTINCT mst_component_id as mstComponentId,kpi_id as kpiId,kpi_name as kpiName,kpi_identifier  kpiIdentifier,mst_common_version_id  mstCommonVersionId," +
            "kpi_group_id  kpiGroupId,kpi_type_id  kpiTypeId,default_collection_interval  defaultCollectionInterval,status,default_operation_id  defaultOperationId," +
            "default_threshold  defaultThreshold,account_id  accountId from view_common_version_kpis where mst_common_version_id = :mstCommonVersionId and not kpi_group_id = 0 and account_id in (1, :accountId)")
    List<ViewCommonVersionKPIsBean> getViewCommonVersionGroupKPIsData(@Bind("mstCommonVersionId") int mstCommonVersionId, @Bind("accountId") int accountId);

    //Fetch producer kpi mapping data from view
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select producer_id  producerId,producer_name  producerName,is_custom  isCustom,status,account_id  accountId,is_kpi_group  isKpiGroup,kpi_type  kpiType,producer_type  producerType,classname," +
            "is_default  isDefault,mst_kpi_details_id  mstKpiDetailsId,mst_component_version_id  mstComponentVersionId,kpi_name  kpiName,mst_component_id  mstComponentId,mst_component_type_id  mstComponentTypeId," +
            "mst_producer_kpi_mapping_id  mstProducerKpiMappingId from view_producer_kpis where mst_kpi_details_id = :mstKpiDetailId and mst_component_version_id = :mstCompVersionId and mst_component_id = :mstCompId " +
            "and mst_component_type_id = :mstCompTypeId and is_kpi_group = 0 and is_default = 1 and account_id in (1, :accountId)")
    ViewProducerKPIsBean getViewProducerNonGroupKPIsData(@Bind("mstKpiDetailId") int mstKpiDetailId, @Bind("mstCompVersionId") int mstCompVersionId, @Bind("mstCompId") int mstCompId,
                                                         @Bind("mstCompTypeId") int mstCompTypeId, @Bind("accountId") int accountId);

    //Fetch producer kpi mapping data from view
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select producer_id  producerId,producer_name  producerName,is_custom  isCustom,status,account_id  accountId,is_kpi_group  isKpiGroup,kpi_type  kpiType,producer_type  producerType," +
            "classname,is_default  isDefault,mst_kpi_details_id  mstKpiDetailsId,mst_component_version_id  mstComponentVersionId,kpi_name  kpiName,mst_component_id  mstComponentId," +
            "mst_component_type_id  mstComponentTypeId,mst_producer_kpi_mapping_id  mstProducerKpiMappingId from view_producer_kpis where mst_kpi_details_id = :mstKpiDetailId " +
            "and mst_component_version_id = :mstCompVersionId and mst_component_id = :mstCompId and mst_component_type_id = :mstCompTypeId and not is_kpi_group = 0 and is_default = 1 and account_id in (1, :accountId)")
    ViewProducerKPIsBean getViewProducerGroupKPIsData(@Bind("mstKpiDetailId") int mstKpiDetailId, @Bind("mstCompVersionId") int mstCompVersionId, @Bind("mstCompId") int mstCompId,
                                                      @Bind("mstCompTypeId") int mstCompTypeId, @Bind("accountId") int accountId);

    //Fetch get master kpi details data from view
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,description,data_type  dataType,is_custom  isCustom,status,kpi_type_id  kpiTypeId,measure_units  measureUnits,cluster_operation  clusterOperation,created_time  createdTime," +
            "updated_time  updatedTime,user_details_id  userDetailsId,account_id  accountId,kpi_group_id  kpiGroupId,identifier  identifier,value_type  valueType from mst_kpi_details " +
            "where id = :kpiId and account_id in (1, :accountId)")
    MasterKPIDetailsBean getMasterKPIDetailsData(@Bind("kpiId") int kpiId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,tag_id tagId,object_id objectId,object_ref_table objectRefTable,tag_key tagKey,tag_value tagValue,created_time createdTime,updated_time updatedTime,account_id accountId,user_details_id userDetailsId from tag_mapping where object_ref_table != 'transaction' and account_id = :account_id")
    List<TagMappingDetails> getTagMappingDetails(@Bind("account_id") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,tag_id tagId,object_id objectId,object_ref_table objectRefTable,tag_key tagKey,tag_value tagValue,created_time createdTime,updated_time updatedTime,account_id accountId,user_details_id userDetailsId from tag_mapping where object_ref_table = 'comp_instance' and account_id = :account_id and tag_id = :tag_id and object_id in (<clusterIds>)")
    List<TagMappingDetails> getTagMappingDetailsForCompInstanceClusters(@Bind("account_id") int accountId, @Bind("tag_id") int tagId, @BindIn("clusterIds") List<Integer> clusterIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,source_id sourceId,source_ref_object sourceRefObject,destination_id destinationId,destination_ref_object destinationRefObject," +
            "account_id accountId,user_details_id userDetailsId from connection_details where account_id in (1, :account_id)")
    List<ConnectionDetails> getConnectionDetails(@Bind("account_id") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,tag_type_id tagTypeId,is_predefined isPredefined,ref_table refTable,created_time createdTime,updated_time updatedTime,account_id accountId," +
            "user_details_id userDetailsId,ref_where_column_name refWhereColumnName,ref_select_column_name refSelectColumnName from tag_details where name = :name and account_id in (1, :accountId)")
    TagDetailsBean getTagDetails(@Bind("name") String name, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,tag_type_id tagTypeId,is_predefined isPredefined,ref_table refTable," +
            "created_time createdTime,updated_time updatedTime,account_id accountId,user_details_id userDetailsId," +
            "ref_where_column_name refWhereColumnName,ref_select_column_name refSelectColumnName " +
            "from tag_details")
    List<TagDetailsBean> getTagDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,mst_type_id mstTypeId,created_time createdTime,updated_time updatedTime,user_details_id userDetailsId,account_id accountId,description,is_custom isCustom,status from mst_sub_type where id = :sub_type_id")
    MasterSubTypeBean getMasterSubTypeForId(@Bind("sub_type_id") int mstSubTypeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,comp_instance_id compInstanceId,mst_producer_kpi_mapping_id mstProducerKpiMappingId,collection_interval collectionInterval,status,created_time createdTime," +
            "updated_time updatedTime,user_details_id userDetailsId,mst_kpi_details_id mstKpiDetailsId,mst_producer_id mstProducerId,notification from comp_instance_kpi_details where comp_instance_id = :compInstId")
    List<CompInstanceKpiDetailsBean> getNonGroupKpiListForAGivenCompInst(@Bind("compInstId") Integer compInstId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,attribute_value attributeValue,status,created_time createdTime,updated_time updatedTime,user_details_id userDetailsId,comp_instance_id compInstanceId," +
            "mst_producer_kpi_mapping_id mstProducerKpiMappingId,collection_interval collectionInterval,mst_kpi_details_id mstKpiDetailsId,is_discovery isDiscovery,kpi_group_name kpiGroupName," +
            "mst_kpi_group_id mstKpiGroupId,mst_producer_id mstProducerId,notification from comp_instance_kpi_group_details where comp_instance_id = :compInstId and attribute_status = 1")
    List<CompInstanceKpiGroupDetailsBean> getGroupKpiListForAGivenCompInst(@Bind("compInstId") Integer compInstId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,attribute_value attributeValue,comp_instance_id compInstanceId,mst_component_attribute_mapping_id mstComponentAttributeMappingId,created_time createdTime,updated_time updatedTime," +
            "user_details_id userDetailsId,mst_common_attributes_id mstCommonAttributesId,attribute_name attributeName from comp_instance_attribute_values where comp_instance_id = :compInstId")
    List<CompInstanceAttributesBean> getAttributeListForAGivenCompInst(@Bind("compInstId") Integer compInstId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id instanceId,common_version_id commonVersionId, common_version_name commonVersionName,mst_component_id compId,component_name componentName," +
            "mst_component_type_id mstComponentTypeId,component_type_name componentTypeName," +
            "mst_component_version_id compVersionId,component_version_name componentVersionName,name instanceName,host_id hostId,status," +
            "host_name hostName,is_cluster isCluster,identifier, host_identifier hostIdentifier, " +
            "host_address hostAddress, supervisor_id supervisorId from view_component_instance where account_id = :account_id and status=1")
    List<CompInstClusterDetails> getCompInstClusterList(@Bind("account_id") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mst_component_id componentId,mst_common_version_id mstCommonVersionId,kpi_id kpiId,kpi_name kpiName," +
            "kpi_identifier identifier, do_analytics availableForAnalytics from view_common_version_kpis where status = 1")
    List<ComponentKpis> getCompKpis(@Bind("account_id") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,tag_type_id tagTypeId,is_predefined isPredefined,ref_table refTable,created_time createdTime,updated_time updatedTime," +
            "account_id accountId,user_details_id userDetailsId,ref_where_column_name refWhereColumnName,ref_select_column_name refSelectColumnName " +
            "from tag_details where account_id in (1, :account_id)")
    List<TagDetailsBean> getTagDetailsForAccount(@Bind("account_id") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select kpiid kpiId,kpi_name kpiName,measure_units kpiUnits,cluster_operation " +
            "clusterOperation,kpi_type kpiType,ifnull(group_id,0) groupId,group_name groupName," +
            "ifnull(discovery,0) isDiscovery,ifnull(group_status,0) groupStatus, rollup_operation " +
            "rollupOperation, cluster_aggregation_type clusterAggType, instance_aggregation_type instanceAggType, " +
            "is_informative isInfo, identifier identifier, is_custom isCustom, value_type valueType, data_type dataType," +
            "cron_expression cronExpression, delta_per_sec deltaPerSec, reset_delta_value resetDeltaValue " +
            "from view_all_kpis where kpi_status = 1")
    List<AllKpiList> getAllKpis();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,comp_instance_id compInstanceId,cluster_id clusterId from component_cluster_mapping where account_id =:accountId")
    List<ClusterInstanceMapping> getClusterInstanceMapping(@Bind("accountId") int accountId);

    @SqlUpdate("insert into account (name,created_time ,updated_time,status,private_key,public_key,user_details_id,identifier) values (:name,:createdTime,:updatedTime,:status,:privateKey,:publicKey,:userIdDetails,:identifier)")
    @GetGeneratedKeys
    int addAccount(@BindBean AccountBean accountBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id appId,name name,controller_type_id controllerTypeId,identifier identifier, plugin_supr_interval pluginSuppressionInterval, plugin_whitelist_status pluginWhitelisted, " +
            "status status,user_details_id createdBy,created_time createdOn, updated_time updatedTime ,account_id accountId " +
            "from controller where account_id = :account_id and status = 1")
    List<Controller> getControllerList(@Bind("account_id") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id appId,name name,controller_type_id controllerTypeId,identifier identifier, " +
            "status status,user_details_id createdBy,account_id accountId,created_time createdOn, updated_time updatedTime " +
            "from controller")
    List<Controller> getAllControllerList();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from account where name = :accountName")
    int getAccountId(@Bind("accountName") String accountName);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,description,created_time createdTime,updated_time updatedTime,account_id accountId,user_details_id userDetailsId,kpi_type_id kpiTypeId," +
            "discovery,regex,status,is_custom isCustom,identifier identifier from mst_kpi_group where account_id in (1, :account_id)")
    List<MasterKpiGroupBean> getMasterKPIGroupList(@Bind("account_id") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select type typeName, typeid typeId, name subTypeName, subtypeid subTypeId from view_types ")
    List<ViewTypes> getAllTypes();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select account_id accountId, cluster_aggregation_type clusterAggregationType, cluster_operation  clusterOperation, data_type  dataType, discovery   discovery, group_identifier  groupIdentifier, groupid  groupid, groupname groupname, groupstatus groupstatus, identifier  identifier, instance_aggregation_type  instanceAggregationType, is_custom  isCustom, kpiid kpiid, kpiname kpiname, kpistatus kpistatus, kpitype  kpitype, measure_units measureUnits, rollup_operation  rollupOperation, value_type valueType from view_kpi_groups")
    List<ViewKpiGroupsBean> getViewKpiGroups();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, time_zone_id timeZoneId, timeoffset offset, user_details_id userDetailsId, account_id accountId, status status from mst_timezone where status=1")
    List<TimezoneDetail> getAllTimezones();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, type, description, classname, status, parameter_type_id parameterTypeId, producer_table_name producerTableName, " +
            "user_details_id userDetailsId, account_id accountId from mst_producer_type where status=1")
    List<ProducerTypeBean> getProducerTypes();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, time_zone_id timeZoneId, timeoffset offset, user_details_id userDetailsId, account_id accountId, status status from mst_timezone where time_zone_id= :timezoneId and status=1")
    TimezoneDetail getTimezonesById(@Bind("timezoneId") String timezoneId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select category_id id, name, category_identifier categoryId, if(is_workload=1,true,false) isWorkLoad " +
            "from view_kpi_category_details where kpi_id=:kpiId")
    KpiCategoryDetails getCategoryDetailsForKpi(@Bind("kpiId") Integer kpiId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select category_id id, name, category_identifier categoryId, if(is_workload=1,true,false) isWorkLoad, kpi_id kpiId " +
            "from view_kpi_category_details ")
    List<KpiCategoryDetails> getCategoryDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,identifier identifier from  mst_category_details where id=:categoryId and account_id in(:accountId,1)")
    CategoryDetailBean getCategoryDetails(@Bind("accountId") Integer accountId,@Bind("categoryId") Integer categoryId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("insert into mst_component_version_kpi_mapping (mst_kpi_details_id, is_custom, " +
            "do_analytics, mst_common_version_id, created_time, updated_time, user_details_id, " +
            "default_collection_interval, status, mst_component_id, mst_component_type_id, " +
            "default_operation_id, default_threshold) " +
            "values (:kpiDetailsId, :isCustom, :doAnalytics, :commonVersionId, :createdTime, " +
            ":updatedTime, :userDetailsId, :defaultCollectionInterval, :status, :componentId, " +
            ":componentTypeId, :defaultOperationId, :defaultThreshold)")
    @GetGeneratedKeys
    int[] bulkInsertIntoComponentVersionKpiMapping(@BindBean List<CompVersionKpiMappingBean> compVerKpiMap);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("insert into mst_component_version_kpi_mapping (mst_kpi_details_id, is_custom, " +
            "do_analytics, mst_common_version_id, created_time, updated_time, user_details_id, " +
            "default_collection_interval, status, mst_component_id, mst_component_type_id, " +
            "default_operation_id, default_threshold) " +
            "values (:kpiDetailsId, :isCustom, :doAnalytics, :commonVersionId, :createdTime, " +
            ":updatedTime, :userDetailsId, :defaultCollectionInterval, :status, :componentId, " +
            ":componentTypeId, :defaultOperationId, :defaultThreshold)")
    @GetGeneratedKeys
    int insertComponentVersionKpiMapping(@BindBean CompVersionKpiMappingBean compVerKpiMap);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mst_kpi_details_id kpiDetailsId, is_custom isCustom, do_analytics doAnalytics, mst_common_version_id commonVersionId, " +
            "mst_component_id componentId, mst_component_type_id componentTypeId,important_id importantId  from mst_component_version_kpi_mapping where mst_kpi_details_id=:kpiId")
    List<CompVersionKpiMappingBean> getComponentMappingForKpi(@Bind("kpiId") int kpiId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id id, mst_kpi_details_id kpiDetailsId, is_custom isCustom, do_analytics doAnalytics, " +
            "mst_common_version_id commonVersionId, created_time createdTime, updated_time updatedTime," +
            "user_details_id userDetailsId, default_collection_interval defaultCollectionInterval," +
            "status status, mst_component_id componentId, mst_component_type_id componentTypeId," +
            "default_operation_id defaultOperationId, default_threshold defaultThreshold,important_id importantId from mst_component_version_kpi_mapping")
    List<CompVersionKpiMappingBean> getCommonVersionOfComponent();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mcv.mst_common_version_id commonVersionId, mc.id componentId " +
            "from mst_component mc join mst_component_version mcv " +
            "where mc.id = mcv.mst_component_id and mc.name = :componentName and mcv.name = :componentVersion")
    CommonVersionCompIdMapping getCommonVersionOfComponent(@Bind("componentName") String componentName, @Bind("componentVersion") String componentVersion);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select kpiid kpiId,kpi_name kpiName,measure_units kpiUnits,cluster_operation " +
            "clusterOperation,kpi_type kpiType,ifnull(group_id,0) groupId,group_name groupName, group_identifier groupIdentifier," +
            "ifnull(discovery,0) isDiscovery,ifnull(group_status,0) groupStatus, rollup_operation " +
            "rollupOperation, cluster_aggregation_type clusterAggType, instance_aggregation_type " +
            "instanceAggType, identifier identifier, is_custom isCustom from view_all_kpis where identifier" +
            "= :identifier and account_id in (1, :accountId)")
    AllKpiList getKpi(@Bind("identifier") String identifier, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, mst_type_id mstTypeId, created_time createdTime, updated_time updatedTime, user_details_id userDetailsId, account_id accountId, description, is_custom isCustom, status from mst_sub_type")
    List<MasterSubTypeBean> getAllMasterSubTypes();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id instanceId,common_version_id commonVersionId, common_version_name commonVersionName,mst_component_id compId,component_name componentName, " +
            "mst_component_type_id mstComponentTypeId,component_type_name componentTypeName," +
            "mst_component_version_id compVersionId,component_version_name componentVersionName,name instanceName,host_id hostId,status," +
            "host_name hostName,is_cluster isCluster,identifier, host_identifier hostIdentifier, " +
            "host_address hostAddress, supervisor_id supervisorId, forensic_agent_type_id forensicAgentTypeId from view_component_instance where account_id = :accountId and status=1")
    List<CompInstClusterDetails> getCompInstClusterListAgentId(@Bind("accountId") int accountId);
}
