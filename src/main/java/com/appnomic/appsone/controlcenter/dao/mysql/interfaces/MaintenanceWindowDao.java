package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompInstanceMaintenanceMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.MaintenanceDetails;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ServiceMaintenanceMapping;
import com.appnomic.appsone.controlcenter.pojo.RecurringBean;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface MaintenanceWindowDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, name, type_id typeId, account_id accountId, status, start_time startTime, end_time endTime, " +
            "created_time createdTime, updated_time updatedTime, user_details_id userDetails from maintenance_details where id= :id")
    MaintenanceDetails getMaintenanceWindowDetails(@Bind("id") Integer id);

    @SqlUpdate("update maintenance_details set start_time=:startTime, end_time=:endTime, updated_time=:updatedTime, user_details_id=:userDetails where id= :id")
    @GetGeneratedKeys
    int updateMaintenanceWindowDetails(@BindBean MaintenanceDetails maintenanceDetailBean);

    @SqlUpdate("update recurring_details set recurring_type_id=:recurringTypeId, start_hr_min=:startHour, end_hr_min=:endHour, duration=:duration," +
            "recurring_data=:recurringData, user_details_id=:userDetails, updated_time=:updatedTime where maintenance_id=:maintenanceId")
    @GetGeneratedKeys
    int updateRecurringWindowDetails(@BindBean RecurringBean recurringDetailsBean, @Bind("maintenanceId") Integer maintenanceId);

    @SqlUpdate("DELETE FROM maintenance_details WHERE id=:id;")
    void deleteMaintenanceWindow(@BindBean MaintenanceDetails maintenanceDetailBean);

    @SqlUpdate("DELETE FROM service_maintenance_mapping WHERE service_id=:serviceId and maintenance_id=:id;")
    void deleteServiceMaintenanceMapping(@BindBean MaintenanceDetails maintenanceDetailBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,comp_instance_id instanceId, user_details_id userDetailsId, created_time createdTime,"+
            "updated_time updatedTime, account_id accountId from comp_instance_maintenance_mapping where maintenance_id = :maintenanceId")
    List<MaintenanceDetails> getInstancesByMaintenanceId(@Bind("maintenanceId") int maintenanceId);

    @SqlUpdate("UPDATE maintenance_details SET `end_time`=:endTime,`status`=:status, `updated_time`=:updatedTime,`user_details_id`=:userDetails where id = :id")
    void updateEndTime(@BindBean MaintenanceDetails maintenanceDetailBean);

    @SqlUpdate("DELETE FROM comp_instance_maintenance_mapping where comp_instance_id=:instanceId and maintenance_id=:id")
    void deleteCompInstanceMaintenanceMapping(@BindBean MaintenanceDetails maintenanceDetailBean);

    @SqlUpdate("INSERT INTO `maintenance_details`(`name`,`type_id`,`account_id`,`status`,`start_time`,`end_time`,`created_time`,`updated_time`, `user_details_id`, `is_custom`) " +
            "VALUES (:name,:typeId,:accountId,:status,:startTime,:endTime,:createdTime,:updatedTime,:userDetails, :isCustom)")
    @GetGeneratedKeys
    int addMaintenanceWindowDetails(@BindBean MaintenanceDetails maintenanceWindowBean);

    @SqlUpdate("INSERT INTO `recurring_details`(`maintenance_id`,`recurring_type_id`,`start_hr_min`,`end_hr_min`,`duration`," +
            "`recurring_data`,`user_details_id`,`created_time`,`updated_time`) " +
            "VALUES (:maintenanceId,:recurringTypeId,:startHour,:endHour,:duration,:recurringData,:userDetails,:createdTime,:updatedTime)")
    @GetGeneratedKeys
    int addRecurringWindowDetails(@BindBean RecurringBean recurringDetailsBean, @Bind("maintenanceId") Integer maintenanceId);

    @SqlUpdate("DELETE FROM recurring_details WHERE id=:id;")
    void deleteRecurringDetails(@Bind("id") int id);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("Select id , maintenance_id maintenanceId, recurring_type_id recurringTypeId, start_hr_min startHour, end_hr_min endHour, duration," +
            "recurring_data recurringData, user_details_id userDetails, created_time createdTime, updated_time updatedTime " +
            "from recurring_details where maintenance_id=:maintenanceId")
    RecurringBean getRecurringDetails(@Bind("maintenanceId") int maintenanceId);

    @SqlUpdate("INSERT INTO `service_maintenance_mapping`(`service_id`,`maintenance_id`,`user_details_id`,`created_time`,`updated_time`,`account_id`) " +
            "VALUES (:serviceId,:maintenanceId,:userDetails,:createdTime,:updatedTime,:accountId)")
    @GetGeneratedKeys
    int addMaintenanceServiceWindowDetails(@BindBean MaintenanceDetails maintenanceWindowBean, @Bind("maintenanceId") Integer maintenanceId);

    @SqlUpdate("INSERT INTO `comp_instance_maintenance_mapping`(`comp_instance_id`,`maintenance_id`,`user_details_id`,`created_time`,`updated_time`,`account_id`) " +
            "VALUES (:instanceId,:maintenanceId,:userDetails,:createdTime,:updatedTime,:accountId)")
    @GetGeneratedKeys
    int addInstanceMaintenanceWindowDetails(@BindBean MaintenanceDetails maintenanceWindowBean, @Bind("maintenanceId") Integer maintenanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery(" select md.id , md.name, md.type_id typeId , md.account_id accountId , md.status , md.start_time startTime , " +
            "md.end_time endTime , md.created_time createdTime , md.updated_time updatedTime , md.user_details_id userDetails" +
            " from service_maintenance_mapping sm, maintenance_details md where sm.service_id=:serviceId and " +
            "sm.account_id=:accountId and sm.maintenance_id=md.id and md.status = 1")
    List<MaintenanceDetails> getMaintenanceDetailsByServiceId(@Bind("serviceId") int serviceId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,service_id serviceId, maintenance_id maintenanceId, user_details_id userDetailsId, created_time createdTime,"+
            "updated_time updatedTime, account_id accountId from service_maintenance_mapping where service_id = :serviceId")
    List<ServiceMaintenanceMapping> getMaintenanceWindowsByServiceId(@Bind("serviceId") int serviceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,comp_instance_id compInstanceId, maintenance_id maintenanceId, user_details_id userDetailsId, created_time createdTime,"+
            "updated_time updatedTime, account_id accountId from comp_instance_maintenance_mapping where comp_instance_id = :instanceId")
    List<CompInstanceMaintenanceMapping> getMaintenanceWindowsByCompInstanceId(@Bind("instanceId") int instanceId);
}
