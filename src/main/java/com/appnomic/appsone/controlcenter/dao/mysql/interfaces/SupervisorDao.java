package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.ComponentInstanceBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompInstanceKPIDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.SupervisorBean;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface SupervisorDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,host_box_name hostBoxName,identifier identifier," +
            "supervisor_type_id supervisorType,version,host_address hostAddress, mode, status " +
            "from supervisor_details where account_id in(:accountId, 1) ")
    List<SupervisorBean> getAccountWiseSupervisorDetailsWithGlobalAccount(@Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,host_box_name hostBoxName,identifier identifier, supervisor_type_id supervisorType, version, " +
            "host_address hostAddress, mode, account_id accountId, previous_command_job_id previousCommandJobId, status " +
            "from supervisor_details where mode='LOCAL'")
    List<SupervisorBean> getLocalModeSupervisorDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,host_box_name hostBoxName,identifier identifier, supervisor_type_id supervisorType, version, " +
            "host_address hostAddress, mode, account_id accountId from supervisor_details")
    List<SupervisorBean> getAllSupervisorDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,host_box_name hostBoxName,identifier identifier," +
            "supervisor_type_id supervisorType,version,host_address hostAddress, status, mode, account_id accountId " +
            "from supervisor_details where identifier=:identifier ")
    SupervisorBean getSupervisorByIdentifier(@Bind("identifier") String identifier);

    @SqlUpdate("INSERT INTO supervisor_details ( name, host_box_name, identifier, " +
            "supervisor_type_id, created_time, updated_time, user_details_id, version, " +
            "host_address, status, account_id, mode) VALUES ( :name, :hostBoxName, :identifier, " +
            ":supervisorType, :createdTime, :updatedTime, :userId, :version, :hostAddress, " +
            ":status, :accountId, :mode)")
    @GetGeneratedKeys
    int insertSupervisor(@BindBean SupervisorBean supervisorBean);

    @SqlUpdate("UPDATE supervisor_details set name=:name, host_box_name=:hostBoxName, " +
            "host_address=:hostAddress, status=:status, updated_time=:updatedTime, " +
            "user_details_id=:userId, mode=:mode, version=:version where identifier=:identifier")
    void updateSupervisor(@BindBean SupervisorBean supervisorBean);

    @SqlUpdate("delete from supervisor_details where name = :supervisorName")
    void deleteSupervisor(@Bind("supervisorName") String supervisorName);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(*) from command_audit_details  where exit_status=0 and supervisor_identifier=:superVisorIdentifier and command_job_id=:commandJobId")
    int checkCommandDetails(@Bind("superVisorIdentifier") String superVisorIdentifier, @Bind("commandJobId") String commandJobId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,identifier,name from comp_instance where status=1 and account_id=:accountId and host_address=:hostAddress and mst_component_type_id=1")
    ComponentInstanceBean getInstanceIdentifier(@Bind("accountId") int accountId, @Bind("hostAddress") String hostAddress);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select c.comp_instance_id instanceId, p.id producerId, c.mst_kpi_details_id kpiId from comp_instance_kpi_details c, mst_producer p where c.mst_producer_id = p.id and p.status = 1 and c.status =1 and p.name = :producerName")
    List<CompInstanceKPIDetailsBean> getKpiInstanceDetails(@Bind("producerName") String producerName);

    @SqlUpdate("UPDATE supervisor_details set previous_command_job_id=:previousCommandJobId where identifier=:identifier")
    void updateSupervisorJobId(@Bind("previousCommandJobId") String previousCommandJobId,@Bind("identifier") String identifier);

}
