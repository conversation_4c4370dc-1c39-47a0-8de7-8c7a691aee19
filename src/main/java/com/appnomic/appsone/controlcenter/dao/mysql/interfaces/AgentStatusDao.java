package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.AgentBean;
import com.appnomic.appsone.controlcenter.beans.CommandBeans;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandTriggerBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.PhysicalAgentBean;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.Date;
import java.util.List;

public interface AgentStatusDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct comp_instance_id compInstanceId,agent_id id from agent_comp_instance_mapping")
    List<AgentBean> getCompAgentDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct comp_instance_id compInstanceId from agent_comp_instance_mapping where agent_id=:agentId")
    List<Integer> getCompAgentMappings(@Bind("agentId") int agentId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select command_id id from agent_commands_triggered where physical_agent_id=:agentId and command_job_id=:commandJobId")
    CommandBeans getAgentCommandStatus(@Bind("agentId") int agentId,@Bind("commandJobId") String commandJobId);

    @SqlUpdate("INSERT INTO agent_commands_triggered (physical_agent_id,command_id,trigger_time,command_job_id,command_status,user_details_id) " +
            "VALUES (:physicalAgentIdentifier, :commandId, :triggerTime,:commandJobId, :commandStatus, :userDetailsId)")
    @GetGeneratedKeys
    int addCommandTriggerDetails(@BindBean CommandTriggerBean commandTriggeredBean);

    @SqlUpdate("delete from agent_commands_triggered where physical_agent_id = :physicalAgentId and command_job_id = :commandJobId")
    int remCommandTriggerDetails(@Bind("physicalAgentId") int physicalAgentId,@Bind("commandJobId") String commandJobId);

    @SqlUpdate("UPDATE physical_agent SET last_job_id = NULL, last_status_id = NULL," +
            " is_command_executed = NULL WHERE id = :physicalAgentId")
    void updatePhysicalAgentJobStatusCommandExecNULL(@Bind("physicalAgentId") int physicalAgentId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select cd.name lastCommandName,mst.name desiredStat,cd.timeout_in_secs timeoutInSecs,acm.trigger_time triggerTime, " +
            "(select count(*) from agent_commands_triggered act where act.physical_agent_id=:physicalAgentId) as noOfCmds,acm.command_id commandId " +
            "from agent_commands_triggered acm,command_details cd,mst_sub_type mst where\n" +
            " acm.command_job_id=:commandJobId and physical_agent_id=:physicalAgentId\n" +
            " and acm.command_id=cd.id and cd.action_id=mst.id ")
    CommandTriggerBean getAgentCommandTriggerStatus(@Bind("physicalAgentId") int physicalAgentId,@Bind("commandJobId") String commandJobId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select aci.comp_instance_id compInstanceId,aci.agent_id id, ci.account_id accountId from agent_comp_instance_mapping aci, comp_instance ci where aci.agent_id = :agentId and ci.id=aci.comp_instance_id")
    List<AgentBean> getCompInstAgentMapping(@Bind("agentId") int agentId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select identifier from supervisor_details where host_address=:hostAddress")
    List<String> getSupervisorIdentifier(@Bind("hostAddress") String hostAddress);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select command_job_id id from agent_commands_triggered where command_job_id=:commandJobId")
    String validCommandJob(@Bind("commandJobId") String commandJobId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(1) from command_details where id =:commandId")
    int validCommandId(@Bind("commandId") int commandId);

    @SqlUpdate("UPDATE physical_agent SET is_command_executed=0,last_job_id=:commandJobId,user_details_id=:userIdentifier,updated_time=:updatedTime " +
            "WHERE identifier=:physicalAgentIdentifier")
    void updateJobId(@Bind("physicalAgentIdentifier") String physicalAgentIdentifier, @Bind("commandJobId") String commandJobId,@Bind("userIdentifier")
            String userIdentifier, @Bind("updatedTime") Date updatedTime);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,last_job_id lastJobId,identifier identifier,user_details_id userDetailsId,is_command_executed lastCommandExecuted " +
            "from physical_agent where is_command_executed=0")
    List<PhysicalAgentBean> getPhysicalAgentsForOngoingCommand();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select identifier from account where id=(select distinct account_id from agent_account_mapping where " +
            "agent_id in (select id from agent where physical_agent_id=:physicalAgentId))")
    String  getAccountIdentifier(@Bind("physicalAgentId") int physicalAgentId);

    @SqlUpdate("INSERT INTO agent_commands_triggered (physical_agent_id,command_id,trigger_time,command_job_id,command_status,user_details_id, agent_id) " +
            "VALUES (:physicalAgentIdentifier, :commandId, :triggerTime,:commandJobId, :commandStatus, :userDetailsId, :agentId)")
    @GetGeneratedKeys
    int addAgentCommandTriggerDetails(@BindBean CommandTriggerBean commandTriggeredBean);
}
