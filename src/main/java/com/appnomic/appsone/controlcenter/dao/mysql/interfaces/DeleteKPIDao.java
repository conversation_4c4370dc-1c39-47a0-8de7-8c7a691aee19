package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import org.skife.jdbi.v2.sqlobject.Bind;
import org.skife.jdbi.v2.sqlobject.SqlUpdate;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

public interface DeleteKPIDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from comp_instance_kpi_configuration where kpi_id = :kpiId and account_id in (1, :accountId)")
    void deleteCompInstanceKpiConfiguration(@Bind("kpiId") int kpiId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from comp_instance_kpi_threshold_details where kpi_id = :kpiId and account_id in (1, :accountId)")
    void deleteCompInstanceKpiThresholdDetails(@Bind("kpiId") int kpiId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from component_kpi_threshold_details where kpi_id = :kpiId")
    void deleteComponentKpiThresholdDetails(@Bind("kpiId") int kpiId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from comp_instance_kpi_group_details where mst_kpi_details_id = :kpiId")
    void deleteCompInstanceKpiGroupDetails(@Bind("kpiId") int kpiId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from comp_instance_kpi_details where mst_kpi_details_id = :kpiId")
    void deleteCompInstanceKpiDetails(@Bind("kpiId") int kpiId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from mst_component_version_kpi_mapping where mst_kpi_details_id = :kpiId")
    void deleteComponentVersionKpiMapping(@Bind("kpiId") int kpiId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from mst_producer_kpi_mapping where mst_kpi_details_id = :kpiId and account_id in (1, :accountId)")
    void deleteProducerKpiMapping(@Bind("kpiId") int kpiId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from tag_mapping where object_ref_table = 'mst_kpi_details' and object_id = :kpiId and " +
            "account_id in (1, :accountId)")
    void deleteKpiCategoryMapping(@Bind("kpiId") int kpiId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from mst_kpi_details where id = :kpiId and account_id in (1, :accountId)")
    void deleteKpiDetails(@Bind("kpiId") int kpiId, @Bind("accountId") int accountId);

    //for Integration Testing only

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from mst_computed_kpi_mappings where computed_kpi_id = :kpiId")
    void deleteComputedKPIMapping(@Bind("kpiId") int kpiId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from mst_computed_kpi_details where mst_kpi_details_id = :kpiId")
    void deleteComputedKPIDetails(@Bind("kpiId") int kpiId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from mst_kpi_group where name = :kpiName")
    void deleteKPIGroupByName(@Bind("kpiName") String kpiName);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("update mst_computed_kpi_mappings set base_kpi_id = :kpiId where id = 1")
    void updateComputedKPIMappingIT(@Bind("kpiId") int kpiId);
}
