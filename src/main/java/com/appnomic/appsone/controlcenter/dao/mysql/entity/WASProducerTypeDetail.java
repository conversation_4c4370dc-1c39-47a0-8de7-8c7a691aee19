package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.ConstraintViolation;
import javax.validation.constraints.Size;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Data
@NoArgsConstructor
public class WASProducerTypeDetail extends ProducerTypeDetail {

    private static final Logger LOGGER = LoggerFactory.getLogger(WASProducerTypeDetail.class);

    @Size(min = 1, max = 45, message = "module name length must be less than or equal to 45")
    private String webSphereModule;

    @Size(min = 1, max = 128, message = "target object name length must be less than or equal to 128")
    private String targetObjectName;

    public WASProducerTypeDetail(int producerId, String createdTime, String updatedTime, String userDetailsId) {
        super(producerId, createdTime, updatedTime, userDetailsId);
    }

    @Override
    public Map<String, String> getDataMap() {
        Map<String, String> map = new HashMap<>();
        map.put("webSphereModule", webSphereModule);
        map.put("targetObjectName", targetObjectName);
        return map;
    }

    public boolean populate(Map<String, String> attributes) {
        this.setTargetObjectName(attributes.get(Constants.WAS_TYPE_TARGET_OBJECT_NAME_ATTRIBUTE));
        this.setWebSphereModule(attributes.get(Constants.WAS_TYPE_MODULE_NAME_ATTRIBUTE));
        return validate();
    }

    private boolean validate() {

        Set<ConstraintViolation<Object>> violations = ValidationUtils.validateFields(this);

        if( ! violations.isEmpty() ) {
            violations.forEach(it -> LOGGER.error(it.getMessage()));
            return false;
        }

        return true;
    }
}
