package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ThresholdDataDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> : 6/3/19
 */
@Slf4j
public class ThresholdDataService {

    public static void addCompInstanceKpiThreshold(List<CompInstKpiThresholdDetailsBean> instKpiThreshold, ThresholdDataDao thresholdDataDao) {
        try {
            thresholdDataDao.addCompInstanceKpiThreshold(instKpiThreshold);
        } catch (Exception e) {
            log.error("Exception while adding component instance kpi threshold -" + e.getMessage(), e);
        }
    }

    public static void addApplicationThreshold(List<ApplicationThresholdDetailsBean> appThresholdDetailsBean, ThresholdDataDao thresholdDataDao) {
        try {
            thresholdDataDao.addApplicationThreshold(appThresholdDetailsBean);
        } catch (Exception e) {
            log.error("Exception while adding application threshold -" + e.getMessage(), e);
        }
    }

    public static void addTransactionResponseThreshold(List<TransactionResponseThresholdViolationBean> transactionResponseThresholdBean, ThresholdDataDao thresholdDataDao) {
        try {
            thresholdDataDao.addTransactionResponseThreshold(transactionResponseThresholdBean);
        } catch (Exception e) {
            log.error("Exception while adding Transaction response threshold -" + e.getMessage(), e);
        }
    }

    public static void addTransactionThresholdDetails(List<TransactionThresholdDetailsBean> transactionThresholdDetailsBean, ThresholdDataDao thresholdDataDao) {
        try {
            thresholdDataDao.addTransactionThresholdDetails(transactionThresholdDetailsBean);
        } catch (Exception e) {
            log.error("Exception while adding Transaction threshold details -" + e.getMessage(), e);
        }
    }

    public static TransactionThresholdDetailsBean getTransactionStaticThresholds(int accountId, int transactionId, int kpiId, ThresholdDataDao thresholdDataDao) {
        try {
            return thresholdDataDao.getTransactionThreshold(accountId, transactionId, kpiId);
        } catch (Exception e) {
            log.error("Exception while getting Static Transaction threshold details for" +
                    "account id [{}], transaction id [{}], kpi id [{}].", accountId, transactionId, kpiId, e);
        }
        return null;
    }

    public static int[] addTransactionStaticThresholds(List<TransactionThresholdDetailsBean> transactionThresholdDetailsBean, int accountId ,ThresholdDataDao thresholdDataDao) {
        try {
            return thresholdDataDao.addTransactionThreshold(transactionThresholdDetailsBean);
        } catch (Exception e) {
            log.error("Error in adding static transaction thresholds for account [{}]. Details: ", accountId, e);
        }
        return new int[0];
    }

    public static int updateTransactionStaticThresholds(TransactionThresholdDetailsBean transactionThresholdDetailsBean, ThresholdDataDao thresholdDataDao) {
        try {
            return thresholdDataDao.updateTransactionThreshold(transactionThresholdDetailsBean);
        } catch (Exception e) {
            log.error("Exception while updating Static Transaction threshold details for" +
                    "account id [{}], transaction id [{}], kpi id [{}].", transactionThresholdDetailsBean.getAccountId(), transactionThresholdDetailsBean.getTransactionId(), transactionThresholdDetailsBean.getKpiId(), e);
        }
        return 0;
    }

    public static void addComponentThresholdDetails(List<ComponentKpiThresholdDetailsBean> componentThresholdDetailsBean, ThresholdDataDao thresholdDataDao) {
        try {
            thresholdDataDao.addComponentThresholdDetails(componentThresholdDetailsBean);
        } catch (Exception e) {
            log.error("Exception while adding Component threshold details -" + e.getMessage(), e);
        }
    }

    public static List<ViewCoverageWinProfDetailsBean> getCoverageWindowsProfiles() {
        ThresholdDataDao thresholdDataDao = MySQLConnectionManager.getInstance().open(ThresholdDataDao.class);
        try {
            return thresholdDataDao.getCoverageWindowsProfiles();
        } catch (Exception e) {
            log.error("Exception while adding application threshold -" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(thresholdDataDao);
        }
        return null;
    }

    public static List<CompInstKpiThresholdDetailsBean> getCompInstanceThresholdDetailsList(int compInstanceId, int accountId) {
        ThresholdDataDao thresholdDataDao = MySQLConnectionManager.getInstance().open(ThresholdDataDao.class);
        try {
            return thresholdDataDao.getCompInstanceThresholdDetailsList(compInstanceId, accountId);
        } catch (Exception e) {
            log.error("Exception while retrieving Component Instance Threshold Details " + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(thresholdDataDao);
        }
        return Collections.emptyList();
    }

    public static List<InstanceKpiAttributeThresholdBean> getCompInstanceThresholdDetail(int accountId, int compInstanceId, int kpiId) throws ControlCenterException {
        ThresholdDataDao thresholdDataDao = MySQLConnectionManager.getInstance().open(ThresholdDataDao.class);
        try {
            return thresholdDataDao.getCompInstanceThresholdDetail(accountId, compInstanceId, kpiId);
        } catch (Exception e) {
            log.error("Exception while retrieving component instance threshold details " + e.getMessage(), e);
            throw new ControlCenterException("Error while retrieving threshold details");
        } finally {
            MySQLConnectionManager.getInstance().close(thresholdDataDao);
        }
    }

    public static List<ComponentKpiThresholdDetailsBean> getComponentThresholdDetailsList(Integer componentId) {
        ThresholdDataDao thresholdDataDao = MySQLConnectionManager.getInstance().open(ThresholdDataDao.class);
        try {
            return thresholdDataDao.getComponentThresholdDetailsList(componentId);
        } catch (Exception e) {
            log.error("Exception while retrieving Component KPI Threshold Details " + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(thresholdDataDao);
        }
        return null;
    }

    public static List<CompInstKpiThresholdDetailsBean> getCompInstThrldDetailsListUsingKPIDetails(Integer compInstanceId, String nullCondition, int kpiId, int kpiGrpId, String attributeVal) {
        ThresholdDataDao thresholdDataDao = MySQLConnectionManager.getInstance().open(ThresholdDataDao.class);
        try {
            return thresholdDataDao.getCompInstThrldDetailsListUsingKPIDetails(compInstanceId, nullCondition, kpiId, kpiGrpId, attributeVal);
        } catch (Exception e) {
            log.error("Exception while retrieving Component Instance Threshold Details " + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(thresholdDataDao);
        }
        return null;
    }

    public static void updateComponentInstThresholdDetails(CompInstKpiThresholdDetailsBean compInstKpiThresholdDetailsBean, ThresholdDataDao thresholdDataDao) {
        try {
            thresholdDataDao.updateComponentInstThresholdDetails(compInstKpiThresholdDetailsBean.getMinThreshold().floatValue(), compInstKpiThresholdDetailsBean.getMaxThreshold().floatValue(), compInstKpiThresholdDetailsBean.getId(), compInstKpiThresholdDetailsBean.getUpdatedTime(), compInstKpiThresholdDetailsBean.getUserDetailsId());
        } catch (Exception e) {
            log.error("Exception while updating threshold Threshold Details " + e.getMessage(), e);
        }
    }

    public static void deleteComponentInstThresholdDetails(Integer id, ThresholdDataDao thresholdDataDao) {
        try {
            thresholdDataDao.deleteComponentInstThresholdDetails(id);
        } catch (Exception e) {
            log.error("Exception while deleting threshold Threshold Details " + e.getMessage(), e);
        }
    }
}
