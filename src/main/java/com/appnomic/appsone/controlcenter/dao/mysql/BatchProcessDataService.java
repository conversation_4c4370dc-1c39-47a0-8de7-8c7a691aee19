package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.BatchProcessMappingBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProcessArgumentBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProcessDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProcessHostDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.BatchProcessDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class BatchProcessDataService extends AbstractDaoService<BatchProcessDao> {

    private static final Logger LOGGER = LoggerFactory.getLogger(BatchProcessDataService.class);

    public List<ProcessDetailsBean> getAllBatchProcessDetails() throws ControlCenterException {
        BatchProcessDao batchProcessDao = getDaoConnection(null, BatchProcessDao.class);
        try {
            return batchProcessDao.getAllBatchProcessDetails();
        } catch (Exception e) {
            LOGGER.error("Error while getting all batch process details from 'process_details' table." + e.getMessage(), e);
            throw new ControlCenterException("Error while getting all batch process details from 'process_details' table.");
        } finally {
            closeDaoConnection(null, batchProcessDao);
        }
    }

    public ProcessDetailsBean getBatchProcessDetailsByNameAndAccount(int accountId, String processName) throws ControlCenterException {
        BatchProcessDao batchProcessDao = getDaoConnection(null, BatchProcessDao.class);
        try {
            return batchProcessDao.getBatchProcessDetailsByNameAndAccount(accountId, processName);
        } catch (Exception e) {
            LOGGER.error("Error while getting all batch process details from 'process_details' table." + e.getMessage(), e);
            throw new ControlCenterException("Error while getting all batch process details from 'process_details' table.");
        } finally {
            closeDaoConnection(null, batchProcessDao);
        }
    }

    public ProcessDetailsBean getBatchProcessDetailsByIdentifier(String processIdentifier) throws ControlCenterException {
        BatchProcessDao batchProcessDao = getDaoConnection(null, BatchProcessDao.class);
        try {
            return batchProcessDao.getBatchProcessDetailsByIdentifier(processIdentifier);
        } catch (Exception e) {
            LOGGER.error("Error while getting all batch process details from 'process_details' table." + e.getMessage(), e);
            throw new ControlCenterException("Error while getting all batch process details from 'process_details' table.");
        } finally {
            closeDaoConnection(null, batchProcessDao);
        }
    }

    public int addBatchProcessDetails(ProcessDetailsBean processDetailsBean, Handle handle) throws ControlCenterException {
        BatchProcessDao batchProcessDao = getDaoConnection(handle, BatchProcessDao.class);
        try {
            return batchProcessDao.addBatchProcessDetails(processDetailsBean);
        } catch (Exception e) {
            LOGGER.error("Error while adding batch process details in 'process_details' table." + e.getMessage(), e);
            throw new ControlCenterException("Error while adding batch process details in 'process_details' table.");
        } finally {
            closeDaoConnection(handle, batchProcessDao);
        }
    }

    public int[] addProcessBatchMapping(List<BatchProcessMappingBean> processDetailsBean, Handle handle) throws ControlCenterException {
        BatchProcessDao batchProcessDao = getDaoConnection(handle, BatchProcessDao.class);
        try {
            return batchProcessDao.addProcessBatchMapping(processDetailsBean);
        } catch (Exception e) {
            LOGGER.error("Error while adding batch process mapping in 'batch_process_mapping' table." + e.getMessage(), e);
            throw new ControlCenterException("Error while adding batch process mapping in 'batch_process_mapping' table.");
        } finally {
            closeDaoConnection(handle, batchProcessDao);
        }
    }

    public void updateBatchProcessDetails(ProcessDetailsBean processDetailsBean, Handle handle) throws ControlCenterException {
        BatchProcessDao batchProcessDao = getDaoConnection(handle, BatchProcessDao.class);
        try {
            batchProcessDao.updateBatchProcessDetails(processDetailsBean);
        } catch (Exception e) {
            LOGGER.error("Error while updating batch process details in 'process_details' table." + e.getMessage(), e);
            throw new ControlCenterException("Error while updating batch process details in 'process_details' table.");
        } finally {
            closeDaoConnection(handle, batchProcessDao);
        }
    }

    public int addBatchProcessHostDetails(ProcessHostDetailsBean processHostDetailsBean, Handle handle) throws ControlCenterException {
        BatchProcessDao batchProcessDao = getDaoConnection(handle, BatchProcessDao.class);
        try {
            return batchProcessDao.addBatchProcessHostDetails(processHostDetailsBean);
        } catch (Exception e) {
            LOGGER.error("Error while adding host details for the batch process details in 'process_host_details' table."
                    + e.getMessage(), e);
            throw new ControlCenterException("Error while adding host details for the batch process details in " +
                    "'process_host_details' table.");
        } finally {
            closeDaoConnection(handle, batchProcessDao);
        }
    }

    public void addBatchProcessHostArguments(List<ProcessArgumentBean> arguments, Handle handle) throws ControlCenterException {
        BatchProcessDao batchProcessDao = getDaoConnection(handle, BatchProcessDao.class);
        try {
            batchProcessDao.addBatchProcessHostArguments(arguments);
        } catch (Exception e) {
            LOGGER.error("Error while adding arguments in 'process_arguments' table." + e.getMessage(), e);
            throw new ControlCenterException("Error while adding arguments in 'process_arguments' table.");
        } finally {
            closeDaoConnection(handle, batchProcessDao);
        }
    }

    public void updateBatchProcessHostArguments(List<ProcessArgumentBean> arguments, Handle handle) throws ControlCenterException {
        BatchProcessDao batchProcessDao = getDaoConnection(handle, BatchProcessDao.class);
        try {
            batchProcessDao.updateBatchProcessHostArguments(arguments);
        } catch (Exception e) {
            LOGGER.error("Error while updating arguments in 'process_arguments' table." + e.getMessage(), e);
            throw new ControlCenterException("Error while updating arguments in 'process_arguments' table.");
        } finally {
            closeDaoConnection(handle, batchProcessDao);
        }
    }

    public ProcessDetailsBean getProcessDetailsWithId(int processId) throws ControlCenterException {
        BatchProcessDao batchProcessDao = getDaoConnection(null, BatchProcessDao.class);
        try {
            return batchProcessDao.getProcessDetailsWithId(processId);
        } catch (Exception e) {
            LOGGER.error("Error while getting batch processes from 'process_details' table." + e.getMessage(), e);
            throw new ControlCenterException("Error while getting batch processes from 'process_details' table.");
        } finally {
            closeDaoConnection(null, batchProcessDao);
        }
    }

    public void disableBatchProcess(int batchProcessId, Handle handle) throws ControlCenterException {
        BatchProcessDao batchProcessDao = getDaoConnection(handle, BatchProcessDao.class);
        try {
            batchProcessDao.disableBatchProcess(batchProcessId);
        } catch (Exception e) {
            LOGGER.error("Error while disabling the batch process." + e.getMessage(), e);
            throw new ControlCenterException("Error while disabling the batch process.");
        } finally {
            closeDaoConnection(handle, batchProcessDao);
        }
    }

    public void disableHostForBatchProcess(int batchProcessId, Handle handle) throws ControlCenterException {
        BatchProcessDao batchProcessDao = getDaoConnection(handle, BatchProcessDao.class);
        try {
            batchProcessDao.disableHostForBatchProcess(batchProcessId);
        } catch (Exception e) {
            LOGGER.error("Error while disabling the batch process." + e.getMessage(), e);
            throw new ControlCenterException("Error while disabling the batch process.");
        } finally {
            closeDaoConnection(handle, batchProcessDao);
        }
    }

    public void disableHostArgsForBatchProcess(int batchProcessId, Handle handle) throws ControlCenterException {
        BatchProcessDao batchProcessDao = getDaoConnection(handle, BatchProcessDao.class);
        try {
            batchProcessDao.disableHostArgsForBatchProcess(batchProcessId);
        } catch (Exception e) {
            LOGGER.error("Error while disabling the batch process." + e.getMessage(), e);
            throw new ControlCenterException("Error while disabling the batch process.");
        } finally {
            closeDaoConnection(handle, batchProcessDao);
        }
    }

    public void deleteProcessIT(Integer processId, Handle handle) throws ControlCenterException {
        BatchProcessDao batchProcessDao = getDaoConnection(handle, BatchProcessDao.class);
        try {
            batchProcessDao.deleteProcessArguments(processId);
            batchProcessDao.deleteProcessHostDetails(processId);
            batchProcessDao.deleteBatchProcessDetails(processId);
        } catch (Exception e) {
            LOGGER.error("Error while deleting batch process." + e.getMessage(), e);
            throw new ControlCenterException("Error while delete batch process.");
        } finally {
            closeDaoConnection(handle, batchProcessDao);
        }
    }


}
