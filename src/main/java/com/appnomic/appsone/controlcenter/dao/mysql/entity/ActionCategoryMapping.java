package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActionCategoryMapping {
    private int id;
    @NonNull
    private int categoryId;
    @NonNull
    private int timeWindowInSecs;
    @NonNull
    private int commandExecTypeId;
    @NonNull
    private int downloadTypeId;
    @NonNull
    private int retries;
    @NonNull
    private int ttlInSecs;
    @NonNull
    private int actionExecTypeId;
    private int objectId;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
    private int actionId;
    private String objectRefTable;
    private int status;
}
