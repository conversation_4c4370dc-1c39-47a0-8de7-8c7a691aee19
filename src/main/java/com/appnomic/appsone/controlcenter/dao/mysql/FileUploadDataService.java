package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.FileProcessedDetailsBean;
import com.appnomic.appsone.controlcenter.beans.FileSummaryDetailsBean;
import com.appnomic.appsone.controlcenter.beans.FileUploadDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.FileUploadDataDao;
import com.appnomic.appsone.controlcenter.pojo.InstallationResponse;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.InstallationAttributeBean;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class FileUploadDataService {

    FileUploadDataService(){}
    private static Logger logger = LoggerFactory.getLogger(FileUploadDataService.class);
    private static final String EXCEPTION_UPDATING_FILE_DETAILS = "Exception while updating  File details -";

    public static FileProcessedDetailsBean getFileProcessedDetailsById(int processedId) {
        FileUploadDataDao fileUploadDataDao = MySQLConnectionManager.getInstance().open(FileUploadDataDao.class);
        try {
            return fileUploadDataDao.getFileProcessedById(processedId);
        } catch (Exception e) {
            logger.error("Exception while retrieving processed details data from table, processedId:" + processedId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(fileUploadDataDao);
        }
        return null;
    }



    public static int addUploadFileDetails(FileUploadDetailsBean fileUploadDetailsBean) {
        FileUploadDataDao fileUploadDataDao = MySQLConnectionManager.getInstance().open(FileUploadDataDao.class);
        try {
            return fileUploadDataDao.addUploadFileDetails(fileUploadDetailsBean);
        } catch (Exception e) {
            logger.error("Exception while adding File details -" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(fileUploadDataDao);
        }
        return -1;
    }

    public static int getFileCountByChecksum(String checksum, int accountId) {
        FileUploadDataDao fileUploadDataDao = MySQLConnectionManager.getInstance().open(FileUploadDataDao.class);
        try {
            return fileUploadDataDao.getFileCountByCheckSum(checksum, accountId);
        } catch (Exception e) {
            logger.error(EXCEPTION_UPDATING_FILE_DETAILS + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(fileUploadDataDao);
        }
        return 0;
    }


    public static int getFileCountByFileName(String fileName, int accountId) {
        FileUploadDataDao fileUploadDataDao = MySQLConnectionManager.getInstance().open(FileUploadDataDao.class);
        try {
            return fileUploadDataDao.getFileCountByName(fileName, accountId);
        } catch (Exception e) {
            logger.error(EXCEPTION_UPDATING_FILE_DETAILS + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(fileUploadDataDao);
        }
        return 0;
    }

    public static FileUploadDetailsBean getFileDetailsById(int uploadId, int accountId) {
        FileUploadDataDao fileUploadDataDao = MySQLConnectionManager.getInstance().open(FileUploadDataDao.class);
        try {
            return fileUploadDataDao.getFileUploadDetailById(uploadId, accountId);
        } catch (Exception e) {
            logger.error("Exception while retrieving the upload details, uploadId:"+uploadId+", accountId:"+accountId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(fileUploadDataDao);
        }
        return null;
    }


    public static void deleteUploadDetails(int uploadId, int accountId) {
        FileUploadDataDao fileUploadDataDao = MySQLConnectionManager.getInstance().open(FileUploadDataDao.class);
        try {
            fileUploadDataDao.deleteFileDetails(uploadId, accountId);
        } catch (Exception e) {
            logger.error(EXCEPTION_UPDATING_FILE_DETAILS  + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(fileUploadDataDao);
        }
    }

    public static int addProcessedFileDetails(FileProcessedDetailsBean fileProcessedDetailsBean) {
        FileUploadDataDao fileuploadDataDao = MySQLConnectionManager.getInstance().open(FileUploadDataDao.class);
        try {
            return fileuploadDataDao.addFileProcessedDetails(fileProcessedDetailsBean);
        } catch (Exception e) {
            logger.error("Exception while adding File processed details -" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(fileuploadDataDao);
        }
        return -1;
    }
    public static void updateProcessedFileDetails(FileProcessedDetailsBean fileProcessedDetailsBean) {
        FileUploadDataDao fileuploadDataDao = MySQLConnectionManager.getInstance().open(FileUploadDataDao.class);
        try {
            fileuploadDataDao.updateFileProcessDetails(fileProcessedDetailsBean);
        } catch (Exception e) {
            logger.error("Exception while updating file processed details -" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(fileuploadDataDao);
        }
    }
    public static void addFileProcessedDetails(List<FileSummaryDetailsBean> fileSummaryDetailsBeans) {
        FileUploadDataDao fileUploadDataDao = MySQLConnectionManager.getInstance().open(FileUploadDataDao.class);
        try {
            fileUploadDataDao.addFileProcessedDetails(fileSummaryDetailsBeans);
        }   catch (Exception e) {
            logger.error("Error occurred while adding file processed details in DB", e);
        } finally {
            MySQLConnectionManager.getInstance().close(fileUploadDataDao);
        }
    }
    public static List<FileUploadDetailsBean> getFileUploadDetailList(Integer accountId) {
        FileUploadDataDao fileUploadDataDao = MySQLConnectionManager.getInstance().getHandle().onDemand(FileUploadDataDao.class);
        try {
            return fileUploadDataDao.getFileUploadDetailList(accountId);
        } catch (Exception e) {
            logger.error("Exception while file upload details data from table" + e.getMessage(), e);
        }
        return new ArrayList<>();

    }
    public static void addFileProcessedDetail(FileSummaryDetailsBean fileSummaryDetailsBean) {
        FileUploadDataDao fileUploadDataDao = MySQLConnectionManager.getInstance().open(FileUploadDataDao.class);
        try {
            fileUploadDataDao.addFileSummaryDetail(fileSummaryDetailsBean);
        }   catch (Exception e) {
            logger.error("Error occurred while adding file processed details in DB", e);
        } finally {
            MySQLConnectionManager.getInstance().close(fileUploadDataDao);
        }
    }
    public static void updateFile(int uploadId,int status) {
        FileUploadDataDao fileUploadDataDao = MySQLConnectionManager.getInstance().open(FileUploadDataDao.class);
        try {
            fileUploadDataDao.updateFileDetails(uploadId,status);
        } catch (Exception e) {
            logger.error("Exception while updating  File details -uploadId:" + uploadId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(fileUploadDataDao);
        }
    }

    public static InstallationAttributeBean getInstallationAttribute(String name) {
        FileUploadDataDao dao = MySQLConnectionManager.getInstance().open(FileUploadDataDao.class);
        try {
            return dao.getInstallationAttribute(name);
        }   catch (Exception e) {
            logger.error("Error occurred while fetching installation attribute.", e);
        } finally {
            MySQLConnectionManager.getInstance().close(dao);
        }
        return null;
    }

    public static InstallationResponse getInstallationAttributes() {
         FileUploadDataDao dao = MySQLConnectionManager.getInstance().open(FileUploadDataDao.class);
        try {
            List<InstallationAttributeBean> installAttrs = dao.getInstallationAttributes();

            InstallationResponse response = new InstallationResponse();
            response.setResponseMessage("");
            response.setResponseStatus("Success");
            response.setResult(installAttrs);

            return response;
        }   catch (Exception e) {
            logger.error("Error occurred while fetching installation attribute.", e);
        } finally {
            MySQLConnectionManager.getInstance().close(dao);
        }

        return null;
    }
}
