package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ApplicationNotificationDao;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.DefaultNotificationPreferences;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.NotificationPreferencesPojo;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class NotificationPreferencesDataService extends AbstractDaoService<ApplicationNotificationDao> {

    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationPreferencesDataService.class);

    public List<NotificationPreferencesPojo> getApplicationNotificationMappingDetails(Integer appId) {
        ApplicationNotificationDao applicationNotificationDao = getDaoConnection(null, ApplicationNotificationDao.class);
        try {
            return applicationNotificationDao.getApplicationNotificationMappingDetails(appId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            closeDaoConnection(null, applicationNotificationDao);
        }
        return new ArrayList<>();
    }

    public int[] updateNotificationConfiguration(List<DefaultNotificationPreferences> entityList) {
        ApplicationNotificationDao applicationNotificationDao = getDaoConnection(null, ApplicationNotificationDao.class);

        try {
            return applicationNotificationDao.updateDefaultPreferences(entityList);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            closeDaoConnection(null, applicationNotificationDao);
        }
        return new int[]{};
    }

    public void addDefaultNotificationPreferences(List<DefaultNotificationPreferences> list, Handle handle) {
        ApplicationNotificationDao applicationNotificationDao = getDaoConnection(handle, ApplicationNotificationDao.class);
        try {
            applicationNotificationDao.addDefaultPreferences(list);
        } catch (Exception e) {
            LOGGER.error("Error occurred while updating default notification preferences", e);
        } finally {
            closeDaoConnection(handle, applicationNotificationDao);
        }
    }
}
