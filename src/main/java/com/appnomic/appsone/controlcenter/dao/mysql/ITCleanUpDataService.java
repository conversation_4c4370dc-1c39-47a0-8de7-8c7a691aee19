package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.ActionCategoryMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.*;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class ITCleanUpDataService {

    private ITCleanUpDataService() {
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(ITCleanUpDataService.class);

    public static void updateComputedKPIMapping(int baseKPI) {
        MySQLConnectionManager.getInstance().open(DeleteKPIDao.class).updateComputedKPIMappingIT(baseKPI);
    }

    public static void deleteComputedKPI(int kpiId) {
        MySQLConnectionManager.getInstance().open(DeleteKPIDao.class).deleteComputedKPIMapping(kpiId);
        MySQLConnectionManager.getInstance().open(DeleteKPIDao.class).deleteComputedKPIDetails(kpiId);
    }

    public static void deleteKPIGroupByName(String groupName) {
        MySQLConnectionManager.getInstance().open(DeleteKPIDao.class).deleteKPIGroupByName(groupName);
    }

    public static void updateCompInstanceStatusByComponentId(int componentId, int status) {
        MySQLConnectionManager.getInstance().open(ComponentInstanceDao.class).updateCompInstanceStatusByComponentId(componentId, status);
    }

    public static void actionsItCleanUp(int id) {
        ActionScriptDao actionScriptDao = MySQLConnectionManager.getInstance().open(ActionScriptDao.class);
        CommandDao commandDao = MySQLConnectionManager.getInstance().open(CommandDao.class);

        try {
            List<ActionCategoryMapping> list = actionScriptDao.getActionCategoryMapping(id);
            if (!list.isEmpty()) {
                int commandId = list.stream()
                        .findFirst().orElse(ActionCategoryMapping.builder().objectId(-1).build())
                        .getObjectId();
                commandDao.deleteCommandArgs(commandId);
                commandDao.deleteCommand(commandId);
            }
            actionScriptDao.deleteActionCategoryMapping(id);
            actionScriptDao.deleteAction(id);
        } catch (Exception e) {
            LOGGER.error("Error during rollback in : ActionsItCleanUp");
        }
    }

    public static void addApplicationITCleanUp(int id)
    {
        try {
            ServiceDao dao = MySQLConnectionManager.getInstance().open(ServiceDao.class);
            MySQLConnectionManager.getInstance().open(TagsDao.class).removeITApplicationNotif(id);
            dao.deleteTagMappingDetailsForController(id);
            dao.deleteController(id);
        } catch (Exception e) {
            LOGGER.error("Error during rollback in : addApplicationItCleanUp ",e);
        }
    }

    public static void addServiceITCleanUp(int accountId, int id) {
        try {
            ServiceDao dao = MySQLConnectionManager.getInstance().open(ServiceDao.class);
            dao.deleteServiceConfigurations(accountId, id);
            dao.deleteServiceKpiThreshold(accountId, id);
            dao.deleteTagMappingDetailsForController(id);
            dao.deleteServiceMappingDetails(id);
            dao.deleteController(id);
            LOGGER.info("Rollback successful.");
        } catch (Exception e) {
            LOGGER.error("Error during rollback in : addApplicationItCleanUp ", e);
        }
    }

    public static void updateInstanceForensicITCleanUp(int instanceId) {
        try {
            MySQLConnectionManager.getInstance().open(CategoryForensicsDao.class).deleteForensicCategoryMappingByInstanceId(instanceId);
            LOGGER.info("Rollback successful.");
        } catch (Exception e) {
            LOGGER.error("Error during rollback in : updateInstanceForensicITCleanUp ", e);
        }
    }
}
