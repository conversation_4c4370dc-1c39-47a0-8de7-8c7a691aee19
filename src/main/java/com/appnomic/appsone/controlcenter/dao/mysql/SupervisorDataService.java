package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.ComponentInstanceBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompInstanceKPIDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.SupervisorBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.SupervisorDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;

public class SupervisorDataService extends AbstractDaoService<SupervisorDao> {

    private static final Logger logger = LoggerFactory.getLogger(SupervisorDataService.class);

    public List<SupervisorBean> getLocalModeSupervisorDetails(Handle handle) {
        SupervisorDao supervisorDao = getDaoConnection(handle, SupervisorDao.class);
        try {
            return supervisorDao.getLocalModeSupervisorDetails();
        } catch (Exception e) {
            logger.error("Error occurred while fetching supervisors details.", e);
        } finally {
            closeDaoConnection(handle, supervisorDao);
        }
        return Collections.emptyList();
    }
    public List<SupervisorBean> getAllSupervisorDetails(Handle handle) {
        SupervisorDao supervisorDao = getDaoConnection(handle, SupervisorDao.class);
        try {
            return supervisorDao.getAllSupervisorDetails();
        } catch (Exception e) {
            logger.error("Error occurred while fetching supervisors details.", e);
        } finally {
            closeDaoConnection(handle, supervisorDao);
        }
        return Collections.emptyList();
    }

    public List<SupervisorBean> getAccountWiseSupervisorDetailsWithGlobalAccount(int accountId, Handle handle) {
        SupervisorDao supervisorDao = getDaoConnection(handle, SupervisorDao.class);
        try {
            return supervisorDao.getAccountWiseSupervisorDetailsWithGlobalAccount(accountId);
        } catch (Exception e) {
            logger.error("Error occurred while fetching supervisors details accountId{}", accountId, e);
        } finally {
            closeDaoConnection(handle, supervisorDao);
        }
        return Collections.emptyList();
    }

    public SupervisorBean getSupervisorByIdentifier(String identifier, Handle handle) {
        SupervisorDao supervisorDao = getDaoConnection(handle, SupervisorDao.class);
        try {
            return supervisorDao.getSupervisorByIdentifier(identifier);
        } catch (Exception e) {
            logger.error("Error occurred while fetching supervisor details by " +
                    "identifier {}", identifier, e);
        } finally {
            closeDaoConnection(handle, supervisorDao);
        }
        return null;
    }

    public int addSupervisor(SupervisorBean supervisorBean, Handle handle) {
        SupervisorDao supervisorDao = getDaoConnection(handle, SupervisorDao.class);
        try {
            return supervisorDao.insertSupervisor(supervisorBean);
        } catch (Exception e) {
            logger.error("Error occurred while adding supervisor. Details {}", e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, supervisorDao);
        }
        return 0;
    }

    public int updateSupervisor(SupervisorBean supervisorBean, Handle handle) {
        SupervisorDao supervisorDao = getDaoConnection(handle, SupervisorDao.class);
        try {
            supervisorDao.updateSupervisor(supervisorBean);
            return 1;
        } catch (Exception e) {
            logger.error("Error occurred while updating supervisor details " +
                    "{}", supervisorBean, e);
        } finally {
            closeDaoConnection(handle, supervisorDao);
        }
        return 0;
    }
    public void updateSupervisorJobId(String commandJobId, String identifier, Handle handle) {
        SupervisorDao supervisorDao = getDaoConnection(handle, SupervisorDao.class);
        try {
            supervisorDao.updateSupervisorJobId(commandJobId, identifier);
        } catch (Exception e) {
            logger.error("Error occurred while updating supervisor details " +
                    "{}", identifier, e);
        } finally {
            closeDaoConnection(handle, supervisorDao);
        }
    }


    public void deleteSupervisor(String supervisorName, Handle handle){
        SupervisorDao supervisorDao = getDaoConnection(handle, SupervisorDao.class);
        try {
            supervisorDao.deleteSupervisor(supervisorName);
        }catch (Exception e){
            logger.error("Error occurred while deleting supervisor [{}]. Details: {}",supervisorName, e.getMessage());
        }finally {
            closeDaoConnection(handle, supervisorDao);
        }
    }

    public int checkCommandDetails(String superVisorIdentifier,String commandJobId) throws ControlCenterException {
        SupervisorDao supervisorDao = getDaoConnection(null, SupervisorDao.class);
        try {
            return supervisorDao.checkCommandDetails(superVisorIdentifier, commandJobId);
        } catch (Exception e) {
            logger.error("Exception while getting count", e);
            throw  new ControlCenterException("Error while getting count information");
        } finally {
            closeDaoConnection(null, supervisorDao);
        }
    }
    public ComponentInstanceBean getInstanceIdentifier(int accountId, String hostAddress) throws ControlCenterException {
        SupervisorDao supervisorDao = getDaoConnection(null, SupervisorDao.class);
        try {
            return supervisorDao.getInstanceIdentifier(accountId,hostAddress);
        } catch (Exception e) {
            logger.error("Exception while getting instance identifier", e);
            throw  new ControlCenterException("Error while getting instance information");
        } finally {
            closeDaoConnection(null, supervisorDao);
        }
    }
    public List<CompInstanceKPIDetailsBean> getKpiInstanceMap(String producerName) throws ControlCenterException {
        SupervisorDao supervisorDao = getDaoConnection(null, SupervisorDao.class);
        try {
            return supervisorDao.getKpiInstanceDetails(producerName);
        } catch (Exception e) {
            logger.error("Exception while getting instance identifier", e);
            throw  new ControlCenterException("Error while getting instance information");
        } finally {
            closeDaoConnection(null, supervisorDao);
        }
    }

}
