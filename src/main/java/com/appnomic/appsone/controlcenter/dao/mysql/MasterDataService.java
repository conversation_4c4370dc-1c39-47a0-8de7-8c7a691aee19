package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommonVersionCompIdMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompVersionKpiMappingBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.KpiCategoryDetails;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ProducerTypeBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.MasterDataDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.*;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by Son<PERSON> J on 28/12/2018
 */

public class MasterDataService extends AbstractDaoService<MasterDataService>{
    private static final Logger logger = LoggerFactory.getLogger(MasterDataService.class);
    private static final String EXCEPTION_TAG_DETAILS = "Exception while getting tag details data from table";
    private static final String EXCEPTION_GETTING_COMPONENT_INSTANCE_DATA = "Exception while getting component instance data from table";
    private static final String EXCEPTION_GETTING_MASTER_KPI_DETAILS = "Exception while getting master kpi details data from table";

    public static List<MasterComponentBean> getComponentMasterData(int accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getComponentMasterData(accountId);
        } catch (Exception e) {
            logger.error("Exception while getting master component data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return new ArrayList<>();
    }

    public static List<MasterComponentTypeBean> getMasterComponentTypeData(int accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getMasterComponentTypesData(accountId);
        } catch (Exception e) {
            logger.error("Exception while getting master component type data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return new ArrayList<>();
    }

    public static List<MasterComponentMappingBean> getMasterComponentMappingData(int accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getMasterComponentMappingData(accountId);
        } catch (Exception e) {
            logger.error("Exception while getting master component mapping data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }


    public static List<MasterComponentVersionBean> getAllMasterComponentVersionData() {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getAllMasterComponentVersionData();
        } catch (Exception e) {
            logger.error("Exception while getting master component version data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static MasterComponentVersionBean getMasterComponentVersionData(int mstCompId, String mstCompVersionName, int accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getMasterComponentVersionData(mstCompId, mstCompVersionName, accountId);
        } catch (Exception e) {
            logger.error("Exception while getting master component version data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static ComponentInstanceBean getHostsData(String hostAddress, int accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            int id = masterDataDao.getHostsId(Constants.HOST);
            if (id == 0) {
                logger.error("No any host is found in mst_component_type table.");
                return null;
            }
            return masterDataDao.getHostsData(hostAddress, id, accountId);
        } catch (Exception e) {
            logger.error( EXCEPTION_GETTING_COMPONENT_INSTANCE_DATA + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static ComponentInstanceBean getCompInstForAccountComInstName(String name, int accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            ComponentInstanceBean componentInstanceBean = masterDataDao.getCompInstForAccountComInstName(name, accountId);
            if (componentInstanceBean != null) {
                int compInstId = componentInstanceBean.getId();
                componentInstanceBean.setNonGroupKpi(masterDataDao.getNonGroupKpiListForAGivenCompInst(compInstId));
                componentInstanceBean.setGroupKpi(masterDataDao.getGroupKpiListForAGivenCompInst(compInstId));
                componentInstanceBean.setAttributes(masterDataDao.getAttributeListForAGivenCompInst(compInstId));
            }
            return componentInstanceBean;
        } catch (Exception e) {
            logger.error(EXCEPTION_GETTING_COMPONENT_INSTANCE_DATA + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static InstanceMappingDetails getInstanceMappingDetails(int instanceId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return InstanceMappingDetails.builder()
                    .attributes(ActionScriptDataService.getAttributeListForConfig(instanceId,null))
                    .nonGroupKpi(masterDataDao.getNonGroupKpiListForAGivenCompInst(instanceId))
                    .groupKpi(masterDataDao.getGroupKpiListForAGivenCompInst(instanceId))
                    .build();
        } catch (Exception e) {
            logger.error(EXCEPTION_GETTING_COMPONENT_INSTANCE_DATA + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static List<AttributesViewBean> getAttributesViewData(int mstCommonVersionId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getAttributeViewData(mstCommonVersionId);
        } catch (Exception e) {
            logger.error(EXCEPTION_GETTING_COMPONENT_INSTANCE_DATA + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static List<ViewCommonVersionKPIsBean> getViewCommonVersionNonGroupKPIsData(int mstCommonVersionId, int accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getViewCommonVersionNonGroupKPIsData(mstCommonVersionId, accountId);
        } catch (Exception e) {
            logger.error("Exception while getting non group data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return Collections.emptyList();
    }

    public static List<ViewCommonVersionKPIsBean> getViewCommonVersionGroupKPIsData(int mstCommonVersionId, int accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getViewCommonVersionGroupKPIsData(mstCommonVersionId, accountId);
        } catch (Exception e) {
            logger.error("Exception while getting group data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return Collections.emptyList();
    }

    public static ViewProducerKPIsBean getViewProducerNonGroupKPIsData(int mstKpiDetailId, int mstCompVersionId, int mstCompId, int mstCompTypeId, int accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getViewProducerNonGroupKPIsData(mstKpiDetailId, mstCompVersionId, mstCompId, mstCompTypeId, accountId);
        } catch (Exception e) {
            logger.error("Exception while getting producer-kpi mapping data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static ViewProducerKPIsBean getViewProducerGroupKPIsData(int mstKpiDetailId, int mstCompVersionId, int mstCompId, int mstCompTypeId, int accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getViewProducerGroupKPIsData(mstKpiDetailId, mstCompVersionId, mstCompId, mstCompTypeId, accountId);
        } catch (Exception e) {
            logger.error("Exception while getting producer-kpi mapping data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static MasterKPIDetailsBean getMasterKPIDetailsData(int kpiId, int accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getMasterKPIDetailsData(kpiId, accountId);
        } catch (Exception e) {
            logger.error(EXCEPTION_GETTING_MASTER_KPI_DETAILS + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static List<ConnectionDetails> getConnectionDetails(Integer accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);

        try {
            return masterDataDao.getConnectionDetails(accountId);
        } catch (Exception e) {
            logger.error(EXCEPTION_GETTING_MASTER_KPI_DETAILS + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static List<TagMappingDetails> getTagMappingDetails(Integer accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);

        try {
            return masterDataDao.getTagMappingDetails(accountId);
        } catch (Exception e) {
            logger.error(EXCEPTION_GETTING_MASTER_KPI_DETAILS + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return new ArrayList<>();

    }

    public static List<TagMappingDetails> getTagMappingDetailsForCompInstanceClusters(Integer accountId, Integer tagId, List<Integer> clusters) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);

        try {
            return masterDataDao.getTagMappingDetailsForCompInstanceClusters(accountId, tagId, clusters);
        } catch (Exception e) {
            logger.error("Error occurred while getting tag mappings for tag Id: [{}], Details: ", tagId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return new ArrayList<>();

    }

    public static List<CompInstClusterDetails> getCompInstanceDetails(int accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getCompInstClusterList(accountId);
        } catch (Exception e) {
            logger.error(EXCEPTION_GETTING_MASTER_KPI_DETAILS + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return new ArrayList<>();

    }

    public static TagDetailsBean getTagDetails(String tagName) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);

        try {
            return masterDataDao.getTagDetails(tagName, 1);
        } catch (Exception e) {
            logger.error(EXCEPTION_TAG_DETAILS + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;

    }

    public static List<TagDetailsBean> getTagDetails() {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);

        try {
            return masterDataDao.getTagDetails();
        } catch (Exception e) {
            logger.error(EXCEPTION_TAG_DETAILS, e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return Collections.emptyList();

    }

    public static MasterSubTypeBean getMasterSubTypeDetailsForId(int subTypeId)  {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getMasterSubTypeForId(subTypeId);
        } catch (Exception e)   {
            logger.error("Error occurred while fetching details for sub type id: "+subTypeId+"\n"+e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static List<ComponentKpis> getCompKpiMapping(int accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);

        try {
            return masterDataDao.getCompKpis(accountId);
        } catch (Exception e) {
            logger.error(EXCEPTION_TAG_DETAILS + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return Collections.emptyList();
    }

    public static List<TagDetailsBean> getTagDetailsForAccount(int accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);

        try {
            return masterDataDao.getTagDetailsForAccount(accountId);
        } catch (Exception e) {
            logger.error(EXCEPTION_TAG_DETAILS + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return Collections.emptyList();
    }

    public static List<AllKpiList> getAllKpisList() {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getAllKpis();
        } catch (Exception e) {
            logger.error(EXCEPTION_TAG_DETAILS + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return Collections.emptyList();

    }

    public static List<ClusterInstanceMapping> getClusterInstanceMapping(int accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getClusterInstanceMapping(accountId);
        } catch (Exception e) {
            logger.error(EXCEPTION_TAG_DETAILS + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return new ArrayList<>();
    }

    public static List<TimezoneDetail> getAllTimezones() {
        MasterDataDao masterDataDao =
                MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getAllTimezones();
        } catch (Exception e) {
            logger.error("Error occurred while fetching time zones from database", e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return new ArrayList<>();
    }

    public static List<ProducerTypeBean> getProducerTypes() {
        MasterDataDao masterDataDao =
                MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getProducerTypes();
        } catch (Exception e) {
            logger.error("Error occurred while fetching time zones from database", e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return new ArrayList<>();
    }

    public static TimezoneDetail getTimezonesById(String timezoneId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getTimezonesById(timezoneId);
        } catch (Exception e) {
            logger.error("Error occurred while fetching time zones from database", e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static List<Controller> getControllerList(Integer accountId){
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getControllerList(accountId);
        } catch (Exception e) {
            logger.error("Exception while getting controller list" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return new ArrayList<>();
    }

    public static List<Controller> getAllControllerList(){
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getAllControllerList();
        } catch (Exception e) {
            logger.error("Exception while getting all controllers" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return new ArrayList<>();
    }

    public static List<MasterKpiGroupBean> getMasterKpiGroupDetails(int accountId)  {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getMasterKPIGroupList(accountId);
        } catch (Exception e)   {

            logger.error("Exception occurred while fetching MasterKpiGroupDetails for account id: [{}]", accountId);

        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return new ArrayList<>();
    }

    public static List<ViewTypes> getAllTypes()  {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getAllTypes();
        } catch (Exception e)   {
            logger.error("Exception occurred while fetching getAllTypes ");
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return Collections.emptyList();
    }

    public static List<ViewKpiGroupsBean> getViewKpiGroups()  {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getViewKpiGroups();
        } catch (Exception e)   {
            logger.error("Exception occurred while fetching ViewKpiGroups ");
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return Collections.emptyList();
    }

    public static KpiCategoryDetails getKpiCategoryDetails(int kpiId)    {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance()
                .open(MasterDataDao.class);
        try {
            return masterDataDao.getCategoryDetailsForKpi(kpiId);
        }   catch (Exception e) {
            logger.error("Error occurred while fetching kpi category details: ",e);
            return null;
        }   finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
    }

    public static List<KpiCategoryDetails> getKpiCategoryDetails()    {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance()
                .open(MasterDataDao.class);
        try {
            return masterDataDao.getCategoryDetails();
        }   catch (Exception e) {
            logger.error("Error occurred while fetching all kpi category details: ",e);
            return Collections.emptyList();
        }   finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
    }

    public static CategoryDetailBean getCategoryDetails(int accountId, int categoryId)    {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance()
                .open(MasterDataDao.class);
        try {
            return masterDataDao.getCategoryDetails(accountId,categoryId);
        }   catch (Exception e) {
            logger.error("Error occurred while fetching  category details: ",e);
            return null;
        }   finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
    }

    private static MasterDataDao getMasterDao(Handle handle) {
        if (handle == null) {
            return MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        } else {
            return handle.attach(MasterDataDao.class);
        }
    }

    private static void closeDaoConnection(Handle handle, MasterDataDao dao) {
        if (handle == null) {
            MySQLConnectionManager.getInstance().close(dao);
        }
    }

    public static int[] bulkInsertIntoComponentVersionKpiMapping(List<CompVersionKpiMappingBean> beans, Handle handle)    {
        MasterDataDao masterDataDao = getMasterDao(handle);
        try {
            return masterDataDao.bulkInsertIntoComponentVersionKpiMapping(beans);
        }   catch (Exception e) {
            logger.error("Error occurred while inserting data into component_version_kpi_mapping. Reason: {}" ,e.getMessage());
            return null;
        }   finally {
            closeDaoConnection(handle, masterDataDao);
        }
    }

    public static int insertIntoComponentVersionKpiMapping(CompVersionKpiMappingBean bean, Handle handle)    {
        MasterDataDao masterDataDao = getMasterDao(handle);
        try {
            return masterDataDao.insertComponentVersionKpiMapping(bean);
        }   catch (Exception e) {
            logger.error("Error occurred while inserting data into component_version_kpi_mapping. Reason: {}" ,e.getMessage());
            return -1;
        }   finally {
            closeDaoConnection(handle, masterDataDao);
        }
    }

    public static List<CompVersionKpiMappingBean> getComponentMappingForKpi(int kpiId, Handle handle)    {
        MasterDataDao masterDataDao = getMasterDao(handle);
        try {
            return masterDataDao.getComponentMappingForKpi(kpiId);
        }   catch (Exception e) {
            logger.error("Error occurred while inserting data into component_version_kpi_mapping. Reason: {}" ,e.getMessage());
            return Collections.emptyList();
        }   finally {
            closeDaoConnection(handle, masterDataDao);
        }
    }

    public static List<CompVersionKpiMappingBean> getComponentVersionKpiMapping()    {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance()
                .open(MasterDataDao.class);
        try {
            return masterDataDao.getCommonVersionOfComponent();
        }   catch (Exception e) {
            logger.error("Error occurred while inserting data into component_version_kpi_mapping. Reason: {}" ,e.getMessage());
            return new ArrayList<>();
        }   finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
    }


    public static CommonVersionCompIdMapping getCommonVersionId(String componentName, String componentVersion) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance()
                .open(MasterDataDao.class);
        try {
            return masterDataDao.getCommonVersionOfComponent(componentName, componentVersion);
        } catch (Exception e) {
            logger.error("Encountered exception while fetching common version details " +
                    "for componentName [{}] and componentVersion [{}]. Reason: {}", componentName, componentVersion, e.getMessage());
            return null;
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
    }

    public static AllKpiList getKpi(String identifier, int accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getKpi(identifier, accountId);
        } catch (Exception e) {
            logger.error("Exception occurred while fetching kpi details for kpi: {}, accountId: {}", identifier,
                    accountId, e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static List<MasterPageActionBean> getPageActionsMasterData() {
        MasterDataDao masterDataDao =
                MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getPageActionsMasterData();
        } catch (Exception e) {
            logger.error("Exception while getting master page action data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public static List<MasterBigFeatureBean> getBigFeaturesMasterData() {
        MasterDataDao masterDataDao =
                MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getBigFeaturesMasterData();
        } catch (Exception e) {
            logger.error("Exception while getting master feature data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return null;
    }

    public List<ComponentInstanceBean> getCompInstForAccount(int accountId, Handle handle) {
        MasterDataDao masterDataDao = getMasterDataDao(handle);
        try {
            return masterDataDao.getCompInstForAccount(accountId);
        } catch (Exception e) {
            logger.error("Exception while getting master feature data from table" + e.getMessage(), e);
        }
        return null;
    }

    public static MasterDataDao getMasterDataDao(Handle handle) {
        if (handle == null) {
            return MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        } else {
            return handle.attach(MasterDataDao.class);
        }
    }

    public static List<MasterSubTypeBean> getAllMasterSubTypes() {
        MasterDataDao masterDataDao =
                MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getAllMasterSubTypes();
        } catch (Exception e) {
            logger.error("Exception while getting master feature data from table" + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return Collections.emptyList();
    }

    public static List<CompInstClusterDetails> getCompInstClusterListAgentId(int accountId) {
        MasterDataDao masterDataDao = MySQLConnectionManager.getInstance().open(MasterDataDao.class);
        try {
            return masterDataDao.getCompInstClusterListAgentId(accountId);
        } catch (Exception e) {
            logger.error(EXCEPTION_GETTING_MASTER_KPI_DETAILS + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(masterDataDao);
        }
        return new ArrayList<>();
    }
}
