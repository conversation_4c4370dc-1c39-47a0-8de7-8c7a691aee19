package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.InstanceHealthPageBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.InstanceHealthPageDetailDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;

public class InstanceHealthPageDetailDataService {
    private InstanceHealthPageDetailDataService() {

    }

    private static final Logger logger = LoggerFactory.getLogger(InstanceHealthPageDetailDataService.class);

    public static List<InstanceHealthPageBean> getInstanceHealthPageDetailList(Handle handle) throws IOException {
        InstanceHealthPageDetailDao instanceHealthPageDetailDao = getInstanceHealthPageDaoObject(handle);
        try {
            return instanceHealthPageDetailDao.getInstanceHealthPageDetailList();
        } catch (Exception e) {
            logger.error("Error occurred while getting InstanceHealthPageDetail list.", e);
            throw new IOException("Error occurred while getting InstanceHealthPageDetail list.");
        } finally {
            closeDaoConnection(handle, instanceHealthPageDetailDao);
        }
    }

    private static InstanceHealthPageDetailDao getInstanceHealthPageDaoObject(Handle handle) {
        if (handle == null) {
            return MySQLConnectionManager.getInstance().open(InstanceHealthPageDetailDao.class);
        } else {
            return handle.attach(InstanceHealthPageDetailDao.class);
        }
    }

    private static void closeDaoConnection(Handle handle, InstanceHealthPageDetailDao dao) {
        if (handle == null) {
            MySQLConnectionManager.getInstance().close(dao);
        }
    }
}
