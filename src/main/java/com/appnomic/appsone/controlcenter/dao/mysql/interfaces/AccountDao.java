package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.MasterTimezoneBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.pojo.TagMappingDetails;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface AccountDao {

    @SqlUpdate("insert into account (name,created_time ,updated_time,status,private_key,public_key,user_details_id,identifier) " +
            "values (:name,:createdTime,:updatedTime,:status,:privateKey,:publicKey,:userIdDetails,:identifier)")
    @GetGeneratedKeys
    int addAccount(@BindBean AccountBean accountBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select a.identifier identifier, a.id id, a.name name, a.public_key publicKey, a.private_key privateKey, " +
            "a.user_details_id userIdDetails, a.status status, a.updated_time updatedTime, a.created_time createdTime " +
            "from account a where a.status = 1")
    List<AccountBean> getAccountList();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mt.id id, mt.timeoffset timeOffset, mt.time_zone_id timeZoneId, mt.account_id accountId " +
            "from tag_mapping tm, mst_timezone mt, tag_details td " +
            "where tm.object_id = :accountId and tm.object_ref_table = 'account' and tm.tag_id = td.id " +
            "and td.name='Timezone' and mt.id = tm.tag_key")
    MasterTimezoneBean getAccountTimezoneDetails(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from account where name = :accountName")
    int getAccountByName(@Bind("accountName") String accountName);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select public_key from account where id = :accountId")
    String getPublicKey(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select private_key from account where id = :accountId")
    String getPrivateKey(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("update account set private_key = :privateKey, public_key = :publicKey where id = :accountId")
    void updateAccountKeys(@Bind("privateKey") String privateKey, @Bind("publicKey") String publicKey, @Bind("accountId") int accountId);

    @SqlUpdate("delete from account where id = :accountId")
    void deleteAccount(@Bind("accountId") int accountId);

    @SqlUpdate("delete from tag_mapping where object_id=:accountId and object_ref_table='account'")
    void deleteTagMapping(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from account where identifier = :identifier and status=1")
    int getAccountByIdentifier(@Bind("identifier") String identifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select identifier from account where id = :accountId")
    String getAccountIdentifierById(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select tag_value tagValue, tag_key tagKey, tag_id tagId, object_id objectId from tag_mapping where object_id=:accountId and object_ref_table='account'")
    List<TagMappingDetails> getAccountTags(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select a.id id, a.name name, a.identifier identifier, a.public_key publicKey, a.private_key privateKey, " +
            "a.user_details_id userIdDetails, a.created_time createdTime, a.updated_time updatedTime " +
            "from account a where a.status = 1 and a.name = :accountName")
    AccountBean getAccountDetailsByName(@Bind("accountName") String accountName);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select a.identifier identifier, a.id id, a.name name, a.public_key publicKey, a.private_key privateKey," +
            " a.user_details_id userIdDetails, a.status status, a.updated_time updatedTime, a.created_time createdTime " +
            "from account a where a.status = 1 and (a.identifier = :account or a.name = :account)")
    AccountBean getAccountDetailsByNameOrIdentifier(@Bind("account") String account);
}
