/* saga<PERSON> created on 23/06/22 inside the package - com.appnomic.appsone.controlcenter.dao.mysql.entity */
package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProducerMapping {
    private int producerId;
    private String producerName;
    private int producerMappingId;
    private int isDefault;
    private int kpiId;
}
