package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.FileProcessedDetailsBean;
import com.appnomic.appsone.controlcenter.beans.FileSummaryDetailsBean;
import com.appnomic.appsone.controlcenter.beans.FileUploadDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.InstallationAttributeBean;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface FileUploadDataDao {

    @GetGeneratedKeys
    @SqlUpdate("INSERT INTO file_upload_details (file_name, file_size, checksum, file_location, upload_by, upload_time, account_id, is_processing) VALUES (:fileName, :fileSize, :checksum, :fileLocation, :uploadBy, :uploadTime, :accountId, :isProcessing)")
    int addUploadFileDetails(@BindBean FileUploadDetailsBean fileUploadDetailsBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id uploadId,file_name fileName,file_size fileSize,checksum,file_location fileLocation,upload_by uploadBy,upload_time uploadTime,account_id accountId,is_processing isProcessing from file_upload_details where account_id = :account_id")
    List<FileUploadDetailsBean> getFileUploadDetailList(@Bind("account_id") Integer accountId);

    @SqlUpdate("UPDATE file_upload_details SET is_processing=:status where id= :id ")
    int updateFileDetails(@Bind("id") int uploadId , @Bind("status") int status);

    @SqlQuery("select count(*) from file_upload_details where account_id= :accountId and checksum = :checksum")
    int getFileCountByCheckSum(@Bind("checksum") String checksum, @Bind("accountId") int accountId);

    @SqlQuery("select count(*) from file_upload_details where account_id= :accountId and file_name = :fileName")
    int getFileCountByName(@Bind("fileName") String fileName, @Bind("accountId") int accountId);

    @SqlUpdate("delete from file_upload_details where id= :uploadId and account_id= :accountId")
    void deleteFileDetails(@Bind("uploadId") int uploadId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id uploadId,file_name fileName,file_size fileSize,checksum,file_location fileLocation,upload_by uploadBy,upload_time uploadTime,account_id accountId,is_processing isProcessing " +
            "from file_upload_details where account_id = :accountId and id = :uploadId")
    FileUploadDetailsBean getFileUploadDetailById(@Bind("uploadId") Integer uploadId, @Bind("accountId") Integer accountId);

    @GetGeneratedKeys
    @SqlUpdate("INSERT INTO file_processed_details (file_name, file_size, checksum, file_location, upload_by, upload_time,processed_by,start_time,end_time,status,account_id, progress) VALUES (:fileName, :fileSize, :checksum, :fileLocation, :uploadBy, :uploadTime, :processedBy, utc_timestamp(), :endTime, :status, :accountId, :progress)")
    int addFileProcessedDetails(@BindBean FileProcessedDetailsBean fileProcessedDetailsBean);

    @SqlUpdate("UPDATE file_processed_details SET progress=:progress,status=:status,end_Time=utc_timestamp()  where id= :processId ")
    int updateFileProcessDetails(@BindBean FileProcessedDetailsBean processedDetailsBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select fd.id uploadId,fd.file_name fileName,fd.file_size fileSize,fd.checksum checksum,fd.file_location fileLocation,fd.upload_by uploadBy,fd.upload_time uploadTime,fd.account_id accountId,ifnull(fp.processed_by,0) processedBy,ifnull(fp.start_time,0) startTime,ifnull(fp.end_time,0) endTime,ifnull(fp.status,0) status from file_upload_details fd left outer join file_processed_details fp on fp.id=fd.id where fd.account_id = :account_id")
    List<FileProcessedDetailsBean> getFileProcessedList(@Bind("account_id") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id processId, file_name fileName,file_size fileSize,checksum ,file_location fileLocation,upload_by uploadBy,upload_time uploadTime,account_id accountId,ifnull(processed_by,0) processedBy,ifnull(start_time,0) startTime,ifnull(end_time,0) endTime,status,progress " +
            "from file_processed_details where id=:processId ")
    FileProcessedDetailsBean getFileProcessedById(@Bind("processId") Integer processId);

    @SqlUpdate("insert into file_summary_details (`file_processed_id`, `key`, `value`, account_id, is_debug_logs, created_time) values (:fileProcessedId,:key,:value, :accountId, :isDebugLogs, utc_timestamp())")
    @GetGeneratedKeys
    int addFileSummaryDetail(@BindBean FileSummaryDetailsBean fileSummaryDetailsBean);

    @SqlBatch("insert into file_summary_details (`file_processed_id`, `key`, `value`, " +
            "account_id, is_debug_logs, created_time) values (:fileProcessedId,:key,:value, " +
            ":accountId, :isDebugLogs, utc_timestamp())")
    @GetGeneratedKeys
    int[] addFileProcessedDetails(@BindBean List<FileSummaryDetailsBean> fileSummaryDetailsBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select name, value from a1_installation_attributes where name = :name")
    InstallationAttributeBean getInstallationAttribute(@Bind("name") String name);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select name, value from a1_installation_attributes")
    List<InstallationAttributeBean> getInstallationAttributes();
}
