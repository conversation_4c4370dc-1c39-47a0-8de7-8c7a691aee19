package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.InstanceHealthPageBean;
import org.skife.jdbi.v2.sqlobject.SqlQuery;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface InstanceHealthPageDetailDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,page_name as pageName,refresh_enabled refreshEnabled,refresh_in_secs refreshInSecs,created_time createdTime ,updated_time updatedTime ,user_details_id usrDetailsId from mst_refresh_conf_details")
    List<InstanceHealthPageBean> getInstanceHealthPageDetailList();
}
