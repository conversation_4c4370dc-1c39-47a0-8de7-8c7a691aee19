package com.appnomic.appsone.controlcenter.dao.mysql.connectors;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.ApplicationToKpiMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.AzureKpi;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.connectors.AzureResourceToKpiMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.connectors.AzureConnectorDataDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.connectors.AzureApplicationDetails;
import com.appnomic.appsone.controlcenter.pojo.connectors.AzureResourceDetails;
import com.appnomic.appsone.controlcenter.pojo.connectors.AzureTokenDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class AzureConnectorDataService {

    private static final Logger logger = LoggerFactory.getLogger(AzureConnectorDataService.class);

    public AzureConnectorDataService(){}

    public int[] addAzureKpis(List<AzureKpi> azureKpiList)
    {
        AzureConnectorDataDao azureConnectorDataDao = getMsqlDataDao();
        try {
            return azureConnectorDataDao.addAzureKpis(azureKpiList);
        } catch (Exception e) {
            logger.error("Error occurred while adding azure KPIs{}", azureKpiList, e);
        } finally {
            MySQLConnectionManager.getInstance().close(azureConnectorDataDao);
        }
        return new int[]{};
    }
    public void updateAzureKpis(List<AzureKpi> azureKpiList)
    {
        AzureConnectorDataDao azureConnectorDataDao = getMsqlDataDao();
        try {
            azureConnectorDataDao.updateAzureKpis(azureKpiList);
        } catch (Exception e) {
            logger.error("Error occurred while updating azure KPIs{}", azureKpiList, e);
        } finally {
            MySQLConnectionManager.getInstance().close(azureConnectorDataDao);
        }
    }
    public List<String> getAzureKpis()
    {
        AzureConnectorDataDao azureConnectorDataDao = getMsqlDataDao();
        try {
            return azureConnectorDataDao.getAzureKpis();
        } catch (Exception e) {
            logger.error("Error occurred while getting azure KPIs{}", (Object) e.getStackTrace());
        } finally {
            MySQLConnectionManager.getInstance().close(azureConnectorDataDao);
        }
        return new ArrayList<>();
    }
    public List<String> getHealKpis()
    {
        AzureConnectorDataDao azureConnectorDataDao = getMsqlDataDao();
        try {
            return azureConnectorDataDao.getHealKpis();
        } catch (Exception e) {
            logger.error("Error occurred while getting heal KPIs{}", (Object) e.getStackTrace());
        } finally {
            MySQLConnectionManager.getInstance().close(azureConnectorDataDao);
        }
        return new ArrayList<>();
    }

    public List<Integer> getAzureKpisList(String kpiDomain)
    {
        AzureConnectorDataDao azureConnectorDataDao = getMsqlDataDao();
        try {
            return azureConnectorDataDao.getAzureKpisList(kpiDomain);
        } catch (Exception e) {
            logger.error("Error occurred while fetching azure kpis list{}", e.getMessage());
        } finally {
            MySQLConnectionManager.getInstance().close(azureConnectorDataDao);
        }
        return new ArrayList<>();
    }

    public void addAzureApplicationDetails(List<AzureApplicationDetails> azureApplicationDetails)
    {
        AzureConnectorDataDao azureConnectorDataDao = getMsqlDataDao();
        try {
            azureConnectorDataDao.deleteAzureApplicationDetails();
            azureConnectorDataDao.addAzureApplicationDetails(azureApplicationDetails);
        } catch (Exception e) {
            logger.error("Error occurred while fetching component details compId{}", azureApplicationDetails, e);
        } finally {
            MySQLConnectionManager.getInstance().close(azureConnectorDataDao);
        }
    }

    public void addAzureResourceDetails(List<AzureResourceDetails> azureResourceDetails)
    {
        AzureConnectorDataDao azureConnectorDataDao = getMsqlDataDao();
        try {
            azureConnectorDataDao.deleteAzureResourceDetails();
            azureConnectorDataDao.addAzureResourceDetails(azureResourceDetails);
        } catch (Exception e) {
            logger.error("Error occurred while fetching component details compId{}", azureResourceDetails, e);
        } finally {
            MySQLConnectionManager.getInstance().close(azureConnectorDataDao);
        }
    }

    public void addAzureTokenDetails(List<AzureTokenDetails> azureTokenDetails)
    {
        AzureConnectorDataDao azureConnectorDataDao = getMsqlDataDao();
        try {
            azureConnectorDataDao.addAzureTokenDetails(azureTokenDetails);
        } catch (Exception e) {
            logger.error("Error occurred while fetching component details compId{}", azureTokenDetails, e);
        } finally {
            MySQLConnectionManager.getInstance().close(azureConnectorDataDao);
        }
    }

    public void addAzureApplicationKpiMapping(List<ApplicationToKpiMapping> azureApplicationToKpiMappings)
    {
        AzureConnectorDataDao azureConnectorDataDao = getMsqlDataDao();
        try {
            azureConnectorDataDao.deleteAzureApplicationKpiMapping();
            azureConnectorDataDao.addAzureApplicationKpiMapping(azureApplicationToKpiMappings);
        } catch (Exception e) {
            logger.error("Error occurred while fetching component details compId{}", azureApplicationToKpiMappings, e);
        } finally {
            MySQLConnectionManager.getInstance().close(azureConnectorDataDao);
        }
    }

    public void addAzureResourceKpiMapping(List<AzureResourceToKpiMapping> azureResourceToKpiMappings)
    {
        AzureConnectorDataDao azureConnectorDataDao = getMsqlDataDao();
        try {
            azureConnectorDataDao.deleteAzureResourceKpiMapping();
            azureConnectorDataDao.addAzureResourceKpiMapping(azureResourceToKpiMappings);
        } catch (Exception e) {
            logger.error("Error occurred while fetching component details compId{}", azureResourceToKpiMappings, e);
        } finally {
            MySQLConnectionManager.getInstance().close(azureConnectorDataDao);
        }
    }

    private AzureConnectorDataDao getMsqlDataDao(){
        return MySQLConnectionManager.getInstance().open(AzureConnectorDataDao.class);
    }


}
