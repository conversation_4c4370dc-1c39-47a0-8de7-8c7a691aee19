package com.appnomic.appsone.controlcenter.dao.opensearch;

import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.OpenSearchConnectionManager;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.opeasearchquery.helper.OpenSearchQueryHelper;
import com.appnomic.appsone.opeasearchquery.queryopts.Documents;
import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.appnomic.appsone.opeasearchquery.results.RawDocumentResults;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.opensearch.AgentCommandsData;
import com.heal.configuration.pojos.opensearch.AgentHealthData;
import com.heal.configuration.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.Result;
import org.opensearch.client.opensearch.core.IndexResponse;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Suman - 29-12-2021
 */
@Slf4j
public class AgentHealthStatusRepo {

    private static final String AGENT_IDENTIFIER_FIELD = "agentIdentifier";
    private static final String COMMAND_JOB_ID_FIELD = "commandJobId";
    private final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    public AgentCommandsData getAgentCommandDetails(String accountIdentifier, String agentIdentifier, String commandJobId) {
        String indexName = Constants.INDEX_PREFIX_HEAL_AGENT_COMMANDS + "_" + accountIdentifier.toLowerCase() + "_*";
        try {

            OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, Constants.INDEX_PREFIX_HEAL_AGENT_COMMANDS);
            if (openSearchClient == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            indexNames.add(indexName);

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair(COMMAND_JOB_ID_FIELD, commandJobId));
            matchFields.add(new NameValuePair(AGENT_IDENTIFIER_FIELD, agentIdentifier));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .timeStampFieldName("lastDataReceivedTimeInGMT")
                    .build();

            RawDocumentResults results = OpenSearchQueryHelper.getRawDocuments(queryOptions, openSearchClient);
            if (results != null && results.getDocuments() != null && !results.getDocuments().isEmpty()) {
                return objectMapper.readValue(results.getDocuments().get(0).getSource(), new TypeReference<>() {
                });
            }

        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error in getting agent health details from {} having agentIdentifier {} & commandJobId {}", indexName, agentIdentifier, commandJobId, e);
        }
        return null;
    }

    public Map<String, Long> getAgentHeartBeatTime(String accountIdentifier) {

        String indexName = Constants.INDEX_PREFIX_HEAL_HEALTH_AGENT + "_" + accountIdentifier.toLowerCase();
        try {

            OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, Constants.INDEX_PREFIX_HEAL_HEALTH_AGENT);
            if (openSearchClient == null) {
                return Collections.emptyMap();
            }

            List<String> indexNames = new ArrayList<>();
            indexNames.add(indexName);

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .isTimeSeriesData(false)
                    .fetchAllRecords(true)
                    .timeStampFieldName("lastDataReceivedTimeInGMT")
                    .build();

            RawDocumentResults results = OpenSearchQueryHelper.getRawDocuments(queryOptions, openSearchClient);

            if (results != null && results.getDocuments() != null && !results.getDocuments().isEmpty()) {
                List<AgentHealthData> agentHealthDataList = objectMapper.readValue(results.getDocuments().stream().map(Documents::getSource).collect(Collectors.toList()).toString(), new TypeReference<>() {
                });
                return agentHealthDataList.stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(AgentHealthData::getAgentIdentifier, AgentHealthData::getLastDataReceivedTimeInGMT, ((x1, x2) -> x1)));
            }
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error in getting all agent heart beat time from index - {}", indexName, e);
        }
        return Collections.emptyMap();
    }


    public void insertAgentHeartBeatTime(String accountId, String agentId, long timeInGMT) {

        String indexName = Constants.INDEX_PREFIX_HEAL_HEALTH_AGENT + "_" + accountId.toLowerCase();
        try {

            AgentHealthData agentHealthData = AgentHealthData.builder()
                    .lastDataReceivedTimeInGMT(timeInGMT)
                    .agentIdentifier(agentId)
                    .accountIdentifier(accountId)
                    .timestamp(DateHelper.getDate(timeInGMT))
                    .build();

            IndexResponse indexResponse = new OpenSearchRepo<AgentHealthData>().insertIndex(indexName, agentHealthData, agentId, accountId, Constants.INDEX_PREFIX_HEAL_HEALTH_AGENT);
            if (indexResponse == null) {
                log.error("Error in inserting index for agent health details, index:{}, agentIdentifier:{}, accountId:{}", indexName, agentId, accountId);
                return;
            }
            if (indexResponse.result() == Result.Created) {
                log.debug("Document inserted in {} index, agentId:{}, accountId:{}", indexName, agentId, accountId);
            }

        } catch (Exception e) {
            log.error("Error while inserting agent heart beat time from index:{}, agentIdentifier:{}, accountId:{}", indexName, agentId, accountId, e);
        }
    }

    public void insertCommandDetails(String accountId, String physicalAgentId, String commandId, String commandJobId,
                                     String currentState, Long lastExecutedTime, Map<String, String> metadata) throws ControlCenterException {
        String indexName = Constants.INDEX_PREFIX_HEAL_AGENT_COMMANDS + "_" + accountId.toLowerCase();
        try {
            String date = DateHelper.getWeeksAsString(lastExecutedTime, lastExecutedTime).get(0);
            indexName = indexName + "_" + date;
            AgentCommandsData agentCommandsData = AgentCommandsData
                    .builder()
                    .agentIdentifier(physicalAgentId)
                    .commandId(commandId)
                    .commandJobId(commandJobId)
                    .lastDataReceivedTimeInGMT(lastExecutedTime)
                    .currentStatus(currentState)
                    .metadata(metadata)
                    .timestamp(DateHelper.getDate(lastExecutedTime))
                    .build();

            IndexResponse indexResponse = new OpenSearchRepo<AgentCommandsData>().insertIndex(indexName, agentCommandsData, commandJobId, accountId, Constants.INDEX_PREFIX_HEAL_AGENT_COMMANDS);
            if (indexResponse == null) {
                throw new ControlCenterException("Error in inserting index for agent health details");
            }
            if (indexResponse.result() == Result.Created) {
                log.debug("Document inserted in {} index", indexName);
            }
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error in inserting the agent health details in {} ", indexName, e);
            throw new ControlCenterException("Error in inserting the agent health details or in closing connection");
        }
    }
}
