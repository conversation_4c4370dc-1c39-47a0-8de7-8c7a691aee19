package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.common.exception.AppsOneException;
import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.beans.ActionDetails;
import com.appnomic.appsone.controlcenter.dao.opensearch.InstanceKpiThresholdRepo;
import com.appnomic.appsone.controlcenter.pojo.agentconfig.CommandDetails;
import com.appnomic.appsone.controlcenter.beans.ProducerType;
import com.appnomic.appsone.controlcenter.businesslogic.GetAdhocMaintenanceWindowBL;
import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.cache.ThreadPool;
import com.appnomic.appsone.controlcenter.cache.keys.ProducerKpis;
import com.appnomic.appsone.controlcenter.common.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.*;
import com.appnomic.appsone.controlcenter.dao.opensearch.AgentHealthStatusRepo;
import com.appnomic.appsone.controlcenter.dao.redis.AgentRepo;
import com.appnomic.appsone.controlcenter.dao.redis.InstanceRepo;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.pojo.agentconfig.*;
import com.appnomic.appsone.controlcenter.util.*;
import com.heal.configuration.enums.KpiType;
import com.heal.configuration.pojos.BasicAgentEntity;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.datareceiver.InstanceData;
import com.heal.configuration.pojos.datareceiver.KpiData;
import com.heal.configuration.pojos.datareceiver.RawKpiData;
import com.heal.configuration.pojos.Agent;
import com.heal.configuration.pojos.opensearch.InstanceKpiThresholds;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.appnomic.appsone.controlcenter.common.Constants.*;
import static java.util.stream.Collectors.*;

public class ConfigurationDataService {

    private static final Logger log = LoggerFactory.getLogger(ConfigurationDataService.class);
    private static final String VALUE = "value";
    private static final String IDENTIFIER = "identifier";
    private static final String TIME_AGENTS = "Time taken for agents:{}";
    private static final String kpiIdentifier = com.appnomic.appsone.util.ConfProperties.getString(Constants.INSTANCE_KPI_AVAILABILITY,
            Constants.INSTANCE_KPI_AVAILABILITY_DEFAULT);
    private static final long HOST_AVAILABILITY_EXPIRY_MINUTES = ConfProperties.getLong(Constants.HOST_AVAILABILITY_REDIS_KEY_EXPIRY_MINUTES,
            Constants.HOST_AVAILABILITY_REDIS_KEY_EXPIRY_MINUTES_DEFAULT);

    private ConfigurationDataService() {
        //Dummy constructor to hide the implicit one
    }

    public static GenericResponse<List<AccountConfiguration>> getAgentConfigurationDetails(Request request) {
        GenericResponse<List<AccountConfiguration>> response = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);
            if (request.queryParams(IDENTIFIER) == null || request.queryParams(IDENTIFIER).trim().isEmpty()) {
                log.error("Agent identifier is invalid. It is either null or empty.");
                response.setData(new ArrayList<>());
                response.setMessage("Agent identifier is invalid. It is either null or empty.");
                response.setResponseStatus(StatusResponse.FAILURE.toString());
                return response;
            }
            response.setMessage(StatusResponse.SUCCESS.name());
            response.setResponseStatus(StatusResponse.SUCCESS.name());
            response.setData(getAgentConfigData(request.queryParams(IDENTIFIER).trim(), requestObject.getHeaders()));
        } catch (AppsOneException e) {
            response.setData(new ArrayList<>());
            response.setMessage(e.getErrorMessage());
            response.setResponseStatus(StatusResponse.FAILURE.toString());
        } catch (Exception e) {
            log.error("Error occurred while fetching the agent configuration data. Details: ", e);
            response.setData(new ArrayList<>());
            response.setMessage(INTERNAL_ERROR_MESSAGE);
            response.setResponseStatus(StatusResponse.FAILURE.toString());
        }
        return response;
    }

    private static List<AccountConfiguration> getAgentConfigData(String agentIdentifier, Map<String, String> requestHeaders) throws AppsOneException {

        AgentHealthStatusRepo agentHealthStatusRepo = new AgentHealthStatusRepo();
        List<AccountConfiguration> accountConfigurations = new ArrayList<>();

        AgentBean agentBean = AgentDataService.getAgentBeanData(agentIdentifier);
        if (agentBean == null) {
            throw new AppsOneException(MessageFormat.format("Invalid agent identifier \"{0}\".", agentIdentifier));
        }

        if (agentBean.getStatus() == 0) {
            throw new AppsOneException(MessageFormat.format("Agent is disabled for identifier \"{0}\".", agentIdentifier));
        }

        Map<Integer, String> kpiImportanceTypes = MasterDataService.getAllTypes().stream()
                .filter(f -> f.getTypeId() == 120)
                .collect(toMap(ViewTypes::getSubTypeId, ViewTypes::getTypeName));

        Map<String, String> compVersionKpiMapping = MasterDataService.getComponentVersionKpiMapping().stream()
                .collect(toMap(c -> c.getComponentId()+":"+c.getCommonVersionId()+":"+c.getKpiDetailsId(),
                        c -> kpiImportanceTypes.getOrDefault(c.getImportantId(), "Low"), (existing, replacement) -> replacement));
        List<AccountBean> accountList = MasterCache.getFilteredAccounts();

        checkComponentAgentVersionHeader(agentIdentifier, requestHeaders, agentBean, accountList);

        ThreadPool.INSTANCE.hostAvailabilityExecutor.execute(() -> buildHostAvailability(agentBean));

        List<ComputedKpiBean> computedKpiBeans = new ArrayList<>();
        try {
            computedKpiBeans = new KPIDataService().getComputationExpressions(null);
        } catch (ControlCenterException e) {
            log.error("Error while fetching base metric IDs. KPIs cannot be marked as base metrics.");
        }

        // get all view types
        List<ViewTypes> viewTypes = MasterDataService.getAllTypes();
        Map<Integer, ViewTypes> subTypeToViewType = viewTypes
                .parallelStream()
                .collect(toMap(ViewTypes::getSubTypeId, Function.identity()));

        List<Integer> baseMetrics = computedKpiBeans.parallelStream().map(ComputedKpiBean::getKpiDetailsId).collect(Collectors.toList());

        Map<Integer, List<ComputedKpiBean>> computedKpiVsSupportingKpis = computedKpiBeans.parallelStream()
                .collect(Collectors.groupingBy(ComputedKpiBean::getComputedKpiId));

        List<AllKpiList> allKpiLists = MasterDataService.getAllKpisList();

        Map<Integer, AllKpiList> allKpiMap = allKpiLists.parallelStream()
                .collect(Collectors.toMap(AllKpiList::getKpiId, Function.identity()));

        long st;
        for (AccountBean account : accountList) {
            long accStart = System.currentTimeMillis();
            st = System.currentTimeMillis();

            agentHealthStatusRepo.insertAgentHeartBeatTime(account.getIdentifier(), agentBean.getPhysicalAgentIdentifier(), System.currentTimeMillis());

            Set<TagMappingBean> agentIdsFromTagMapping = new CompInstanceDataService()
                    .getServiceVsAgentIdsFromTagMapping(account.getId(), null);
            log.debug("Time taken for Agent Tags for account: {}, {} ms.", account.getName(), System.currentTimeMillis()-st);

            st = System.currentTimeMillis();
            List<TagDetailsBean> tagDetailsBeans = MasterDataService.getTagDetailsForAccount(account.getId());
            log.debug("Time taken for tag details for account: {}, {} ms.", account.getName(), System.currentTimeMillis()-st);

            st = System.currentTimeMillis();
            AgentConfig agentConfig = getAgentConfiguration(agentBean,  agentIdsFromTagMapping, tagDetailsBeans, account.getId());
            if (agentConfig == null) {
                continue;
            }
            log.debug("Time taken for agent config for account: {}, {} ms.", account.getName(), System.currentTimeMillis()-st);

            AccountConfiguration accountConfiguration = new AccountConfiguration();
            List<AgentBean> agentBeanList = AgentDataService.getForensicAgentsByAccountId(account.getId());

            agentBeanList = agentBeanList.stream().filter(Objects::nonNull).collect(toList());

            ForensicAgent forensicAgent = null;
            if(!agentBeanList.isEmpty()) {
                forensicAgent = new ForensicAgent();
                forensicAgent.setForensicId(agentBeanList.get(0).getAgentTypeId());
                forensicAgent.setForensicName(agentBeanList.get(0).getName());
                forensicAgent.setForensicIdentifier(agentBeanList.get(0).getUniqueToken());
                forensicAgent.setForensicPhysicalAgentIdentifier(agentBeanList.get(0).getPhysicalAgentIdentifier());
            }

            List<ServiceConfigurationBean> serviceKpiSuppressionPersistenceDao = ServiceConfigurationDataService.getServiceConfiguration(account.getId());
            log.debug("service level persistence and suppression found with size {}", serviceKpiSuppressionPersistenceDao.size());

            String applicableTo = "instances";

            List<ServiceKpiThreshold> serviceKpiThresholdList = ImportServicesDataService
                    .getServiceKpiThresholdsByServiceApplicableTo(account.getId(), applicableTo);
            log.debug("service kpi threshold list is {} that is applicable to {}", serviceKpiThresholdList, applicableTo);


            st = System.currentTimeMillis();
            accountConfiguration.setAgentList(Collections.singletonList(agentConfig));
            log.trace(TIME_AGENTS, (System.currentTimeMillis() - st));

            st = System.currentTimeMillis();

            ActionScriptDataService actionScriptDataService = new ActionScriptDataService();

            List<ActionCategoryCommandDetails> actionCategoryCommandDetails = null;
            try {
                actionCategoryCommandDetails = actionScriptDataService.getCategoryKeyMapping(account.getId());
                log.debug("action with category and command mapping has size {}, account id {}", actionCategoryCommandDetails.size(), account.getId());
            } catch (ControlCenterException e) {
                log.error("Error while fetching action with category and command mapping {}", e.getMessage(), e);
            }


            List<CategoryKpiMapping> categoryByKpi = ComponentDataService.getCategoryByKpi(account.getId(), null);
            if (categoryByKpi == null) {
                continue;
            }
            log.debug("category and kpi details for account id {}, size {}", account.getId(), categoryByKpi.size());

            List<CompInstanceClusterConfig> compInstClusterConfigList =
                    getInstanceClusterKpiDetails(account, allKpiMap,
                            baseMetrics, computedKpiVsSupportingKpis, subTypeToViewType,
                            actionCategoryCommandDetails, categoryByKpi, serviceKpiSuppressionPersistenceDao,
                            serviceKpiThresholdList, agentConfig.getCompInstanceIds(), compVersionKpiMapping);

            if (compInstClusterConfigList.isEmpty()) {
                continue;
            }
            accountConfiguration.setCompInstanceDetail(compInstClusterConfigList);
            log.debug("Time taken for component instances:{}", (System.currentTimeMillis() - st));

            accountConfiguration.setAccountId(account.getIdentifier());
            accountConfiguration.setAccountName(account.getName());
            accountConfiguration.setId(account.getId());
            accountConfiguration.setPublicKey(account.getPublicKey());
            accountConfiguration.setForensicAgent(forensicAgent);

            // get instance forensic trigger status
            CategoryForensicsDataService categoryForensicsDataService = new CategoryForensicsDataService();
            Map<String, String> instanceIdMap =
                    compInstClusterConfigList.parallelStream().collect(toMap(CompInstanceClusterConfig::getInstanceId,
                    CompInstanceClusterConfig::getIdentifier));
            List<CompInstanceForensicCategoryBean> compInstanceForensicCategoryList =
                    categoryForensicsDataService.getCompInstanceForensicDetailsForAllInstanceId(instanceIdMap.keySet(), null);


            // add forensic action for JIT forensic.
            List<CommandDetails> commandDetails = getCommandDetails(account.getId(), subTypeToViewType,
                    actionCategoryCommandDetails, compInstanceForensicCategoryList, instanceIdMap);
            accountConfiguration.setForensicActions(commandDetails);

            accountConfigurations.add(accountConfiguration);
            log.debug("Time taken for fetching ({}) acc data is {} ms.", accountConfiguration.getAccountName(), (System.currentTimeMillis() - accStart));
        }

        return accountConfigurations;
    }

    private static List<CommandDetails> getCommandDetails(int accountId, Map<Integer, ViewTypes> subTypeToViewType, List<ActionCategoryCommandDetails> actionCategoryCommandDetails, List<CompInstanceForensicCategoryBean> compInstanceForensicCategoryList, Map<String, String> instanceIdMap) {
        try {

            ActionScriptDataService actionScriptDataService = new ActionScriptDataService();

            Map<Integer, List<ActionCategoryCommandDetails>> categoryMap = actionCategoryCommandDetails
                    .parallelStream()
                    .collect(groupingBy(ActionCategoryCommandDetails::getCommandId));

            // get all actions - ActionDetails
            List<ActionDetails> allActions = actionScriptDataService.getActionScriptDetailsWithStandardType();
            log.debug("all actions with standard type has size {}", allActions.size());
            Map<Integer, ActionDetails> actionDetailsMap = allActions
                    .parallelStream()
                    .collect(toMap(ActionDetails::getId, Function.identity()));

            // get command argument details
            List<CommandArg> commandArgumentList = actionScriptDataService.getAllCommandArguments(accountId);
            log.debug("command argument list with size {}", commandArgumentList.size());
            Map<Integer, List<CommandArgument>> commandArgumentMap = commandArgumentList
                    .parallelStream()
                    .map(x -> CommandArgument.builder()
                            .key(x.getKey())
                            .value(x.getValue())
                            .argumentType(x.getArgumentType())
                            .id(x.getId())
                            .defaultValue(x.getDefaultValue())
                            .valueType(x.getValueType())
                            .placeHolder(x.getIsPlaceHolder() != 0)
                            .build())
                    .collect(groupingBy(CommandArgument::getId));
            log.debug("map of command id and argument has size {}", commandArgumentMap.size());
            log.trace("command id with list of argument {}", commandArgumentMap);

            // get all command details.
            List<CommandDetailsBean> commandDetailsBeans = CommandDataService.getCommandDetailsByForensicId();
            log.debug("total command details found is {}", commandDetailsBeans.size());


            Map<Integer, List<CompInstanceForensicCategoryBean>> actionIdInstanceIdentifierMap = compInstanceForensicCategoryList.parallelStream()
                    .filter(x -> x.getShouldTrigger() == 0)
                    .collect(groupingBy(CompInstanceForensicCategoryBean::getObjectId));


            return commandDetailsBeans.parallelStream().map(command -> {
                Map<String, Integer> result = new HashMap<>();
                List<ActionDetails> actionDetailsList = new ArrayList<>();
                List<ActionMappingKey> actionMappingKeys = new ArrayList<>();
                if(categoryMap.containsKey((command.getId()))){
                    List<ActionDetails> s = categoryMap.get(command.getId())
                            .parallelStream()
                            .map(x -> actionDetailsMap.get(x.getActionId()))
                            .distinct()
                            .collect(toList());
                    actionDetailsList.addAll(s);

                    if(actionIdInstanceIdentifierMap.containsKey(command.getId())) {
                        List<CompInstanceForensicCategoryBean> matchedByCommandIdInstanceForensicDetails =
                                actionIdInstanceIdentifierMap.get(command.getId());

                        log.debug("filter by command id list of instance forensic details {}",
                                matchedByCommandIdInstanceForensicDetails);

                        Map<String, Integer> instanceIdentifierForensicMap = matchedByCommandIdInstanceForensicDetails.parallelStream()
                                .filter(instanceForensic -> instanceIdMap.containsKey(String.valueOf(instanceForensic.getCompInstanceId())))
                                .collect(Collectors.toMap(
                                        instanceForensic -> instanceIdMap.get(String.valueOf(instanceForensic.getCompInstanceId())),
                                        CompInstanceForensicCategoryBean::getShouldTrigger
                                ));

                        result.putAll(instanceIdentifierForensicMap);

                        log.debug("should trigger forensic for instance identifier {}, for command id {}",
                                instanceIdentifierForensicMap,
                                command.getId());
                    }

                    actionMappingKeys.addAll(categoryMap.get(command.getId())
                            .parallelStream()
                            .map(actionCategoryCommandDetails1 -> ActionMappingKey
                                    .builder()
                                    .type(actionCategoryCommandDetails1.getType())
                                    .id(actionCategoryCommandDetails1.getCategoryId())
                                    .name(actionCategoryCommandDetails1.getCategoryName())
                                    .identifier(actionCategoryCommandDetails1.getCategoryIdentifier())
                                    .build())
                            .collect(toList()));

                }

                ViewTypes commandOutputSubType = new ViewTypes();
                if (subTypeToViewType.containsKey(command.getOutputTypeId())) {
                    commandOutputSubType = subTypeToViewType.get(command.getOutputTypeId());
                }

                ViewTypes subType = new ViewTypes();
                if (subTypeToViewType.containsKey(command.getActionId())) {
                    subType = subTypeToViewType.get(command.getActionId());
                }

                return CommandDetails
                        .builder()
                        .id(command.getId())
                        .name(command.getName())
                        .identifier(command.getIdentifier())
                        .suppression(command.getSuppression())
                        .signature(command.getSignature())
                        .commandType(subType.getTypeName())
                        .commandOutType(commandOutputSubType.getSubTypeName())
                        .forensicActionType(subType.getSubTypeName())
                        .commandName(command.getCommandName())
                        .timeoutInSecs(command.getTimeOutInSecs())
                        .producerType(ProducerType.fromValue(command.getProducerTypeId()))
                        .arguments(commandArgumentMap.containsKey(command.getId()) ? commandArgumentMap.get(command.getId()) : new ArrayList<>())
                        .actionDetails(actionDetailsList)
                        .mappingKeys(actionMappingKeys)
                        .instanceMapping(result)
                        .build();

            }).filter(Objects::nonNull).collect(toList());
        } catch (Exception exception) {
            log.error("Exception occurred while getting all the command details {}", exception.getMessage(), exception);
            return new ArrayList<>();
        }
    }

    public static GenericResponse<List<RulesBean>> getRulesConfigDetails(Request request, Response response) {
        GenericResponse<List<RulesBean>> responseObject = new GenericResponse<>();
        List<RulesBean> result = new ArrayList<>();

        String agentId = request.params(AGENT_IDENTIFIER);
        log.debug("getting rules detail for agent identifier: {}", agentId);

        try {
            List<AccountAgentMapping> accountAgentMappings = AgentDataService.getAgentAccountMappingByAgentId(agentId);

            for (AccountAgentMapping aam : accountAgentMappings) {
                log.debug("iterating over account id: {}", aam.getAccountId());

                List<TagMappingDetails> tags = MasterDataService.getTagMappingDetails(aam.getAccountId());
                List<TagDetailsBean> tagDetailsBeanList = MasterDataService.getTagDetailsForAccount(aam.getAccountId());

                int controllerTagId = tagDetailsBeanList.parallelStream()
                        .filter(tagDetail -> CONTROLLER.equalsIgnoreCase(tagDetail.getName()))
                        .map(TagDetailsBean::getId)
                        .findFirst()
                        .orElse(0);

                List<String> serviceList;
                if (tags != null) {
                    serviceList = tags.parallelStream()
                            .filter(tag -> tag.getObjectRefTable().equalsIgnoreCase(AGENT_TABLE))
                            .filter(tag -> tag.getTagId() == controllerTagId)
                            .filter(tag -> tag.getObjectId() == aam.getAgentId())
                            .map(TagMappingDetails::getTagKey)
                            .collect(Collectors.toList());

                    log.debug("services found: {}", serviceList);

                    List<Controller> controllers = CommonUtils.getControllersByType(SERVICES_CONTROLLER_TYPE, aam.getAccountId());

                    Map<String, Controller> controllerMap = new HashMap<>();

                    controllers.forEach(controller -> {
                        if (controller.getStatus() == 1) {
                            controllerMap.put(controller.getAppId(), controller);
                        }
                    });


                    final List<String> serviceNameList = serviceList.stream().filter(controllerMap::containsKey).collect(Collectors.toList());

                    Predicate<TagMappingDetails> rulesPredicate =
                            a -> a.getObjectRefTable().equalsIgnoreCase(RULES_TABLE);
                    List<TagMappingDetails> rulesTagMappingForServiceList;

                    rulesTagMappingForServiceList = tags.stream()
                            .filter(rulesPredicate)
                            .filter(tag -> tag.getTagId() == controllerTagId)
                            .filter(tag -> serviceNameList.contains(tag.getTagKey()))
                            .collect(Collectors.toList());

                    List<Integer> ruleIdList = rulesTagMappingForServiceList.stream()
                            .filter(tag -> tag.getTagId() == controllerTagId)
                            .filter(tag -> serviceNameList.contains(tag.getTagKey()))
                            .map(TagMappingDetails::getObjectId)
                            .collect(Collectors.toList());

                    List<RulesBean> rulesBeanList = RulesDataService.getRules(ruleIdList);

                    for (RulesBean rule : rulesBeanList) {
                        List<Map<String, String>> tagMap = new ArrayList<>();

                        rulesTagMappingForServiceList.stream()
                                .filter(tag -> tag.getObjectId() == rule.getId()).forEach(tagMappingDetails -> {
                                    Map<String, String> tag = getTag(tagDetailsBeanList, tagMappingDetails,
                                            aam.getAccountId());
                                    if (tag != null)
                                        tagMap.add(tag);
                                });
                        rule.setTags(tagMap);

                    }

                    log.debug("total rules found for the agent and account: {}", rulesBeanList.size());
                    result.addAll(rulesBeanList);
                }
            }
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            response.status(200);
            responseObject.setData(result);

        } catch (Exception e) {
            log.error("Error in getting rules by agent identifier: " + agentId, e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(400);
        }
        return responseObject;
    }

    private static List<CompInstanceClusterConfig> getInstanceClusterKpiDetails(AccountBean account,
                                                                                Map<Integer, AllKpiList> allKpiMap,
                                                                                List<Integer> baseMetrics,
                                                                                Map<Integer, List<ComputedKpiBean>> computedKpiVsSupportingKpis,
                                                                                Map<Integer, ViewTypes> subTypeToViewType,
                                                                                List<ActionCategoryCommandDetails> actionCategoryCommandDetails,
                                                                                List<CategoryKpiMapping> categoryByKpi,
                                                                                List<ServiceConfigurationBean> serviceKpiSuppressionPersistence,
                                                                                List<ServiceKpiThreshold> serviceKpiThresholdList,
                                                                                List<Integer> compInstanceIds,
                                                                                Map<String, String> compVersionKpiMapping) {
        List<CompInstanceClusterConfig> clusterConfigList = new ArrayList<>();

        try {
            List<CompInstClusterDetails> compInstancesList = MasterDataService.getCompInstClusterListAgentId(account.getId());
            Map<Integer, CompInstClusterDetails> compInstancesMap = compInstancesList.stream().collect(Collectors.toMap(CompInstClusterDetails::getInstanceId, Function.identity()));

            // Clusters id and identifier map
            Map<Integer, String> clustersMap = compInstancesList
                    .parallelStream()
                    .filter(t -> t.getIsCluster() == 1)
                    .collect(Collectors.toMap(CompInstClusterDetails::getInstanceId, CompInstClusterDetails::getIdentifier));

            // Instances with their cluster details
            Map<Integer, Set<Integer>> instanceClusterMap = MasterDataService.getClusterInstanceMapping(account.getId())
                    .parallelStream()
                    .collect(Collectors.groupingBy(ClusterInstanceMapping::getCompInstanceId,
                            Collectors.mapping(ClusterInstanceMapping::getClusterId, Collectors.toSet())));

            List<CompInstanceControllerTag> compInstancesTags = new CompInstanceDataService().getControllersMappedToCompInstance(account.getId(), null);

            Map<Boolean, List<CompInstanceControllerTag>> partitionedOutput = compInstancesTags.parallelStream()
                    .collect(Collectors.partitioningBy(t -> 191 == t.getControllerTypeId()));

            Map<Integer, List<CompInstanceControllerTag>> mappedApps = partitionedOutput.get(true).parallelStream()
                    .collect(groupingBy(CompInstanceControllerTag::getObjectId));

            Map<Integer, List<CompInstanceControllerTag>> mappedServices = partitionedOutput.get(false).parallelStream()
                    .collect(groupingBy(CompInstanceControllerTag::getObjectId));

            List<ComponentKpis> componentKpisList = MasterDataService.getCompKpiMapping(account.getId());

            InstanceKpiThresholdRepo instanceKpiThresholdRepo = new InstanceKpiThresholdRepo();

            clusterConfigList = compInstanceIds.parallelStream()
                    .filter(compInstancesMap::containsKey)
                    .map(instanceId -> {
                        CompInstanceClusterConfig config = new CompInstanceClusterConfig();

                        try {

                            CompInstClusterDetails instance = compInstancesMap.get(instanceId);
                            List<String> clusterConfigs = new ArrayList<>();
                            GetAdhocMaintenanceWindowBL getAdhocMaintenanceWindowBL = new GetAdhocMaintenanceWindowBL();

                            Set<Integer> clusterIds = instanceClusterMap.getOrDefault(instance.getInstanceId(), new HashSet<>());
                            //populating cluster identifiers for instances only
                            if (instance.getIsCluster() == 0 && clusterIds != null) {
                                clusterIds.stream().filter(clustersMap::containsKey)
                                        .forEach(clusterId -> clusterConfigs.add(clustersMap.get(clusterId)));
                            }

                            config.setInstanceId(String.valueOf(instance.getInstanceId()));
                            config.setInstanceName(instance.getInstanceName());
                            config.setIdentifier(instance.getIdentifier());
                            config.setIsCluster(instance.getIsCluster() > 0);
                            config.setClusterId(clusterConfigs);
                            config.setComponentId(instance.getCompId() + "");
                            config.setComponentName(instance.getComponentName());
                            config.setComponentTypeId(instance.getMstComponentTypeId());
                            config.setComponentType(instance.getComponentTypeName());
                            config.setComponentVersionId(instance.getCompVersionId());
                            config.setComponentVersion(instance.getComponentVersionName());
                            config.setCommonVersionId(instance.getCommonVersionId());
                            config.setCommonVersion(instance.getCommonVersionName());
                            config.setSupervisorId(instance.getSupervisorId());
                            config.setHostId(instance.getHostId());
                            config.setHostIdentifier(instance.getHostIdentifier());

                            // set forensic agent type name
                            if(subTypeToViewType.containsKey(instance.getForensicAgentTypeId())){
                                config.setForensicAgentType(subTypeToViewType.get(instance.getForensicAgentTypeId()).getSubTypeName());
                            }
                            else {
                                config.setForensicAgentType("SupervisorAgent");
                            }

                            try {
                                config.setMaintenanceDetails(getAdhocMaintenanceWindowBL.getMaintenanceConfig(instance.getInstanceId(), instance.getInstanceName()));
                            } catch (AppsOneException e) {
                                log.debug("Error while fetching maintenance details for instance {} ", instance.getInstanceName(), e);
                            }

                            Set<Integer> instIds = (config.getIsCluster()) ? Collections.singleton(instance.getInstanceId()) : clusterIds;

                            Objects.requireNonNull(instIds).forEach(instId -> {
                                if (mappedApps.get(instId) == null && mappedServices.get(instId) == null) {
                                    log.error("Controller tags does not exists for instance id:{}", instId);
                                    return;
                                }

                                config.getApplicationId().addAll(mappedApps.getOrDefault(instId, new ArrayList<>()).parallelStream()
                                        .map(CompInstanceControllerTag::getControllerIdentifier).collect(toList()));

                                config.getServiceDetails().addAll(mappedServices.getOrDefault(instId, new ArrayList<>()).parallelStream()
                                        .map(c -> ServiceConfig.builder()
                                                .serviceId(c.getControllerIdentifier())
                                                .id(c.getControllerId())
                                                .serviceName(c.getControllerName()).build()).collect(toList()));
                            });

                            InstanceMappingDetails mappingDetails = MasterCache.getInstanceMappingDetails(instance.getInstanceId());
                            log.debug("mapping for instance to kpi is {}", mappingDetails);
                            if (mappingDetails != null) {
                                Map<String, List<InstanceKpiThresholds>> realTimeThresholdByInstanceId =
                                        instanceKpiThresholdRepo.getRealTimeThresholdByInstanceId(account.getIdentifier(), instance.getIdentifier());
                                config.setKpis(getCompInstanceKpiConfigPerCompInst(instance, account.getId(), allKpiMap, mappingDetails,
                                        componentKpisList, baseMetrics, computedKpiVsSupportingKpis,
                                        config.getServiceDetails(), actionCategoryCommandDetails, categoryByKpi,
                                        serviceKpiSuppressionPersistence, serviceKpiThresholdList, realTimeThresholdByInstanceId, compVersionKpiMapping));

                                List<ComponentAttributes> componentAttributesArrayList = new ArrayList<>();
                                if (mappingDetails.getAttributes() != null) {
                                    mappingDetails.getAttributes().forEach(attributes -> {
                                        ComponentAttributes componentAttributesBean = new ComponentAttributes();
                                        componentAttributesBean.setAttributeId(attributes.getAttributeId());
                                        componentAttributesBean.setAttributeName(attributes.getAttributeName());
                                        componentAttributesBean.setAttributeType(attributes.getAttributeType());
                                        componentAttributesBean.setAttributeValue(attributes.getAttributeValue());
                                        componentAttributesBean.setStatus(attributes.getStatus());
                                        componentAttributesBean.setIsCustom(attributes.getIsCustom());
                                        componentAttributesArrayList.add(componentAttributesBean);
                                    });
                                    config.setAttributes(componentAttributesArrayList);
                                }
                            }
                            log.debug("Added instance details: {}.", config);
                            return config;
                        } catch (Exception e) {
                            log.error("Error occurred while populating component instance id, {}", instanceId, e);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error occurred while getting instance " + e, e);
        }
        return clusterConfigList;
    }

    public static AgentConfig getAgentConfiguration(AgentBean agent, Set<TagMappingBean> agentIdsFromTagMapping,
                                                    List<TagDetailsBean> tagDetailsBeanList, int accountId) {
        AgentConfig agentConfig = new AgentConfig();

        try {
            Map<Integer, Set<TagMappingBean>> agentIdsVsTagMappings = agentIdsFromTagMapping.parallelStream()
                    .collect(groupingBy(TagMappingBean::getObjectId, toSet()));

            List<Map<String, String>> tags = new ArrayList<>();
            List<Integer> compInstanceIds = new CompInstanceDataService().getCompInstIsByAgent(agent.getId());
            agentConfig.setId(agent.getId());
            agentConfig.setPhysicalAgentId(agent.getPhysicalAgentId());
            agentConfig.setSupervisorId(agent.getSupervisorId());
            agentConfig.setAgentId(agent.getUniqueToken());
            agentConfig.setAgentName(agent.getName());
            agentConfig.setVersion(agent.getVersion());
            agentConfig.setAgentMode(agent.getMode());
            agentConfig.setStatus(agent.getStatus());
            agentConfig.setAgentTypeId(agent.getAgentTypeId());
            agentConfig.setCommunicationInterval(agent.getCommunicationInterval());
            agentConfig.setAgentTypeName(RulesDataService.getNameFromMSTSubType(agentConfig.getAgentTypeId()));
            agentConfig.setForensicEnabled(agent.isForensicsEnabled() ? 1 : 0);

            PhysicalAgentBean physicalAgentBean = AgentDataService.getPhysicalAgentDetailsUsingId(agent.getPhysicalAgentId());
            if (physicalAgentBean != null) {
                agentConfig.setPhysicalAgentIdentifier(physicalAgentBean.getIdentifier());
            } else {
                log.warn("PhysicalAgent is unavailable for id [{}]", agent.getPhysicalAgentId());
            }

            agentIdsVsTagMappings.getOrDefault(agent.getId(), new HashSet<>())
                    .parallelStream()
                    .forEach(tagMappingDetails -> {
                        Map<String, String> tag = getTag(tagDetailsBeanList, tagMappingDetails, accountId);
                        if (tag != null) {
                            tags.add(tag);
                        }
                    });

            agentConfig.setTags(tags);

            agentConfig.setCompInstanceIds(compInstanceIds);

            ComponentAgentBean componentAgentBean = ComponentAgentDataService.getComponentAgent(agentConfig.getId());

            if (componentAgentBean != null) {
                DataCommunicationDetailsBean dataCommunicationDetailsBean = MasterCache
                        .getDataCommunicationDetails(componentAgentBean.getDataCommunicationId());

                if (MasterCache.getMstSubTypeForSubTypeId(componentAgentBean.getConfigOperationModeId()) != null) {
                    agentConfig.setConfigOperationMode(MasterCache.getMstSubTypeForSubTypeId(componentAgentBean.getConfigOperationModeId()).getSubTypeName());
                }
                if (null != MasterCache.getMstSubTypeForSubTypeId(componentAgentBean.getDataOperationModeId())) {
                    agentConfig.setDataOperationMode(MasterCache.getMstSubTypeForSubTypeId(componentAgentBean.getDataOperationModeId()).getSubTypeName());
                }

                if (dataCommunicationDetailsBean != null) {
                    CollectionAgentParam param = new CollectionAgentParam();
                    param.setTimeoutMultiplier(componentAgentBean.getTimeoutMultiplier());
                    param.setDataProtocol(dataCommunicationDetailsBean.getName());
                    param.setDataAddress(dataCommunicationDetailsBean.getHost());
                    param.setDataPort(dataCommunicationDetailsBean.getPort());
                    param.setDataEndpoint(dataCommunicationDetailsBean.getEndpoint());
                    param.setForensicEndpoint(dataCommunicationDetailsBean.getForensicEndpoint());
                    agentConfig.setCollectionAgentParam(param);
                }
            }
            return agentConfig;
        } catch (Exception e) {
            log.error("Error occurred while getting agentConf data, agent identifier:{}", agent.getUniqueToken(), e);
            return null;
        }
    }

    private static List<CompInstKpiConfig> getCompInstanceKpiConfigPerCompInst(CompInstClusterDetails compInstClusterDetails,
                                                                               int accountId,
                                                                               Map<Integer, AllKpiList> allKpiMap,
                                                                               InstanceMappingDetails mappingDetails,
                                                                               List<ComponentKpis> componentKpisList,
                                                                               List<Integer> baseMetrics,
                                                                               Map<Integer, List<ComputedKpiBean>> computedKpiVsSupportingKpis,
                                                                               List<ServiceConfig> serviceDetails,
                                                                               List<ActionCategoryCommandDetails> actionCategoryCommandDetails,
                                                                               List<CategoryKpiMapping> categoryByKpi,
                                                                               List<ServiceConfigurationBean> serviceKpiSuppressionPersistence,
                                                                               List<ServiceKpiThreshold> serviceKpiThresholdList,
                                                                               Map<String,
                                                                                       List<InstanceKpiThresholds>> realTimeKpiThresholdByInstanceId,
                                                                               Map<String, String> compVersionKpiMap) throws ServerException {

        //Thresholds at instance kpi level are not used by any modules.
        Map<Integer, List<CompInstanceKpiDetailsBean>> compInstNonGroupKpiDetailsMap = mappingDetails.getNonGroupKpi()
                .parallelStream().collect(groupingBy(CompInstanceKpiDetailsBean::getMstKpiDetailsId));

        Map<Integer, List<CompInstanceKpiGroupDetailsBean>> compInstGroupKpiDetailsMap = mappingDetails.getGroupKpi()
                .parallelStream().collect(groupingBy(CompInstanceKpiGroupDetailsBean::getMstKpiDetailsId));

        Map<Integer, ComponentKpis> componentKpisMap = componentKpisList.parallelStream()
                .filter(kpi -> kpi.getComponentId() == compInstClusterDetails.getCompId() &&
                        kpi.getMstCommonVersionId() == compInstClusterDetails.getCommonVersionId())
                .collect(Collectors.toMap(ComponentKpis::getKpiId, Function.identity(), (val1, val2) -> {
                    log.warn("Duplicate timezone entries found for ComponentKpis. Choosing the first entry available.");
                    return val1;
                }));

        // get instance level kpi thresholds
        List<CompInstKpiThresholdDetailsBean> thresholdDetailsBeans =
                ThresholdDataService.getCompInstanceThresholdDetailsList(compInstClusterDetails.getInstanceId(), accountId);

        // convert to common object type of kpi
        List<KpiThresholdDetailBeans> kpiThresholdList = thresholdDetailsBeans
                .parallelStream()
                .map(instanceThreshold -> KpiThresholdDetailBeans
                        .builder()
                        .compInstanceId(instanceThreshold.getCompInstanceId())
                        .startTime(DateTimeUtil.getTimeInGMT(instanceThreshold.getStartTime().getTime()))
                        .endTime(instanceThreshold.getEndTime() == null ? null :
                                DateTimeUtil.getTimeInGMT(instanceThreshold.getStartTime().getTime()))
                        .kpiId(instanceThreshold.getKpiId())
                        .serviceId(0)
                        .kpiAttribute(instanceThreshold.getKpiAttribute())
                        .id(instanceThreshold.getId())
                        .status(instanceThreshold.getStatus())
                        .severity(instanceThreshold.getSeverity())
                        .minThreshold(instanceThreshold.getMinThreshold())
                        .maxThreshold(instanceThreshold.getMaxThreshold())
                        .operationId(instanceThreshold.getOperationId())
                        .thresholdLevel(true)
                        .thresholdType(THRESHOLD_TYPE_VALUE)
                        .build())
                .collect(toList());

        kpiThresholdList.addAll(getServiceLevelStaticThreshold(compInstClusterDetails, serviceDetails, compInstNonGroupKpiDetailsMap, compInstGroupKpiDetailsMap, serviceKpiSuppressionPersistence, serviceKpiThresholdList));

        Map<Integer, List<KpiThresholdDetailBeans>> kpiVsThresholdDetails = kpiThresholdList
                .parallelStream().collect(groupingBy(KpiThresholdDetailBeans::getKpiId));

        List<InstKpiAttrPersistenceSuppressionBean> persistenceSuppressionBeans = new ArrayList<>();
        Map<KpiIdVsAttribute, InstKpiAttrPersistenceSuppressionBean> kpiVsPersistenceSuppressionDetails;
        Map<Integer, List<InstKpiAttrPersistenceSuppressionBean>> availabilityKpiVsPersistenceSuppressionDetails;
        try {
            persistenceSuppressionBeans = new KPIDataService().fetchCompInstanceKpiPerSupValuesForCompInstance(compInstClusterDetails.getInstanceId(), accountId);
        } catch (ControlCenterException e) {
            log.error("Error while fetching persistence suppression configuration for instanceId [{}]. Persistence suppression configuration" +
                    " will not be available", compInstClusterDetails.getInstanceId());
        }

        kpiVsPersistenceSuppressionDetails = persistenceSuppressionBeans
                .parallelStream().collect(toMap(c -> new KpiIdVsAttribute(c.getKpiId(), c.getAttributeValue()), Function.identity()));
        availabilityKpiVsPersistenceSuppressionDetails = persistenceSuppressionBeans
                .parallelStream().collect(groupingBy(InstKpiAttrPersistenceSuppressionBean::getKpiId));
        KPIDataService kpiDataService = new KPIDataService();
        List<KpiMaintenanceStatusBean> kpiMaintenanceStatusBean;
        try {
            kpiMaintenanceStatusBean = kpiDataService.fetchCompInstanceKpiMaintenanceStatus(compInstClusterDetails.getInstanceId(), accountId);

        } catch (ControlCenterException e) {
            throw new ServerException(e.getMessage());
        }

        Set<Integer> instanceKPIs = new HashSet<>(compInstNonGroupKpiDetailsMap.keySet());
        instanceKPIs.addAll(compInstGroupKpiDetailsMap.keySet());
        Map<KpiIdVsAttribute, InstKpiAttrPersistenceSuppressionBean> finalKpiVsPersistenceSuppressionDetails = kpiVsPersistenceSuppressionDetails;
        Map<Integer, List<InstKpiAttrPersistenceSuppressionBean>> finalAvailabilityKpiVsPersistenceSuppressionDetails = availabilityKpiVsPersistenceSuppressionDetails;
        return instanceKPIs.parallelStream()
                .filter(allKpiMap::containsKey)
                .map(kpiId -> {
                    AllKpiList allKpiList = allKpiMap.get(kpiId);
                    int compInstKpiId;
                    int collectionInterval;
                    int status, notification;
                    int instProducerId;
                    int availableForAnalytics = 0;
                    String attributeValue = null;
                    Map<String, String> attributeValues = new HashMap<>();
                    //List<String> attributes = new ArrayList<>();

                    ComponentKpis componentKPI = componentKpisMap.get(kpiId);

                    if (componentKPI != null) {
                        availableForAnalytics = componentKPI.getAvailableForAnalytics();
                    }

                    if (compInstGroupKpiDetailsMap.containsKey(kpiId)) {
                        CompInstanceKpiGroupDetailsBean bean = compInstGroupKpiDetailsMap.get(kpiId).get(0);
                        compInstKpiId = bean.getId();
                        collectionInterval = bean.getCollectionInterval();
                        status = bean.getStatus();
                        notification = bean.getNotification();
                        List<String> attrValues = new ArrayList<>();
                        attributeValues = new HashMap<>();
                        instProducerId = bean.getMstProducerId();

                        for (CompInstanceKpiGroupDetailsBean kpiBean : compInstGroupKpiDetailsMap.get(kpiId)) {
                            if (StringUtils.isNotEmpty(kpiBean.getAttributeValue())) {
                                attrValues.add(kpiBean.getAttributeValue());
                                attributeValues.put(kpiBean.getAttributeValue(), StringUtils.defaultIfEmpty(kpiBean.getAliasName(), kpiBean.getAttributeValue()));
                            }
                        }
                        Collections.sort(attrValues);
                        attributeValue = String.join("|", attrValues);
                    } else if (compInstNonGroupKpiDetailsMap.containsKey(kpiId)) {
                        CompInstanceKpiDetailsBean bean = compInstNonGroupKpiDetailsMap.get(kpiId).get(0);
                        compInstKpiId = bean.getId();
                        collectionInterval = bean.getCollectionInterval();
                        status = bean.getStatus();
                        notification = bean.getNotification();
                        instProducerId = bean.getMstProducerId();
                        //attributes.add(ALL);
                    } else {
                        return null;
                    }


                    CompInstKpiConfig compInstKpiConfig = new CompInstKpiConfig();

                    Map<Integer, Integer> kpiCategoryKMap = categoryByKpi
                            .parallelStream()
                            .collect(toMap(CategoryKpiMapping::getKpiId, CategoryKpiMapping::getCategoryId));

                    if(kpiCategoryKMap.containsKey(kpiId)){
                        int categoryId = kpiCategoryKMap.get(kpiId);
                        Optional<CategoryDetails> o = actionCategoryCommandDetails.parallelStream()
                                .filter(x -> categoryId == x.getCategoryId())
                                .map(y -> CategoryDetails.builder()
                                        .name(y.getCategoryName())
                                        .id(y.getCategoryId())
                                        .identifier(y.getCategoryIdentifier())
                                        .build()
                                )
                                .distinct()
                                .findFirst();

                        if(o.isPresent()){
                            compInstKpiConfig.setCategoryIdentifier(o.get().getIdentifier());
                            compInstKpiConfig.setCategoryId(o.get().getId());
                            compInstKpiConfig.setCategoryName(o.get().getName());
                        }
                    }


                    compInstKpiConfig.setId(kpiId);
                    compInstKpiConfig.setName(allKpiList.getKpiName());
                    compInstKpiConfig.setCompInstKpiId(compInstKpiId);
                    compInstKpiConfig.setKpiAliasName(allKpiList.getIdentifier());
                    compInstKpiConfig.setCollectionInterval(collectionInterval);
                    compInstKpiConfig.setStatus(status);
                    compInstKpiConfig.setKpiUnit(allKpiList.getKpiUnits());
                    compInstKpiConfig.setAttributeValue(attributeValue);
                    compInstKpiConfig.setKpiType(allKpiList.getKpiType());
                    compInstKpiConfig.setIsGroup(allKpiList.getGroupId() > 0);
                    compInstKpiConfig.setGroupName(allKpiList.getGroupName());
                    compInstKpiConfig.setGroupId(allKpiList.getGroupId());
                    compInstKpiConfig.setGroupIdentifier(allKpiList.getIdentifier());
                    compInstKpiConfig.setIsCustom(allKpiList.getIsCustom());
                    compInstKpiConfig.setDiscovery(allKpiList.getIsDiscovery());
                    compInstKpiConfig.setAvailableForAnalytics(availableForAnalytics);
                    compInstKpiConfig.setValueType(allKpiList.getValueType());
                    compInstKpiConfig.setDataType(allKpiList.getDataType());
                    compInstKpiConfig.setIsInfo(allKpiList.getIsInfo());
                    compInstKpiConfig.setDeltaPerSec(allKpiList.getDeltaPerSec());
                    compInstKpiConfig.setResetDeltaValue(allKpiList.getResetDeltaValue());
                    compInstKpiConfig.setCronExpression(allKpiList.getCronExpression());
                    compInstKpiConfig.setNotification(notification);
                    compInstKpiConfig.setAttributeValues(attributeValues);

                    compInstKpiConfig.setIsBaseMetric(baseMetrics.contains(kpiId) ? 1 : 0);
                    List<ComputedKpiBean> computedKpiBean = computedKpiVsSupportingKpis.getOrDefault(kpiId, new ArrayList<>());

                    if (!computedKpiBean.isEmpty()) {
                        compInstKpiConfig.setComputationExpression(computedKpiBean.get(0).getFormula());

                        List<String> kpiIdentifiers = computedKpiBean.parallelStream()
                                .map(c -> "kpi".concat(String.valueOf(c.getKpiDetailsId()))).collect(toList());

                        compInstKpiConfig.setKpiIdentifiers(kpiIdentifiers);
                    }

                    if (compInstKpiConfig.getIsGroup() && allKpiList.getClusterAggType() > 0 && allKpiList.getInstanceAggType() > 0) {
                        compInstKpiConfig.setClusterAggType(MasterCache.getMstSubTypeForSubTypeId(allKpiList.getClusterAggType()).getSubTypeName());
                        compInstKpiConfig.setInstanceAggType(MasterCache.getMstSubTypeForSubTypeId(allKpiList.getInstanceAggType()).getSubTypeName());
                    }

                    compInstKpiConfig.setAggOperation(ClusterOperation.valueOf(allKpiList.getClusterOperation().toUpperCase()));
                    compInstKpiConfig.setRollUpOperation(ClusterOperation.valueOf(allKpiList.getRollupOperation().toUpperCase()));

                    Pair<Integer, List<CompInstKpiProducerDetail>> pair = getCompInstKpiProducerDetails(accountId,
                            instProducerId, compInstClusterDetails.getCompVersionId(), kpiId);
                    compInstKpiConfig.setProducers(pair.getRight());
                    compInstKpiConfig.setDefaultProducerId(pair.getLeft());

                    // set realtime (NOR) threshold for kpis
                    Map<String, List<KpiViolationConfig>> configs = new HashMap<>();
                    if(!realTimeKpiThresholdByInstanceId.isEmpty() && realTimeKpiThresholdByInstanceId.containsKey(kpiId+"")){
                        List<InstanceKpiThresholds> instanceKpiThresholds1 =
                                realTimeKpiThresholdByInstanceId.getOrDefault(kpiId+"",
                                        new ArrayList<>());
                        for (InstanceKpiThresholds instanceKpiThresholds : instanceKpiThresholds1) {
                            if (instanceKpiThresholds != null) {
                                KpiViolationConfig kpiViolationConfig = new KpiViolationConfig();

                                kpiViolationConfig.setAttributeValue(instanceKpiThresholds.getKpiAttribute());
                                kpiViolationConfig.setOperation(instanceKpiThresholds.getOperationType());
                                kpiViolationConfig.setThresholdLevel("instance");
                                kpiViolationConfig.setThresholdType("NOR");
                                kpiViolationConfig.setMaxThreshold(instanceKpiThresholds.getThresholds().get("Upper"));
                                kpiViolationConfig.setMinThreshold(instanceKpiThresholds.getThresholds().get("Lower"));
                                kpiViolationConfig.setStartTime(DateTimeUtil.getTimeInGMT(instanceKpiThresholds.getStartTime()));
                                kpiViolationConfig.setEndTime(instanceKpiThresholds.getEndTime() == 0L ? null :
                                        DateTimeUtil.getTimeInGMT(instanceKpiThresholds.getEndTime()));

                                setNORLevelPersistenceSuppression(serviceDetails, instanceKpiThresholds,
                                        compInstGroupKpiDetailsMap, compInstNonGroupKpiDetailsMap, compInstClusterDetails,
                                        serviceKpiSuppressionPersistence, kpiViolationConfig);

                                configs.put(instanceKpiThresholds.getKpiAttribute(), Collections.singletonList(kpiViolationConfig));
                            }
                        }
                    } else if (kpiVsThresholdDetails.containsKey(kpiId)) {
                        configs.putAll(kpiVsThresholdDetails.getOrDefault(kpiId, new ArrayList<>())
                                .parallelStream().map(thresholdDetailsBean -> {
                                    String attrVal = thresholdDetailsBean.getKpiAttribute();
                                    KpiViolationConfig kpiViolationConfig = new KpiViolationConfig();
                                    kpiViolationConfig.setAttributeValue(attrVal);

                                    InstKpiAttrPersistenceSuppressionBean configBean = finalKpiVsPersistenceSuppressionDetails.get(new KpiIdVsAttribute(kpiId, attrVal));

                                    ViewTypes operationTypeView = MasterCache.getMstSubTypeForSubTypeId(thresholdDetailsBean.getOperationId());

                                    if (operationTypeView == null) {
                                        log.error("Operation ID [{}] is invalid. Static threshold for KPI [{}] and attribute [{}] cannot be populated",
                                                thresholdDetailsBean.getOperationId(), kpiId, attrVal);
                                    } else {
                                        kpiViolationConfig.setStartTime(thresholdDetailsBean.getStartTime());
                                        kpiViolationConfig.setEndTime(thresholdDetailsBean.getEndTime());

                                        kpiViolationConfig.setOperation(operationTypeView.getSubTypeName());
                                        kpiViolationConfig.setMinThreshold(thresholdDetailsBean.getMinThreshold());
                                        kpiViolationConfig.setMaxThreshold(thresholdDetailsBean.getMaxThreshold());
                                        kpiViolationConfig.setStatus(thresholdDetailsBean.getStatus());
                                        kpiViolationConfig.setSeverity(thresholdDetailsBean.getSeverity());
                                        if(thresholdDetailsBean.isThresholdLevel()){
                                            kpiViolationConfig.setThresholdLevel("instance");
                                        } else {
                                            kpiViolationConfig.setThresholdLevel("service");
                                            kpiViolationConfig.setPersistence(thresholdDetailsBean.getPersistence());
                                            kpiViolationConfig.setSuppression(thresholdDetailsBean.getSuppression());
                                        }
                                        kpiViolationConfig.setThresholdType(thresholdDetailsBean.getThresholdType());
                                    }

                                    if (configBean != null) {
                                        kpiViolationConfig.setPersistence(configBean.getPersistence());
                                        kpiViolationConfig.setSuppression(configBean.getSuppression());
                                    }

                                    return kpiViolationConfig;

                                }).collect(groupingBy(KpiViolationConfig::getAttributeValue)));
                    } else {
                        configs = finalAvailabilityKpiVsPersistenceSuppressionDetails.getOrDefault(kpiId, new ArrayList<>())
                                .parallelStream().map(persistenceSuppressionBean -> {
                                    String attrVal = persistenceSuppressionBean.getAttributeValue();
                                    KpiViolationConfig kpiViolationConfig = new KpiViolationConfig();
                                    kpiViolationConfig.setAttributeValue(attrVal);
                                    kpiViolationConfig.setPersistence(persistenceSuppressionBean.getPersistence());
                                    kpiViolationConfig.setSuppression(persistenceSuppressionBean.getSuppression());
                                    return kpiViolationConfig;

                                }).collect(groupingBy(KpiViolationConfig::getAttributeValue));
                    }

                    kpiMaintenanceStatusBean.parallelStream()
                            .filter(c -> c.getKpiGroupId() == allKpiList.getGroupId()
                                    && c.getKpiId() == allKpiList.getKpiId())
                            .findFirst().ifPresent(maintenanceStatusBean -> compInstKpiConfig.setIsMaintenanceExcluded(maintenanceStatusBean.getIsMaintenanceExcluded() == -1 ? 0 : maintenanceStatusBean.getIsMaintenanceExcluded()));

                    compInstKpiConfig.setKpiViolationConfigMap(configs);
                    compInstKpiConfig.setImportanceType(
                            compVersionKpiMap.getOrDefault(compInstClusterDetails.getCompId()+":"+compInstClusterDetails.getCompVersionId()+":"+kpiId, "Low"));

                    return compInstKpiConfig;
                }).filter(Objects::nonNull)
                .collect(toList());
    }

    private static void setNORLevelPersistenceSuppression(List<ServiceConfig> serviceDetails,
                                                          InstanceKpiThresholds instanceKpiThresholds,
                                                          Map<Integer, List<CompInstanceKpiGroupDetailsBean>> compInstGroupKpiDetailsMap,
                                                          Map<Integer, List<CompInstanceKpiDetailsBean>> compInstNonGroupKpiDetailsMap,
                                                          CompInstClusterDetails compInstClusterDetails,
                                                          List<ServiceConfigurationBean> serviceKpiSuppressionPersistence, KpiViolationConfig kpiViolationConfig) {
        List<Integer> serviceIdList =
                serviceDetails.stream().filter(service -> Objects.equals(service.getServiceId(), instanceKpiThresholds.getServiceIdentifier()))
                        .map(ServiceConfig::getId).collect(toList());

        if(compInstGroupKpiDetailsMap.containsKey(Long.valueOf(instanceKpiThresholds.getKpiId()).intValue())){
            List<CompInstanceKpiGroupDetailsBean> instance =
                    compInstGroupKpiDetailsMap.get(Long.valueOf(instanceKpiThresholds.getKpiId()).intValue())
                            .stream()
                            .filter(instKPI -> instKPI.getCompInstanceId() == compInstClusterDetails.getInstanceId())
                            .collect(toList());
            if(!instance.isEmpty()) {
                List<ServiceConfigurationBean> filterPersSupp =
                        serviceKpiSuppressionPersistence.parallelStream()
                                .filter(y -> serviceIdList.contains(y.getServiceId()))
                                .filter(x -> x.getStartCollectionInterval() * 60 >= instance.get(0).getCollectionInterval())
                                .collect(toList());
                kpiViolationConfig.setPersistence(!filterPersSupp.isEmpty() ?
                        filterPersSupp.get(0).getNorPersistence() : 0);
                kpiViolationConfig.setSuppression(!filterPersSupp.isEmpty() ?
                        filterPersSupp.get(0).getNorSuppression() : 0);
            }
        } else if(compInstNonGroupKpiDetailsMap.containsKey(Long.valueOf(instanceKpiThresholds.getKpiId()).intValue())){
            List<CompInstanceKpiDetailsBean> instance =
                    compInstNonGroupKpiDetailsMap.get(Long.valueOf(instanceKpiThresholds.getKpiId()).intValue());
            if(!instance.isEmpty()) {
                List<ServiceConfigurationBean> filterPersSupp =
                        serviceKpiSuppressionPersistence.parallelStream()
                                .filter(y -> serviceIdList.contains(y.getServiceId()))
                                .filter(x -> x.getStartCollectionInterval() * 60 >= instance.get(0).getCollectionInterval())
                                .collect(toList());
                kpiViolationConfig.setPersistence(!filterPersSupp.isEmpty() ?
                        filterPersSupp.get(0).getNorPersistence() : 0);
                kpiViolationConfig.setSuppression(!filterPersSupp.isEmpty() ?
                        filterPersSupp.get(0).getNorSuppression() : 0);
            }
        }
    }

    private static List<KpiThresholdDetailBeans> getServiceLevelStaticThreshold(CompInstClusterDetails compInstClusterDetails,
                                                                                List<ServiceConfig> serviceDetails,
                                                                                Map<Integer, List<CompInstanceKpiDetailsBean>> compInstNonGroupKpiDetailsMap,
                                                                                Map<Integer, List<CompInstanceKpiGroupDetailsBean>> compInstGroupKpiDetailsMap,
                                                                                List<ServiceConfigurationBean> serviceKpiSuppressionPersistence,
                                                                                List<ServiceKpiThreshold> serviceKpiThresholdList) {

        List<KpiThresholdDetailBeans> kpiThresholdList = new ArrayList<>();

        // get service level kpi thresholds
        if (!serviceDetails.isEmpty()) {

            List<Integer> serviceIdList = serviceDetails.parallelStream().map(ServiceConfig::getId).collect(toList());
            List<ServiceKpiThreshold> filteredServiceKpiThreshold = serviceKpiThresholdList
                    .parallelStream()
                    .filter(list -> serviceIdList.contains(list.getServiceId()))
                    .collect(Collectors.toList());

            List<ServiceConfigurationBean> filteredSuppressionPersistence = serviceKpiSuppressionPersistence
                    .parallelStream()
                    .filter(list -> serviceIdList.contains(list.getServiceId()))
                    .collect(Collectors.toList());

            if (!filteredServiceKpiThreshold.isEmpty()) {
                Map<Integer, List<ServiceKpiThreshold>> serviceKpiThreshold = filteredServiceKpiThreshold
                        .parallelStream()
                        .filter(threshold -> threshold.getOperationTypeId() != null)
                        .collect(groupingBy(ServiceKpiThreshold::getKpiId));

                // get persistence suppression for kpi at service level based on collection interval
                kpiThresholdList =
                        serviceKpiThreshold.keySet().parallelStream().map(kpiId -> {
                            List<ServiceConfigurationBean> persistenceSuppressionByCollectionInterval = new ArrayList<>();

                            if (!filteredSuppressionPersistence.isEmpty()) {
                                if (compInstGroupKpiDetailsMap.containsKey(kpiId)) {
                                    log.debug("finding the collection interval for group kpi with kpi id {}", kpiId);
                                    persistenceSuppressionByCollectionInterval = compInstGroupKpiDetailsMap.get(kpiId)
                                            .parallelStream().filter(x -> x.getCompInstanceId() == compInstClusterDetails.getInstanceId())
                                            .flatMap(y -> filteredSuppressionPersistence.parallelStream()
                                                    .filter(k -> k.getStartCollectionInterval() * 60 >= y.getCollectionInterval())
                                            ).collect(toList());

                                } else if (compInstNonGroupKpiDetailsMap.containsKey(kpiId)) {
                                    log.debug("finding the collection interval for non group kpi with kpi id {}", kpiId);
                                    persistenceSuppressionByCollectionInterval = compInstNonGroupKpiDetailsMap.get(kpiId)
                                            .parallelStream().filter(x -> x.getCompInstanceId() == compInstClusterDetails.getInstanceId())
                                            .flatMap(y -> filteredSuppressionPersistence.parallelStream()
                                                    .filter(k -> k.getStartCollectionInterval() * 60 >= y.getCollectionInterval()))
                                            .collect(toList());
                                }
                            }
                            // convert to common object type of kpi
                            return KpiThresholdDetailBeans.builder()
                                    .id(serviceKpiThreshold.get(kpiId).get(0).getId())
                                    .kpiId(serviceKpiThreshold.get(kpiId).get(0).getKpiId())
                                    .serviceId(serviceKpiThreshold.get(kpiId).get(0).getServiceId())
                                    .kpiAttribute(serviceKpiThreshold.get(kpiId).get(0).getKpiAttribute())
                                    .minThreshold(serviceKpiThreshold.get(kpiId).get(0).getMinThreshold())
                                    .maxThreshold(serviceKpiThreshold.get(kpiId).get(0).getMaxThreshold())
                                    .operationId(serviceKpiThreshold.get(kpiId).get(0).getOperationTypeId())
                                    .status(serviceKpiThreshold.get(kpiId).get(0).getStatus())
                                    .severity(serviceKpiThreshold.get(kpiId).get(0).getUserSeverity())
                                    .startTime(serviceKpiThreshold.get(kpiId).get(0).getStartTime().toString())
                                    .endTime(serviceKpiThreshold.get(kpiId).get(0).getEndTime() == null ? null : serviceKpiThreshold.get(kpiId).get(0).getEndTime().toString())
                                    .thresholdType(THRESHOLD_TYPE_VALUE)
                                    .suppression(!persistenceSuppressionByCollectionInterval.isEmpty() ? persistenceSuppressionByCollectionInterval.get(0).getSorSuppression() : 0)
                                    .persistence(!persistenceSuppressionByCollectionInterval.isEmpty() ? persistenceSuppressionByCollectionInterval.get(0).getSorPersistence() : 0)
                                    .build();
                        })
                        .filter(Objects::nonNull)
                        .collect(toList());
            }
        }
        return kpiThresholdList;
    }

    private static Pair<Integer, List<CompInstKpiProducerDetail>> getCompInstKpiProducerDetails(int accountId,
                                                                                                int instProducerId,
                                                                                                int mstCompVersionId,
                                                                                                int kpiId) {
        List<CompInstKpiProducerDetail> list = new ArrayList<>();

        ProducerKpis producerKpis = new ProducerKpis();
        producerKpis.setAccountId(accountId);
        producerKpis.setMstKpiDetailsId(kpiId);
        producerKpis.setMstCompVersionId(mstCompVersionId);

        List<ViewProducerKPIsBean> viewProducerKPIsBeans = ProducerDataService
                .getViewProducerKPIsData(accountId, kpiId, mstCompVersionId);

        int defaultProducerId = 0;

        if (viewProducerKPIsBeans == null) {
            return new ImmutablePair<>(defaultProducerId, list);
        }

        for (ViewProducerKPIsBean viewProducerKPIsBean : viewProducerKPIsBeans) {
            CompInstKpiProducerDetail bean = new CompInstKpiProducerDetail();
            bean.setProducerId(viewProducerKPIsBean.getProducerId());
            bean.setProducerName(viewProducerKPIsBean.getProducerName());
            bean.setProducerType(viewProducerKPIsBean.getProducerType());
            bean.setStatus(viewProducerKPIsBean.getStatus());
            bean.setIsCustom(viewProducerKPIsBean.getIsCustom());
            bean.setIsDefault(viewProducerKPIsBean.getIsDefault());
            bean.setClassName(viewProducerKPIsBean.getClassname());
            bean.setAccountId(viewProducerKPIsBean.getAccountId());
            bean.setKpiProducerMappingId(viewProducerKPIsBean.getMstProducerKpiMappingId());
            bean.setThresholdInSecs(viewProducerKPIsBean.getThresholdInSecs());
            bean.setTimeoutInSecs(viewProducerKPIsBean.getTimeoutInSecs());
            if (bean.getProducerId() == instProducerId) {
                defaultProducerId = instProducerId;
            }

            ProducerTypeDetail typeDetail = MasterCache.getProducerTypeDetail(bean.getProducerId(), bean.getProducerType());
            if (typeDetail != null) {
                bean.setTypeDetails(typeDetail.getDataMap());
            }

            List<ProducerParameter> parameters = MasterCache.getProducerParameter(bean.getProducerId());
            bean.setParameters(parameters);
            list.add(bean);
        }

        return new ImmutablePair<>(defaultProducerId, list);
    }

    public static KeycloakSettingsResponse getKeyCloakSettings() {
        long st = System.currentTimeMillis();
        KeycloakSettingsResponse response = new KeycloakSettingsResponse();
        response.setResponseMessage("Success");
        response.setResponseStatus(StatusResponse.SUCCESS.toString());

        Map<String, Object> data = ConfProperties.getKeycloakSSOConfigurations();

        long start = System.currentTimeMillis();
        String setup = new UserDataService().getSetup();
        log.debug("Time take for setup method is {} ms", (System.currentTimeMillis() - start));
        if (setup == null) {
            log.error("UNABLE TO FETCH SETUP TYPE FROM DB.");
        } else {
            data.put("isActiveDirectory", Constants.SETUP_AD_INTEGRATION.equalsIgnoreCase(setup));
        }

        response.setData(data);
        log.debug("Time take for getKeyCloakSettings method is {} ms", (System.currentTimeMillis() - st));
        return response;
    }

    public static Map<String, String> getTag(List<TagDetailsBean> tagDetailsBeanList, TagMappingDetails tagMappingDetails, int accountId) {
        Map<String, String> tag = new HashMap<>();
        Optional<TagDetailsBean> findAny = tagDetailsBeanList.stream()
                .filter(tagDetailsBean -> tagDetailsBean.getId() == tagMappingDetails.getTagId())
                .findAny();
        /*
         * If tag details is present then get the tags data
         */
        if (findAny.isPresent()) {
            TagDetailsBean tagDetailsBean = findAny.get();
            String refTable = tagDetailsBean.getRefTable();
            /*
             * If reference table is null then add the key, value from tag mapping data
             */
            if (refTable == null || refTable.equalsIgnoreCase("null") || refTable.isEmpty()) {
                tag.put("key", tagMappingDetails.getTagKey());
                tag.put("value", tagMappingDetails.getTagValue());
                tag.put("type", tagDetailsBean.getName());
                return tag;
            }
            /*
             * If reference table is not null then get the key and value from table
             */
            TagPreDefinedData preDefinedData = TagsDataService.getTagData(refTable, tagMappingDetails.getTagValue(),
                    tagDetailsBean.getRefSelectColumnName(), tagDetailsBean.getRefWhereColumnName(), accountId);
            if (preDefinedData == null)
                preDefinedData = TagsDataService.getTagData(refTable, tagMappingDetails.getTagKey(),
                        tagDetailsBean.getRefSelectColumnName(), tagDetailsBean.getRefWhereColumnName(), accountId);
            if (preDefinedData != null) {
                tag.put("key", preDefinedData.getTagKey());
                tag.put("value", preDefinedData.getTagValue());
                tag.put("type", tagDetailsBean.getName());
                return tag;
            }
        }
        return null;
    }

    public static Map<String, String> getTag(List<TagDetailsBean> tagDetailsBeanList, TagMappingBean tagMappingDetails, int accountId) {
        Map<String, String> tag = new HashMap<>();
        Optional<TagDetailsBean> findAny = tagDetailsBeanList.stream()
                .filter(tagDetailsBean -> tagDetailsBean.getId() == tagMappingDetails.getTagId())
                .findAny();
        /*
         * If tag details is present then get the tags data
         */
        if (findAny.isPresent()) {
            TagDetailsBean tagDetailsBean = findAny.get();
            String refTable = tagDetailsBean.getRefTable();
            /*
             * If reference table is null then add the key, value from tag mapping data
             */
            if (refTable == null || refTable.equalsIgnoreCase("null") || refTable.isEmpty()) {
                tag.put("key", tagMappingDetails.getTagKey());
                tag.put(VALUE, tagMappingDetails.getTagValue());
                tag.put("type", tagDetailsBean.getName());
                return tag;
            }
            /*
             * If reference table is not null then get the key and value from table
             */
            TagPreDefinedData preDefinedData = TagsDataService.getTagData(refTable, tagMappingDetails.getTagValue(),
                    tagDetailsBean.getRefSelectColumnName(), tagDetailsBean.getRefWhereColumnName(), accountId);
            if (preDefinedData == null)
                preDefinedData = TagsDataService.getTagData(refTable, tagMappingDetails.getTagKey(),
                        tagDetailsBean.getRefSelectColumnName(), tagDetailsBean.getRefWhereColumnName(), accountId);
            if (preDefinedData != null) {
                tag.put("key", preDefinedData.getTagKey());
                tag.put(VALUE, preDefinedData.getTagValue());
                tag.put("type", tagDetailsBean.getName());
                return tag;
            }
        }
        return null;
    }

    public static void buildHostAvailability(AgentBean agentBean) {
        log.debug("Inside buildHostAvailability({}) method.", agentBean);
        AgentRepo agentRepo = new AgentRepo();
        try {
            com.heal.configuration.pojos.Agent agent = agentRepo.getAgent(agentBean.getUniqueToken());
            String accountIdentifier = agent.getAccountIdentifiers().stream().findFirst().orElse(null);
            if (accountIdentifier == null) {
                log.warn("Account is not found in DB for sending host availability kpi data to DR, agentIdentifier:{}, hostAddress:{}", agentBean.getUniqueToken(), agentBean.getHostAddress());
                return;
            }

            InstanceRepo instanceRepo = new InstanceRepo();
            List<RawKpiData> rawKpiDataList = new ArrayList<>();
            com.heal.configuration.pojos.CompInstClusterDetails instanceDetails;

            instanceDetails = instanceRepo.getAgentInstances(accountIdentifier, agent.getIdentifier())
                    .stream()
                    .filter(i -> i.getStatus() == 1) // filtering with active host instances.
                    .map(i -> instanceRepo.getInstanceDetailByIdentifier(accountIdentifier, i.getIdentifier())) // converting basic entity to comp instance cluster details.
                    .filter(Objects::nonNull)
                    .map(inst -> {
                        if(inst.getComponentTypeId() == 1) { // host instance return the bean
                            return inst;
                        } else {
                            return instanceRepo.getInstanceDetailByIdentifier(accountIdentifier, inst.getHostIdentifier()); // if component instance then get the host instance using host identifier.
                        }
                    })
                    .filter(Objects::nonNull)
                    .filter(i -> i.getHostAddress().equals(agent.getHostAddress())) // filtering with agent host address to instance host address
                    .findAny()
                    .orElse(null);

            if (instanceDetails == null) {
                log.warn("No instances mapped for agentIdentifier:{}, hostAddress:{}, So host availability for this address will be skipped.", agentBean.getUniqueToken(), agentBean.getHostAddress());
                return;
            }

            String dateInGMT = DateTimeUtil.getTimeInGMT();

            int healthExists = instanceRepo.getHostAvailability(accountIdentifier, instanceDetails.getIdentifier(), dateInGMT);
            if (healthExists == 1) {
                log.info("Host Availability data marked as received for accountIdentifier:{}, instanceIdentifier:{}, hostAddress:{}", accountIdentifier, instanceDetails.getIdentifier(), instanceDetails.getHostAddress());
                return;
            }


            CompInstKpiEntity kpiEntity = instanceRepo.getInstanceWiseKpisByIdentifier(accountIdentifier, instanceDetails.getIdentifier(), kpiIdentifier);
            if (kpiEntity == null) {
                log.warn("KPI not found for accountIdentifier:{}, instanceIdentifier:{}, hostAddress:{}, kpiIdentifier:{}", accountIdentifier, instanceDetails.getIdentifier(), instanceDetails.getHostAddress(), kpiIdentifier);
                return;
            }

            if (kpiEntity.getStatus() == 0) {
                log.warn("KPI is marked as Inactive for accountIdentifier:{}, instanceIdentifier:{}, hostAddress:{}, kpiIdentifier:{}", accountIdentifier, instanceDetails.getIdentifier(), instanceDetails.getHostAddress(), kpiIdentifier);
                return;
            }

            RawKpiData rawKpiData = RawKpiData.builder()
                    .agentIdentifier(agentBean.getUniqueToken())
                    .instanceData(Collections.singletonList(InstanceData.builder()
                            .identifier(instanceDetails.getIdentifier())
                            .kpiData(Collections.singletonList(KpiData.builder()
                                    .kpiUid(kpiEntity.getId())
                                    .timeInGMT(dateInGMT)
                                    .kpiType(KpiType.Availability)
                                    .kpiName(kpiEntity.getIdentifier())
                                    .val("1")
                                    .errorCode("")
                                    .isKpiGroup(false)
                                    .kpiGroupName("")
                                    .collectionInterval(60)
                                    .build()))
                            .build()))
                    .build();

            rawKpiDataList.add(rawKpiData);

            boolean isUpdatedToDR = HttpClient.sendHostAvailabilityToDR(rawKpiDataList);

            if(isUpdatedToDR) {
                log.trace("Marking host availability in redis key at time {},  for instance {} of account {}", dateInGMT, instanceDetails.getIdentifier(), accountIdentifier);

                instanceRepo.updateHostAvailability(accountIdentifier, instanceDetails.getIdentifier(), dateInGMT, HOST_AVAILABILITY_EXPIRY_MINUTES);
            }
        } catch (Exception e) {
            log.error("Error occurred while processing host availability data, agentDetails:{}", agentBean, e);
            CCCache.INSTANCE.updateCCErrors(1);
        }
    }

    private static void checkComponentAgentVersionHeader(String agentIdentifier, Map<String, String> requestHeaders, AgentBean agentByIdentifier, List<AccountBean> accountList) {
        String componentAgentVersion = requestHeaders.get(VERSION);
        log.debug("Component agent version obtained from request header coming from agent {} is {}", agentIdentifier, componentAgentVersion);

        BindInDataService bindInDataService = new BindInDataService();
        List<AgentBean> compInstAgentMapping = AgentStatusDataService.getCompInstAgentMapping(agentByIdentifier.getId());

        if (compInstAgentMapping == null || compInstAgentMapping.isEmpty()) {
            log.error("Component instance agent mapping data obtained from DB is either null or empty. Hence returning");
            return;
        }

        int accountId = compInstAgentMapping.get(0).getAccountId();

        Optional<AccountBean> account = accountList.parallelStream().filter(f -> f.getId() == accountId).findFirst();
        if(!account.isPresent()){
            log.error("From the list of accounts, could not find the accountId {} obtained from Instance Agent Mapping query. Please check the status of the account {}", accountId, accountId);
            return;
        }

        List<Integer> instanceIds = compInstAgentMapping.parallelStream().map(AgentBean::getCompInstanceId).collect(toList());
        List<ComponentInstanceBean> componentInstancesByIds = bindInDataService.getComponentInstancesByIds(instanceIds, accountId, null);

        if (componentInstancesByIds == null || componentInstancesByIds.isEmpty()) {
            log.error("List of instance details obtained from DB is either null or empty. Hence returning");
            return;
        }

        boolean updateDB;
        if (componentAgentVersion == null || componentAgentVersion.trim().isEmpty()) {
            updateDB = emptyComponentAgentVersionHandling(agentByIdentifier, componentInstancesByIds, instanceIds, account.get());
        } else {
            updateDB = nonEmptyComponentAgentVersionHandling(agentByIdentifier, componentAgentVersion, componentInstancesByIds, instanceIds, account.get());
        }

        if (updateDB) {
            int agentUpdate = AgentDataService.updateAgentDetails(agentByIdentifier);
            if (agentUpdate == 0) {
                log.error("Something went wrong while updating agent details for the agent {} in DB", agentByIdentifier.getUniqueToken());
            }

            int[] compInstanceUpdate = bindInDataService.updateCompInstanceDetails(componentInstancesByIds, null);
            if (compInstanceUpdate.length == 0) {
                log.error("Something went wrong while updating comp instance details for the ids {} in DB", componentInstancesByIds);
            }
        }
    }

    private static boolean nonEmptyComponentAgentVersionHandling(AgentBean agentByIdentifier, String componentAgentVersion, List<ComponentInstanceBean> componentInstancesByIds, List<Integer> instanceIds,  AccountBean account) {
        log.trace("The request header coming from component-agent {} contains the component agent version. Setting the version and enabling the forensic_enabled flag in DB", agentByIdentifier.getUniqueToken());
        if (agentByIdentifier.isForensicsEnabled()) {
            log.trace("Forensic enabled flag is already set to true");
            return false;
        } else {
            String finalVersion;
            String existingVersion = agentByIdentifier.getVersion();

            if (existingVersion == null || !existingVersion.equalsIgnoreCase(componentAgentVersion)) {
                agentByIdentifier.setVersion(componentAgentVersion);
                finalVersion = componentAgentVersion;
            } else {
                finalVersion = existingVersion;
            }

            agentByIdentifier.setForensicsEnabled(true);
            componentInstancesByIds.forEach(f-> f.setForensicsAgentTypeId(agentByIdentifier.getAgentTypeId()));
            makeChangesInAgentRedisKeys(agentByIdentifier, account, instanceIds, true, finalVersion);
            return true;
        }
    }

    private static boolean emptyComponentAgentVersionHandling(AgentBean agentByIdentifier, List<ComponentInstanceBean> componentInstancesByIds, List<Integer> instanceIds, AccountBean account) {
        log.trace("The version of the component agent {} obtained from the request header is either empty or null. Verifying whether the forensic_enabled flag is in the true state", agentByIdentifier.getUniqueToken());

        if (agentByIdentifier.isForensicsEnabled()) {
            log.trace("Forensic enabled flag was set to true in the previous GET call for agent {}. Setting it to false", agentByIdentifier.getUniqueToken());
            agentByIdentifier.setForensicsEnabled(false);
            agentByIdentifier.setVersion(null);
            componentInstancesByIds.forEach(f-> f.setForensicsAgentTypeId(agentByIdentifier.getAgentTypeId()));
            makeChangesInAgentRedisKeys(agentByIdentifier, account, instanceIds,false, null);
            return true;
        }
        else {
            log.trace("Forensic enabled flag is already set to false. Hence DB update is not needed");
            return false;
        }
    }

    private static void makeChangesInAgentRedisKeys(AgentBean agentByIdentifier, AccountBean account, List<Integer> instanceIds, boolean forensicsEnabled, String version) {
        AgentRepo agentRepo = new AgentRepo();
        InstanceRepo instanceRepo = new InstanceRepo();
        /*Populate account level agent redis key*/
        List<BasicAgentEntity> agents = agentRepo.getAgents(account.getIdentifier());
        if (agents != null && !agents.isEmpty()) {
            agents.parallelStream().filter(f -> f.getIdentifier().equalsIgnoreCase(agentByIdentifier.getUniqueToken()))
                    .peek(m -> {
                        m.setForensicsEnabled(forensicsEnabled);
                        m.setVersion(version);
                    }).collect(toList());
            agentRepo.updateAgents(account.getIdentifier(), agents);
        }

        /*Populate agent redis key*/
        Agent agentDetails = agentRepo.getAgentDetails(agentByIdentifier.getUniqueToken());
        if (agentDetails != null) {
            agentDetails.setVersion(version);
            agentDetails.setForensicsEnabled(forensicsEnabled);
            agentRepo.updateAgentDetailsForAgentIdentifier(agentDetails);
        }

        /*Populate instance keys*/
        List<com.heal.configuration.pojos.CompInstClusterDetails> instances = instanceRepo.getInstances(account.getIdentifier());

        if(instances == null || instances.isEmpty()) {
            log.error("Obtained null or empty instances redis key from redis for account {}", account.getIdentifier());
            return;
        }

        for (com.heal.configuration.pojos.CompInstClusterDetails instance : instances) {
            if (instanceIds.contains(instance.getId())) {
                instance.setForensicAgentTypeId(agentByIdentifier.getAgentTypeId());
                /*Populate instance by identifier redis key*/
                com.heal.configuration.pojos.CompInstClusterDetails instanceDetailByIdentifier = instanceRepo.getInstanceDetailByIdentifier(account.getIdentifier(), instance.getIdentifier());
                if (instanceDetailByIdentifier != null) {
                    instanceDetailByIdentifier.setForensicAgentTypeId(agentByIdentifier.getAgentTypeId());
                    instanceRepo.updateInstanceByIdentifier(account.getIdentifier(), instanceDetailByIdentifier);
                }
            }
        }
        instanceRepo.updateInstances(account.getIdentifier(), instances);
    }
}
