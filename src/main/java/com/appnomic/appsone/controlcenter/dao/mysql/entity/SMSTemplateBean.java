package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.pojo.SMSTemplate;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Data
public class SMSTemplateBean {

    private int id;
    private String userDetailsId;
    private String smsContent;
    private boolean status;
    private String name;
    private int templateTypeId;
    private String mobileNumbers;
    private String templateStatus;

    public SMSTemplate getSMSTemplate(){

        List<String> mobileNumbersList = new ArrayList<>();
        if(StringUtils.isNotEmpty(this.mobileNumbers)){
            String[] numbers = this.mobileNumbers.split(",");
            for(String number : numbers){
                mobileNumbersList.add(number.trim());
            }
        }
        String templateType = MasterCache.getMstSubTypeForSubTypeId(
                this.templateTypeId).getSubTypeName();

        return SMSTemplate.builder()
                .id(this.id)
                .userId(this.userDetailsId)
                .smsContent(this.smsContent)
                .status(this.status)
                .name(this.name)
                .templateTypeId(this.templateTypeId)
                .templateType(templateType)
                .mobileNumbers(mobileNumbersList)
                .templateStatus(this.templateStatus)
                .build();
    }
}
