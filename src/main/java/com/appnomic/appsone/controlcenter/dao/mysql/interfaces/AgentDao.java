package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.AgentBean;
import com.appnomic.appsone.controlcenter.beans.AgentCompInstMappingBean;
import com.appnomic.appsone.controlcenter.beans.AgentServAppMapping;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.PhysicalAgentBean;
import com.appnomic.appsone.controlcenter.pojo.AccountAgentMapping;
import com.appnomic.appsone.controlcenter.pojo.AgentInstanceMappingPojo;
import com.appnomic.appsone.controlcenter.pojo.AgentPhysicalAgentPojo;
import com.appnomic.appsone.controlcenter.pojo.IdValuePojo;
import com.heal.configuration.entities.BasicBean;
import com.heal.configuration.pojos.BasicEntity;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface AgentDao {
    //Fetch agent data from view
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select a.id,unique_token uniqueToken,a.name,a.agent_type_id agentTypeId," +
            "a.created_time createdTime,a.updated_time updatedTime,a.user_details_id userDetailsId," +
            "a.status, a.host_address hostAddress, a.mode, a.description, a.physical_agent_id physicalAgentId, " +
            "a.forensics_enabled forensicsEnabled, a.version, pa.identifier physicalAgentIdentifier " +
            " from agent a, physical_agent pa where unique_token = :agentUid and a.physical_agent_id=pa.id")
    AgentBean getAgentBeanData(@Bind("agentUid") String agentUid);

    
    //Fetch agent data from view
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,unique_token uniqueToken,name,agent_type_id agentTypeId," +
            "created_time createdTime,updated_time updatedTime,user_details_id userDetailsId," +
            "status,host_address hostAddress,mode,description from agent where name = :agentName")
    AgentBean getAgentBeanDataForName(@Bind("agentName") String agentName);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,unique_token uniqueToken,physical_agent_id physicalAgentId, name,agent_type_id agentTypeId,created_time " +
            "createdTime,updated_time updatedTime,user_details_id userDetailsId,status," +
            "host_address hostAddress,mode,description, supervisor_id supervisorId from agent")
    List<AgentBean> getAgentList();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,unique_token uniqueToken,physical_agent_id physicalAgentId, name,agent_type_id agentTypeId,created_time " +
            "createdTime,updated_time updatedTime,user_details_id userDetailsId,status," +
            "host_address hostAddress,mode,description, supervisor_id supervisorId from agent where physical_agent_id = :physicalAgentId")
    List<AgentBean> getAgentsListUsingPhysicalAgentId(@Bind("physicalAgentId") int physicalAgentId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,identifier identifier, created_time createdTime, updated_time updatedTime, user_details_id userDetailsId, " +
            "last_job_id lastJobId, last_status_id lastStatusId, is_command_executed lastCommandExecuted from physical_agent where identifier=:physicalAgentIdentifier")
    PhysicalAgentBean getPhysicalAgentDetailsUsingIdentifier(@Bind("physicalAgentIdentifier") String physicalAgentIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,identifier identifier, created_time createdTime, updated_time updatedTime, user_details_id userDetailsId, " +
            "last_job_id lastJobId, last_status_id lastStatusId, is_command_executed lastCommandExecuted from physical_agent where id=:physicalAgentId")
    PhysicalAgentBean getPhysicalAgentDetailsUsingId(@Bind("physicalAgentId") int physicalAgentId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,identifier identifier, created_time createdTime, updated_time updatedTime, user_details_id userDetailsId, " +
            "last_job_id lastJobId, last_status_id lastStatusId, is_command_executed lastCommandExecuted from physical_agent")
    List<PhysicalAgentBean> getAllPhysicalAgentDetails();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct a.id, unique_token uniqueToken,physical_agent_id physicalAgentId, name,agent_type_id agentTypeId,a.created_time createdTime," +
            "a.updated_time updatedTime,a.user_details_id userDetailsId,a.status, a.host_address hostAddress, mode,description, a.communication_interval communicationInterval, " +
            "supervisor_id supervisorId, version, pa.identifier physicalAgentIdentifier from agent a, agent_account_mapping aam, physical_agent pa " +
            "where account_id=:accountId and aam.agent_id=a.id and a.physical_agent_id=pa.id")
    List<AgentBean> getAgentAccountMapping(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select acm.id id, acm.agent_id agentId, acm.account_id accountId from agent_account_mapping acm, agent " +
            "where agent.id = acm.agent_id and agent.unique_token = :identifier")
    List<AccountAgentMapping> getAgentAccountMappingByAgentId(@Bind("identifier") String identifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select acm.id id, acm.agent_id agentId, acm.account_id accountId, agent.unique_token uniqueToken from agent_account_mapping acm, agent, " +
            "physical_agent pa where agent.id=acm.agent_id and agent.physical_agent_id=pa.id and pa.identifier=:physicalAgentIdentifier")
    AgentBean fetchAgentAccountMapping(@Bind("physicalAgentIdentifier") String physicalAgentIdentifier);

    @SqlUpdate("insert into agent (unique_token, physical_agent_id, name, agent_type_id, created_time, updated_time, user_details_id, " +
            "status, host_address, mode, description) values (:uniqueToken, :physicalAgentId, :name, :agentTypeId, :createdTime, " +
            ":updatedTime, :userDetailsId, :status, :hostAddress, :mode, :description)")
    @GetGeneratedKeys
    int addAgent(@BindBean AgentBean agentBean);

    @SqlUpdate("insert into physical_agent (identifier, created_time, updated_time, user_details_id, " +
            "last_job_id, last_status_id, is_command_executed) values (:identifier, :createdTime, " +
            ":updatedTime, :userDetailsId, :lastJobId, :lastStatusId, :lastCommandExecuted)")
    @GetGeneratedKeys
    int addPhysicalAgent(@BindBean PhysicalAgentBean physicalAgentBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from physical_agent where identifier=:agentIdentifier")
    int checkIfAgentExists(@Bind("agentIdentifier") String agentIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct a.id,a.unique_token uniqueToken,a.name,a.agent_type_id agentTypeId,a.created_time createdTime," +
            "a.updated_time updatedTime,a.user_details_id userDetailsId, a.version," +
            "a.status,a.host_address hostAddress,a.mode,a.description,a.physical_agent_id physicalAgentId,acm.comp_instance_id compInstanceId " +
            "from agent_comp_instance_mapping acm, agent a where a.id=acm.agent_id " +
            "and acm.comp_instance_id =:compInstanceId")
     List<AgentBean> getAgentCompDetails(@Bind("compInstanceId") int compInstanceId);

    @SqlQuery("select vt.name from agent_commands_triggered ac,command_details cd ,view_types vt where ac.physical_agent_id=:agentUid and ac.command_id=cd.id and cd.action_id=vt.subtypeid order by ac.trigger_time desc limit 1,1")
    String getLastDesiredStatus(@Bind("agentUid") int agentUid);

    @SqlUpdate("update agent set status = 0, updated_time = :updatedTime, user_details_id = :userDetailsId where unique_token = :uniqueToken")
    void remAgent(@BindBean AgentBean agentBean);

    @SqlUpdate("update agent set physical_agent_id = :physicalAgentId, name = :name, agent_type_id = :agentTypeId, status = :status, host_address = :hostAddress, " +
            "mode = :mode, description = :description, updated_time = :updatedTime, user_details_id = :userDetailsId where unique_token = :uniqueToken")
    void updateAgent(@BindBean AgentBean agentBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select m.agent_id agentId, group_concat(distinct m.servicenames) services, " +
            "group_concat(distinct m.applicationnames)  applications " +
            "from (select a.agent_id, " +
            "(select group_concat(service_name) from view_cluster_services v where v.id = ccm.cluster_id) servicenames, " +
            "(select group_concat(distinct m.application_name) from view_application_service_mapping m, view_cluster_services v " +
            "where m.service_id=v.service_id  and v.id = ccm.cluster_id ) applicationnames " +
            "from comp_instance c, component_cluster_mapping ccm,agent_comp_instance_mapping a " +
            "where c.id = ccm.comp_instance_id and c.status = 1 and a.comp_instance_id = c.id ) m group by m.agent_id")
    List<AgentServAppMapping> getAgentToServicesAndApplicationsMapping();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, unique_token uniqueToken, host_address hostAddress, physical_agent_id physicalAgentId, " +
            "installation_path installationPath from agent where host_address=:hostAddress")
    List<AgentBean> getRelativePathForPSA(@Bind("hostAddress") String hostAddress);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select last_status_id lastStatusId, id from physical_agent")
    List<PhysicalAgentBean> getPhysicalAgentStatus();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select acim.comp_instance_id instanceId, acim.agent_id agentId, ag.name agentName," +
            " ag.unique_token uniqueToken, pa.id physicalAgentId, pa.identifier physicalAgentIdentifier," +
            " mst.id agentTypeId, mst.name agentTypeName, mst.status status from" +
            " agent_comp_instance_mapping acim,mst_sub_type mst,agent ag,physical_agent pa," +
            " agent_account_mapping aam where acim.agent_id = ag.id and aam.agent_id = ag.id" +
            " and aam.account_id = :accountId and ag.agent_type_id = mst.id and" +
            " ag.physical_agent_id = pa.id and mst.mst_type_id=1 and mst.status=1")
    List<AgentInstanceMappingPojo> getInstanceAgentMapping(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select agent_id from agent_comp_instance_mapping where comp_instance_id = :compInstanceId")
    int getAgentId(@Bind("compInstanceId") int compInstanceId);

    // for integration testing
    @SqlUpdate("delete from agent_account_mapping where agent_id = :agentId")
    void remAgentAccMappingIT(@Bind("agentId") int agentId);

    @SqlUpdate("delete from component_agent where agent_id = :agentId")
    void remCompAgentIT(@Bind("agentId") int agentId);

    @SqlUpdate("delete from agent where id = :agentId")
    void remAgentIT(@Bind("agentId") int agentId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from mst_sub_type where mst_type_id = 1 and status = 1 and id = :agentTypeId")
    int checkIfAgentTypeExists(@Bind("agentTypeId") int agentTypeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select a.id, a.unique_token uniqueToken, a.physical_agent_id physicalAgentId, a.name, " +
            "a.agent_type_id agentTypeId, a.created_time createdTime, a.updated_time updatedTime, " +
            "a.user_details_id userDetailsId,status, a.host_address hostAddress, a.mode, a.description, " +
            "a.supervisor_id supervisorId from agent a, agent_account_mapping am" +
            " where a.agent_type_id=:agentTypeId and a.id=am.agent_id and am.account_id in(1, :accountId)")
    List<AgentBean> getAgentsByTypeIdAndAccountId(@Bind("accountId") int accountId, @Bind("agentTypeId") int agentTypeId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select a.id, a.unique_token uniqueToken, a.physical_agent_id physicalAgentId, a.name, " +
            "a.agent_type_id agentTypeId, a.created_time createdTime, a.updated_time updatedTime, " +
            "a.user_details_id userDetailsId,status, a.host_address hostAddress, a.mode, a.description, " +
            "a.supervisor_id supervisorId, pa.identifier physicalAgentIdentifier, am.account_id accountId " +
            "from agent a, agent_account_mapping am, physical_agent pa where a.id=am.agent_id and " +
            "a.physical_agent_id=pa.id and am.account_id = :accountId")
    List<AgentBean> getAgentsByAccountId(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select a.agent_type_id agentTypeId, ac.agent_id agentId, ac.comp_instance_id compInstanceId from " +
            "agent_comp_instance_mapping ac, agent a where ac.comp_instance_id = :instanceId and ac.agent_id = a.id")
    List<AgentCompInstMappingBean> getAgentMappingByCompInstanceId(@Bind("instanceId") int instanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(id) from agent where id = :agentId")
    int isAgentIdExists(@Bind("agentId") int agentId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(id) from agent_account_mapping where account_id in (1, :accountId)")
    int getAgentCountForAccount(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(id) from agent where unique_token = :agentIdentifier")
    int isAgentIdetifierExists(@Bind("agentIdentifier") String agentIdentifier);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("update agent set name = ifnull(:name, name), status = ifnull(:status, status), host_address = ifnull(:hostAddress, host_address), " +
            "mode = ifnull(:mode, mode), description = ifnull(:description, description), updated_time = :updatedTime, user_details_id = :userDetailsId " +
            "where unique_token = :uniqueToken")
    int updateAgentConfig(@BindBean AgentBean agentBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from agent_account_mapping where agent_id = :agentId and account_id = :accountId")
    int deleteAgentAccountMapping(@Bind("agentId") int agentId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from physical_agent where id = :physicalAgentId")
    int deletePhysicalAgent(@Bind("physicalAgentId") int physicalAgentId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, physical_agent_id physicalAgentId from agent where agent_type_id = :agentTypeId and host_address = :hostAddress")
    List<AgentBean> getAgentsByTypeNHostAddress(@Bind("agentTypeId") int agentTypeId, @Bind("hostAddress") String hostAddress);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select comp_instance_id id, agent_id value from agent_comp_instance_mapping")
    List<IdValuePojo> getAllAgentIdOfCompInstId();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select a.id agentId, pa.identifier physicalAgentIdentifier, pa.created_time createdTime," +
            " pa.updated_time updatedTime, pa.user_details_id userDetailsId, pa.last_job_id lastJobId," +
            " pa.last_status_id lastStatusId, pa.is_command_executed lastCommandExecuted," +
            " pa.id physicalAgentId from physical_agent pa join agent a on pa.id=a.physical_agent_id")
    List<AgentPhysicalAgentPojo> getPhysicalAgentIdsFromAgentId();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from agent_comp_instance_mapping where agent_id = :agentId")
    int deleteAgentInstance(@Bind("agentId") int agentId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select unique_token identifier from agent where id = :agentId")
    String getAgentIdentifier(@Bind("agentId") int agentId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct tag_value from tag_mapping where account_id = :accountId and tag_id = :tagId order by tag_value")
    List<String> getAgentDataSources(@Bind("accountId") int accountId, @Bind("tagId") int tagId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select c.id, c.name, c.identifier, c.created_time createdTime, c.updated_time updatedTime, c.user_details_id lastModifiedBy, a.status from agent_comp_instance_mapping, comp_instance c, agent a where a.id=agent_id and c.id=comp_instance_id and agent_id = :agentId and c.account_id=:accountId and c.id=:instanceId")
    List<BasicEntity> getAgentToInstanceMapping(@Bind("instanceId") int instanceId, @Bind("accountId") int accountId, @Bind("agentId") int agentId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select application_id id, application_name name, application_identifier identifier," +
            " service_identifier concernedConfigIdentifier, service_id concernedConfigId, service_name concernedConfigName" +
            " from view_application_service_mapping vasm where vasm.account_id= :accountId")
    List<BasicBean> getAllApplicationMappedToServices(@Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("update agent set version = :version, forensics_enabled = :forensicsEnabled " +
            "where unique_token = :uniqueToken")
    int updateAgentDetails(@Bind("version") String version, @Bind("forensicsEnabled") boolean forensicEnabled, @Bind("uniqueToken") String uniqueToken);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select a.id, a.unique_token uniqueToken, a.physical_agent_id physicalAgentId, a.name, " +
            "a.agent_type_id agentTypeId, a.created_time createdTime, a.updated_time updatedTime, " +
            "a.user_details_id userDetailsId,status, a.host_address hostAddress, a.mode, a.description, " +
            "a.supervisor_id supervisorId, pa.identifier physicalAgentIdentifier, am.account_id accountId " +
            "from agent a, agent_account_mapping am, physical_agent pa where a.id=am.agent_id and " +
            "a.physical_agent_id=pa.id and am.account_id = :accountId and a.agent_type_id=218;")
    List<AgentBean> getForensicAgentsByAccountId(@Bind("accountId") int accountId);

}
