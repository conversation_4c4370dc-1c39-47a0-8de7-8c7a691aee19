package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ServiceTransaction;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.TransactionAuditConfigurationBean;
import com.appnomic.appsone.controlcenter.beans.TransactionStatusEntity;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.TransactionDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.AutoAcceptanceSettingsPojo;
import com.appnomic.appsone.controlcenter.pojo.Transaction;
import com.appnomic.appsone.controlcenter.pojo.TransactionStatusPojo;
import com.appnomic.appsone.controlcenter.pojo.TxnAndGroupBean;
import com.heal.configuration.entities.BasicTransactionBean;
import org.skife.jdbi.v2.Handle;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR> Parekaden : 24/1/19
 */
public class TransactionDataService extends AbstractDaoService<TransactionDao> {
    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(TransactionDataService.class);
    private static final String STATUS_STRING = "Status";


    public static TransactionBean getTransaction(Integer accountId, String txnName, TransactionDao transactionDao) {
        logger.trace("{} getTransaction , PARAMS: ({},{})", Constants.INVOKED_METHOD, accountId, txnName);
        TransactionBean transactionBean = transactionDao.getTransaction(accountId, txnName);

        if (transactionBean == null) {
            logger.warn("Unable to find transaction: {} in account: {}", txnName, accountId);
            return null;
        }
        Integer txnId = transactionBean.getId();
        List<SubTransactionBean> subTransactionBeanList = transactionDao.getSubTransaction(txnId);

        if (subTransactionBeanList == null || subTransactionBeanList.isEmpty()) {
            logger.warn("Sub transaction details for transaction : {} does not exist in DB", transactionBean.getName());
            return null;
        }
        transactionBean.setSubTransaction(subTransactionBeanList);

        for (SubTransactionBean subTransactionBean : subTransactionBeanList) {
            Integer subTxnId = subTransactionBean.getId();
            List<TransactionMatcherDetailsBean> transactionMatcherDetailsBeanList = transactionDao.getTransactionMatcherDetails(subTxnId);
            if (transactionMatcherDetailsBeanList == null || transactionMatcherDetailsBeanList.isEmpty()) {
                logger.warn("Unable to transaction pattern information for transaction id: {}", transactionBean.getId());
            }
            subTransactionBean.setTransactionMatcherDetailsList(transactionMatcherDetailsBeanList);
        }

        logger.debug("Found transaction with details: {}", transactionBean);
        return transactionBean;
    }

    public static Integer addTransaction(TransactionBean transactionBean, TransactionDao transactionDao, Transaction transaction) {
        logger.trace("{} addTransaction, PARAMS: {}", Constants.INVOKED_METHOD, transactionBean.toString());
        int transactionId = 0;
        List<Integer> subTransactionIdList = new ArrayList<>();
        List<Integer> transactionMatcherIdList = new ArrayList<>();
        try {
            transactionId = transactionDao.addTransaction(transactionBean);
            if (transactionId < 1) {
                logger.error("Exiting , since it failed to add transaction: {}", transactionBean);
                transaction.getResponseMessage().getMessages().put(STATUS_STRING, "failed to add information to transaction table");
                return -1;
            }

            logger.debug("Added transaction table entry for: {}", transactionBean);
            logger.debug("Transaction id: {}", transactionId);
            List<SubTransactionBean> subTransactionBeanList = transactionBean.getSubTransaction();

            for (SubTransactionBean subTransactionBean : subTransactionBeanList) {
                subTransactionBean.setTransactionId(transactionId);
                logger.debug("Added transaction id: {}", subTransactionBean);
                int subTransactionId = transactionDao.addSubTransaction(subTransactionBean);
                if (subTransactionId < 1) {
                    logger.error("Error occurred while adding sub transaction: {}.", subTransactionBean);
                    transaction.getResponseMessage().getMessages().put("Status", "failed to add entry in sub transaction");
                    return -1;
                }
                subTransactionIdList.add(subTransactionId);
                List<TransactionMatcherDetailsBean> transactionMatcherDetailsBeanList = subTransactionBean.getTransactionMatcherDetailsList();

                for (TransactionMatcherDetailsBean transactionMatcherDetailsBean : transactionMatcherDetailsBeanList) {
                    transactionMatcherDetailsBean.setSubTransactionId(subTransactionId);
                    int transactionMatcherId = transactionDao.addTransactionMatcherDetails(transactionMatcherDetailsBean);
                    if (transactionMatcherId < 1) {
                        logger.error("Error occurred while adding transaction matcher entry: {}", transactionMatcherDetailsBean);
                        transaction.getResponseMessage().getMessages().put("Status", "Error occurred while adding transaction matcher");
                        return -1;
                    }
                    transactionMatcherIdList.add(transactionMatcherId);
                }
            }

            return transactionId;
        } catch (Exception e) {
            logger.error("Error occurred while adding transaction: {} into database", transactionBean.getName(), e);
            return -1;
        }
    }

    public List<TransactionAuditConfigurationBean> getTxnAuditConfigurationDetails(List<Integer> txnIds, Handle handle) throws ControlCenterException {
        TransactionDao dao = getDaoConnection(handle, TransactionDao.class);
        try {
            return dao.getTxnAuditConfigurationDetails(txnIds);
        } catch (Exception e) {
            logger.error("Error occurred while getting transaction audit configuration details. " + e.getMessage(), e);
            logger.debug("trace:", e);
            throw new ControlCenterException("Error occurred while getting transaction audit configuration details.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }


    public void addTxnAuditConfigurationDetails(List<TransactionAuditConfigurationBean> auditDetails, Handle handle)
            throws ControlCenterException {
        TransactionDao dao = getDaoConnection(handle, TransactionDao.class);
        try {
            dao.addTxnAuditConfigurationDetails(auditDetails);
        } catch (Exception e) {
            logger.error("Error occurred while adding transaction audit configuration details. " + e.getMessage(), e);
            logger.debug("trace:", e);
            throw new ControlCenterException("Error occurred while adding transaction audit configuration details.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void updateTxnAuditConfigurationDetails(List<TransactionAuditConfigurationBean> auditDetails, Handle handle)
            throws ControlCenterException {
        TransactionDao dao = getDaoConnection(handle, TransactionDao.class);
        try {
            dao.updateTxnAuditConfigurationDetails(auditDetails);
        } catch (Exception e) {
            logger.error("Error occurred while adding transaction audit configuration details. " + e.getMessage(), e);
            logger.debug("trace:", e);
            throw new ControlCenterException("Error occurred while adding transaction audit configuration details.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void updateTransactionStatus(List<TransactionStatusEntity> transactionStatusEntityList, String lastUpdatedBy) throws ControlCenterException {
        TransactionDao dao = getDaoConnection(null, TransactionDao.class);
        try {
            dao.updateTransactionStatus(transactionStatusEntityList, lastUpdatedBy);
        } catch (Exception e) {
            logger.error("Exception while updating transaction status : {}", transactionStatusEntityList, e);
            throw new ControlCenterException("Exception while updating transaction status.");
        } finally {
            closeDaoConnection(null, dao);
        }
    }

    public void deleteTransactionsAndDependencies(List<TransactionStatusEntity> transactionStatusEntityList, Handle handle) throws ControlCenterException {
        TransactionDao dao = getDaoConnection(handle, TransactionDao.class);
        try {

            for (TransactionStatusEntity transactionStatusEntity : transactionStatusEntityList) {
                List<SubTransactionBean> subTransactionBeanList = dao.getSubTransactionBean(transactionStatusEntity.getId());
                if (!subTransactionBeanList.isEmpty()) {
                    dao.deleteTransactionFromTransactionMatcherDetails(subTransactionBeanList);
                }
            }

            dao.deleteSubTransactions(transactionStatusEntityList);

            dao.deleteTransactionFromTransactionResponseThresholdTable(transactionStatusEntityList);

            dao.deleteTransactionFromTransactionThresholdDetailsTable(transactionStatusEntityList);

            dao.deleteTransactionFromTransactionAuditConfigurationTable(transactionStatusEntityList);

            dao.deleteTransactionsFromTransactionTable(transactionStatusEntityList);

            dao.deleteTxnAuditDetails(transactionStatusEntityList);

            dao.deleteTxnGroupMapping(transactionStatusEntityList);

        } catch (Exception e) {
            logger.error("Exception while deleting transaction : {}", transactionStatusEntityList, e);
            throw new ControlCenterException("Exception while deleting transaction status.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public TransactionStatusPojo getTxnIdFromTxnIdentifier(String identifier, int id) {
        TransactionDao dao = getDaoConnection(null, TransactionDao.class);
        try {
            return dao.getTxnIdAndIdentifier(identifier, id);
        } catch (Exception e) {
            logger.error("Exception occurred while getting txn id from txn identifier {} or id {}", identifier, id);
        } finally {
            closeDaoConnection(null, dao);
        }

        return null;
    }

    public void deleteTxnAuditConfigurationDetailsById(Integer id, Handle handle) throws ControlCenterException {
        TransactionDao dao = getDaoConnection(handle, TransactionDao.class);
        try {
            dao.deleteTxnAuditConfigurationDetailsById(id);
        } catch (Exception e) {
            logger.error("Error while deleting transaction audit configurations." + e.getMessage(), e);
            throw new ControlCenterException("Error while deleting transaction audit configurations.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public List<TransactionAuditConfigurationBean> getAuditDetailsForTransactions() throws ControlCenterException {
        TransactionDao dao = getDaoConnection(null, TransactionDao.class);
        try {
            return dao.getAuditDetailsForTransactions();
        } catch (Exception e) {
            logger.error("Exception while getting transaction audit details. " + e.getMessage(), e);
            throw new ControlCenterException("Error while fetching transaction audit details.");
        } finally {
            closeDaoConnection(null, dao);
        }
    }

    public static List<TxnAndGroupBean> getTxnAndGroupList(int accountId) {
        TransactionDao transactionDaoAtomicConn = MySQLConnectionManager.getInstance().open(TransactionDao.class);
        try {
            return transactionDaoAtomicConn.getTxnAndGroupPerAccount(accountId);
        } catch (Exception e) {
            logger.error("Error occurred while getting txn for the account " + accountId + " ", e);
        } finally {
            MySQLConnectionManager.getInstance().close(transactionDaoAtomicConn);
        }
        return new ArrayList<>();
    }

    public TransactionCountBean getServiceTransactionCount(Integer serviceId, int accountId) {
        TransactionDao transactionDao = getDaoConnection(null, TransactionDao.class);

        try {
            return transactionDao.getServiceTransactionCount(serviceId, accountId);
        } catch (Exception e) {
            logger.error("Exception while retrieving the transaction  details list", e);
        } finally {
            closeDaoConnection(null, transactionDao);
        }

        return new TransactionCountBean();
    }


    public List<ServiceTransaction> getTransactions(int accountId) {
        TransactionDao transactionDao = getDaoConnection(null, TransactionDao.class);

        try {
            return transactionDao.getTransactions(accountId);
        } catch (Exception e) {
            logger.error("Exception while retrieving the transactions for account id:{}", accountId, e);
        } finally {
            closeDaoConnection(null, transactionDao);
        }

        return Collections.emptyList();
    }

    public com.heal.configuration.pojos.Transaction getTransaction(int txnId) {
        TransactionDao transactionDao = getDaoConnection(null, TransactionDao.class);

        try {
            return transactionDao.getTransactionById(txnId);
        } catch (Exception e) {
            logger.error("Exception while retrieving the transactions for txn id:{}", txnId, e);
        } finally {
            closeDaoConnection(null, transactionDao);
        }

        return null;
    }

    public int getServiceIdForTxn(int txnId) {
        TransactionDao transactionDao = getDaoConnection(null, TransactionDao.class);

        try {
            return transactionDao.getServiceIdForTxn(txnId);
        } catch (Exception e) {
            logger.error("Exception while retrieving the serviceId for txn id:{}", txnId, e);
        } finally {
            closeDaoConnection(null, transactionDao);
        }
        return 0;
    }

    public TransactionAuditConfigurationBean getTxnAuditConfiguration(int txnId) {
        TransactionDao transactionDao = getDaoConnection(null, TransactionDao.class);

        try {
            return transactionDao.getTxnAuditConfiguration(txnId);
        } catch (Exception e) {
            logger.error("Exception while retrieving the Txn audit Configuration for txn id:{}", txnId, e);
        } finally {
            closeDaoConnection(null, transactionDao);
        }
        return null;
    }

    public Map<String, String> getTxnPattern(int transactionId) {
        TransactionDao transactionDao = getDaoConnection(null, TransactionDao.class);

        try {
            Map<String, String> txnPatternsMap = new HashMap<>();
            List<com.heal.configuration.entities.SubTransactionBean> txnPatterns = transactionDao.getTxnPatterns(transactionId);

            if (txnPatterns == null) {
                return txnPatternsMap;
            }
            for (com.heal.configuration.entities.SubTransactionBean txnPattern : txnPatterns) {
                txnPatternsMap.put(Constants.TXN_PATTERN_URL, txnPattern.getUrl());
                txnPatternsMap.put(txnPattern.getName(), txnPattern.getValue());
            }
            return txnPatternsMap;
        } catch (Exception e) {
            logger.error("Exception while retrieving transaction patterns", e);
        } finally {
            closeDaoConnection(null, transactionDao);
        }
        return null;
    }

    public void updateTransactionSettingTable(AutoAcceptanceSettingsPojo autoAcceptanceSettingsPojos, int service_id, Handle handle) throws ControlCenterException {
        TransactionDao transactionDao = getDaoConnection(handle, TransactionDao.class);

        try {
            transactionDao.updateTransactionSettingTable(autoAcceptanceSettingsPojos, service_id);
        } catch (Exception e) {
            logger.error("Exception while updating transaction auto acceptance settings : {}, in transaction_settings table", autoAcceptanceSettingsPojos);
            throw new ControlCenterException("Exception while updating transaction auto acceptance settings in transaction settings table");
        } finally {
            closeDaoConnection(handle, transactionDao);
        }
    }

    public void updateSchedulerDetailsTable(String cronExpression, int id, Handle handle) throws ControlCenterException {
        TransactionDao transactionDao = getDaoConnection(handle, TransactionDao.class);

        try {
            transactionDao.updateSchedulerDetailsTable(cronExpression, id);
        } catch (Exception e) {
            logger.error("Exception while updating cron expression : {} in scheduler details table", cronExpression);
            throw new ControlCenterException("Exception while updating cron expression");
        } finally {
            closeDaoConnection(handle, transactionDao);
        }
    }
    public ServiceTransactionSettingBean getTransactionSetting(int accountId, int serviceId) throws ControlCenterException {
        TransactionDao transactionDao = getDaoConnection(null, TransactionDao.class);

        try {
            return transactionDao.getTransactionSetting(accountId, serviceId);
        } catch (Exception e) {
            logger.error("Exception while getting transaction setting details for account id : {} and service id : {} ", accountId, serviceId);
            throw new ControlCenterException("Exception while getting transaction setting");
        } finally {
            closeDaoConnection(null, transactionDao);
        }
    }

    public List<BasicTransactionBean> getDiscardedTransactionsService(int accountId, int serviceId) throws ControlCenterException {
        TransactionDao transactionDao = getDaoConnection(null, TransactionDao.class);

        try {
            return transactionDao.getTransactionsService(accountId, serviceId);
        } catch (Exception e) {
            logger.error("Exception while getting Discarded transaction details from DB for account id : {} and service id : {} ", accountId, serviceId);
            CCCache.INSTANCE.updateCCErrors(1);
            throw new ControlCenterException("Exception while getting discarded transaction");
        } finally {
            closeDaoConnection(null, transactionDao);
        }
    }

    public void updateCommitDetails(String lastCommitTime, int accountId, int serviceId, int lastAcceptedTxnCount, int lastDiscardedTxnCount, String lastUpdatedBy, Handle handle) throws ControlCenterException {
        TransactionDao transactionDao = getDaoConnection(handle, TransactionDao.class);

        try {
            transactionDao.updateCommitDetails(lastAcceptedTxnCount, lastDiscardedTxnCount, lastCommitTime, accountId, serviceId, lastUpdatedBy);
        } catch (Exception e) {
            logger.error("Exception while updating CommitDetails for service id: {} in transaction_settings table", serviceId);
            CCCache.INSTANCE.updateCCErrors(1);
            throw new ControlCenterException("Exception while updating CommitDetails");
        } finally {
            closeDaoConnection(handle, transactionDao);
        }
    }
}
