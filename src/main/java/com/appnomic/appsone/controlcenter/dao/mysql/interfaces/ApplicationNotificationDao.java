package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.DefaultNotificationPreferences;
import com.appnomic.appsone.controlcenter.pojo.NotificationPreferencesPojo;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

@UseStringTemplate3StatementLocator
public interface ApplicationNotificationDao {

    @SqlBatch("INSERT INTO application_notification_mapping (application_id,notification_type_id,signal_type_id,signal_severity_id,account_id,created_time, updated_time, user_details_id) " +
            "VALUES (:applicationId, :notificationTypeId, :signalTypeId,:signalSeverityId, :accountId, :createdTime, :updatedTime, :userDetailsId)")
    @GetGeneratedKeys
    int[] addDefaultPreferences(@BindBean List<DefaultNotificationPreferences> defaultNotificationPreferences);

    @SqlBatch("UPDATE application_notification_mapping SET notification_type_id = :notificationTypeId, updated_time = :updatedTime, user_details_id = :userDetailsId WHERE account_id = :accountId AND application_id = :applicationId AND signal_type_id = :signalTypeId AND signal_severity_id = :signalSeverityId")
    int[] updateDefaultPreferences(@BindBean List<DefaultNotificationPreferences> defaultNotificationPreferences);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select application_id applicationId, signal_type_id signalTypeId, signal_severity_id severityTypeId, notification_type_id notificationTypeId from application_notification_mapping where application_id = :applicationId")
    List<NotificationPreferencesPojo> getApplicationNotificationMappingDetails(@Bind("applicationId") int applicationId);
}