package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.CompInstanceForensicCategoryBean;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.CategoryForensicsDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.pojo.CategoryForensicDetails;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;
import java.util.Set;

public class CategoryForensicsDataService extends AbstractDaoService<CategoryForensicsDao> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CategoryForensicsDataService.class);

    public List<CategoryForensicDetails> getCategoryForensicMappingsForComponentId(int componentId, int accountId, Handle handle) throws ControlCenterException {
        CategoryForensicsDao dao = getDaoConnection(handle, CategoryForensicsDao.class);
        try {
            return dao.getCategoryForensicMappingsForComponentId(componentId, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching category forensic mappings for componentId : {}", componentId, e);
            throw new ControlCenterException("Exception while fetching category forensic mappings for componentId .");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public CompInstanceForensicCategoryBean getCompInstanceForensicCategoryDetailsForInstanceId(int instanceId, int categoryId, int actionId, Handle handle) throws ControlCenterException {
        CategoryForensicsDao dao = getDaoConnection(handle, CategoryForensicsDao.class);
        try {
            return dao.getCompInstanceForensicCategoryDetailsForInstanceId(instanceId, categoryId, actionId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching category forensic mappings for instanceId : {}", instanceId, e);
            throw new ControlCenterException("Exception while fetching category forensic mappings for instanceId .");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public List<CompInstanceForensicCategoryBean> getCompInstanceForensicDetailsForInstanceId(int instanceId, Handle handle) {
        CategoryForensicsDao dao = getDaoConnection(handle, CategoryForensicsDao.class);
        try {
            return dao.getCompInstanceForensicDetailsForInstanceId(instanceId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching category forensic mappings for instanceId : {}", instanceId, e);
            return Collections.emptyList();
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public CompInstanceForensicCategoryBean getActionCategoryMappingDetails(int categoryId, int actionId, Handle handle) throws ControlCenterException {
        CategoryForensicsDao dao = getDaoConnection(handle, CategoryForensicsDao.class);
        try {
            return dao.getActionCategoryMappingDetails(categoryId, actionId);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching category forensic mapping details : ", e);
            throw new ControlCenterException("Exception while fetching category forensic mapping details.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void addCompInstanceForensicCategoryDetails(List<CompInstanceForensicCategoryBean> list, Handle handle) throws ControlCenterException {
        CategoryForensicsDao dao = getDaoConnection(handle, CategoryForensicsDao.class);
        try {
            dao.addCompInstanceForensicCategoryDetails(list);
        } catch (Exception e) {
            LOGGER.error("Exception while adding instance - category forensic mapping details : ", e);
            throw new ControlCenterException("Exception while adding instance - category forensic mapping details");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void updateCompInstanceForensicCategoryDetails(List<CompInstanceForensicCategoryBean> list, Handle handle) throws ControlCenterException {
        CategoryForensicsDao dao = getDaoConnection(handle, CategoryForensicsDao.class);
        try {
            dao.updateCompInstanceForensicCategoryDetails(list);
        } catch (Exception e) {
            LOGGER.error("Exception while updating instance - category forensic mapping details : ", e);
            throw new ControlCenterException("Exception while updating instance - category forensic mapping details");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public List<CompInstanceForensicCategoryBean> getCompInstanceForensicDetailsForAllInstanceId(Set<String> instanceIds,
                                                                                                 Handle handle) {
        CategoryForensicsDao dao = getDaoConnection(handle, CategoryForensicsDao.class);
        try {
            return dao.getCompInstanceForensicDetailsForAllInstanceId(instanceIds);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching category forensic mappings for instanceIds : {}", instanceIds, e);
            return Collections.emptyList();
        } finally {
            closeDaoConnection(handle, dao);
        }
    }
}
