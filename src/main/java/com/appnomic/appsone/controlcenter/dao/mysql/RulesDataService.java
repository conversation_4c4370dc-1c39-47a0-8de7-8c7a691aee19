package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.BindInDao;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.RulesDao;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.HttpPatternDataBean;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.RulesHelperPojo;
import com.heal.configuration.pojos.PairData;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class RulesDataService {

    private static final Logger logger = LoggerFactory.getLogger(RulesDataService.class);

    public static List<RulesBean> getRules(int accountId) {
        RulesDao rulesDao =
                MySQLConnectionManager.getInstance().getHandle().onDemand(RulesDao.class);
        List<RulesBean> rules = new ArrayList<>();
        try {
            rules = rulesDao.getRulesByAccountId(accountId);
            fillRulesBean(rules, rulesDao);
            return rules;
        } catch (Exception e) {
            logger.error("Error occurred while fetching rules", e);
        } finally {
            MySQLConnectionManager.getInstance().close(rulesDao);
        }

        return rules;
    }

    public static List<RuleDetailsBean> getRuleList(int serviceId, int tagId, int accountId) {
        RulesDao rulesDao = MySQLConnectionManager.getInstance().open(RulesDao.class);
        try {
            return rulesDao.getRuleList(serviceId, Constants.RULES_TABLE, tagId, accountId);
        } catch (Exception e) {
            logger.error("Exception while retrieving the rule list", e);
        } finally {
            MySQLConnectionManager.getInstance().close(rulesDao);
        }
        return Collections.emptyList();
    }

    public static List<RulesBean> getRules(List<Integer> ruleIds) {
        BindInDao bindInDao =
                MySQLConnectionManager.getInstance().getHandle().onDemand(BindInDao.class);

        RulesDao rulesDao =
                MySQLConnectionManager.getInstance().getHandle().onDemand(RulesDao.class);
        List<RulesBean> rules = new ArrayList<>();
        if(ruleIds == null || ruleIds.isEmpty()){
            return rules;
        }
        try {
            rules = bindInDao.getRulesByIds(ruleIds);
            fillRulesBean(rules, rulesDao);
            return rules;
        } catch (Exception e) {
            logger.error("Error occurred while fetching rules", e);
        } finally {
            MySQLConnectionManager.getInstance().close(rulesDao);
        }

        return rules;
    }

    private static void fillRulesBean(List<RulesBean> rules, RulesDao rulesDao) {
        for (RulesBean rule : rules) {
            rule.setRuleFor(getNameFromMSTSubType(rule.getRuleTypeId()));
            RegexTypeDetailBean regexTypeDetailBean = rulesDao.getRegexTypeDetail(rule.getId());
            RequestTypeDetailBean requestTypeDetailBean =
                    rulesDao.getRequestTypeDetail(rule.getId());
            if (requestTypeDetailBean != null) {
                requestTypeDetailBean.setPayloadTypeName(getNameFromMSTSubType(requestTypeDetailBean.getPayloadTypeId()));
                List<PairDataBean> pairDataBeans = rulesDao.getPairDataList(rule.getId(),
                        requestTypeDetailBean.getId());
                for (PairDataBean pairData : pairDataBeans) {
                    pairData.setPairTypeName(getNameFromMSTSubType(pairData.getPairTypeId()));
                }
                requestTypeDetailBean.setPairData(pairDataBeans);


            }
            rule.setRegexTypeDetails(regexTypeDetailBean);
            rule.setRequestTypeDetails(requestTypeDetailBean);
        }
    }

    public static String getNameFromMSTSubType(Integer mstSubTypeId) {
        MasterSubTypeBean masterSubTypeBean =
                MasterDataService.getMasterSubTypeDetailsForId(mstSubTypeId);
        return masterSubTypeBean != null ? masterSubTypeBean.getName() : null;
    }

    public static List<RuleDetailsBean> getRuleList(int serviceId, String ruleTable, int tagId, int accountId, Handle handle) {
        RulesDao rulesDao = getRulesDao(handle);
        try{
            return rulesDao.getRuleList(serviceId, ruleTable, tagId, accountId);
        } catch (Exception e) {
            logger.error("Exception while retrieving the rule list", e);
        } finally {
            MySQLConnectionManager.getInstance().close(rulesDao);
        }
        return Collections.emptyList();
    }

    public static int addRule(RulesBean rulesBean, Handle handle) {
        RulesDao rulesDao = getRulesDao(handle);
        try {
            return rulesDao.addRulesDetails(rulesBean);
        } catch (Exception e) {
            logger.error("Error occurred while adding rules " + e.getMessage(), e);
            logger.debug("trace:", e);
        } finally {
            closeDaoConnection(handle, rulesDao);
        }
        return -1;
    }

    public static int addHttpPatterns(HttpPatternDataBean httpPatternDataBean, Handle handle) {
        RulesDao rulesDao = getRulesDao(handle);
        try {
            return rulesDao.addHttpPatterns(httpPatternDataBean);
        } catch (Exception e) {
            logger.error("Error occurred while adding http pattern details in http_patterns table: ", e);
        } finally {
            closeDaoConnection(handle, rulesDao);
        }
        return -1;
    }

    public static void updateRulesOrder(List<RulesBean> rulesBeansList) throws DataProcessingException {
        RulesDao rulesDao = MySQLConnectionManager.getInstance().open(RulesDao.class);
        try {
            rulesDao.updateOrderDetails(rulesBeansList);
        } catch (Exception e) {
            logger.error("Exception while updating the rules order.", e);
            throw new DataProcessingException("Exception while updating the rules order." + e);
        } finally {
            MySQLConnectionManager.getInstance().close(rulesDao);
        }
    }

    public List<RulesHelperPojo> getRulesHelperPojo(int accountId, int serviceId) {
        logger.info("Getting rules helper pojo for the account id {} and service id {}", accountId, serviceId);
        RulesDao rulesDao = MySQLConnectionManager.getInstance().open(RulesDao.class);
        List<RulesHelperPojo> rulesHelperPojo;
        try {
            rulesHelperPojo = rulesDao.getRulesHelperPojo(accountId, serviceId);
        } catch (Exception e) {
            logger.error("Exception while getting the rules helper pojo.", e);
            return Collections.emptyList();
        } finally {
            MySQLConnectionManager.getInstance().close(rulesDao);
        }
        return rulesHelperPojo;
    }

    private static RulesDao getRulesDao(Handle handle){
        if(handle == null){
            return MySQLConnectionManager.getInstance().open(RulesDao.class);
        }
        else{
            return handle.attach(RulesDao.class);
        }
    }
    private static void closeDaoConnection(Handle handle, RulesDao dao){
        if(handle == null){
            MySQLConnectionManager.getInstance().close(dao);
        }
    }

    public List<PairData> getPairDataListForHttpPatterns(int id, int httpId) {
        logger.error("Getting http patterns pair data list for rule id {} and http id {}", id, httpId);
        RulesDao rulesDao = MySQLConnectionManager.getInstance().open(RulesDao.class);
        List<PairData> pairDataBeans;
        try {
            pairDataBeans = rulesDao.getDataBeans(id, httpId);
        } catch (Exception e) {
            logger.error("Exception while getting the rules helper pojo.", e);
            return Collections.emptyList();
        } finally {
            MySQLConnectionManager.getInstance().close(rulesDao);
        }
        return pairDataBeans;
    }
}