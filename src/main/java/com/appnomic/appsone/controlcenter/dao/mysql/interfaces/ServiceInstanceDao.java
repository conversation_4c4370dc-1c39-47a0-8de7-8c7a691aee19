package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.CompInstanceKpiGroupDetailsBean;
import com.appnomic.appsone.controlcenter.pojo.ServiceInstanceAttributeBean;
import com.heal.configuration.pojos.ServiceInstanceDetails;
import com.heal.configuration.pojos.ServiceInstanceKpiEntity;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;

import java.util.List;

public interface ServiceInstanceDao {
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("INSERT INTO service_instance " +
            "(name, identifier, status, account_id, service_id, " +
            "mst_component_id, mst_component_version_id, mst_common_version_id, " +
            "mst_component_type_id, created_time, updated_time, user_details_id) " +
            "VALUES (:name, :identifier, :status, :accountId, :serviceId, " +
            ":componentId, :componentVersionId, :commonVersionId, " +
            ":componentTypeId, :createdTime, :updatedTime, :lastModifiedBy)")
    @GetGeneratedKeys
    int insertServiceInstance(@BindBean ServiceInstanceDetails serviceInstanceDetails);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO service_instance_kpi_details " +
            "(service_instance_id, mst_producer_kpi_mapping_id, collection_interval, " +
            "status, created_time, updated_time, user_details_id, mst_kpi_details_id, " +
            "mst_producer_id, notification) " +
            "VALUES (:serviceInstanceId, :producerKpiMappingId, :collectionInterval, " +
            ":status, :createdTime, :updatedTime, :userDetailsId, :id, :producerId, :notificationsEnabled)")
    void insertServiceInstanceKpiDetailsList(@BindBean List<ServiceInstanceKpiEntity> serviceInstanceKpiEntities);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO service_instance_kpi_group_details " +
            "(service_instance_id, mst_producer_kpi_mapping_id, collection_interval, " +
            "status, created_time, updated_time, user_details_id, mst_kpi_details_id, " +
            "mst_producer_id, notification, mst_kpi_group_id, kpi_group_name, is_discovery, " +
            "attribute_value, attribute_status, alias_name) " +
            "VALUES (:compInstanceId, :mstProducerKpiMappingId, :collectionInterval, " +
            ":status, :createdTime, :updatedTime, :userDetailsId, :id, :mstProducerId, " +
            ":notification, :mstKpiGroupId, :kpiGroupName, :isDiscovery, :attributeValue, " +
            ":attributeStatus, :aliasName)")
    void insertServiceInstanceGroupKpiDetailsList(@BindBean List<CompInstanceKpiGroupDetailsBean> serviceInstanceGroupKpiDetailsList);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("INSERT INTO service_instance_attribute_values " +
            "(attribute_value, service_instance_id, mst_component_attribute_mapping_id, " +
            "created_time, updated_time, user_details_id, mst_common_attributes_id, attribute_name) " +
            "VALUES (:attributeValue, :serviceInstanceId, :componentAttributeMappingId, " +
            ":createdTime, :updatedTime, :userDetailsId, :mstCommonAttributesId, :attributeName)")
    void insertServiceInstanceAttribute(@BindBean List<ServiceInstanceAttributeBean> serviceInstanceAttributeBean);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from service_instance where service_id=:serviceId and account_id=:accountId")
    int getServiceInstanceIdByServiceId(@Bind("serviceId") int serviceId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from service_instance where service_id=:serviceId and account_id=:accountId")
    void deleteServiceInstanceByServiceId(@Bind("serviceId") int serviceId, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from service_instance_kpi_details where service_instance_id=:serviceInstanceId")
    void deleteServiceInstanceKpiDetailsByServiceInstanceId(@Bind("serviceInstanceId") int serviceInstanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from service_instance_kpi_group_details where service_instance_id=:serviceInstanceId")
    void deleteServiceInstanceGroupKpiDetailsByServiceInstanceId(@Bind("serviceInstanceId") int serviceInstanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from service_instance_attribute_values where service_instance_id=:serviceInstanceId")
    void deleteServiceInstanceAttributeByServiceInstanceId(@Bind("serviceInstanceId") int serviceInstanceId);

}
