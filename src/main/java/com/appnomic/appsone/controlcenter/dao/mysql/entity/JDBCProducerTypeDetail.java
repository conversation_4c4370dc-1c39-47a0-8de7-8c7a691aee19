package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.ConstraintViolation;
import javax.validation.constraints.Size;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Data
@NoArgsConstructor
public class JDBCProducerTypeDetail extends ProducerTypeDetail {

    private static final Logger LOGGER = LoggerFactory.getLogger(JDBCProducerTypeDetail.class);

    @Size(min = 1, max = 64, message = "driver name length must be less than or equal to 64")
    private String jdbcDriver;

    @Size(min = 1, max = 256, message = "url length must be less than or equal to 256")
    private String jdbcUrl;

    @Size(min = 1, max = 45, message = "query type length must be less than or equal to 45")
    private String query_type;

    @Size(min = 1, max = 65535, message = "query length must be less than or equal to 65535")
    private String query;

    private Integer isQueryEncrypted;

    public JDBCProducerTypeDetail(int producerId, String createdTime, String updatedTime, String userDetailsId) {
        super(producerId, createdTime, updatedTime, userDetailsId);
    }

    @Override
    public Map<String, String> getDataMap() {
        Map<String, String> map = new HashMap<>();
        map.put("jdbcDriver", jdbcDriver);
        map.put("jdbcUrl", jdbcUrl);
        map.put("query_type", query_type);
        map.put("query", query);
        return map;
    }

    public boolean populate(Map<String, String> attributes) {
        this.setJdbcDriver(attributes.get(Constants.JDBC_TYPE_DRIVER_ATTRIBUTE));
        this.setJdbcUrl(attributes.get(Constants.JDBC_TYPE_URL_ATTRIBUTE));
        this.setQuery(attributes.get(Constants.JDBC_TYPE_QUERY_ATTRIBUTE));
        this.setQuery_type(attributes.get(Constants.JDBC_TYPE_QUERY_RESULT_ATTRIBUTE));
        Integer isEncryptedQuery = (attributes.get(
                Constants.JDBC_TYPE_IS_QUERY_ENCRYPTED_ATTRIBUTE) == null ) ? null :
                Integer.parseInt(attributes.get(Constants.JDBC_TYPE_IS_QUERY_ENCRYPTED_ATTRIBUTE));
        this.setIsQueryEncrypted(isEncryptedQuery);
        return validate();
    }

    private boolean validate() {
        Set<ConstraintViolation<Object>> violations = ValidationUtils.validateFields(this);

        if( ! violations.isEmpty() ) {
            violations.forEach(it -> LOGGER.error(it.getMessage()));
            return false;
        }

        if( this.getIsQueryEncrypted() != 0 && this.getIsQueryEncrypted() != 1 ) {
            LOGGER.error("Invalid input for received for is query encrypted, acceptable {0,1}");
            return false;
        }

        return true;
    }
}
