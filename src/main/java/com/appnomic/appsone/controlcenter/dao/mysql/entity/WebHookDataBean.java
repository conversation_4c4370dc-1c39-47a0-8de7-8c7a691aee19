package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebHookDataBean {
    int id;
    String url;
    Timestamp createdTime;
    Timestamp updatedTime;
    String userDetailsId;
    int accountId;
}

