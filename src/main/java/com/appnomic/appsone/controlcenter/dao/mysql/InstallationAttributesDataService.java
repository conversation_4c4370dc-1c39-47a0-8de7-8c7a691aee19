package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.InstallationAttributesDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class InstallationAttributesDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(InstallationAttributesDataService.class);

    public boolean checkForOfflineHeal(String userId) {
        InstallationAttributesDao installationAttributesDao = MySQLConnectionManager.getInstance().open(InstallationAttributesDao.class);
        boolean offlineHeal = false;
        try {

            String installationMode = installationAttributesDao.checkForInstallationMode(userId);

            if (installationMode != null && installationMode.length() != 0 && installationMode.equalsIgnoreCase("Offline")) {
                offlineHeal = true;
            }
            return offlineHeal;
        } catch (Exception e) {
            LOGGER.error("Exception encountered while adding static threshold. Reason: {}", e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(installationAttributesDao);
        }
        return false;
    }

    public static boolean checkForKeycloakAttributeForUser(String userId, Handle handle) {
        InstallationAttributesDao installationAttributesDao = getInstallationAttributesDao(handle);
        boolean keycloakUserFound = false;
        try {

            String installationMode = installationAttributesDao.checkForUnMapAttribute(userId);

            if (installationMode != null && installationMode.length() != 0 && installationMode.equalsIgnoreCase("Keycloak")) {
                keycloakUserFound = true;
            }
            return keycloakUserFound;
        } catch (Exception e) {
            LOGGER.error("Exception encountered while adding static threshold. Reason: {}", e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, installationAttributesDao);
        }
        return false;
    }

    private static InstallationAttributesDao getInstallationAttributesDao(Handle handle) {
        if (handle == null) {
            return MySQLConnectionManager.getInstance().open(InstallationAttributesDao.class);
        } else {
            return handle.attach(InstallationAttributesDao.class);
        }
    }

    public static String checkForServiceMaintenanceFlag(Handle handle) {
        InstallationAttributesDao installationAttributesDao = getInstallationAttributesDao(handle);
        try {
            return installationAttributesDao.checkForServiceMaintenanceFlag();
        } catch (Exception e) {
            LOGGER.error("Exception encountered while getting service maintenance check monitorEnabled flag. Reason: {}", e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, installationAttributesDao);
        }
        return "true";
    }

    private static void closeDaoConnection(Handle handle, InstallationAttributesDao dao) {
        if (handle == null) {
            MySQLConnectionManager.getInstance().close(dao);
        }
    }
}