package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ServiceTransaction;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.TransactionAuditConfigurationBean;
import com.appnomic.appsone.controlcenter.pojo.AutoAcceptanceSettingsPojo;
import com.appnomic.appsone.controlcenter.pojo.TransactionStatusPojo;
import com.appnomic.appsone.controlcenter.pojo.TxnAndGroupBean;
import com.heal.configuration.entities.BasicTransactionBean;
import com.heal.configuration.pojos.Transaction;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;
import org.skife.jdbi.v2.unstable.BindIn;

import java.util.List;

@UseStringTemplate3StatementLocator
public interface TransactionDao {
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,status,audit_enabled auditEnabled,is_autoconfigured isAutoConfigured,created_time createdTime,updated_time updatedTime,user_details_id userDetailsId,account_id accountId,transaction_type_id transactionTypeId,pattern_hashcode patternHashcode,description,identifier from transaction where account_id=:accountId and name=:txnName")
    TransactionBean getTransaction(@Bind("accountId") Integer accountId, @Bind("txnName") String txnName);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,created_time createdTime,updated_time updatedTime,user_details_id userDetailsId,http_method httpMethod,http_url httpUrl,transaction_id transactionId from sub_transactions where transaction_id=:txnId")
    List<SubTransactionBean> getSubTransaction(@Bind("txnId") Integer txnId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,sub_transaction_id subTransactionId,transaction_attribute_id transactionAttributeId,attribute_1 attribute1,attribute_2 attribute2,attribute_3 attribute3,user_details_id userDetailsId,created_time createdTime,updated_time updatedTime from transaction_matcher_details where sub_transaction_id=:subTxnId")
    List<TransactionMatcherDetailsBean> getTransactionMatcherDetails(@Bind("subTxnId") Integer subTxnId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id,transaction_id transactionId, audit_capture_type_id auditCaptureTypeId, display_name displayName, lookup_name lookupName, created_time createdTime, updated_time updatedTime, user_details_id userDetailsId, status FROM transactions_audit_configuration WHERE transaction_id in (<txnIds>)")
    List<TransactionAuditConfigurationBean> getTxnAuditConfigurationDetails(@BindIn("txnIds") List<Integer> txnIds);


    @SqlUpdate("INSERT INTO transaction (name, status, audit_enabled, is_autoconfigured, created_time, updated_time, user_details_id, account_id, transaction_type_id, pattern_hashcode, description, identifier, rule_id,is_business_txn) VALUES (:name, :status, :auditEnabled, :isAutoConfigured, :createdTime, :updatedTime, :userDetailsId, :accountId, :transactionTypeId, :patternHashcode, :description, :identifier, :ruleId, :isBusinessTxn)")
    @GetGeneratedKeys
    int addTransaction(@BindBean TransactionBean transactionBean);

    @SqlUpdate("INSERT INTO sub_transactions (created_time, updated_time, user_details_id, http_method, http_url, transaction_id) VALUES (:createdTime, :updatedTime, :userDetailsId, :httpMethod, :httpUrl, :transactionId)")
    @GetGeneratedKeys
    int addSubTransaction(@BindBean SubTransactionBean subTransactionBean);

    @SqlUpdate("INSERT INTO transaction_matcher_details (sub_transaction_id, transaction_attribute_id, attribute_1, attribute_2, attribute_3, user_details_id, created_time, updated_time) VALUES (:subTransactionId, :transactionAttributeId, :attribute1, :attribute2 , :attribute3, :userDetailsId, :createdTime, :updatedTime)")
    @GetGeneratedKeys
    int addTransactionMatcherDetails(@BindBean TransactionMatcherDetailsBean transactionMatcherDetailsBean);

    @SqlBatch("INSERT INTO transactions_audit_configuration (transaction_id, audit_capture_type_id, display_name, lookup_name, " +
            "created_time, updated_time, user_details_id, status) VALUES (:transactionId, :auditCaptureTypeId, :displayName, " +
            ":lookupName, :createdTime, :updatedTime, :userDetailsId, :status)")
    void addTxnAuditConfigurationDetails(@BindBean List<TransactionAuditConfigurationBean> txnAuditConfigurationBeanList);

    @SqlBatch("update transactions_audit_configuration set display_name = ifnull(:displayName, display_name), lookup_name = " +
            "ifnull(:lookupName, lookup_name), updated_time = :updatedTime, user_details_id = :userDetailsId " +
            "where id = :id and transaction_id = :transactionId")
    void updateTxnAuditConfigurationDetails(@BindBean List<TransactionAuditConfigurationBean> txnAuditConfigurationBeanList);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("DELETE FROM `transactions_audit_configuration` WHERE id = :id")
    void deleteTxnAuditConfigurationDetailsById(@Bind("id") Integer txnAuditConfigurationId);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, display_name displayName, lookup_name lookupName, transaction_id transactionId from " +
            "transactions_audit_configuration")
    List<TransactionAuditConfigurationBean> getAuditDetailsForTransactions();

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select a.id txnId,a.name txnName,a.status,a.audit_enabled isAutoEnabled,a.is_autoconfigured isAutoConfigured, a.percentile_enabled percentileEnabled, " +
            "a.user_details_id userDetailsId,a.is_business_txn as isBusinessTxn,m.name as transactionTypeName," +
            "a.pattern_hashcode patternHashCode,a.description,a.identifier,a.account_id accountId, a.monitor_enabled monitorEnabled " +
            "from transaction a, mst_sub_type m where a.status = 1 AND m.id = a.transaction_type_id  " +
            "AND a.account_id=:account_id")
    List<TxnAndGroupBean> getTxnAndGroupPerAccount(@Bind("account_id") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select created_time requestDiscoveredTime from transaction where rule_id= :ruleId and account_id = :accountId")
    List<RuleDetailsBean> getLastDiscoveredRequest(@Bind("ruleId") Integer ruleId,@Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select transaction_id,group_concat(concat(attribute_1,\"=\",attribute_2)) value,ST.http_url url,transaction_attribute_id,vt.name name from sub_transactions ST  Left outer join transaction_matcher_details TM on TM.sub_transaction_id=ST.id Left join view_types vt on TM.transaction_attribute_id = vt.subtypeid where ST.transaction_id= :TxnId group by vt.subtypeid, ST.transaction_id")
    List<AttributeTransactionDetailsBean> getAttributeTransaction(@Bind("TxnId") Integer txnId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select sum(monitor_enabled) txnMonitoredCount, count(id) txnCount from view_transaction_service where status = 1 and serviceId = :serviceId and account_id = :accountId")
    TransactionCountBean getServiceTransactionCount(@Bind("serviceId") Integer serviceId, @Bind("accountId") Integer accountId);


    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select serviceId,id txnId from view_transaction_service where status = 1 and account_id = :accountId")
    List<ServiceTransaction> getTransactions(@Bind("accountId") Integer accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, status, created_time createdTime, updated_time updatedTime, name, identifier, user_details_id lastModifiedBy, audit_enabled auditEnabled, monitor_enabled monitorEnabled, percentile_enabled percentileEnabled, rule_id ruleId from transaction where id = :id")
    Transaction getTransactionById(@Bind("id") int id);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select serviceId from view_transaction_service where id = :id")
    int getServiceIdForTxn(@Bind("id") Integer id);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select display_name displayName, lookup_name lookupName from transactions_audit_configuration where transaction_id = :id")
    TransactionAuditConfigurationBean getTxnAuditConfiguration(@Bind("id") Integer id);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select transaction_id transactionId, ifnull(group_concat(concat(attribute_1,\"=\",attribute_2)),\" \") value, ST.http_url url," +
            "ifnull(vt.name,\" \") name from sub_transactions ST  left outer join transaction_matcher_details TM on TM.sub_transaction_id=ST.id left join view_types vt " +
            "on TM.transaction_attribute_id = vt.subtypeid  where ST.transaction_id= :txnId group by ST.transaction_id, vt.subtypeid")
    List<com.heal.configuration.entities.SubTransactionBean> getTxnPatterns(@Bind("txnId") Integer txnId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("update transaction_settings set max_volume_count = ifnull(:minRequestCount, max_volume_count), " +
            "max_transactions_limit = ifnull(:maxAutoAcceptedRequests, max_transactions_limit), " +
            "auto_commit_duration = ifnull(:holdDuration, auto_commit_duration) " +
            "where service_id = :service_id")
    void updateTransactionSettingTable(@BindBean AutoAcceptanceSettingsPojo autoAcceptanceSettingsPojos, @Bind("service_id") Integer service_id);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("update scheduler_details set cron_expression = ifnull(:cron_expression, cron_expression) where id = :scheduler_details_Id")
    void updateSchedulerDetailsTable(@Bind("cron_expression") String cronExpression, @Bind("scheduler_details_Id") Integer id);

    @SqlBatch("update transaction set status = :status, user_details_id = :lastUpdatedBy where id = :id")
    void updateTransactionStatus(@BindBean List<TransactionStatusEntity> transactionStatusEntityList, @Bind("lastUpdatedBy") String lastUpdatedBy);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("delete from transaction where id = :id")
    void deleteTransactionsFromTransactionTable(@BindBean List<TransactionStatusEntity> transactionStatusEntityList);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("delete from sub_transactions where transaction_id = :id")
    void deleteSubTransactions(@BindBean List<TransactionStatusEntity> transactionStatusEntityList);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("delete from transaction_matcher_details where sub_transaction_id = :id")
    void deleteTransactionFromTransactionMatcherDetails(@BindBean List<SubTransactionBean> subTransactionBeanList);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("delete from transaction_response_threshold where transaction_id = :id")
    void deleteTransactionFromTransactionResponseThresholdTable(@BindBean List<TransactionStatusEntity> transactionStatusEntityList);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("delete from transaction_threshold_details where transaction_id = :id")
    void deleteTransactionFromTransactionThresholdDetailsTable(@BindBean List<TransactionStatusEntity> transactionStatusEntityList);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("delete from transactions_audit_configuration where transaction_id = :id")
    void deleteTransactionFromTransactionAuditConfigurationTable(@BindBean List<TransactionStatusEntity> transactionStatusEntityList);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from sub_transactions where transaction_id = :txnId")
    List<SubTransactionBean> getSubTransactionBean(@Bind("txnId") Integer txnId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id, identifier txnIdentifier FROM transaction WHERE identifier = :txnIdentifier OR id = :id")
    TransactionStatusPojo getTxnIdAndIdentifier(@Bind("txnIdentifier") String txnIdentifier, @Bind("id") int id);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, account_id accountId, service_id serviceId, last_committed_time lastCommitedTime, last_committed_transactions lastCommittedTransactions, " +
            "max_volume_count maxVolumeCount, max_transactions_limit maxTransactionsLimit, created_time createdTime, updated_time updatedTime, " +
            "user_details_id userDetailsId, auto_commit_duration autoCommitDuration, scheduler_details_id schedulerDetailsId from transaction_settings " +
            "where account_id = :accountId and service_id = :serviceId")
    ServiceTransactionSettingBean getTransactionSetting(@Bind("accountId") int accountId, @Bind("serviceId") int serviceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("delete from transaction_audit_details where transaction_id = :id")
    void deleteTxnAuditDetails(@BindBean List<TransactionStatusEntity> txnIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("delete from transaction_group_mapping where object_id = :id")
    void deleteTxnGroupMapping(@BindBean List<TransactionStatusEntity> txnIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT t.id id,t.name name,t.status status,t.audit_enabled auditEnabled," +
            " t.created_time createdTime, t.updated_time updatedTime,t.user_details_id lastModifiedBy,t.identifier identifier," +
            " t.rule_id ruleId,t.monitor_enabled monitorEnabled,t.percentile_enabled percentileEnabled,c.id concernedConfigId" +
            " FROM((tag_mapping tm JOIN controller c) JOIN transaction t) WHERE ((tm.tag_id = 1) AND (tm.object_ref_table = 'transaction')" +
            " AND (tm.account_id = t.account_id) AND (tm.tag_key = c.id) AND (t.status = 0) AND (t.id = tm.object_id)" +
            " AND (c.status = 1) AND (c.controller_type_id = 192) AND (c.account_id = tm.account_id)) AND (c.account_id = :accountId)" +
            " AND (c.id = :serviceId)")
    List<BasicTransactionBean> getTransactionsService(@Bind("accountId") int accountId, @Bind("serviceId") int serviceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("update transaction_settings set last_committed_time = :lastCommitTime, last_committed_transactions = :lastAcceptedTxnCount," +
            " last_discarded_transactions = :lastDiscardedTxnCount, user_details_id = :lastUpdatedBy where service_id = :serviceId and account_id = :accountId")
    void updateCommitDetails(@Bind("lastAcceptedTxnCount") int lastAcceptedTxnCount, @Bind("lastDiscardedTxnCount") int lastDiscardedTxnCount,
                             @Bind("lastCommitTime") String lastCommitTime, @Bind("accountId") int accountId,
                             @Bind("serviceId") Integer serviceId, @Bind("lastUpdatedBy") String lastUpdatedBy);
}
