package com.appnomic.appsone.controlcenter.dao.mysql.entity;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.ConstraintViolation;
import javax.validation.constraints.Size;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Data
@NoArgsConstructor
public class WMIProducerTypeDetail extends ProducerTypeDetail{

    private static final Logger LOGGER = LoggerFactory.getLogger(WMIProducerTypeDetail.class);

    @Size(min = 1, max = 256, message = "script name length must be less than or equal to 256")
    private String scriptName;
    private String signature;

    public WMIProducerTypeDetail(int producerId, String createdTime, String updatedTime, String userDetailsId) {
        super(producerId, createdTime, updatedTime, userDetailsId);
    }

    @Override
    public Map<String, String> getDataMap() {
        Map<String, String> map = new HashMap<>();
        map.put("scriptName", scriptName);
        map.put("signature", signature);
        return map;
    }

    public boolean populate(Map<String, String> attributes) {
        this.setScriptName(attributes.get(Constants.WMI_TYPE_SCRIPT_NAME_ATTRIBUTE));
        this.setSignature(attributes.get(Constants.WMI_TYPE_SIGNATURE_ATTRIBUTE));
        return validate();
    }

    private boolean validate() {

        Set<ConstraintViolation<Object>> violations = ValidationUtils.validateFields(this);

        if( ! violations.isEmpty() ) {
            violations.forEach(it -> LOGGER.error(it.getMessage()));
            return false;
        }

        return true;
    }
}
