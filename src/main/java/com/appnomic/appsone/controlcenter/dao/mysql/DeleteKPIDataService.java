package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.DeleteKPIDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DeleteKPIDataService extends AbstractDaoService<DeleteKPIDao> {

    private static final Logger LOGGER = LoggerFactory.getLogger(DeleteKPIDataService.class);


    public void deleteCompInstanceKpiConfiguration(int kpiId, int accountId, Handle handle) throws ControlCenterException {
        DeleteKPIDao dao = getDaoConnection(handle, DeleteKPIDao.class);
        try {
            dao.deleteCompInstanceKpiConfiguration(kpiId, accountId);
        } catch (Exception e) {
            LOGGER.error("Error while deleting component instance kpi configuration. Reason: {}", e.getMessage(), e);
            throw new ControlCenterException("Error while deleting component instance kpi configuration.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deleteCompInstanceKpiThresholdDetails(int kpiId, int accountId, Handle handle) throws ControlCenterException {
        DeleteKPIDao dao = getDaoConnection(handle, DeleteKPIDao.class);
        try {
            dao.deleteCompInstanceKpiThresholdDetails(kpiId, accountId);
        } catch (Exception e) {
            LOGGER.error("Error while deleting component instance kpi threshold details. Reason: {}", e.getMessage(), e);
            throw new ControlCenterException("Error while deleting component instance kpi threshold details.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deleteComponentKpiThresholdDetails(int kpiId, Handle handle) throws ControlCenterException {
        DeleteKPIDao dao = getDaoConnection(handle, DeleteKPIDao.class);
        try {
            dao.deleteComponentKpiThresholdDetails(kpiId);
        } catch (Exception e) {
            LOGGER.error("Error while deleting component kpi threshold details. Reason: {}", e.getMessage(), e);
            throw new ControlCenterException("Error while deleting component kpi threshold details.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deleteCompInstanceKpiGroupDetails(int kpiId, Handle handle) throws ControlCenterException {
        DeleteKPIDao dao = getDaoConnection(handle, DeleteKPIDao.class);
        try {
            dao.deleteCompInstanceKpiGroupDetails(kpiId);
        } catch (Exception e) {
            LOGGER.error("Error while deleting component instance group kpi details. Reason: {}", e.getMessage(), e);
            throw new ControlCenterException("Error while deleting component instance group kpi details.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deleteCompInstanceKpiDetails(int kpiId, Handle handle) throws ControlCenterException {
        DeleteKPIDao dao = getDaoConnection(handle, DeleteKPIDao.class);
        try {
            dao.deleteCompInstanceKpiDetails(kpiId);
        } catch (Exception e) {
            LOGGER.error("Error while deleting component instance kpi details. Reason: {}", e.getMessage(), e);
            throw new ControlCenterException("Error while deleting component instance kpi details.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deleteComponentVersionKpiMapping(int kpiId, Handle handle) throws ControlCenterException {
        DeleteKPIDao dao = getDaoConnection(handle, DeleteKPIDao.class);
        try {
            dao.deleteComponentVersionKpiMapping(kpiId);
        } catch (Exception e) {
            LOGGER.error("Error while deleting component version KPI mapping. Reason: {}", e.getMessage(), e);
            throw new ControlCenterException("Error while deleting component version KPI mapping.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deleteProducerKpiMapping(int kpiId, int accountId, Handle handle) throws ControlCenterException {
        DeleteKPIDao dao = getDaoConnection(handle, DeleteKPIDao.class);
        try {
            dao.deleteProducerKpiMapping(kpiId, accountId);
        } catch (Exception e) {
            LOGGER.error("Error while deleting producer KPI mapping. Reason: {}", e.getMessage(), e);
            throw new ControlCenterException("Error while deleting producer KPI mapping.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deleteKpiCategoryMapping(int kpiId, int accountId, Handle handle) throws ControlCenterException {
        DeleteKPIDao dao = getDaoConnection(handle, DeleteKPIDao.class);
        try {
            dao.deleteKpiCategoryMapping(kpiId, accountId);
        } catch (Exception e) {
            LOGGER.error("Error while deleting category KPI mapping. Reason: {}", e.getMessage(), e);
            throw new ControlCenterException("Error while deleting category KPI mapping.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deleteKpiDetails(int kpiId, int accountId, Handle handle) throws ControlCenterException {
        DeleteKPIDao dao = getDaoConnection(handle, DeleteKPIDao.class);
        try {
            dao.deleteKpiDetails(kpiId, accountId);
        } catch (Exception e) {
            LOGGER.error("Error while deleting KPI details. Reason: {}", e.getMessage(), e);
            throw new ControlCenterException("Error while deleting KPI details.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

}
